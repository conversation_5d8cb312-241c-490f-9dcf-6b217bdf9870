﻿@model AppTech.MSMS.Domain.Models.AgentPoint

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.LabelFor(model => model.AgentID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.AgentID, (SelectList) ViewBag.Agents, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.AgentID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.RemittancePointID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.RemittancePointID, (SelectList) ViewBag.Points, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.RemittancePointID, "", new {@class = "text-danger"})
    </div>
</div>