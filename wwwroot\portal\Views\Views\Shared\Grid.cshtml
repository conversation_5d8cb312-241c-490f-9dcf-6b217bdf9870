﻿@using System.Data
<div>
    <div class="col-sm-12 align-center" style="border-bottom: 1px solid black; margin-top: 1px; margin-bottom: 20px; width: 100%; padding: 8px 0px; height: 30px; font: bold 18px Helvetica, Sans-Serif;">
        @ViewBag.Title
    </div>

    <div class="table-header">
    </div>
    <table id="" class="table table-striped table-bordered table-responsive ">

        <thead>
        <tr>

            @foreach (DataColumn col in Model.Columns)
            {
                <th>@col.Caption</th>
            }


        </tr>
        </thead>

        <tbody>

        @foreach (DataRow row in Model.Rows)
        {
            <tr>

                @foreach (var cell in row.ItemArray)
                {
                    <td>@cell.ToString()</td>
                }
            </tr>
        }

        </tbody>
    </table>
</div>

<!-- page specific plugin scripts -->
<script src="~/Scripts/jquery.dataTables.min.js"></script>
<script src="~/Scripts/jquery.dataTables.bootstrap.min.js"></script>
<script src="~/Scripts/dataTables.buttons.min.js"></script>
<script src="~/Scripts/dataTables.select.min.js"></script>