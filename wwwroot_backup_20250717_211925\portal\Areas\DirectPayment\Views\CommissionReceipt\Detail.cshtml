﻿@model IEnumerable<AppTech.MSMS.Domain.Models.CommissionReceiptLine>
<table class="table table-hover">
    <thead>
        <tr>
            <th>اسم الحساب</th>
            <th>اجمالي التحصيل</th>
            <th>مبلغ العمولة</th>
        </tr>
    </thead>
    @foreach (var Line in Model)
    {
        <tbody>
            <tr>
                @if (Line != null)
                {
                    <td>@Html.DisplayFor(modelItem => Line.Account.Name)</td>
                    <td>@Html.DisplayFor(modelItem => Line.TotalTopup)</td>
                    <td>@Html.DisplayFor(modelItem => Line.Amount)</td>
                }
            </tr>
        </tbody>
    }
    <tfoot>
        <tr style="background:lightgray">
            <td colspan="3" style="padding: 16px 14px 0 0;">
                إجمالي العمولات
            </td>
            <td>
                <input type="number" id="totalAmount" name="totalAmount" value="@ViewBag.Amount" disabled />
            </td>
        </tr>
    </tfoot>
</table>

