﻿@model AppTech.MSMS.Domain.Models.Brochure
@{
    Layout = "~/Views/Shared/_MultiForm.cshtml";
}

@Html.HiddenFor(model => model.ID)

<div class="form-group">
    @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Description, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Description, "", new { @class = "text-danger" })
    </div>
</div>

@*<div class="form-group">
        @Html.LabelFor(model => model.Channel, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Channel, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Channel, "", new { @class = "text-danger" })
        </div>
    </div>*@


<div class="form-group">
    @Html.LabelFor(model => model.ImageUrl, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <input type="file" name="ImageData" size="40" onchange="showImg(this)">
    </div>
</div>
<img class="img-thumbnail" width="150" height="150" id="preview"
     src="@Url.Action("GetImage", "Brochure",
              new {Model.ID})" />