﻿@model IEnumerable<AppTech.MSMS.Domain.Models.WifiCard>
<table class="table table-hover">
    <thead>
        <tr>
            <th>رقم السريال</th>
            <th>الكرت</th>
            <th>كلمة السر</th>
            <th>الوصف</th>
            <th></th>

        </tr>
    </thead>
    @foreach (var Card in Model)
    {
        <tbody>
            <tr>
                @if (Card != null)
                {

                    <td>@Html.DisplayFor(modelItem => Card.SerialNo)</td>
                    <td>@Html.DisplayFor(modelItem => Card.Username)</td>
                    <td>@Html.DisplayFor(modelItem => Card.Password)</td>
                    <td>@Html.DisplayFor(modelItem => Card.Description)</td>
                    <td>
                        <Button class="btn btn-link" onclick="openEditModal('@Card.ID','@Card.FactionID','@Card.ProviderID')">
                            <i class="ace-icon fa fa-edit bigger-110"></i>
                            تعديل
                        </Button>
                          <Button class="btn btn-link" onclick="unbind('@Card.ID')">
                            <i class="ace-icon fa fa-trash-o bigger-110"></i>
                            حذف
                        </Button>

                    </td>
                }
            </tr>
        </tbody>
    }
</table>
<script>
     function openModal(id) {
        i('open modal id' + id);
        openViewAsModal('Wifi/WifiCard/AddOrEditCards?ID=' + id, " جديد");
    }
    function openEditModal(id,FactionID,ProviderID) {
        i('open modal id' + id);
        openViewAsModal('Wifi/WifiCard/AddOrEditCards?ID=' + id + '&ProviderID='+ ProviderID + '&FactionID='+ FactionID );
    }

     function unbind(id) {
         i('remove bundle id' + id);
         var confirms = confirm("هل تريد حذف العنصر؟");
         if (confirms ==true) {

        var fId =@ViewBag.FactionID;
         
        i('remove bundle fId' + fId);
        var url = 'Wifi/WifiCard/Remove?id=' + id+ '&factionId=' + fId;

        AjaxCall(url)
            .done(function (response) {
              //  ar(response.Message);
                $("#list").replaceWith(response);
            }).fail(function (xhr, textStatus, errorThrown) {
                    parseAndShowError(xhr, textStatus, errorThrown);
                });
         }

    }

</script>
