﻿@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
    <ul class="nav nav-list">


        <li class="active">
            <a href="/#!/dashboard">
                <i class="menu-icon fa fa-tachometer"></i>
                <span class="menu-text"> الرئيسية </span>
            </a>

            <b class="arrow"></b>
        </li>

        <li class="">
            <a href="/#!/route/clients/Notification">
                <i class="menu-icon fa fa-mobile-phone"></i>
                <span class="menu-text"> الأشعارات </span>
            </a>

            <b class="arrow"></b>
        </li>
        @if (!(bool)ViewBag.IsApiAccount)
        {
            <li class="">
                <a href="/#!/route/clients/Charging">
                    <i class="menu-icon fa fa-list-alt"></i>
                    <span class="menu-text">الخدمات</span>
                    <b class="arrow"></b>
                </a>

                <b class="arrow"></b>
            </li>
        }

        <li class="">
            <a href="/#!/route/clients/TopupPayment">
                <i class="menu-icon fa fa-list-alt"></i>
                <span class="menu-text">التحصيلات</span>
                <b class="arrow"></b>
            </a>

            <b class="arrow"></b>
        </li>

        @if (CurrentUser.CurrentSession.Party.IsWifiProvider)
        {

            <li class="">
                <a href="" class="dropdown-toggle">
                    <i class="menu-icon fa fa-list-alt"></i>
                    <span class="menu-text">
                        الواي فاي
                    </span>
                    <b class="arrow fa fa-angle-down"></b>
                </a>

                <b class="arrow"></b>

                <ul class="submenu">

                    <li class="">
                        <a href="/#!/route/Wifi/WifiFaction/Faction/@CurrentUser.CurrentSession.Party.AccountID">
                            <i class="menu-icon fa fa-caret-right"></i>
                            فئات الكروت
                        </a>
                        <b class="arrow"></b>
                    </li>

                    <li class="">
                        <a href="/#!/route/clients/WifiPayment/addoredit">
                            <i class="menu-icon fa fa-caret-right"></i>
                            كروت الشبكة
                        </a>
                        <b class="arrow"></b>
                    </li>


                </ul>
            </li>

        }

        @if (CurrentUser.CurrentSession.Party.IsSatelliteProvider)
        {
            <li class="">
                <a href="" class="dropdown-toggle">
                    <i class="menu-icon fa fa-list-alt"></i>
                    <span class="menu-text">
                        القنوات
                    </span>
                    <b class="arrow fa fa-angle-down"></b>
                </a>

                <b class="arrow"></b>
                <ul class="submenu">
                    <li class="">
                        <a href="/#!/route/clients/SatelliteOrder">
                            <i class="menu-icon fa fa-list-alt"></i>
                            <span class="menu-text">طلبات القنوات</span>
                            <b class="arrow"></b>
                        </a>

                        <b class="arrow"></b>
                    </li>
                    <li class="">
                        <a href="/#!/route/Satellite/SatelliteFaction/Faction/@CurrentUser.CurrentSession.Party.AccountID">
                            <i class="menu-icon fa fa-caret-right"></i>
                            فئات القنوات
                        </a>
                        <b class="arrow"></b>
                    </li>
                </ul>
            </li>
        }


        @if (!(bool)ViewBag.IsApiAccount)
        {
            if (DomainManager.LicensedModules.Any(x => x.Equals("Cards")))
            {
                <li class="">
                    <a href="" class="dropdown-toggle">
                        <i class="menu-icon fa fa-list-alt"></i>
                        <span class="menu-text">

                            خدمات كروت الاونلاين

                        </span>
                        <b class="arrow fa fa-angle-down"></b>
                    </a>

                    <b class="arrow"></b>

                    <ul class="submenu">
                        <li class="">
                            <a href="/#!/route/clients/CardOrder/addoredit">
                                <i class="menu-icon fa fa-caret-right"></i>
                                شحن كرت اونلاين
                            </a>
                            <b class="arrow"></b>
                        </li>
                        <li class="">
                            <a href="/#!/route/clients/CardPayment/addoredit">
                                <i class="menu-icon fa fa-caret-right"></i>
                                شراء كرت اونلاين
                            </a>
                            <b class="arrow"></b>
                        </li>


                    </ul>
                </li>

            }

            if (DomainManager.LicensedModules.Any(x => x.Equals("Remittance")))
            {
                if (!(bool)ViewBag.IsApiAccount)
                {
                    <li class="">
                        <a href="" class="dropdown-toggle">
                            <i class="menu-icon fa fa-list-alt"></i>
                            <span class="menu-text">
                                الحوالات

                            </span>
                            <b class="arrow fa fa-angle-down"></b>
                        </a>

                        <b class="arrow"></b>

                        <ul class="submenu">
                            <li class="">
                                <a href="/#!/route/clients/transferorder/addoredit">
                                    <i class="menu-icon fa fa-caret-right"></i>
                                    طلب ارسال/سحب حوالة شركات
                                </a>
                                <b class="arrow"></b>
                            </li>

                            @if (DomainManager.DirectRemittance)
                            {
                                <li class="">
                                    <a href="/#!/route/Clients/DirectRemittance">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        أرسال حوالة شركات مباشر
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            }
                            @if (DomainManager.NetworkRemittance)
                            {

                                <li class="">
                                    <a href="/#!/route/Remittance/RemittanceIn">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        أرسال حوالة شبكة
                                    </a>
                                    <b class="arrow"></b>
                                </li>


                                if (CurrentUser.Type == UserType.Agent)
                                {

                                    <li class="">
                                        <a href="/#!/route/Remittance/RemittanceOut">
                                            <i class="menu-icon fa fa-caret-right"></i>
                                            تسليم حوالة شبكة
                                        </a>
                                        <b class="arrow"></b>
                                    </li>


                                }

                            }

                            <li class="">
                                <a href="/#!/route/Remittance/TransferIn">
                                    <i class="menu-icon fa fa-caret-right"></i>
                                    الحوالات المستلمة
                                </a>
                                <b class="arrow"></b>
                            </li>
                            @if (!DomainManager.DirectRemittance)
                            {
                                <li class="">
                                    <a href="/#!/route/Remittance/TransferOut">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        الحوالات المرسلة
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            }




                        </ul>








                    </li>
                }
            }


        }

        <li class="">

            <a href="" class="dropdown-toggle">
                <i class="menu-icon fa fa-list-alt"></i>
                <span class="menu-text">
                    التقارير

                </span>
                <b class="arrow fa fa-angle-down"></b>
            </a>

            <b class="arrow"></b>

            <ul class="submenu">
                <li class="">
                    <a href="/#!/route/clients/ClientReport">
                        <i class="menu-icon fa fa-caret-right"></i>
                        كشف حساب
                    </a>
                    <b class="arrow"></b>
                </li>


                <li class="">
                    <a href="/#!/route/clients/ClientReport/Topups">
                        <i class="menu-icon fa fa-caret-right"></i>
                        تقرير التحصيلات
                    </a>
                    <b class="arrow"></b>
                </li>

                <li class="">
                    <a href="/#!/route/clients/ClientReport/Transations">
                        <i class="menu-icon fa fa-caret-right"></i>
                        العمليات
                    </a>
                    <b class="arrow"></b>
                </li>

                <li class="">
                    <a href="/#!/route/clients/ClientReport/Orders">
                        <i class="menu-icon fa fa-caret-right"></i>
                        الطلبات
                    </a>
                    <b class="arrow"></b>
                </li>


                @if (CurrentUser.Type == UserType.Merchant)
                {
                    <li class="">
                        <a href="/#!/route/merchants/MerchantReport/Payments">
                            <i class="menu-icon fa fa-list-alt"></i>
                            تقرير المدفوعات
                        </a>
                        <b class="arrow"></b>
                    </li>
                }
            </ul>
        </li>
        @if ((bool)ViewBag.IsApiAccount)
        {
            <li class="">
                <a href="" class="dropdown-toggle">
                    <i class="menu-icon fa fa-list-alt"></i>
                    <span class="menu-text">
                        الاي بي اي

                    </span>
                    <b class="arrow fa fa-angle-down"></b>
                </a>

                <b class="arrow"></b>

                <ul class="submenu">
                    <li class="">
                        <a href="/#!/route/clients/Info">
                            <i class="menu-icon fa fa-caret-right"></i>
                            بيانات الربط
                        </a>
                        <b class="arrow"></b>
                    </li>

                    <li class="">
                        <a href="/#!/route/clients/ClientReport/Factions">
                            <i class="menu-icon fa fa-caret-right"></i>
                            الباقات والفئات
                        </a>
                        <b class="arrow"></b>
                    </li>
                </ul>

            </li>
        }




    </ul>