﻿@using System.Data

<style>
    td.highlight {
        background-color: whitesmoke !important;
    }
</style>
<div class="align-center" style="margin-bottom:20px">
    <strong>
        @if (Model != null)
        {
            @ViewBag.Title
        }
    </strong>
</div>
<div class="table-responsive ">
  
    <table id="simple-table" class="table  table-bordered table-hover display" cellspacing="0" style="width:100%">

        @{


            if (Model != null)
            {

                <thead>
                    <tr>
                        <th rowspan="2">اسم الحساب</th>
                        <th colspan="2"> </th>
                        <th colspan="2">اول الفترة</th>
                        <th colspan="2">خلال الفتره</th>
                        <th colspan="2">الرصيد الختامي</th>

                    </tr>
                    <tr>

                        <th>رقم الحساب</th>
                        <th>نوع الحساب</th>

                        <th>مدين</th>
                        <th>دائن</th>

                        <th>مدين</th>
                        <th>دائن</th>

                        <th>مدين</th>
                        <th>دائن</th>

                    </tr>
                </thead>

                @*<thead>
                        <tr>

                                <th rowspan="3">4</th>
                                <th rowspan="2">1</th>
                                <th rowspan="2">2</th>
                                <th rowspan="2">3</th>

                        </tr>
                        <tr>

                            @foreach (DataColumn col in Model.Result.Columns)
                            {
                                <th>@col.Caption</th>
                            }
                        </tr>
                    </thead>*@
                <tbody>

                    @for (var i = 0; i < Model.Result.Rows.Count - Model.Footer; i++)
                    {
                        <tr>
                            @foreach (var cell in Model.Result.Rows[i].ItemArray)
                            {
                                <td class="align-right">@cell.ToString()</td>
                            }

                        </tr>
                    }
                </tbody>
                <thead>
                    @for (var i = Model.Result.Rows.Count - Model.Footer; i < Model.Result.Rows.Count; i++)
                    {
                        <tr>
                            @if (i < 0)
                            {
                                return;
                            }
                            @foreach (var cell in Model.Result.Rows[i].ItemArray)
                            {
                                <td class="align-right">@cell.ToString()</td>
                            }

                        </tr>
                    }
                </thead>
            }
        }
    </table>

</div>


<script>

    $(function () {
        try {


            var lastIdx = null;
            var table = $('#simple-table').DataTable({
                //    drawCallback: function(){
                //   var api = this.api();

                //   api.columns().every( function () {
                //       i('color cols')

                //       var data = this.data();

                //      if($.inArray('1', data) !== -1){
                //             $(this.nodes()).addClass('highlight');
                //      } else {
                //         $(this.nodes()).removeClass('highlight');
                //      }
                //   });

                //},
                "paging": false,
                "lengthChange": false,
                "searching": false,
                "ordering": false,
            });

            $('#simple-table tbody').on('mouseover', 'td', function ()
            { var colIdx = table.cell(this).index().column; if (colIdx !== lastIdx) { $(table.cells().nodes()).removeClass('highlight'); $(table.column(colIdx).nodes()).addClass('highlight'); } }).on('mouseleave', function () { $(table.cells().nodes()).removeClass('highlight'); });

        }
        catch (e) {
            ar(e);
        }

        $('#simple-table tr').each(function () {

            var type = $(this).find("td:eq(2)").html();
            if (type === 'رئيسي') {
                $(this).addClass('success');
            }

            var status = Number($(this).find("td:eq(1)").html());
            //    i("acc-num= " + status);
            if (status === 1 || status === 2 || status === 3 || status === 4) {
                $(this).addClass('warning');
            }

            else if (status === 0) {
                $(this).addClass('warning');
            }
            //else if (status === 'معلقة' || status === 'غير معروف') {
            //      $(this).addClass('warning');
            //}
            //else if (status === 'فشل') {
            //     $(this).addClass('Danger');
            ////    $(this).css('background-color', 'Danger');
            //}
        });
    });
</script>