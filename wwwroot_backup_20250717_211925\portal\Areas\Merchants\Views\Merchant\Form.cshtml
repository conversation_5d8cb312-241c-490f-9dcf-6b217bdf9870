﻿@using AppTech.MSMS.Domain
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.Merchant
@{
    Layout = "~/Views/Shared/_MultiForm.cshtml";
}

@Html.HiddenFor(model => model.ID)


<div class="form-group">
    @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.CategoryID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.CategoryID, (SelectList)ViewBag.Categories)
        @Html.ValidationMessageFor(model => model.CategoryID, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Description, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Description, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.OwnerName, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.OwnerName, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.OwnerName, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.PhoneNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.PhoneNumber, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.PhoneNumber, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Address, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Address, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Address, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">

    <label class="col-sm-2 control-label">صوره الشعار</label>
    <div style="position: relative;">
        <input type="file" name="ImageData" size="40" onchange="showImg(this)">
    </div>

    <img class="img-thumbnail" width="150" height="150" id="preview"
         src="@Url.Action("GetImage", "Merchant",
                  new {Model.ID})" />

</div>


<script>

    $(function () {
        i('on load');
        $("#SyncAccountID").on('change',
            function () {
                i('on change sync account select');
                var name = $("#SyncAccountID option:selected").text();
                $("#OwnerName").val(name);
            });
    });
</script>