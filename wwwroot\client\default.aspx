﻿<%@ Page Language="C#" AutoEventWireup="true" %>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppTech System - CLIENT</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container { 
            text-align: center; 
            background: rgba(255,255,255,0.1); 
            padding: 40px; 
            border-radius: 15px; 
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            max-width: 600px;
        }
        h1 { 
            font-size: 3em; 
            margin: 0 0 20px 0; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .status { 
            font-size: 1.2em; 
            margin: 20px 0; 
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            margin: 10px; 
            background: rgba(255,255,255,0.2); 
            color: white; 
            text-decoration: none; 
            border-radius: 8px; 
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }
        .btn:hover { 
            background: rgba(255,255,255,0.3); 
            transform: translateY(-2px);
        }
        .info { 
            margin: 20px 0; 
            font-size: 0.9em; 
            opacity: 0.8; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ًںڑ€ AppTech CLIENT</h1>
        <div class="status">
            <p>âœ… ط§ظ„ظ†ط¸ط§ظ… ظٹط¹ظ…ظ„ ط¨ظ†ط¬ط§ط­</p>
            <p>ًں“ٹ ط¬ط§ظ‡ط² ظ„ظ„ط§ط³طھط®ط¯ط§ظ…</p>
            <p>ًں”§ طھظ… ط§ظ„ط¥طµظ„ط§ط­ ظˆط§ظ„طھط­ط¯ظٹط«</p>
        </div>
        <div>
            <a href="/portal" class="btn">ًںڈ  ط§ظ„ط¨ظˆط§ط¨ط© ط§ظ„ط±ط¦ظٹط³ظٹط©</a>
            <a href="/api" class="btn">ًں”Œ API</a>
            <a href="/client" class="btn">ًں‘¥ ط§ظ„ط¹ظ…ظ„ط§ط،</a>
            <a href="/collections_system" class="btn">ًں’° ط§ظ„طھط­طµظٹظ„ط§طھ</a>
        </div>
        <div class="info">
            <p>طھظ… ط¥طµظ„ط§ط­ ط§ظ„ظ†ط¸ط§ظ… ظˆطھط­ط¯ظٹط« ط¬ظ…ظٹط¹ ط§ظ„ظ…ظ„ظپط§طھ ط§ظ„ظ…ط·ظ„ظˆط¨ط©</p>
            <p>ط§ظ„طھط§ط±ظٹط®: <%= DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") %></p>
        </div>
    </div>
</body>
</html>
