﻿@model IEnumerable<AppTech.MSMS.Domain.Models.AccountUser>

@{
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}
<p>
    <a class="blue" href="" onclick="openModal('@ViewBag.ParentID', '@ViewBag.ParentType')">
        <i class="ace-icon fa fa-plus bigger-150"></i>
        <span> أضافة مستخدم جديد </span>
    </a>
</p>
<div id="list">
    @Html.Partial("_Users")
</div>
<p>

    <a href="@ViewBag.ReturnUrl" class="btn btn-sm btn-primary btn-white btn-round">
        <i class="ace-icon fa fa-rss bigger-150 middle orange2"></i>
        <span class="bigger-110">الرجوع الى الخلف</span>

        <i class="icon-on-right ace-icon fa fa-arrow-right"></i>
    </a>
</p>
@Html.Partial("_Modal")

<script>


//var url = "~/Scripts/bootbox.js";
//$.getScript(url);
    function openModal(id, type) {
        i('open modal id' + id + ' type ' + type);
        openViewAsModal('Security/User/AddOrEditUser?parentType=' + type + '&parentId=' + id, "مستخدم جديد");
    }

    function permissions(id) {
        var title = "أعداد صلاحيات مستخدم الوكيل";
        openViewAsModal("/Security/User/Permission/" + id, title);
    }

    function resetpass(id) {


        bootbox.confirm("سوف يتم اعداد كلمة المرور لهذا المستخدم , هل انت متأكد?",
            function(result) {
                if (result) {
                    try {

                        $.ajax({
                            url: '@Url.Action("ResetPassword", "User")',
                            data: { id: id },
                            success: function(data) {
                                alert(data);
                            },
                            error: function(xhr, ajaxOptions, thrownError) {
                                alert(xhr.responseText);

                            }
                        });
                    } catch (e) {
                        alert(e);
                    }

                }
            });
    }
</script>