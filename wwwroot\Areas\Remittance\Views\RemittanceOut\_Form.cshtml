﻿@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.RemittanceOut
@{
    Layout = "~/Views/Shared/_MultiLayout.cshtml";
}

@Html.HiddenFor(x => x.RemittanceID)
@if (CurrentUser.Type == UserType.Admin)
{
    <div class="form-group">
        @Html.LabelFor(model => model.CreditorAccountID, new {@class = "control-label col-md-2"})
        <div class="col-md-10">

            @Html.Obout(new ComboBox("CreditorAccountID")
            {
                Width = 300,
                SelectedValue = Model.CreditorAccountID == 0 ? null : Model.CreditorAccountID.ToString(),
                FilterType = ComboBoxFilterType.Contains,
                LoadingText = "Loading"
            })

            @Html.ValidationMessageFor(model => model.CreditorAccountID)
        </div>
    </div>
}
@*<div class="form-group">
    @Html.LabelFor(model => model.CurrencyID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CurrencyID, (SelectList)ViewBag.Currencies, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.CurrencyID, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Amount, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Amount, "", new { @class = "text-danger" })
    </div>
</div>*@<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">بطاقة المستفيد</label>
    <div style="position: relative;">
        <input type="file" name="ImageData" size="40" onchange="showImg(this)">
    </div>
    <img class="img-thumbnail" width="150" height="150" id="preview"/>
</div>

@*<img id="formloader" class="img-center" src="@Url.Content("~/Content/images/loader-64x/Preloader_4.gif")" alt="loading" />*@

<script>

    $(function() {

        //  hideFormLoading();

        //$('#Amount').on('input',
        //    function () {
        //        i('Amount key down');

        //        var words = toWords($('#Amount').val());
        //        i('Amount to words ' + words);
        //    });
        try {
            formHelper.onSuccess = function(data) {
                log('remittanceout.onSuccess');
                //        hideFormLoading();
                i('data> ' + data);
                //  $('#crudform')[0].reset();

                ar('تم صرف الحوالة بنجاح');
                //if (confirm("هل تريد طباعة سند صرف حوالة")) {
                //    PrintReceipt('صرف حوالة', data);
                //}
                window.location.href = '/#!/route/Remittance/RemittanceOut/Delivered/' + data.Id;
            }
            formHelper.onBegin = function(context) {
                var msg = "سوف يتم صرف هذه الحوالة هل انت متأكد";
                if (!confirm(msg)) {
                    i('dont withdraw');
                    return false;
                }
                return true;
                //      showFormLoading();
            };
        } catch (e) {
        }
    });


</script>