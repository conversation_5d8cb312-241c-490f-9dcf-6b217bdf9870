﻿@model AppTech.MSMS.Domain.Reports.Models.SimReportModel
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Domain
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


    <div class="form-horizontal">

        <span class="lbl">الحالة</span>
        @Html.EnumDropDownListFor(model => model.State)
        <div class="space-6"></div>



        <span class="lbl" id="dateTitle"> </span>
        @{
            Html.RenderPartial("_DateControl");
        }
        <span class="lbl">الحساب</span>
        <select id="AccountID" name="AccountID" class="select2" placeholder="كافة الحسابات"></select>
        <div class="space-6"></div>


        <span class="lbl">المستخدم </span>
        @Html.DropDownListFor(m => m.UserID, (SelectList)ViewBag.Users)
        <div class="space-6"></div>

        <span class="lbl">النوع</span>
        @Html.EnumDropDownListFor(model => model.SimType)
        <div class="space-6"></div>







        <span class="lbl"> نوع التقرير</span>
        @Html.EnumDropDownListFor(m => m.Type)


        <div class="space-6"></div>
        <div class="space-6"></div>
        <div id="SimNumberRow">
            <span class="lbl">رقم الشريحة</span>
            @Html.EditorFor(model => model.SimNumber, new
       {
           htmlAttributes = new { @placeholder = "كافة الشرائح" }
       })
        </div>
        <div class="space-6"></div>

        <div id="GroupByUserRow">
            <span class="lbl">التجميع بالمستخدمين</span>
            @Html.EditorFor(model => model.GroupByUser)
        </div>

        <div class="space-6"></div>
        <div class="space-6"></div>
        <div id="GroupByAccountRow">
            <span class="lbl">التجميع بالحسابات</span>
            @Html.EditorFor(model => model.GroupByAccount)
        </div>
        <div class="space-6"></div>
    </div>
        <script>

            function setDateTitle() {
                var val = Number($("#State").val());
                i('setDateTil val '+val);
                if (val === 1) 
                    $("#dateTitle").text('تاريخ التفعيل');
                else
                    $("#dateTitle").text('تاريخ الأدخال');
            }
            function setReportType() {
                var index = $("#Type")[0].selectedIndex;
                if (index === 0) {

                    $("#SimNumberRow").hide();
                    $("#GroupByUserRow").show();
                    $("#GroupByAccountRow").show();
                }
                else {
                    $("#SimNumberRow").show();
                    $("#GroupByUserRow").hide();
                    $("#GroupByAccountRow").hide();
                }
            }
            $(function () {

                $("#State")[0].selectedIndex = 1;
                setReportType();
                $("#Type").on('change', function () {
                    setReportType();
                });

                
                setDateTitle();
                $("#State").on('change', function () {
                    setDateTitle();
                });
                console.log('topupreport load');
                AjaxCall('/Print/GetParties').done(function (response) {
                    console.log('get parties');
                    if (response.length > 0) {
                        $('#AccountID').html('');
                        var options = '<option value="0">كافة الحسابات</option>';
                        for (var i = 0; i < response.length; i++) {
                            options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
                        }
                        $('#AccountID').append(options);

                    }
                }).fail(function (xhr, textStatus, errorThrown) {
                    parseAndShowError(xhr, textStatus, errorThrown);
                });

                //select2
                $('.select2').css('width', '200px').select2({ allowClear: true });
                $('#select2-multiple-style .btn').on('click',
                    function (e) {
                        var target = $(this).find('input[type=radio]');
                        var which = parseInt(target.val());
                        if (which == 2) $('.select2').addClass('tag-input-style');
                        else $('.select2').removeClass('tag-input-style');
                    });
            });

        </script>
