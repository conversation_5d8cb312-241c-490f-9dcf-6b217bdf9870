﻿@using AppTech.MSMS.Domain.Entities
@model AppTech.MSMS.Domain.Models.BagatPayment
@Html.Partial("_Alert")
    <div id="sub">
        @if (ViewBag.Balance != null)
        {
                <div class="form-group form-inline">
                    @Html.Label("رصيد المشترك :", new { @class = "control-label col-md-2" })
            <div class="col-md-10 form-inline">
                <div id="balance-row">
                    <Label>
                        @ViewBag.Balance
                    </Label>
                </div>

                <div  id="linetype-row">
                    <input type="text" id="lineType" name="LineType" value="@ViewBag.LineType" readonly="readonly" />
                </div>
                
            </div>
                </div>
                }




                @if (ViewBag.Offers != null)
                {
                    <div class="form-group">
                        @Html.LabelFor(model => model.OfferCode, new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(m => m.OfferCode, (SelectList)ViewBag.Offers)
                            @Html.ValidationMessageFor(model => model.OfferCode)
                        </div>
                    </div>


                    <div class="form-group">
                        @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
                        <div class="col-md-10 form-inline">
                            @Html.EditorFor(m => m.Amount, new { @class = "target" })
                            @Html.ValidationMessageFor(model => model.Amount)


                            <div class="checkbox">
                                <input type="checkbox" id="Borrowed" style="margin:0 7px" />
                            </div>
                            <label>متسلف</label>
                            <button id="subbmit"  type="button" class="btn btn-default btn-bold btn-round loading" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار">فحص السلفه</button>
                        </div>



                    </div>

                    <div class="form-group">
                        @Html.Label("التنفيذ" ,new { @class = "control-label col-md-2" })
                        <div class="col-md-10 form-inline">
                            @Ajax.ActionLink(
                    "تسديد",
                    "Topup",
                    null,
                    new AjaxOptions
                    {
                        LoadingElementId = "loader",
                        OnSuccess = "onTopupSuccess",
                        OnFailure = "onTopupFailure",
                        OnBegin = "onTopupBegin",
                        HttpMethod = "GET"
                    },
                    new { @class = "btn btn-primary btn-round", onclick = "this.href = '/clients/YMOfferPayment/topup?amount=' + $('#Amount').val() + '&sno=' +$('#SubscriberNumber').val()" }
                    )
                            <button class="btn btn-primary btn-bold btn-round" type="submit" name="submitButton" id="onlybagat" value="onlybagat">
                                <i class="ace-icon fa fa-mobile-phone bigger-110"></i>
                                تفعيل من الرصيد
                            </button>

                            <button class="btn btn-primary btn-bold btn-round" type="submit" name="submitButton" id="topupandBagat" value="topupandBagat">
                                <i class="ace-icon fa fa-mobile-phone bigger-110"></i>
                                تسديد + تفعيل
                            </button>
                        </div>
                    </div>


                }

                @if (Model.Offers != null && Model.Offers.Count > 0)
                {
                    <span class="label-info">
                        الأشتراكات الحالية

                    </span>
                    <table class="table table-responsive table-hover">
                        <thead>
                            <tr>
                                <th>
                                    الكود
                                </th>
                                <th>
                                    الباقة
                                </th>

                                <th>
                                    تاريخ الأشتراك
                                </th>

                                <th>
                                    تاريخ الأنتهاء
                                </th>
                                <th>
                                </th>
                            </tr>

                        </thead>
                        @for (var i = 0; i < Model.Offers.Count; i++)
                        {
                            <tr>


                                <td>
                                    @Html.TextBoxFor(modelItem => Model.Offers[i].ID, htmlAttributes: new { @readonly = "readonly" })
                                </td>
                                <td>
                                    @Html.TextBoxFor(modelItem => Model.Offers[i].Name)
                                </td>

                                <td>
                                    @Html.TextBoxFor(modelItem => Model.Offers[i].StartDate)

                                </td>

                                <td>
                                    @Html.TextBoxFor(modelItem => Model.Offers[i].ExpireDate)
                                </td>
                                <td>

                                    <button class="btn btn-link" onclick="renew_offer('@Model.Offers[i].ID', '@Model.Offers[i].Name', 2); ">
                                        <i class="ace-icon fa fa-refresh bigger-120 green"></i>
                                        تجديد
                                    </button>
                                    <button class="btn btn-link " onclick="renew_offer('@Model.Offers[i].ID', '@Model.Offers[i].Name', 3);">
                                        <i class="ace-icon fa fa-remove bigger-120 green"></i>
                                        إلغاء
                                    </button>

                                </td>
                            </tr>
                        }

                    </table>
                }

            </div>
            <script>
          $(function() {

               $('.loading').on('click',
            function() {
                var $this = $(this);
                $this.button('loading');
                   });

              $('#OfferCode').on('change',
                  function() {
                      var code = this.value;
                      i('OfferCode onchange: val ' + code);
                      //var num = Number($("#AccountState").children("option:selected").val());
                      var data = { code: code }
                      AjaxCall('/Clients/YMOfferPayment/GetPrice', data).done(function(response) {
                          i('GetPrice res ' + response);
                          if (Number(response) > 0) {
                              if ($("#Borrowed").is(":checked") == true) {
                              $("#Amount").val(Number(response)+118);
                              }
                              else
                              $("#Amount").val(response);
                          }
                      }).fail(function(error) {
                          i(error);
                      });
                  });

              $("#Borrowed").on("change", function () {
       //     console.log(10);
            if ($("#Borrowed").is(":checked") == true) {
                $('#Amount').val(Number($('#Amount').val()) + 118);
                //$("#subbmit").hide();
                return;
            }
            $('#Amount').val(Number($('#Amount').val()) - 118);
            $("#subbmit").show();

        });

        $("#subbmit").on("click", function () {
            var sno = $('#SubscriberNumber').val();
            var accId = $('#AccountID').val();
            console.log('sno '+sno);
            $.ajax({
                method: "POST",
                url: "@Url.Action("CheckLoang","YMOfferPayment")",
                data: {num:sno,accountId:accId},
                success: function (data) {
                    resetButton();
                    console.log(data);
                    if (data == "True" && $("#Borrowed").is(":checked") == false) {
                        $('#Borrowed').click();

                    }

                    if (data == "False") {
                        ar('غير متسلف');
                    }
                    else {
                        ar(' هذا الرقم متسلف');
                    }
                },
                error: function () {
                    resetButton();
                    alert("لم يتمكن من فحص السلفة");
                }
            });
        });
          });

            </script>
