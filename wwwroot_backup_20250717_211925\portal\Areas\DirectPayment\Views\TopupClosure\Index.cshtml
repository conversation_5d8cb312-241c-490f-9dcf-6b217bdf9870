﻿@model AppTech.MSMS.Domain.Reports.Models.TopupModel
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Domain
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}

<span class="lbl">التاريخ</span>
@Html.EditorFor(model => model.StartDate, new
           {
               htmlAttributes = new {@class = "date-picker", @readonly = "@readonly"}
           })
@Html.ValidationMessageFor(model => model.StartDate, "", new { @class = "text-danger" })
<div class="space-6"></div>

<span class="lbl">اسم المزود </span>
@Html.DropDownListFor(model => model.ProviderID, (SelectList)ViewBag.Providers)
<div class="space-6"></div>


<script>

    try {
        $('.date-picker').datepicker({
            dateFormat: "dd/MM/yy",
            changeMonth: true,
            changeYear: true,
            yearRange: "-60:+0",
            autoclose: true,
            todayHighlight: true,

        }).next().on('click',
            function() {
                $(this).prev().focus();
            });
    } catch (e) {
        alert("Couldnt set date-picker: " + e);
    }

</script>