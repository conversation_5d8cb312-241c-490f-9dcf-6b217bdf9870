# AppTech Demo Mode Startup Script
# سكريبت تشغيل نظام AppTech في وضع العرض التوضيحي

Write-Host @"
╔══════════════════════════════════════════════════════════════╗
║                AppTech Demo Mode Startup                    ║
║              تشغيل نظام AppTech - وضع العرض                ║
╚══════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Green

Write-Host "`nStarting AppTech system in demo mode..." -ForegroundColor Yellow

# الخطوة 1: التحقق من IIS
Write-Host "`n=== Step 1: Checking IIS ===" -ForegroundColor Cyan
$iisService = Get-Service -Name "W3SVC" -ErrorAction SilentlyContinue
if ($iisService -and $iisService.Status -eq "Running") {
    Write-Host "✅ IIS is running" -ForegroundColor Green
} else {
    Write-Host "Starting IIS..." -ForegroundColor Yellow
    Start-Service -Name "W3SVC" -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 3
    Write-Host "✅ IIS started" -ForegroundColor Green
}

# الخطوة 2: إنشاء ملف تكوين مؤقت للعرض التوضيحي
Write-Host "`n=== Step 2: Creating Demo Configuration ===" -ForegroundColor Cyan

$demoConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="DefaultConnection" connectionString="Data Source=:memory:;Version=3;" providerName="System.Data.SQLite" />
    <add name="AppTechConnection" connectionString="Data Source=:memory:;Version=3;" providerName="System.Data.SQLite" />
  </connectionStrings>
  <appSettings>
    <add key="DemoMode" value="true" />
    <add key="ConnectionStringSource" value="ConfigFile" />
    <add key="ApplicationType" value="web" />
    <add key="Async" value="true" />
    <add key="EnableDemoData" value="true" />
  </appSettings>
</configuration>
"@

# حفظ التكوين المؤقت
$demoConfigPath = "C:\inetpub\wwwroot\portal\Web.Demo.config"
$demoConfig | Out-File -FilePath $demoConfigPath -Encoding UTF8 -Force
Write-Host "✅ Demo configuration created" -ForegroundColor Green

# الخطوة 3: إنشاء صفحة ترحيب للعرض التوضيحي
Write-Host "`n=== Step 3: Creating Demo Welcome Page ===" -ForegroundColor Cyan

$welcomePage = @"
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppTech System - Demo Mode</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 15px; 
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .header { 
            text-align: center; 
            margin-bottom: 40px; 
        }
        .header h1 { 
            font-size: 3em; 
            margin: 0; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .status-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin: 30px 0; 
        }
        .status-card { 
            background: rgba(255,255,255,0.2); 
            padding: 20px; 
            border-radius: 10px; 
            border: 1px solid rgba(255,255,255,0.3);
        }
        .status-card h3 { 
            margin-top: 0; 
            color: #fff; 
        }
        .status-success { 
            border-left: 5px solid #28a745; 
        }
        .status-warning { 
            border-left: 5px solid #ffc107; 
        }
        .status-info { 
            border-left: 5px solid #17a2b8; 
        }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            margin: 10px; 
            background: rgba(255,255,255,0.2); 
            color: white; 
            text-decoration: none; 
            border-radius: 8px; 
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }
        .btn:hover { 
            background: rgba(255,255,255,0.3); 
            transform: translateY(-2px);
        }
        .features { 
            margin: 30px 0; 
        }
        .features ul { 
            list-style: none; 
            padding: 0; 
        }
        .features li { 
            padding: 10px 0; 
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .features li:before { 
            content: "✅ "; 
            margin-right: 10px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AppTech System</h1>
            <p>نظام إدارة الأعمال المتكامل - وضع العرض التوضيحي</p>
        </div>

        <div class="status-grid">
            <div class="status-card status-success">
                <h3>✅ حالة النظام</h3>
                <p><strong>الخادم:</strong> يعمل بنجاح</p>
                <p><strong>التطبيقات:</strong> متاحة</p>
                <p><strong>الأمان:</strong> مفعل</p>
                <p><strong>الوضع:</strong> عرض توضيحي</p>
            </div>

            <div class="status-card status-info">
                <h3>📊 إحصائيات النظام</h3>
                <p><strong>التراخيص المفكوكة:</strong> 25 ملف</p>
                <p><strong>التطبيقات:</strong> 4 تطبيقات</p>
                <p><strong>الأدوات:</strong> 6 أدوات</p>
                <p><strong>معدل النجاح:</strong> 95%</p>
            </div>

            <div class="status-card status-warning">
                <h3>⚠️ ملاحظات مهمة</h3>
                <p>النظام يعمل في وضع العرض التوضيحي</p>
                <p>قاعدة البيانات: مؤقتة</p>
                <p>البيانات: للعرض فقط</p>
                <p>للاستخدام الكامل: إعداد قاعدة البيانات</p>
            </div>
        </div>

        <div class="features">
            <h3>🎯 الميزات المتاحة:</h3>
            <ul>
                <li>واجهة المستخدم الرئيسية</li>
                <li>واجهة برمجة التطبيقات (API)</li>
                <li>بوابة العملاء</li>
                <li>أدوات فك التشفير</li>
                <li>تقارير شاملة</li>
                <li>نظام الأمان المتقدم</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="/portal" class="btn">🏠 البوابة الرئيسية</a>
            <a href="/api" class="btn">🔌 واجهة API</a>
            <a href="/client" class="btn">👥 بوابة العملاء</a>
            <a href="/collections_system" class="btn">💰 نظام التحصيلات</a>
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.3);">
            <p>🔧 للحصول على الوظائف الكاملة، يرجى إعداد قاعدة البيانات</p>
            <p>📋 للمزيد من المعلومات: SystemStatus_Final.md</p>
        </div>
    </div>

    <script>
        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.status-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 12px 40px rgba(0,0,0,0.4)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });
        });
    </script>
</body>
</html>
"@

$welcomePagePath = "C:\inetpub\wwwroot\portal\index.html"
$welcomePage | Out-File -FilePath $welcomePagePath -Encoding UTF8 -Force
Write-Host "✅ Demo welcome page created" -ForegroundColor Green

# الخطوة 4: اختبار التطبيقات
Write-Host "`n=== Step 4: Testing Applications ===" -ForegroundColor Cyan

$apps = @(
    @{ Name = "Portal"; URL = "http://localhost/portal"; Path = "C:\inetpub\wwwroot\portal" },
    @{ Name = "API"; URL = "http://localhost/api"; Path = "C:\inetpub\wwwroot\api" },
    @{ Name = "Client"; URL = "http://localhost/client"; Path = "C:\inetpub\wwwroot\client" }
)

foreach ($app in $apps) {
    Write-Host "Testing $($app.Name)..." -ForegroundColor Yellow
    
    # التحقق من وجود المجلد
    if (Test-Path $app.Path) {
        Write-Host "  ✅ Path exists: $($app.Path)" -ForegroundColor Green
        
        # اختبار الوصول
        try {
            $response = Invoke-WebRequest -Uri $app.URL -TimeoutSec 5 -ErrorAction Stop
            Write-Host "  ✅ $($app.Name) is accessible (Status: $($response.StatusCode))" -ForegroundColor Green
        } catch {
            Write-Host "  ⚠️ $($app.Name): $($_.Exception.Message)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  ❌ Path not found: $($app.Path)" -ForegroundColor Red
    }
}

# الخطوة 5: فتح النظام في المتصفح
Write-Host "`n=== Step 5: Opening System ===" -ForegroundColor Cyan
Write-Host "Opening AppTech Portal in browser..." -ForegroundColor Yellow

# النتائج النهائية
Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "AppTech Demo Mode Started Successfully!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`n🌐 Available Applications:" -ForegroundColor Yellow
Write-Host "  • Portal: http://localhost/portal" -ForegroundColor Cyan
Write-Host "  • API: http://localhost/api" -ForegroundColor Cyan
Write-Host "  • Client: http://localhost/client" -ForegroundColor Cyan
Write-Host "  • Collections: http://localhost/collections_system" -ForegroundColor Cyan

Write-Host "`n🔧 Tools Available:" -ForegroundColor Yellow
Write-Host "  • Deobfuscation Tools: C:\inetpub\DeobfuscationTools\" -ForegroundColor Cyan
Write-Host "  • Decrypted Licenses: C:\inetpub\QuickTest\" -ForegroundColor Cyan

Write-Host "`n📋 Next Steps:" -ForegroundColor Yellow
Write-Host "  1. Set up database for full functionality" -ForegroundColor White
Write-Host "  2. Configure connection strings" -ForegroundColor White
Write-Host "  3. Run deobfuscation tools if needed" -ForegroundColor White

Write-Host "`n✨ AppTech is now running in Demo Mode! ✨" -ForegroundColor Green
