﻿@{
    ViewBag.Title = "أعدادات المصارفة";
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}
<div class="setting">
    <div class="partialContents" data-url="/CurrencyExchange/CurrencyExchangeSetting/form">
        @Html.Partial("_Indicator")
    </div>
</div>
<script>

    var site = site || {};
    site.baseUrl = site.baseUrl || "";
    //i('site.baseUrl:' + site.baseUrl);
    $(document).ready(function(e) {
        //$("#page-title").val('الأعدادات');
        $(".partialContents").each(function(index, item) {
            var url = site.baseUrl + $(item).data("url");
            if (url && url.length > 0) {
                $(item).load(url);

            }
        });
    });

</script>
<script>


    function OnFormBegin(context) {
    }

    function onCrudSuccess(data) {
        ar(data);
        resetButton();
    }

    function onCrudFailure(xhr, textStatus, errorThrown) {

        resetButton();
        i('textStatus ' + textStatus);
        i('errorThrown ' + errorThrown);

        var msg = parseXhr(xhr);
        log('onCrudFailure xhr msg:' + msg);
        alert(msg);
    }
</script>