﻿# Resource Extractor for .NET Assemblies
param([string]$AssemblyPath, [string]$OutputDir = ".\Resources")

try {
    Add-Type -AssemblyName System.Reflection
    $assembly = [System.Reflection.Assembly]::LoadFile($AssemblyPath)
    $resources = $assembly.GetManifestResourceNames()
    
    if (!(Test-Path $OutputDir)) {
        New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    }
    
    Write-Host "Found $($resources.Count) resources in assembly" -ForegroundColor Green
    
    foreach ($resource in $resources) {
        try {
            $stream = $assembly.GetManifestResourceStream($resource)
            $outputFile = Join-Path $OutputDir $resource
            $fileStream = [System.IO.File]::Create($outputFile)
            $stream.CopyTo($fileStream)
            $fileStream.Close()
            $stream.Close()
            Write-Host "Extracted: $resource" -ForegroundColor Cyan
        } catch {
            Write-Host "Failed to extract: $resource" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
