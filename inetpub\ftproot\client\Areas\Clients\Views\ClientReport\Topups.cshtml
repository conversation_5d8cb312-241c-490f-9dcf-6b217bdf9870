﻿@model AppTech.MSMS.Domain.Reports.Models.TopupModel
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Domain
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


<div class="form-horizontal">
    @{
        Html.RenderPartial("_DateControl");
    }

    <span class="lbl">اسم الخدمة</span>
    @Html.DropDownListFor(model => model.ServiceID, (SelectList)ViewBag.Services)
    <div class="space-6"></div>

    <span class="lbl">الحالة</span>
    @Html.EnumDropDownListFor(model => model.Status)
    <div class="space-6"></div>

    <span class="lbl"> نوع التقرير</span>
    @Html.EnumDropDownListFor(m => m.Type)

</div>
