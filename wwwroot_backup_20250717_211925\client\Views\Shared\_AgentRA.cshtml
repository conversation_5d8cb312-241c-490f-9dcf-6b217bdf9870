﻿@using AppTech.MSMS.Web.Models
@Html.ActionButton((string)ViewBag.PageName, "SHOW", "", "eye", "detail", "التفاصيل", false)
@Html.Partial("_RecordAction", new ActionModel { ID = Model.ID })
@Html.ActionButton((string)ViewBag.PageName, "USERS", "", "user-plus", "users", "المستخدمين", true)
@Html.ActionButton((string)ViewBag.PageName, "POINTS", "النقاط", "users", "clients", "النقاط", true)
@Html.ActionButton((string)ViewBag.PageName, "PERMISSIONS", "الصلاحيات", "shield", "permissions", "الصلاحيات", true)
