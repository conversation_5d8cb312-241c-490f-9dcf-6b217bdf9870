﻿@model AppTech.MSMS.Domain.Reports.Models.TransferModel
@{
	ViewBag.Title = " تقرير الحوالات الشركات";
	Layout = "~/Views/Shared/_Report.cshtml";
}

@{
	Html.RenderPartial("_DateControl");
}

<span class="lbl">اسم الحساب </span>
	<select id="AccountID" name="AccountID" class="select2" placeholder="كافة الحسابات"></select>
	<div class="space-6"></div>

<span class="lbl"> نوع الحوالة</span>
@Html.EnumDropDownListFor(m => m.Type)

<div class="hr hr-dotted hr-24"></div>
<div class="space-6"></div>
<span class="lbl"> الجهة</span>
@Html.DropDownListFor(m => m.ExchangerID, (SelectList) ViewBag.Exchangers)

<div class="hr hr-dotted hr-24"></div>
<div class="space-6"></div>
<span class="lbl"> العملة</span>
@Html.DropDownListFor(m => m.CurrencyID, (SelectList) ViewBag.Currencies)

@*<div class="hr hr-dotted hr-24"></div>
<div class="space-6"></div>
<span class="lbl"> المستخدم</span>
@Html.DropDownListFor(m => m.UserID, (SelectList) ViewBag.Users)*@

<script>
    $(function () {
        console.log('topupreport load');
        AjaxCall('/Print/GetParties').done(function (response) {
            console.log('get parties');
            if (response.length > 0) {
                $('#AccountID').html('');
                var options = '<option value="0">كافة الحسابات</option>';
                for (var i = 0; i < response.length; i++) {
                    options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
                }
                $('#AccountID').append(options);

            }
        }).fail(function (xhr, textStatus, errorThrown) {
            parseAndShowError(xhr, textStatus, errorThrown);
        });

        //select2
        $('.select2').css('width', '200px').select2({ allowClear: true });
        $('#select2-multiple-style .btn').on('click',
            function (e) {
                var target = $(this).find('input[type=radio]');
                var which = parseInt(target.val());
                if (which == 2) $('.select2').addClass('tag-input-style');
                else $('.select2').removeClass('tag-input-style');
            });
    });

</script>