﻿<div class="btn-group">
    @Html.ActionButton((string)ViewBag.PageName, "EDIT", "", "pencil", "edit-record", "تعديل ")
    @Html.ActionButton((string)ViewBag.PageName, "DELETE", "", "trash-o red", "delete-record", "حذف ")
</div>
@*<div class="hidden-md hidden-lg">
    <div class="inline pos-rel">
        <button class="btn btn-minier btn-primary dropdown-toggle" data-toggle="dropdown" data-position="auto">
            <i class="ace-icon fa fa-cog icon-only bigger-110"></i>
        </button>
        <ul class="dropdown-menu dropdown-only-icon dropdown-yellow dropdown-menu-right dropdown-caret dropdown-close">
            <li>
                @Html.ActionButton((string)ViewBag.PageName, "EDIT", "", "pencil", "edit-record", "تعديل ")
            </li>
            <li>
                @Html.ActionButton((string)ViewBag.PageName, "DELETE", "", "trash-o red", "delete-record", "حذف ")
            </li>
        </ul>

    </div>
</div>*@
