﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.TransferOut

@{
    Layout = "/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    @Html.LabelFor(model => model.TransferNumber, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.TransferNumber, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.TransferNumber, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group form-inline">
    @Html.LabelFor(model => model.Amount, new {@class = "control-label col-md-2"})
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Amount, new {@class = "col-md-8"})
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.CurrencyID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CurrencyID, (SelectList) ViewBag.Currencies, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.CurrencyID, "", new {@class = "text-danger"})
    </div>
</div>

@*<div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">العمولة</h3>
        </div>
        <div class="panel-body">


            <div class="form-group">
                @Html.Label("العمولة", new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <input type="text" id="CommissionAmount" name="CommissionAmount" readonly="readonly" />
                </div>
            </div>


            <div class="form-group">
                @Html.Label("عملة العمولة", new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <input type="text" id="currencyname" name="currencyname" readonly="readonly" />
                </div>
            </div>

        </div>

        <div class="panel-footer">
            <input type="button" class="btn btn-white" value="احتساب العمولة" onclick="calcCommission();" />
        </div>
    </div>*@
<div class="form-group">
    @Html.LabelFor(model => model.ExchangerID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.ExchangerID, (SelectList) ViewBag.Exchangers, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.ExchangerID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.AccountID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.Obout(new ComboBox("AccountID")
        {
            Width = 300,
            SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains,
            LoadingText = "Loading"
        })
        @*<select id="AccountID" name="AccountID" class="select2" placeholder="أختر حساب"></select>*@
        @Html.ValidationMessageFor(model => model.AccountID, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.BeneficiaryName, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.BeneficiaryName, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.BeneficiaryName, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.BeneficiaryPhone, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.BeneficiaryPhone, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.BeneficiaryPhone, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SenderName, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderName, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.SenderName, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SenderPhone, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderPhone, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.SenderPhone, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Date, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Date, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Date, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
    </div>
</div>

<script>

    function calcCommission() {

        var num = Number($("#AccountID").val());
        i('AccountID= ' + num);
        var data = {
            CurrencyID: $("#CurrencyID").val(),
            Amount: $("#Amount").val(),
            RemittanceType: 0,
            ExchangerID: num
        };
        AjaxCall('/Remittance/TransferOut/CalcCommission', JSON.stringify(data), 'POST').done(function(response) {

            $("#CommissionAmount").val(response.Amount);
            $("#currencyname").val(response.currencyName);


        }).fail(function (xhr, textStatus, errorThrown) {
                    parseAndShowError(xhr, textStatus, errorThrown);
        });
    }
</script>

@*<script>


    $(function () {
        console.log('topupreport load');

        AjaxCall('/Print/GetAccounts').done(function (response) {
            console.log('get parties');
            if (response.length > 0) {
                $('#AccountID').html('');
                var options = '<option value="0"> أختر حساب</option>';
                for (var i = 0; i < response.length; i++) {
                    options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
                }
                $('#AccountID').append(options);

            }
        }).fail(function (error) {
            alert(error.StatusText);
        });
        //select2
        $('.select2').css('width', '200px').select2({ allowClear: true });
        $('#select2-multiple-style .btn').on('click', function (e) {
            var target = $(this).find('input[type=radio]');
            var which = parseInt(target.val());
            if (which == 2) $('.select2').addClass('tag-input-style');
            else $('.select2').removeClass('tag-input-style');
        });
    });

</script>*@