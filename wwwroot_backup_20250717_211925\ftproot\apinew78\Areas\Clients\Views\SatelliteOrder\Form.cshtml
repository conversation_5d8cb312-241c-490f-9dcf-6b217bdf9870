﻿@model AppTech.MSMS.Domain.Models.SatellitePayment

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
    <div>
        <h3 class="" style="text-align: center;color: #e00a00;">بيانات الطلب</h3>
        <div class="form-group">
            @Html.LabelFor(model => model.SatelliteProvider.Name, new { @class = "contol-label col-md-2 align-left" })
            <div class="col-md-10 form-inline">
                @Html.DisplayFor(model => model.SatelliteProvider.Name, new { @class = "col-md-8" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.SatelliteFaction.Name, new { @class = "contol-label col-md-2 align-left" })
            <div class="col-md-10 form-inline">
                @Html.DisplayFor(model => model.SatelliteFaction.Name, new { @class = "col-md-8" })
            </div>
        </div>
        <div class="form-group form-inline">
            @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
            <div class="col-md-10 form-inline">
                @Html.DisplayFor(model => model.Amount, new { @class = "col-md-8" })
            </div>
        </div>
          <div class="form-group form-inline">
            @Html.LabelFor(model => model.SubscriptionTerm, new { @class = "control-label col-md-2" })
            <div class="col-md-10 form-inline">
                @Html.DisplayFor(model => model.SubscriptionTerm, new { @class = "col-md-8" })
            </div>
        </div>
        <div class="form-group form-inline">
            @Html.LabelFor(model => model.SubscriptionNumber, new { @class = "control-label col-md-2" })
            <div class="col-md-10 form-inline">
                @Html.DisplayFor(model => model.SubscriptionNumber, new { @class = "col-md-8" })
            </div>
        </div>
        <hr />
        <h3 style="text-align: center;color: #058c15;">معالجة الطلب</h3>
        <div class="form-group">
            @Html.LabelFor(model => model.Status, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownListFor(model => model.Status, new[]
                  {

                      new SelectListItem {Text = "انتظار المعالجة", Value = "0"},
                      new SelectListItem {Text = "قبول الطلب", Value = "1"},
                      new SelectListItem {Text = "رفض الطلب", Value = "2"}
                  })
                @Html.ValidationMessageFor(model => model.Status)
            </div>
        </div>
    </div>

