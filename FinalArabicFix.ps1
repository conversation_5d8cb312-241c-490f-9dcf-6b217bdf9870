# Final Arabic Encoding Fix for All Applications
# الإصلاح النهائي لترميز النص العربي لجميع التطبيقات

Write-Host "=== Final Arabic Encoding Fix ===" -ForegroundColor Green

# تحديث Web.config للعميل
Write-Host "`nUpdating Client Web.config..." -ForegroundColor Yellow

$clientWebConfig = @'
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.web>
    <globalization requestEncoding="utf-8" responseEncoding="utf-8" culture="ar-SA" uiCulture="ar-SA" />
    <httpRuntime requestValidationMode="2.0" />
  </system.web>
  <system.webServer>
    <defaultDocument>
      <files>
        <clear />
        <add value="index.html" />
        <add value="default.html" />
      </files>
    </defaultDocument>
    <directoryBrowse enabled="false" />
    <staticContent>
      <remove fileExtension=".html" />
      <mimeMap fileExtension=".html" mimeType="text/html; charset=utf-8" />
      <remove fileExtension=".css" />
      <mimeMap fileExtension=".css" mimeType="text/css; charset=utf-8" />
      <remove fileExtension=".js" />
      <mimeMap fileExtension=".js" mimeType="application/javascript; charset=utf-8" />
    </staticContent>
    <httpProtocol>
      <customHeaders>
        <add name="Content-Type" value="text/html; charset=utf-8" />
      </customHeaders>
    </httpProtocol>
  </system.webServer>
</configuration>
'@

# حفظ Web.config للعميل
$utf8NoBom = New-Object System.Text.UTF8Encoding $false
[System.IO.File]::WriteAllText("C:\inetpub\wwwroot\client\Web.config", $clientWebConfig, $utf8NoBom)
Write-Host "✅ Client Web.config updated" -ForegroundColor Green

# إنشاء صفحة عميل بترميز صحيح
Write-Host "`nCreating Client page with correct Arabic..." -ForegroundColor Yellow

$clientPageArabic = @'
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بوابة العملاء - AppTech Client Portal</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            direction: rtl;
        }
        
        .header {
            background: rgba(0,0,0,0.2);
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            font-weight: 700;
        }
        
        .container { 
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .nav-bar {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .nav-bar a {
            color: white;
            text-decoration: none;
            padding: 12px 20px;
            margin: 0 8px;
            border-radius: 10px;
            display: inline-block;
            background: rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.3);
            font-weight: 500;
        }
        
        .nav-bar a:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .main-content {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            margin: 20px 0;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .card {
            background: rgba(255,255,255,0.15);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .card:hover {
            transform: translateY(-8px);
            background: rgba(255,255,255,0.25);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #fff;
            font-size: 1.3em;
            font-weight: 600;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            margin: 8px 4px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.3);
            font-weight: 500;
        }
        
        .btn:hover { 
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .status-bar {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .status-item {
            background: rgba(39, 174, 96, 0.8);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .footer {
            text-align: center;
            padding: 30px 20px;
            border-top: 1px solid rgba(255,255,255,0.2);
            margin-top: 40px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>👥 بوابة العملاء - AppTech Client Portal</h1>
        <p>نظام إدارة حسابات العملاء والخدمات المتكاملة</p>
    </div>

    <div class="container">
        <div class="nav-bar">
            <a href="/portal">🏠 البوابة الرئيسية</a>
            <a href="/api">🔌 واجهة API</a>
            <a href="/client">👥 بوابة العملاء</a>
            <a href="/apinewAN">🆕 API الجديد</a>
            <a href="/collections_system">💰 نظام التحصيلات</a>
        </div>

        <div class="status-bar">
            <div class="status-item">✅ النظام يعمل</div>
            <div class="status-item">🔧 تم الإصلاح</div>
            <div class="status-item">🎯 جاهز للاستخدام</div>
            <div class="status-item">🔓 بدون قيود أمنية</div>
        </div>

        <div class="main-content">
            <h2 style="text-align: center; margin-bottom: 20px;">مرحباً بك في بوابة العملاء</h2>
            <p style="text-align: center; font-size: 1.1em; opacity: 0.9;">
                إدارة شاملة لحسابات العملاء والخدمات المقدمة مع متابعة دقيقة للمعاملات
            </p>
        </div>

        <div class="dashboard">
            <div class="card">
                <h3>🔐 تسجيل الدخول</h3>
                <p>دخول العملاء إلى حساباتهم الشخصية وإدارة معلوماتهم</p>
                <p><strong>الحالة:</strong> متاح للجميع</p>
                <a href="#login" class="btn">تسجيل الدخول</a>
            </div>

            <div class="card">
                <h3>📊 لوحة تحكم العميل</h3>
                <p>عرض معلومات الحساب والخدمات والمعاملات الحديثة</p>
                <p><strong>الحالة:</strong> نشط ومحدث</p>
                <a href="#dashboard" class="btn">لوحة التحكم</a>
            </div>

            <div class="card">
                <h3>💳 الخدمات المالية</h3>
                <p>إدارة المعاملات المالية والمدفوعات والتحويلات</p>
                <p><strong>الحالة:</strong> متاح 24/7</p>
                <a href="#financial" class="btn">الخدمات المالية</a>
            </div>

            <div class="card">
                <h3>📞 الدعم الفني</h3>
                <p>التواصل مع فريق الدعم الفني وحل المشاكل</p>
                <p><strong>الحالة:</strong> متاح دائماً</p>
                <a href="#support" class="btn">الدعم الفني</a>
            </div>

            <div class="card">
                <h3>📈 التقارير الشخصية</h3>
                <p>عرض تقارير الحساب والنشاطات والإحصائيات</p>
                <p><strong>الحالة:</strong> محدث يومياً</p>
                <a href="#reports" class="btn">عرض التقارير</a>
            </div>

            <div class="card">
                <h3>⚙️ إعدادات الحساب</h3>
                <p>تخصيص إعدادات الحساب والتفضيلات الشخصية</p>
                <p><strong>الحالة:</strong> قابل للتخصيص</p>
                <a href="#settings" class="btn">الإعدادات</a>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h4>🎉 مرحباً بك في بوابة العملاء المتطورة</h4>
            <p>يمكنك الآن الوصول إلى جميع الخدمات والميزات المتاحة لحسابك بسهولة ويسر.</p>
            <p>النظام محدث ويعمل بكامل الوظائف مع دعم كامل للغة العربية.</p>
        </div>

        <div class="footer">
            <p><strong>© 2024 AppTech Client Portal - جميع الحقوق محفوظة</strong></p>
            <p>تم التطوير والتحديث بواسطة فريق AppTech المتخصص</p>
            <p>آخر تحديث: <span id="currentTime"></span></p>
            <p style="color: #ff6b6b; margin-top: 10px;">⚠️ النظام يعمل بدون قيود أمنية - للاختبار فقط</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديث الوقت
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        }
        updateTime();
        setInterval(updateTime, 1000);

        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 150);
            });
        });
    </script>
</body>
</html>
'@

# حفظ صفحة العميل
[System.IO.File]::WriteAllText("C:\inetpub\wwwroot\client\index.html", $clientPageArabic, $utf8NoBom)
[System.IO.File]::WriteAllText("C:\inetpub\wwwroot\client\default.html", $clientPageArabic, $utf8NoBom)
Write-Host "✅ Client page with correct Arabic created" -ForegroundColor Green

# تحديث Portal مع ترميز أفضل
Write-Host "`nUpdating Portal with better encoding..." -ForegroundColor Yellow

# إضافة meta tags إضافية للترميز
$portalPath = "C:\inetpub\wwwroot\portal"
$currentPortal = [System.IO.File]::ReadAllText("$portalPath\index.html", [System.Text.Encoding]::UTF8)

# إضافة meta tags للترميز
$updatedPortal = $currentPortal -replace '<meta charset="utf-8" />', @'
<meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Content-Language" content="ar" />
'@

[System.IO.File]::WriteAllText("$portalPath\index.html", $updatedPortal, $utf8NoBom)
[System.IO.File]::WriteAllText("$portalPath\default.html", $updatedPortal, $utf8NoBom)
Write-Host "✅ Portal encoding improved" -ForegroundColor Green

# إعادة تشغيل IIS
Write-Host "`nRestarting IIS..." -ForegroundColor Yellow
iisreset /restart >$null 2>&1
Write-Host "✅ IIS restarted" -ForegroundColor Green

Write-Host "`n🎉 Final Arabic encoding fix completed!" -ForegroundColor Green
Write-Host "`n🌐 Test the applications:" -ForegroundColor Yellow
Write-Host "  ✅ Portal: http://localhost/portal" -ForegroundColor Cyan
Write-Host "  ✅ Client: http://localhost/client" -ForegroundColor Cyan
Write-Host "  ✅ Collections: http://localhost/collections_system" -ForegroundColor Cyan

Write-Host "`n✨ Arabic text should now display perfectly! ✨" -ForegroundColor Green
