﻿@using Obout.Mvc.ComboBox

<style>
    .borderless td, .borderless th {
        border: none;
    }
</style>
@model AppTech.MSMS.Web.Models.BalanceSheetModel
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}
<div class="form-horizontal">
    @{
        Html.RenderPartial("_DateControl");
    }

    <span class="lbl">@ViewBag.AccountLabel </span>
    <div class="form-group">
        <div class="col-md-10">
            @Html.Obout(new ComboBox("AccountID")
            {
                Width = 230,
                FilterType = ComboBoxFilterType.Contains
            })

        </div>
    </div>

    <table id="search-filter-table" class="table table-responsive borderless">
        <tr>
            <td>        <span class="lbl"> نوع التقرير</span></td>
            <td>  @Html.EnumRadioButton(m => m.Type)</td>
        </tr>
        <tr>
            <td>  <span class="lbl"> العملة</span></td>
            <td>  @Html.DropDownListFor(m => m.CurrencyID, (SelectList)ViewBag.Currencies)</td>
        </tr>

        <tr>
            <td>  <span class="lbl"> المستند</span></td>
            <td>@Html.DropDownListFor(model => model.VoucherID, (SelectList)ViewBag.Vouchers, new { htmlAttributes = new { @class = "form-control" } })</td>
        </tr>

        <tr>
            <td><span class="lbl"> الحالة   </span></td>
            <td> @Html.EnumDropDownListFor(m => m.BalanceState)</td>
        </tr>
        <tr>
            <td> <span class="lbl">بدون رصيد سابق</span></td>
            <td> @Html.EditorFor(m => m.NoPrevBalance)</td>
        </tr>
        <tr id="row-gross">
            <td> <span class="lbl">التجميع بالمستند</span></td>
            <td> @Html.EditorFor(m => m.GroupbyVoucher)</td>
        </tr>
    </table>
        
       
        <div class="space-6"></div>
       
       

        <div >
            <div class="space-6"></div>
           
           
        </div>

</div>
@RenderBody()

<script>
    $("select#CurrencyID").prop('selectedIndex', 1);  
    $('input[type=radio][name=Type]').change(function () {

        if (this.value === 'تفصيلي') {
            $("#row-gross").hide();
        }
        else{
            $("#row-gross").show();
        }
    });

    //function exportToPdf(self) {
    //    i('exportToPdf');
    //    var form = $(self).closest("form");
    //    var action =form.attr("action") + "?";
    //    i('action ' + action);
    //    var url = "?";
    //    var urlElements = [];
    //    form.children().each(function () {
    //        //|| $(this).is("select")
    //        if ($(this).is("input")) {
    //            urlElements.push($(this).attr("name") + "=" + $(this).attr("value"));     
    //        }

    //    });
    //    urlElements = urlElements.join("&");
    //    url += form.serialize();

    //    url = "/" + $("#Controller").val() + "/ExportPdf" + url;
    //    i('url ' + url);
    //       window.location.href =url;

    //}
</script>