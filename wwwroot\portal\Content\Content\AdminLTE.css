/* All images should be responsive */
img {
    max-width: 100% !important;
}

/* Background colors */
.bg-red,
.bg-yellow,
.bg-aqua,
.bg-blue,
.bg-light-blue,
.bg-green,
.bg-navy,
.bg-teal,
.bg-olive,
.bg-lime,
.bg-orange,
.bg-fuchsia,
.bg-purple,
.bg-maroon,
.bg-black {
    color: #f9f9f9 !important;
}

.bg-gray {
    background-color: #eaeaec !important;
}

.bg-black {
    background-color: #222222 !important;
}

.bg-red {
    background-color: #f56954 !important;
}

.bg-yellow {
    background-color: #f39c12 !important;
}

.bg-aqua {
    background-color: #00c0ef !important;
}

.bg-blue {
    background-color: #0073b7 !important;
}

.bg-light-blue {
    background-color: #3c8dbc !important;
}

.bg-green {
    background-color: #00a65a !important;
}

.bg-navy {
    background-color: #001f3f !important;
}

.bg-teal {
    background-color: #39cccc !important;
}

.bg-olive {
    background-color: #3d9970 !important;
}

.bg-lime {
    background-color: #01ff70 !important;
}

.bg-orange {
    background-color: #ff851b !important;
}

.bg-fuchsia {
    background-color: #f012be !important;
}

.bg-purple {
    background-color: #932ab6 !important;
}

.bg-maroon {
    background-color: #85144b !important;
}
/* Text colors */
.text-red {
    color: #f56954 !important;
}

.text-yellow {
    color: #f39c12 !important;
}

.text-aqua {
    color: #00c0ef !important;
}

.text-blue {
    color: #0073b7 !important;
}

.text-black {
    color: #222222 !important;
}

.text-light-blue {
    color: #3c8dbc !important;
}

.text-green {
    color: #00a65a !important;
}

.text-navy {
    color: #001f3f !important;
}

.text-teal {
    color: #39cccc !important;
}

.text-olive {
    color: #3d9970 !important;
}

.text-lime {
    color: #01ff70 !important;
}

.text-orange {
    color: #ff851b !important;
}

.text-fuchsia {
    color: #f012be !important;
}

.text-purple {
    color: #932ab6 !important;
}

.text-maroon {
    color: #85144b !important;
}
/*Hide elements by display none only*/
.hide {
    display: none !important;
}
/* Remove borders */
.no-border {
    border: 0px !important;
}
/* Remove padding */
.no-padding {
    padding: 0px !important;
}
/* Remove margins */
.no-margin {
    margin: 0px !important;
}
/* Remove box shadow */
.no-shadow {
    box-shadow: none !important;
}
/* Don't display when printing */
@media print {
    .no-print {
        display: none;
    }

    .left-side,
    .header,
    .content-header {
        display: none;
    }

    .right-side {
        margin: 0;
    }
}
/*
Gradient Background colors
*/
.bg-teal-gradient {
    background: #39cccc !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #39cccc), color-stop(1, #7adddd)) !important;
    background: -ms-linear-gradient(bottom, #39cccc, #7adddd) !important;
    background: -moz-linear-gradient(center bottom, #39cccc 0%, #7adddd 100%) !important;
    background: -o-linear-gradient(#7adddd, #39cccc) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#7adddd', endColorstr='#39cccc', GradientType=0) !important;
    color: #fff;
}

.bg-light-blue-gradient {
    background: #3c8dbc !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #3c8dbc), color-stop(1, #67a8ce)) !important;
    background: -ms-linear-gradient(bottom, #3c8dbc, #67a8ce) !important;
    background: -moz-linear-gradient(center bottom, #3c8dbc 0%, #67a8ce 100%) !important;
    background: -o-linear-gradient(#67a8ce, #3c8dbc) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#67a8ce', endColorstr='#3c8dbc', GradientType=0) !important;
    color: #fff;
}

.bg-blue-gradient {
    background: #0073b7 !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #0073b7), color-stop(1, #0089db)) !important;
    background: -ms-linear-gradient(bottom, #0073b7, #0089db) !important;
    background: -moz-linear-gradient(center bottom, #0073b7 0%, #0089db 100%) !important;
    background: -o-linear-gradient(#0089db, #0073b7) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0089db', endColorstr='#0073b7', GradientType=0) !important;
    color: #fff;
}

.bg-aqua-gradient {
    background: #00c0ef !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #00c0ef), color-stop(1, #14d1ff)) !important;
    background: -ms-linear-gradient(bottom, #00c0ef, #14d1ff) !important;
    background: -moz-linear-gradient(center bottom, #00c0ef 0%, #14d1ff 100%) !important;
    background: -o-linear-gradient(#14d1ff, #00c0ef) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#14d1ff', endColorstr='#00c0ef', GradientType=0) !important;
    color: #fff;
}

.bg-yellow-gradient {
    background: #f39c12 !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #f39c12), color-stop(1, #f7bc60)) !important;
    background: -ms-linear-gradient(bottom, #f39c12, #f7bc60) !important;
    background: -moz-linear-gradient(center bottom, #f39c12 0%, #f7bc60 100%) !important;
    background: -o-linear-gradient(#f7bc60, #f39c12) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f7bc60', endColorstr='#f39c12', GradientType=0) !important;
    color: #fff;
}

.bg-purple-gradient {
    background: #932ab6 !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #932ab6), color-stop(1, #b959d9)) !important;
    background: -ms-linear-gradient(bottom, #932ab6, #b959d9) !important;
    background: -moz-linear-gradient(center bottom, #932ab6 0%, #b959d9 100%) !important;
    background: -o-linear-gradient(#b959d9, #932ab6) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#b959d9', endColorstr='#932ab6', GradientType=0) !important;
    color: #fff;
}

.bg-green-gradient {
    background: #00a65a !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #00a65a), color-stop(1, #00ca6d)) !important;
    background: -ms-linear-gradient(bottom, #00a65a, #00ca6d) !important;
    background: -moz-linear-gradient(center bottom, #00a65a 0%, #00ca6d 100%) !important;
    background: -o-linear-gradient(#00ca6d, #00a65a) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00ca6d', endColorstr='#00a65a', GradientType=0) !important;
    color: #fff;
}

.bg-red-gradient {
    background: #f56954 !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #f56954), color-stop(1, #f89384)) !important;
    background: -ms-linear-gradient(bottom, #f56954, #f89384) !important;
    background: -moz-linear-gradient(center bottom, #f56954 0%, #f89384 100%) !important;
    background: -o-linear-gradient(#f89384, #f56954) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f89384', endColorstr='#f56954', GradientType=0) !important;
    color: #fff;
}

.bg-black-gradient {
    background: #222222 !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #222222), color-stop(1, #3c3c3c)) !important;
    background: -ms-linear-gradient(bottom, #222222, #3c3c3c) !important;
    background: -moz-linear-gradient(center bottom, #222222 0%, #3c3c3c 100%) !important;
    background: -o-linear-gradient(#3c3c3c, #222222) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#3c3c3c', endColorstr='#222222', GradientType=0) !important;
    color: #fff;
}

.bg-maroon-gradient {
    background: #85144b !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #85144b), color-stop(1, #b11b64)) !important;
    background: -ms-linear-gradient(bottom, #85144b, #b11b64) !important;
    background: -moz-linear-gradient(center bottom, #85144b 0%, #b11b64 100%) !important;
    background: -o-linear-gradient(#b11b64, #85144b) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#b11b64', endColorstr='#85144b', GradientType=0) !important;
    color: #fff;
}

.connectedSortable {
    min-height: 100px;
}


    /*
    Component: Small boxes
*/
    .small-box {
        position: relative;
        display: block;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
        margin-bottom: 15px;
    }
    .small-box > .inner {
        padding: 10px;
    }
    .small-box > .small-box-footer {
        position: relative;
        text-align: center;
        padding: 3px 0;
        color: #fff;
        color: rgba(255, 255, 255, 0.8);
        display: block;
        z-index: 10;
        background: rgba(0, 0, 0, 0.1);
        text-decoration: none;
    }
    .small-box > .small-box-footer:hover {
        color: #fff;
        background: rgba(0, 0, 0, 0.15);
    }
    .small-box h3 {
        font-size: 38px;
        font-weight: bold;
        margin: 0 0 10px 0;
        white-space: nowrap;
        padding: 0;
    }
    .small-box p {
        font-size: 15px;
    }
    .small-box p > small {
        display: block;
        color: #f9f9f9;
        font-size: 13px;
        margin-top: 5px;
    }
    .small-box h3,
    .small-box p {
        z-index: 5px;
    }
    .small-box .icon {
        position: absolute;
        top: auto;
        bottom: 5px;
        right: 5px;
        z-index: 0;
        font-size: 90px;
        color: rgba(0, 0, 0, 0.15);
    }
    .small-box:hover {
        text-decoration: none;
        color: #f9f9f9;
    }
    .small-box:hover .icon {
        animation-name: tansformAnimation;
        animation-duration: .5s;
        animation-iteration-count: 1;
        animation-timing-function: ease;
        animation-fill-mode: forwards;
        -webkit-animation-name: tansformAnimation;
        -webkit-animation-duration: .5s;
        -webkit-animation-iteration-count: 1;
        -webkit-animation-timing-function: ease;
        -webkit-animation-fill-mode: forwards;
        -moz-animation-name: tansformAnimation;
        -moz-animation-duration: .5s;
        -moz-animation-iteration-count: 1;
        -moz-animation-timing-function: ease;
        -moz-animation-fill-mode: forwards;
    }
    @keyframes tansformAnimation {
        from {
            font-size: 90px;
        }
        to {
            font-size: 100px;
        }
    }
    @-webkit-keyframes tansformAnimation {
        from {
            font-size: 90px;
        }
        to {
            font-size: 100px;
        }
    }
    @media screen and (max-width: 480px) {
        .small-box {
            text-align: center;
        }
        .small-box .icon {
            display: none;
        }
        .small-box p {
            font-size: 12px;
        }
    }
