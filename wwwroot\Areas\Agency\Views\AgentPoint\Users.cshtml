﻿@model IEnumerable<AppTech.MSMS.Domain.Models.AccountUser>

@{
    Layout = "~/Views/Shared/_FormModal.cshtml";
}
<strong class="blue">ربط المستخدمين</strong>
<br/>
<div class="hr hr-dotted hr-24"></div>
<input type="hidden" name="pointId" value="@ViewBag.PointID"/>
@foreach (var item in Model)
{
    <input id="chk@(item.UserID)"
           name="items"
           type="checkbox"
           class="action"
           value="@item.UserID"
           checked="@true"/>
    <strong>@item.UserInfo1.UserName</strong> @*<br />*@

    <div class="space-10"></div>
}