﻿@model AppTech.MSMS.Domain.Models.TopupOrder

@{
    Layout = "~/Views/Shared/_FormLayout.cshtml";
}


<input type="hidden" name="AccountID" value="@ViewBag.AccountID"/>
<input type="hidden" name="Device" value="Web"/>

<div class="form-group">

    @Html.LabelFor(model => model.ServiceID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ServiceID, (SelectList) ViewBag.Services, new {onchange = " setService( this) "})
        @Html.ValidationMessageFor(model => model.ServiceID)
    </div>
</div>

<div class="form-group" id="water-region">

    @Html.Label("المنطقة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownList("WaterSubCode", (SelectList) ViewBag.WaterRegions)
    </div>
</div>


<div class="form-group" id="elec-region">

    @Html.Label("المنطقة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownList("ElecSubCode", (SelectList) ViewBag.ElecRegions)
    </div>
</div>

<div class="form-group" id="offers">

    @Html.Label("الباقة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownList("OfferID", (SelectList) ViewBag.Offers)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.SubscriberNumber, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SubscriberNumber)
        @Html.ValidationMessageFor(model => model.SubscriberNumber)
    </div>
</div>



@*<div class="form-group" id="amount">
    @Html.LabelFor(model => model.Amount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Amount)
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>*@
<div class="form-group form-inline" id="amount">
    @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Amount, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>


<div class="form-group">


    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note)
        @Html.ValidationMessageFor(model => model.Note)
    </div>
</div>


<script>

    $(function() {
        $("#amount").show();
        $("#water-region").hide();
        $("#elec-region").hide();
        $("#offers").hide();
    });

    function setService(self) {

        //  document.write(self.options[self.selectedIndex].value);
        var serviceId = self.options[self.selectedIndex].value;
        if (serviceId == 8) {
            $("#water-region").show();
            $("#amount").show();
            $("#elec-region").hide();
            $("#offers").hide();
        } else if (serviceId == 9) {
            $("#elec-region").show();
            $("#amount").show();
            $("#water-region").hide();
            $("#offers").hide();
        } else if (serviceId == 11) {
            $("#offers").show();
            $("#amount").hide();
            $("#water-region").hide();
            $("#elec-region").hide();
        } else {
            $("#amount").show();
            $("#elec-region").hide();
            $("#elec-region").hide();
            $("#offers").hide();
        }

    }
</script>