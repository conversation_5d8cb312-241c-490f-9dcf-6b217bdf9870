﻿@model AppTech.MSMS.Domain.Models.AccountRegion
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.LabelFor(model => model.RegionID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.RegionID, (SelectList)ViewBag.Regions, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.RegionID)
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.AccountID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.AccountID, (SelectList)ViewBag.Accounts, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.AccountID)
    </div>
</div>
