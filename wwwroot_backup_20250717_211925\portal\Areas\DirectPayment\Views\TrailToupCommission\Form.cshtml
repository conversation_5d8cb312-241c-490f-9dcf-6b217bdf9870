﻿@model AppTech.MSMS.Domain.Models.TrailToupCommission
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    @Html.LabelFor(model => model.MobileNetworkID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.MobileNetworkID, (SelectList)ViewBag.MobileNetworks, new { })
        @Html.ValidationMessageFor(model => model.MobileNetworkID, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.FromAmount, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.FromAmount, new { htmlAttributes = new { } })
        @Html.ValidationMessageFor(model => model.FromAmount, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ToAmount, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.ToAmount, new { htmlAttributes = new { } })
        @Html.ValidationMessageFor(model => model.ToAmount, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.PersonnalPrice, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.PersonnalPrice, new { htmlAttributes = new { } })
        @Html.ValidationMessageFor(model => model.PersonnalPrice, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Percentage, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Percentage, new { htmlAttributes = new { } })
        @Html.ValidationMessageFor(model => model.Percentage, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.TextAreaFor(model => model.Note, new { htmlAttributes = new { @class = "form-control", rows = "3" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>