# AppTech Deobfuscation Script
# هذا السكريبت لفك تشفير وحماية ملفات AppTech

param(
    [string]$SourcePath = "C:\inetpub",
    [string]$OutputPath = "C:\inetpub\Deobfuscated",
    [switch]$ProcessAll,
    [switch]$BackupOriginal = $true
)

# إعداد المسارات
$De4dotPath = "C:\inetpub\de4dot\de4dot.exe"
$DnSpyPath = "C:\inetpub\DeobfuscationTools\dnSpy\dnSpy.exe"

Write-Host "=== AppTech Deobfuscation Tool ===" -ForegroundColor Green
Write-Host "Source Path: $SourcePath" -ForegroundColor Yellow
Write-Host "Output Path: $OutputPath" -ForegroundColor Yellow

# إنشاء مجلد الإخراج
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "Created output directory: $OutputPath" -ForegroundColor Green
}

# إنشاء مجلد النسخ الاحتياطية
if ($BackupOriginal) {
    $BackupPath = "$OutputPath\Backup"
    if (!(Test-Path $BackupPath)) {
        New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
    }
}

# البحث عن ملفات AppTech DLL
Write-Host "`nSearching for AppTech assemblies..." -ForegroundColor Cyan
$AppTechDlls = Get-ChildItem -Path $SourcePath -Filter "AppTech*.dll" -Recurse | Where-Object { $_.Directory.Name -eq "bin" }

Write-Host "Found $($AppTechDlls.Count) AppTech assemblies:" -ForegroundColor Yellow
foreach ($dll in $AppTechDlls) {
    Write-Host "  - $($dll.FullName)" -ForegroundColor White
}

# دالة لفك التشفير باستخدام de4dot
function Deobfuscate-Assembly {
    param(
        [string]$InputFile,
        [string]$OutputFile
    )
    
    Write-Host "`nDeobfuscating: $InputFile" -ForegroundColor Cyan
    
    # نسخ احتياطي
    if ($BackupOriginal) {
        $BackupFile = Join-Path $BackupPath (Split-Path $InputFile -Leaf)
        Copy-Item $InputFile $BackupFile -Force
        Write-Host "Backup created: $BackupFile" -ForegroundColor Green
    }
    
    # تشغيل de4dot
    $de4dotArgs = @(
        "`"$InputFile`"",
        "--output", "`"$OutputFile`"",
        "--detect-obfuscators",
        "--preserve-tokens",
        "--keep-names"
    )
    
    try {
        $process = Start-Process -FilePath $De4dotPath -ArgumentList $de4dotArgs -Wait -PassThru -NoNewWindow -RedirectStandardOutput "$OutputPath\de4dot_output.log" -RedirectStandardError "$OutputPath\de4dot_error.log"
        
        if ($process.ExitCode -eq 0) {
            Write-Host "Successfully deobfuscated: $OutputFile" -ForegroundColor Green
            return $true
        } else {
            Write-Host "Failed to deobfuscate: $InputFile (Exit Code: $($process.ExitCode))" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "Error running de4dot: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# دالة لتحليل التجميع
function Analyze-Assembly {
    param(
        [string]$AssemblyPath
    )
    
    Write-Host "`nAnalyzing assembly: $AssemblyPath" -ForegroundColor Cyan
    
    try {
        # تحميل التجميع للتحليل
        $assembly = [System.Reflection.Assembly]::LoadFile($AssemblyPath)
        $types = $assembly.GetTypes()
        
        Write-Host "Assembly Info:" -ForegroundColor Yellow
        Write-Host "  - Name: $($assembly.GetName().Name)" -ForegroundColor White
        Write-Host "  - Version: $($assembly.GetName().Version)" -ForegroundColor White
        Write-Host "  - Types Count: $($types.Count)" -ForegroundColor White
        
        # البحث عن أنواع الحماية
        $securityTypes = $types | Where-Object { $_.Name -like "*Security*" -or $_.Name -like "*License*" -or $_.Name -like "*Encrypt*" }
        if ($securityTypes) {
            Write-Host "  - Security/License Types Found:" -ForegroundColor Red
            foreach ($type in $securityTypes) {
                Write-Host "    * $($type.FullName)" -ForegroundColor White
            }
        }
        
        return $true
    } catch {
        Write-Host "Error analyzing assembly: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# معالجة الملفات
Write-Host "`n=== Starting Deobfuscation Process ===" -ForegroundColor Green

$successCount = 0
$failCount = 0

foreach ($dll in $AppTechDlls) {
    $relativePath = $dll.FullName.Substring($SourcePath.Length + 1)
    $outputFile = Join-Path $OutputPath $relativePath
    $outputDir = Split-Path $outputFile -Parent
    
    # إنشاء مجلد الإخراج
    if (!(Test-Path $outputDir)) {
        New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    }
    
    # تحليل الملف الأصلي
    Write-Host "`n--- Processing: $($dll.Name) ---" -ForegroundColor Magenta
    Analyze-Assembly -AssemblyPath $dll.FullName
    
    # فك التشفير
    if (Deobfuscate-Assembly -InputFile $dll.FullName -OutputFile $outputFile) {
        $successCount++
        
        # تحليل الملف المفكوك
        if (Test-Path $outputFile) {
            Write-Host "`nAnalyzing deobfuscated file:" -ForegroundColor Cyan
            Analyze-Assembly -AssemblyPath $outputFile
        }
    } else {
        $failCount++
    }
}

# تقرير النتائج
Write-Host "`n=== Deobfuscation Complete ===" -ForegroundColor Green
Write-Host "Successfully processed: $successCount files" -ForegroundColor Green
Write-Host "Failed to process: $failCount files" -ForegroundColor Red
Write-Host "Output directory: $OutputPath" -ForegroundColor Yellow

if (Test-Path "$OutputPath\de4dot_output.log") {
    Write-Host "`nde4dot output log available at: $OutputPath\de4dot_output.log" -ForegroundColor Cyan
}

if (Test-Path "$OutputPath\de4dot_error.log") {
    Write-Host "de4dot error log available at: $OutputPath\de4dot_error.log" -ForegroundColor Cyan
}

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Review deobfuscated files in: $OutputPath" -ForegroundColor White
Write-Host "2. Use dnSpy to analyze: $DnSpyPath" -ForegroundColor White
Write-Host "3. Check logs for any errors or warnings" -ForegroundColor White
