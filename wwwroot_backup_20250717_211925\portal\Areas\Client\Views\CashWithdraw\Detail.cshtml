﻿@model AppTech.MSMS.Domain.Models.CashOut
<div class="col-sm-offset-1 col-sm-10">

    <div class="form-horizontal">


        <div>


            <div class="row">
                <dt>
                    @Html.DisplayNameFor(model => model.CreatedTime)
                </dt>

                <dd>
                    @Html.DisplayFor(model => model.CreatedTime)
                </dd>

                <dt>
                    @Html.DisplayNameFor(model => model.Number)
                </dt>

                <dd>
                    @Html.DisplayFor(model => model.Number)
                </dd>
            </div>


            <dt>
                @Html.DisplayNameFor(model => model.Currency.Name)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.Currency.Name)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.Account.Name)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.Account.Name)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.Date)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.Date)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.Note)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.Note)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.Amount)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.Amount)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.DCAmount)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.DCAmount)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.DebitorAccountID)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.DebitorAccountID)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.RefNumber)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.RefNumber)
            </dd>


            <dt>
                @Html.DisplayNameFor(model => model.Delivery)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.Delivery)
            </dd>


            <hr/>

        </div><!-- /end form border -->
    </div><!-- /end form horizontal -->


</div>


<p>
    @*@Html.ActionLink("Edit", "AddOrEdit", new { id = Model.ID }) |
    @Html.ActionLink("Back to List", "Index")*@
</p>