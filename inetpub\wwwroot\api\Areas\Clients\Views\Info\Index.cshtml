﻿@model AppTech.MSMS.Domain.Models.AccountApi
@using AppTech.MSMS.Domain;
@{

    ViewBag.Title = "API Rest ";
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}
<style>
    

        div.b {
            word-wrap: break-word;
        }
</style>

@if (Model.Permitted)
{
    <div class="alert alert-block alert-success">
        <button type="button" class="close" data-dismiss="alert">
            <i class="ace-icon fa fa-times"></i>
        </button>
        <i class="ace-icon fa fa-check green"></i>
        This account is permitted to use  Api Rest ePayment System
    </div>

    <div class="row">
        <div class="col-xs-12">
            <div class="space-12"></div>
            <div class="profile-user-info profile-user-info-striped">

                <div class="profile-info-row">
                    <div class="profile-info-name"> Account-ID </div>

                    <div class="profile-info-value">
                        <span class="editable" id="age"> @Html.DisplayFor(model => model.GuidNumber) </span>
                        <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.GuidNumber)">
                            <i class="ace-icon fa fa-copy"></i> نسخ
                        </button>
                    </div>
                </div>


                <div class="profile-info-row">
                    <div class="profile-info-name">Token </div>

                    <div class="profile-info-value">
                        <span class="editable" id="username"> @Html.DisplayFor(model => model.Token)</span>
                        <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.Token)">
                            <i class="ace-icon fa fa-copy"></i> نسخ
                        </button>
                    </div>
                </div>





                <div class="profile-info-row">
                    <div class="profile-info-name"> IpAddress Restricted </div>
                    <div class="profile-info-value">
                        <span class="editable"> @Html.DisplayFor(model => model.IsAllowed)</span>

                    </div>
                </div>

                <div class="profile-info-row">
                    <div class="profile-info-name">  Ip Addresses </div>
                    <div class="profile-info-value">
                        <span class="editable"> @Html.DisplayFor(model => model.IpAddress)</span>
                    </div>
                </div>

                <div class="profile-info-row">
                    <div class="profile-info-name"> WebHook Enabled </div>
                    <div class="profile-info-value">
                        <span class="editable"> @Html.DisplayFor(model => model.Binded)</span>
                    </div>
                </div>
                <div class="profile-info-row">
                    <div class="profile-info-name"> WebHook Url </div>
                    <div class="profile-info-value">
                        <span class="editable"> @Html.DisplayFor(model => model.Binding)</span>
                    </div>
                </div>

                <div class="profile-info-row">
                    <div class="profile-info-name"> WebHook Token </div>
                    <div class="profile-info-value">
                        <span class="editable"> @Html.DisplayFor(model => model.PublicKey)</span>
                    </div>
                </div>



            </div>


        </div>
    </div>

    <hr />
    @*
    <div class="row">
        <div class="col-xs-12">
            <div class="widget-box">
                <div class="widget-header">
                    <h4 class="smaller">
                        AccessToken
                        <small></small>
                    </h4>
                </div>

                <div class="widget-body">
                    <div class="widget-main">
                        <div class="b">
                            @Html.DisplayFor(model => model.Token)
                        </div>

                        <hr />

                        <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.Token)">
                            <i class="ace-icon fa fa-copy"></i> نسخ
                        </button>
                    </div>
                </div>
            </div>
        </div><!-- /.col -->
        <!-- /.col -->
    </div>*@

}
else
{
    <div class="alert alert-block alert-warning">
        <button type="button" class="close" data-dismiss="alert">
            <i class="ace-icon fa fa-times"></i>
        </button>

        This account is not permitted to use  Api Rest ePayment System
    </div>
}


