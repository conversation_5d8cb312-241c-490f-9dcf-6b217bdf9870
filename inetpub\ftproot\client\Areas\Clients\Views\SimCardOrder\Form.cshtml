﻿@model AppTech.MSMS.Domain.Models.SimCardOrder

@{
    Layout = "~/Views/Shared/_MultiLayout.cshtml";
}


<input type="hidden" name="Device" value="Web" />

<div class="form-group">
    <label class="col-sm-2 control-label">نوع الشريحة</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.SimType, new[]
        {
            new SelectListItem {Text = "شريحة", Value = "شريحة"},
            new SelectListItem {Text = "برمجة", Value = "برمجة"}
        })
    </div>
    @Html.ValidationMessageFor(model => model.SimType)
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">نوع الشريحة</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.LineType, new[]
        {
            new SelectListItem {Text = "دفع مسبق", Value = "دفع مسبق"},
            new SelectListItem {Text = "فوترة", Value = "فوترة"}
        })
    </div>
    @Html.ValidationMessageFor(model => model.LineType)
</div>
<div class="form-group">
    @Html.LabelFor(model => model.SimNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.SimNumber)
        @Html.ValidationMessageFor(model => model.SimNumber)
    </div>
</div>
@*<div class="form-group">
    @Html.LabelFor(model => model.ESDN, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ESDN, (SelectList)ViewBag.Specials, new { })
    </div>
</div>*@


<div class="form-group">
    @Html.LabelFor(model => model.SubscriberNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.SubscriberNumber)
        @Html.ValidationMessageFor(model => model.SubscriberNumber)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.CustomerName, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.CustomerName)
        @Html.ValidationMessageFor(model => model.CustomerName)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.CustomerAddress, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.CustomerAddress)
        @Html.ValidationMessageFor(model => model.CustomerAddress)
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.BirthDate, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.BirthDate)
        @Html.ValidationMessageFor(model => model.BirthDate)
    </div>
</div>


@*

    <div class="form-group">
        @Html.LabelFor(model => model.ESDN, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.ESDN)
            @Html.ValidationMessageFor(model => model.ESDN)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.MSISDN, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.MSISDN)
            @Html.ValidationMessageFor(model => model.MSISDN)
        </div>
    </div>

*@
<div class="form-group">
    <label class="col-sm-2 control-label">نوع البطاقة</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.PersonalCardType, new[]
        {
            new SelectListItem {Text = "شخصية", Value = "شخصية"},
            new SelectListItem {Text = "جواز سفر", Value = "جواز سفر"},
            new SelectListItem {Text = "عائلية", Value = "عائلية"},
            new SelectListItem {Text = "عسكرية", Value = "عسكرية"},
            new SelectListItem {Text = "أخرى", Value = "أخرى"}
        })


    </div>
    @Html.ValidationMessageFor(model => model.PersonalCardType)
</div>


<div class="form-group">
    @Html.LabelFor(model => model.CardNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.CardNumber)
        @Html.ValidationMessageFor(model => model.CardNumber)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.CardIssuePlace, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.CardIssuePlace)
        @Html.ValidationMessageFor(model => model.CardIssuePlace)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.IssueDate, new { @class = "control-label col-md-2 date-picker" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.IssueDate)
        @Html.ValidationMessageFor(model => model.IssueDate)
    </div>
</div>

@*<div class="form-group">
        @Html.LabelFor(model => model.ExpireDate, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.ExpireDate)
            @Html.ValidationMessageFor(model => model.ExpireDate)
        </div>
    </div>*@

<div class="space-8"></div>

<div class="form-group">
    <label class="col-sm-2 control-label">صوره البطاقة</label>
    <div class="col-sm-10">
        <input type="file" name="ImageData" title="search image" id="ImageData" onchange="show(this)" />
        <div>
            <img id="user_img" height="100" width="90" style="border: solid" />
        </div>
    </div>
</div>
@*
    <div class="form-group">
        @Html.LabelFor(model => model.FrontCardImage, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.FrontCardImage)
            @Html.ValidationMessageFor(model => model.FrontCardImage)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.BackCardImage, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.BackCardImage)
            @Html.ValidationMessageFor(model => model.BackCardImage)
        </div>
    </div>

*@