<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="false" />
    <add key="UnobtrusiveJavaScriptEnabled" value="false" />
    <add key="vs:EnableBrowserLink" value="false" />
    <add key="SecurityEnabled" value="false" />
    <add key="AuthenticationRequired" value="false" />
    <add key="SSLRequired" value="false" />
    <add key="CertificateValidation" value="false" />
  </appSettings>
  
  <system.web>
    <compilation debug="true" targetFramework="4.8" tempDirectory="~/App_Data/Temp/" />
    <httpRuntime targetFramework="4.8" maxRequestLength="51200" executionTimeout="300" />
    <customErrors mode="Off" />
    <trust level="Full" />
    <authentication mode="None" />
    <authorization>
      <allow users="*" />
    </authorization>
    <httpModules>
      <clear />
    </httpModules>
    <httpHandlers>
      <clear />
      <add verb="*" path="*" type="System.Web.DefaultHttpHandler" />
    </httpHandlers>
    <pages validateRequest="false" enableEventValidation="false" viewStateEncryptionMode="Never" />
    <sessionState mode="Off" />
    <trace enabled="false" />
    <globalization requestEncoding="utf-8" responseEncoding="utf-8" />
  </system.web>
  
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <modules runAllManagedModulesForAllRequests="false">
      <clear />
    </modules>
    <handlers>
      <clear />
      <add name="StaticFile" path="*" verb="*" modules="StaticFileModule,DefaultDocumentModule,DirectoryListingModule" resourceType="Either" requireAccess="Read" />
    </handlers>
    <defaultDocument>
      <files>
        <clear />
        <add value="index.html" />
        <add value="default.html" />
        <add value="default.aspx" />
        <add value="index.aspx" />
      </files>
    </defaultDocument>
    <directoryBrowse enabled="false" />
    <httpErrors errorMode="Detailed" />
    <security>
      <authentication>
        <anonymousAuthentication enabled="true" />
        <windowsAuthentication enabled="false" />
        <basicAuthentication enabled="false" />
      </authentication>
      <authorization>
        <clear />
        <add accessType="Allow" users="*" />
      </authorization>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="52428800" />
      </requestFiltering>
    </security>
    <httpProtocol>
      <customHeaders>
        <clear />
      </customHeaders>
    </httpProtocol>
  </system.webServer>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="12.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
