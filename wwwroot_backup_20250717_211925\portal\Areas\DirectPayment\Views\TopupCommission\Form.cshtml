﻿
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.TopupCommission
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="row">
    <div class="search-area well well-sm">
        <div class="search-filter-header bg-primary">
            <h5 class="smaller no-margin-bottom">
                <i class="ace-icon fa fa-sliders light-green bigger-130"></i>&nbsp; البحث خلال فتره
            </h5>
        </div>
        <div class="space-10"></div>
        <div class="hr hr-dotted"></div>
        <div class="col-xs-12 col-sm-6">
            <div class="form-group">
                @Html.Label("اسم الحساب", new { @class = "control-label col-md-2" })
                <div class="col-md-10">

                    @Html.Obout(new ComboBox("CreditorAccountID")
                    {
                        Width = 300,
                        SelectedValue = Model.CreditorAccountID == 0 ? null : Model.CreditorAccountID.ToString(),
                        FilterType = ComboBoxFilterType.Contains,
                        LoadingText = "Loading"
                    })

                    @Html.ValidationMessageFor(model => model.CreditorAccountID)
                </div>
            </div>


            <div class="form-group">
                @Html.LabelFor(model => model.ServiceID, new {@class = "control-label col-md-2"})
                <div class="col-md-10">
                    @Html.DropDownListFor(m => m.ServiceID, (SelectList) ViewBag.Services)
                    @Html.ValidationMessageFor(model => model.ServiceID)
                </div>
            </div>

        </div>
        <div class="col-xs-12 col-sm-6">
            <div class="form-group">
                @Html.LabelFor(model => model.StartDate, new {@class = "control-label col-md-2"})
                <div class="col-md-10">
                    @Html.EditorFor(model => model.StartDate)
                    @Html.ValidationMessageFor(model => model.StartDate, "", new {@class = "text-danger"})
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.EndDate, new {@class = "control-label col-md-2"})
                <div class="col-md-10">
                    @Html.EditorFor(model => model.EndDate)
                    @Html.ValidationMessageFor(model => model.EndDate, "", new {@class = "text-danger"})
                </div>
            </div>

        </div>
        <div class="space-6"></div>
        <div class="space-6"></div>
        <div class="hr hr-dotted"></div>
        <div class="text-right">
            @Ajax.ActionLink(
                "أستعلام",
                "GetGross",
                null,
                new AjaxOptions
                {
                    LoadingElementId = "loader",
                    OnSuccess = "onSuccess",
                    HttpMethod = "GET" // <-- HTTP method
                },
                new {@class = "btn btn-default btn-round btn-white", onclick = "this.href = '/directpayment/topupcommission/GetGross?aid=' + document.getElementById('CreditorAccountID').value +'&sid=' + document.getElementById('ServiceID').value +'&frdate=' + document.getElementById('StartDate').value +'&todate=' + document.getElementById('EndDate').value;"}
                )
            @Html.ValidationMessageFor(model => model.RefNumber)
        </div>

        <div class="space-4"></div>
    </div>
</div>

<div id="form">

    <div class="form-group">
        @Html.Label("عدد العمليات", new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            <div class="checkbox">
                @Html.EditorFor(model => model.OpsCount)
                @Html.ValidationMessageFor(model => model.OpsCount, "", new {@class = "text-danger", @readonly = "readonly"})
            </div>
        </div>
    </div>

    <div class="form-group">
        @Html.Label("إجمالي المبلغ", new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            <input type="text" id="RefNumber" name="RefNumber" readonly="readonly"/>
        </div>
    </div>


    <div class="form-group">
        @Html.LabelFor(model => model.Percentage, new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EditorFor(model => model.Percentage)
            @Html.ValidationMessageFor(model => model.Percentage, "", new {@class = "text-danger"})
        </div>
    </div>


    <div class="form-group">
        @Html.Label("إحمالي العمولة", new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EditorFor(model => model.Amount)
            @Html.ValidationMessageFor(model => model.Amount, "", new {@class = "text-danger", @readonly = "readonly"})
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EditorFor(model => model.Note)
            @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger", @readonly = "readonly"})
        </div>
    </div>

</div>


<script>

    $(function() {
        $("#ServiceID", "#CreditorAccountID", "#StartDate", "EndDate").on('change',
            function() {
                $("#RefNumber").val('');
                $("#OpsCount").val('');
                $("#Percentage").val('');
                $("#Amount").val('');
            });
    });

    $('#OpsCount').prop('readonly', true);
    $('#Amount').prop('readonly', true);
    $("#form").hide();

    function onSuccess(data) {
        if (data.Success) {

            $("#form").show();
            $("#RefNumber").val(data.total);
            $("#OpsCount").val(data.num);
        } else {
            alert(data.Message);
        }
    }

    $('#Percentage').on('input',
        function() {
            i('perc input ');
            var rate = Number($('#Percentage').val());
            var refNumber = Number($('#RefNumber').val().replace(',', ''));
            var commission = refNumber * (rate / 100);
            i('commission ' + commission);
            $('#Amount').val(commission);
        });


</script>