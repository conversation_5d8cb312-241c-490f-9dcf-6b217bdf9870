<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة التحصيلات - Collections System</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 15px; 
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .header { 
            text-align: center; 
            margin-bottom: 40px; 
        }
        .header h1 { 
            font-size: 3em; 
            margin: 0; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .status-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin: 30px 0; 
        }
        .status-card { 
            background: rgba(255,255,255,0.2); 
            padding: 20px; 
            border-radius: 10px; 
            border: 1px solid rgba(255,255,255,0.3);
            transition: transform 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.4);
        }
        .status-card h3 { 
            margin-top: 0; 
            color: #fff; 
        }
        .status-success { 
            border-left: 5px solid #27ae60; 
        }
        .status-warning { 
            border-left: 5px solid #f39c12; 
        }
        .status-info { 
            border-left: 5px solid #3498db; 
        }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            margin: 10px; 
            background: rgba(255,255,255,0.2); 
            color: white; 
            text-decoration: none; 
            border-radius: 8px; 
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }
        .btn:hover { 
            background: rgba(255,255,255,0.3); 
            transform: translateY(-2px);
        }
        .features { 
            margin: 30px 0; 
        }
        .features ul { 
            list-style: none; 
            padding: 0; 
        }
        .features li { 
            padding: 10px 0; 
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .features li:before { 
            content: "✅ "; 
            margin-right: 10px; 
        }
        .alert {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid rgba(231, 76, 60, 0.5);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 نظام إدارة التحصيلات</h1>
            <p>Collections Management System</p>
        </div>

        <div class="status-grid">
            <div class="status-card status-success">
                <h3>✅ حالة النظام</h3>
                <p><strong>الخادم:</strong> يعمل بنجاح</p>
                <p><strong>الملفات:</strong> متاحة</p>
                <p><strong>الواجهة:</strong> جاهزة</p>
                <p><strong>الحالة:</strong> نشط</p>
            </div>

            <div class="status-card status-warning">
                <h3>⚠️ قاعدة البيانات</h3>
                <p><strong>MySQL:</strong> غير متصل</p>
                <p><strong>البيانات:</strong> غير متاحة</p>
                <p><strong>الوظائف:</strong> محدودة</p>
                <p><strong>الحل:</strong> إصلاح MySQL</p>
            </div>

            <div class="status-card status-info">
                <h3>📊 معلومات النظام</h3>
                <p><strong>النوع:</strong> نظام PHP</p>
                <p><strong>قاعدة البيانات:</strong> MySQL</p>
                <p><strong>الخادم:</strong> Apache/IIS</p>
                <p><strong>الإصدار:</strong> 2.0</p>
            </div>
        </div>

        <div class="alert">
            <h3>🔧 ملاحظة مهمة</h3>
            <p>نظام Collections System يحتاج إلى MySQL لكي يعمل بالكامل. حالياً النظام يعمل في وضع العرض فقط.</p>
        </div>

        <div class="features">
            <h3>🎯 الوظائف المتاحة (عند توصيل قاعدة البيانات):</h3>
            <ul>
                <li>إدارة التحصيلات المالية</li>
                <li>تقارير مفصلة وأسبوعية</li>
                <li>إدارة الوكلاء والمحصلين</li>
                <li>نسخ احتياطية للبيانات</li>
                <li>تصدير التقارير إلى Excel</li>
                <li>واجهة مستخدم سهلة الاستخدام</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <h3>🔗 روابط النظام:</h3>
            <a href="reports.php" class="btn">📊 التقارير</a>
            <a href="add_collection.php" class="btn">➕ إضافة تحصيل</a>
            <a href="manage_agents.php" class="btn">👥 إدارة الوكلاء</a>
            <a href="backup.php" class="btn">💾 النسخ الاحتياطية</a>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <h3>🌐 الأنظمة الأخرى:</h3>
            <a href="/portal" class="btn">🏠 AppTech Portal</a>
            <a href="/api" class="btn">🔌 API</a>
            <a href="/client" class="btn">👥 العملاء</a>
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.3);">
            <p>🔧 لتشغيل النظام بالكامل: إصلاح MySQL في XAMPP</p>
            <p>📋 للمزيد من المعلومات: SystemStatus_Final.md</p>
            <p>🛠️ أدوات فك التشفير: C:\inetpub\DeobfuscationTools\</p>
        </div>
    </div>
</body>
</html>
