﻿@using System.Data

<div class="align-center" style="margin-bottom:20px">
    <strong>
        @if (Model != null)
        {
            @ViewBag.Title
        }
    </strong>
</div>



<div class="table-responsive ">
    <table id="simple-table" class="table  table-bordered table-hover">

        @{
            if (Model != null)
            {
                <thead>
                    <tr>

                        @foreach (DataColumn col in Model.Result.Columns)
                        {
                            <th>@col.Caption</th>
                        }
                    </tr>
                </thead>
                <tbody>

                    @for (var i = 0; i < Model.Result.Rows.Count - Model.Footer; i++)
                    {
                        <tr>
                            @foreach (var cell in Model.Result.Rows[i].ItemArray)
                            {
                                <td class="align-right">@cell.ToString()</td>
                            }

                        </tr>
                    }
                </tbody>
                <thead>
                    @for (var i = Model.Result.Rows.Count - Model.Footer; i < Model.Result.Rows.Count; i++)
                    {
                        <tr>
                            @if (i < 0)
                            {
                                return;
                            }
                            @foreach (var cell in Model.Result.Rows[i].ItemArray)
                            {
                                <td class="align-right">@cell.ToString()</td>
                            }

                        </tr>
                    }
                </thead>
            }
        }
    </table>

</div>

<script>
    $('#simple-table tr').each(function () {
        var row = $(this);
        row.find("td").each(function () {
        //   var status = $(this).find("td:eq(11)").html();
        //   var status = $(this).find("td:eq('الحالة')").html();
        var status = $(this).html();

        i("status= " + status);
        if (status === 'رصيد سابق') {
            //  $(this).addClass('label label-success arrowed-in arrowed-in-right');
            row.addClass('warning');
        }


        //var status = Number($(this).find("td:eq(1)").html());
        ////    i("acc-num= " + status);
        //if (status === 1 || status === 2 || status === 3 || status === 4) {
        //    $(this).addClass('warning');
        //}



    });
    });
</script>