﻿@model AppTech.MSMS.Domain.Models.PartyGroup
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
@Html.ValidationSummary(true, "", new {@class = "text-danger"})
<div class="form-group">
    @Html.LabelFor(model => model.Name, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Name, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Description, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Description, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Description, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.Label("اختر الحسابات", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.ListBoxFor(model => model.SelectedItems, (SelectList) ViewBag.Items, new {htmlAttributes = new {@class = "chosen-select"}})
        @Html.ValidationMessageFor(model => model.SelectedItems, "", new {@class = "text-danger"})
    </div>
</div>