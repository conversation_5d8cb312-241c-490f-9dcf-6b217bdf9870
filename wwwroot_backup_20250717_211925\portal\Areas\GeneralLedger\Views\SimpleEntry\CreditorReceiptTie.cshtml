﻿@model AppTech.MSMS.Web.Models.ReceiptModel
@Styles.Render("~/Content/print")

<div class=" col-sm-12" style="border: 1px solid black;">
    <div class="table table-responsive">
        <table id="simple-table" class="table table-condensed no-border" style="border-bottom: 1px solid black;margin: 1px 0 0 0;">
            <tr>
                
                <td class="align-center col-sm-3  no-border">
                    رقم السند  <br />
                    <div style="border: 1px solid black;  font: bold 18px Helvetica, Sans-Serif;padding: 7px 0;">
                        @Html.DisplayFor(model => model.Number)
                    </div>
                </td>
              
                <td class="align-center col-sm-6  no-border">
                    <div style="border: 1px solid black; font: bold 18px Helvetica, Sans-Serif; font-size: 38px; padding: 7px 0;">
                        @Html.DisplayFor(model => model.Type)
                    </div>
                </td>
                <td class="align-center col-sm-3  no-border">
                    التاريخ <br/>
                    <div style="border: 1px solid black; font: bold 18px Helvetica, Sans-Serif; padding: 7px 0;">
                        @Html.DisplayFor(model => model.Date)
                    </div>
                </td>
            </tr>
        </table>

        <table id="simple-table" class="table table-condensed">
            <tr>
                <td class="no-border">
                    <b>
                        السيد :
                    </b>
                </td>
                <td colspan="3" class="no-border">
                    @Html.DisplayFor(model => model.AccountName)
                </td>
                @*<td class="no-border">
                    <b>
                        رقم الحساب @Html.DisplayFor(model => model.Delivery)
                    </b>
                </td>*@
            </tr>
            <tr>
                <td class="no-border"></td>
                <td colspan="4" class="no-border">
                    نــــود اشعـــاركــــــــــم اننــا قيدنـــــــــا لحســــابــكــم لدينــا حــــســـــب التفــــــاصيـــــــل الــتـــالـيـه
                </td>
            </tr>
            <tr>
                <td class="no-border"></td>
                <td class="align-center no-border">
                    <b>
                        العملة
                    </b>
                </td>
                <td class="align-center no-border">
                    <b>
                        المبلغ   
                    </b>
                </td>
                <td class="no-border"></td>
                <td class="no-border"></td>
            </tr>
            <tr>
                <td class="no-border"></td>
                <td class="align-center">
                    @Html.DisplayFor(model => model.CurrencyName)
                </td>
                <td class="align-center" >
                    #
                    @Html.DisplayFor(model => model.Amount)
                    #
                </td>
                <td class="no-border"></td>
                <td class="no-border"></td>

            </tr>
            <tr>
                <td class="no-border"></td>
                <td colspan="4">
                    @Html.DisplayFor(model => model.AmountInText) لاغير
                </td>
            </tr>
            <tr>
                <td class="no-border">
                    <b>
                        البيان  :
                    </b>
                </td>
                <td colspan="4">
                    مقابل تحويل لحسابكم لدينا بتاريخ :  @Html.DisplayFor(model => model.Date) <br />
                    من حساب السيد :..  @Html.DisplayFor(model => model.FundName) <br />
                    @Html.DisplayFor(model => model.Note)
                </td>

            </tr>
        </table>
    </div>
</div>