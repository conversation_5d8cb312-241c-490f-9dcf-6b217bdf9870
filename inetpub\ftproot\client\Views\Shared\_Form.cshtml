﻿<div id="main-form">
    @using (Ajax.BeginForm(new AjaxOptions
    {
        OnBegin = "return OnFormBegin()",
        OnSuccess = "onCrudSuccess",
        OnFailure = "onCrudFailure",

        

    }))
    {
        <section>
            <div class="form-horizontal">
                @Html.AntiForgeryToken()
                @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                @RenderBody()
                
            </div>
        </section>
        <div class="space-10"></div>
        <div class="modal-footer">
            <button type="button" id="cancelModal" class="btn btn-danger btn-round " ><i class="glyphicon glyphicon-remove" data-dismiss="modal"></i> إلغاء</button>
            @Html.Partial("_FormAction")
            <span class="alert"></span>
        
        </div>
    }
</div>

<script>
        var id = 0;

    $(function () {


        $("#formloader").hide();
        if ($("#Amount")[0]) {
            //  $("#Amount").val('');
            $('#Amount').on('change input',
                function () {
                    i('Amount key down');
                    var words = tafqeet($('#Amount').val());
                    $('#words').text(words);
                });
        }

        if ($("#Date")[0]) {
            var date = $("#Date").val();
            i('date ' + date);
            if (date.includes('0001')) {
                $("#Date").val(getToday());
            }
            //$("#Date").addClass("date-picker");
        }

        $('#cancelModal').on('click',
            function() {
                closeDialog();
            });
    });

    var formHelper = {
        onSuccess: function (data) {
            hideFormLoading();
            closeDialog();
            showSuccess(data.Message);
            fetchData(false, data.Message.includes('تعديل'));
        },
        onBegin: function (context) {

            var confirmMsg = "";

            try {
                confirmMsg =  $("#modal").find(".modal-title").text();
             
            } catch (e) {

            } 
            try {
                if ($("#Amount")[0]) {
                    confirmMsg +=' '+ $("#Title").val() + '\n بمبلغ ' + $("#Amount").val();

                    if ($("#CurrencyID")[0]) {
                        confirmMsg +=' '+$("#CurrencyID option:selected").html() ;
                    }
                }
               
            } catch (e) {

            } 
            confirmMsg += '\n هل انت متأكد ؟ ';
            if (confirm(confirmMsg)) {
                return true;
            }
            else {
                resetButton();
                return false;
                }

        }
    }

    function OnFormBegin(context) {
        return formHelper.onBegin(context);
    }

    function onCrudSuccess(data) {
        formHelper.onSuccess(data);
    }

    function onCrudFailure(xhr, textStatus, errorThrown) {
        hideFormLoading();
        parseAndShowError(xhr, textStatus, errorThrown);
    }



</script>

<script>

    try {
        $('.date-picker').datepicker({
            dateFormat: "dd/MM/yy",
            changeMonth: true,
            changeYear: true,
            yearRange: "-60:+0",
            autoclose: true,
            todayHighlight: true,

        }).next().on('click',
            function () {
                $(this).prev().focus();
            });
    } catch (e) {
        alert("Couldnt set date-picker: " + e);
    }
  
</script>