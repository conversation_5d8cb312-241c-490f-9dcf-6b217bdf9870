﻿@using System.Data
@using AppTech.Common.Extensions
@using AppTech.MSMS.Web.Models
@using AppTech.MSMS.Web.Code.Paginate.
@model  AppTech.MSMS.Web.Code.Paginate.PagedList
@{
    var condtion = Model.Condition;
}

<style>
    .table-wrapper-scroll-y {
        display: block;
        max-height: 700px;
        overflow-y: auto;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }

    .table > thead > tr:first-child > td {
        border: none;
    }

    .table-bordered td {
        border: none !important;
        border-right: solid 0px #ccc !important;
    }


    th {
        background-color: white;
        color: steelblue;
        border-right: none !important;
        border-left: none !important;
    }
</style>

<div id="list">
    Order List
    <input type="hidden" id="QryCondition" value="@condtion" />
    <div class="table-responsive ">
        <table id="simple-table" class="table table-hover table-bordered table-striped">
            <thead class="">
                <tr height="40" class="text-center">
                    @foreach (DataColumn col in Model.Items.Columns)
                    {
                        if (!col.Caption.Equals("ID"))
                        {
                            <th data-sortable="true">@col.Caption</th>
                        }
                    }
                    <th style="text-align: right;" class="none-print-element"></th>
                </tr>
            </thead>
            <tbody id="table-body">
                @foreach (DataRow row in Model.Items.Rows)
                {
                    <tr height="" data-id="@row["ID"]">
                        @for (var i = 0; i < row.ItemArray.Length; i++)
                        {
                            if (i != 0)
                            {
                                if (row.ItemArray[i] is decimal)
                                {
                                    <td style="text-align: right;">@row.ItemArray[i].ToDecimal().ToString("#.##")</td>
                                }
                                else
                                {
                                    if (row["الحالة"].ToString().Equals("مرحلة"))
                                    {

                                        <td style="text-align: right;" class="label label-success arrowed-in arrowed-in-right">

                                            <span class="label label-success arrowed-in arrowed-in-right">@row.ItemArray[i].ToString()</span>
                                        </td>
                                    }
                                    else if (row["الحالة"].ToString().Equals("ملغية"))
                                    {

                                        <td style="text-align: right;" class="label label-success arrowed-in arrowed-in-right">

                                            <span class="label label-danger arrowed-in arrowed-in-right">@row.ItemArray[i].ToString()</span>
                                        </td>
                                    }

                                    else
                                    {
                                        < td style = "text-align: right;" > @row.ItemArray[i].ToString() </ td >
                                    }
                                }
                            }
                        }
                        <td style="text-align: right;" class="none-print-element record-actions">

                            <div class="btn-group form-inline">
                                @{
                                    if (!string.IsNullOrEmpty(Model.RecordAction))
                                    {
                                        @Html.Partial(Model.RecordAction, new ActionModel { ID = row["ID"].ToLong(), Row = row })
                                    }
                                    else
                                    {
                                        @Html.Partial("_RecordAction", new ActionModel { ID = row["ID"].ToLong() })
                                    }
                                }
                            </div>
                        </td>
                    </tr>
                }
            </tbody>

            <tfoot>
                <tr></tr>
            </tfoot>
        </table>
    </div>
    <div class="center none-print-element">
      
        footer
    </div>
</div>

<script src="~/Scripts/table/Data.js"></script>

