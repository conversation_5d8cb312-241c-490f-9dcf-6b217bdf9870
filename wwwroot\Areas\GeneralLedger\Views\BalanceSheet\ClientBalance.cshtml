﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Web.Models.ClientsBalancseModel

@{
    Layout = "~/Views/Shared/_Report.cshtml";
}

<div class="form-horizontal">


    <div class="form-group">
        @Html.Label("كافة العملاء", new {@class = "control-label col-md-6"})
        <div class="col-md-6">
            @Html.CheckBoxFor(x => x.AllClients, new {})
        </div>
    </div>

    <div class="hr hr-dotted hr-24"></div>
    <div class="space-6"></div>

    <div id="client">
        <span class="lbl">اسم العميل </span>
        <div class="form-group">
            <div class="col-md-10">
                @Html.Obout(new ComboBox("AccountID")
                {
                    Width = 230,
                    FilterType = ComboBoxFilterType.Contains
                })

            </div>
        </div>

        <div class="hr hr-dotted hr-24"></div>
        <div class="space-6"></div>
    </div>


    <div class="form-group">
        @Html.Label("الحالة", new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EnumDropDownListFor(x => x.Status)
        </div>
    </div>
</div>

<script>
    $('#AllClients').on('click',
        function() {
            $("#client").toggle(!this.checked);
        });
</script>