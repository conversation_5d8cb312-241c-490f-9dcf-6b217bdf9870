{"version": 3, "file": "angular-touch.min.js", "lineCount": 9, "mappings": "A;;;;;aAKC,SAAQ,CAACA,CAAD,CAASC,CAAT,CAAkB,CA0S3BC,QAASA,EAAkB,CAACC,CAAD,CAAgBC,CAAhB,CAA2BC,CAA3B,CAAsC,CAC/DC,CAAAC,UAAA,CAAkBJ,CAAlB,CAAiC,CAAC,QAAD,CAAW,QAAX,CAAqB,QAAQ,CAACK,CAAD,CAASC,CAAT,CAAiB,CAQ7E,MAAO,SAAQ,CAACC,CAAD,CAAQC,CAAR,CAAiBC,CAAjB,CAAuB,CAKpCC,QAASA,EAAU,CAACC,CAAD,CAAS,CAS1B,GAAKC,CAAAA,CAAL,CAAkB,MAAO,CAAA,CACzB,KAAIC,EAASC,IAAAC,IAAA,CAASJ,CAAAK,EAAT,CAAoBJ,CAAAI,EAApB,CACTC,EAAAA,EAAUN,CAAAO,EAAVD,CAAqBL,CAAAM,EAArBD,EAAsChB,CAC1C,OAAOkB,EAAP,EAvBwBC,EAuBxB,CACIP,CADJ,EAEa,CAFb,CAEII,CAFJ,EAnB0BI,EAmB1B,CAGIJ,CAHJ,EArBqBK,EAqBrB,CAIIT,CAJJ,CAIaI,CAhBa,CAJ5B,IAAIM,EAAelB,CAAA,CAAOI,CAAA,CAAKT,CAAL,CAAP,CAAnB,CAEIY,CAFJ,CAEiBO,CAFjB,CAuBIK,EAAe,CAAC,OAAD,CACd1B,EAAA2B,UAAA,CAAkBhB,CAAA,oBAAlB,CAAL,EACEe,CAAAE,KAAA,CAAkB,OAAlB,CAEFpB,EAAAqB,KAAA,CAAYnB,CAAZ,CAAqB,CACnB,MAASoB,QAAQ,CAACjB,CAAD,CAASkB,CAAT,CAAgB,CAC/BjB,CAAA,CAAcD,CACdQ,EAAA,CAAQ,CAAA,CAFuB,CADd,CAKnB,OAAUW,QAAQ,CAACD,CAAD,CAAQ,CACxBV,CAAA,CAAQ,CAAA,CADgB,CALP,CAQnB,IAAOY,QAAQ,CAACpB,CAAD,CAASkB,CAAT,CAAgB,CACzBnB,CAAA,CAAWC,CAAX,CAAJ,EACEJ,CAAAyB,OAAA,CAAa,QAAQ,EAAG,CACtBxB,CAAAyB,eAAA,CAAuB/B,CAAvB,CACAqB,EAAA,CAAahB,CAAb,CAAoB,CAAC2B,OAAQL,CAAT,CAApB,CAFsB,CAAxB,CAF2B,CARZ,CAArB,CAgBGL,CAhBH,CA5BoC,CARuC,CAA9C,CAAjC,CAD+D,CAlRjE,IAAIrB,EAAUL,CAAAqC,OAAA,CAAe,SAAf,CAA0B,EAA1B,CAEdhC,EAAAiC,KAAA,CAAa,CAAEC,eAAgB,OAAlB,CAAb,CA+BAlC;CAAAmC,QAAA,CAAgB,QAAhB,CAA0B,CAAC,QAAQ,EAAG,CAwBpCC,QAASA,EAAc,CAACV,CAAD,CAAQ,CACzBW,CAAAA,CAAgBX,CAAAW,cAAhBA,EAAuCX,CAC3C,KAAIY,EAAUD,CAAAC,QAAA,EAAyBD,CAAAC,QAAAC,OAAzB,CAAwDF,CAAAC,QAAxD,CAAgF,CAACD,CAAD,CAC1FG,EAAAA,CAAKH,CAAAI,eAALD,EAAqCH,CAAAI,eAAA,CAA6B,CAA7B,CAArCD,EAAyEF,CAAA,CAAQ,CAAR,CAE7E,OAAO,CACLvB,EAAGyB,CAAAE,QADE,CAEL7B,EAAG2B,CAAAG,QAFE,CALsB,CAW/BC,QAASA,EAAS,CAACvB,CAAD,CAAewB,CAAf,CAA0B,CAC1C,IAAIC,EAAM,EACVnD,EAAAoD,QAAA,CAAgB1B,CAAhB,CAA8B,QAAQ,CAAC2B,CAAD,CAAc,CAElD,CADIjD,CACJ,CADgBkD,CAAA,CAAeD,CAAf,CAAA,CAA4BH,CAA5B,CAChB,GACEC,CAAAvB,KAAA,CAASxB,CAAT,CAHgD,CAApD,CAMA,OAAO+C,EAAAI,KAAA,CAAS,GAAT,CARmC,CA/B5C,IAAID,EAAiB,CACnB,MAAS,CACPxB,MAAO,WADA,CAEP0B,KAAM,WAFC,CAGPvB,IAAK,SAHE,CADU,CAMnB,MAAS,CACPH,MAAO,YADA,CAEP0B,KAAM,WAFC,CAGPvB,IAAK,UAHE,CAIPD,OAAQ,aAJD,CANU,CAYnB,QAAW,CACTF,MAAO,aADE,CAET0B,KAAM,aAFG,CAGTvB,IAAK,WAHI,CAITD,OAAQ,eAJC,CAZQ,CA0CrB;MAAO,CAkCLH,KAAMA,QAAQ,CAACnB,CAAD,CAAU+C,CAAV,CAAyB/B,CAAzB,CAAuC,CAAA,IAE/CgC,CAF+C,CAEvCC,CAFuC,CAI/C7C,CAJ+C,CAM/C8C,CAN+C,CAQ/CC,EAAS,CAAA,CAEbnC,EAAA,CAAeA,CAAf,EAA+B,CAAC,OAAD,CAAU,OAAV,CAAmB,SAAnB,CAC/BhB,EAAAoD,GAAA,CAAWb,CAAA,CAAUvB,CAAV,CAAwB,OAAxB,CAAX,CAA6C,QAAQ,CAACK,CAAD,CAAQ,CAC3DjB,CAAA,CAAc2B,CAAA,CAAeV,CAAf,CACd8B,EAAA,CAAS,CAAA,CAETF,EAAA,CADAD,CACA,CADS,CAETE,EAAA,CAAU9C,CACN2C,EAAA,MAAJ,EACEA,CAAA,MAAA,CAAuB3C,CAAvB,CAAoCiB,CAApC,CAPyD,CAA7D,CAUA,KAAIgC,EAASd,CAAA,CAAUvB,CAAV,CAAwB,QAAxB,CACb,IAAIqC,CAAJ,CACErD,CAAAoD,GAAA,CAAWC,CAAX,CAAmB,QAAQ,CAAChC,CAAD,CAAQ,CACjC8B,CAAA,CAAS,CAAA,CACLJ,EAAA,OAAJ,EACEA,CAAA,OAAA,CAAwB1B,CAAxB,CAH+B,CAAnC,CAQFrB,EAAAoD,GAAA,CAAWb,CAAA,CAAUvB,CAAV,CAAwB,MAAxB,CAAX,CAA4C,QAAQ,CAACK,CAAD,CAAQ,CAC1D,GAAK8B,CAAL,EAQK/C,CARL,CAQA,CACA,IAAID,EAAS4B,CAAA,CAAeV,CAAf,CAEb2B,EAAA,EAAU1C,IAAAC,IAAA,CAASJ,CAAAO,EAAT,CAAoBwC,CAAAxC,EAApB,CACVuC,EAAA,EAAU3C,IAAAC,IAAA,CAASJ,CAAAK,EAAT,CAAoB0C,CAAA1C,EAApB,CAEV0C,EAAA,CAAU/C,CA5HSmD,GA8HnB,CAAIN,CAAJ,EA9HmBM,EA8HnB,CAAmCL,CAAnC,GAKIA,CAAJ,CAAaD,CAAb,EAEEG,CACA,CADS,CAAA,CACT,CAAIJ,CAAA,OAAJ,EACEA,CAAA,OAAA,CAAwB1B,CAAxB,CAJJ,GASEA,CAAAkC,eAAA,EACA,CAAIR,CAAA,KAAJ,EACEA,CAAA,KAAA,CAAsB5C,CAAtB,CAA8BkB,CAA9B,CAXJ,CALA,CARA,CAT0D,CAA5D,CAsCArB,EAAAoD,GAAA,CAAWb,CAAA,CAAUvB,CAAV,CAAwB,KAAxB,CAAX,CAA2C,QAAQ,CAACK,CAAD,CAAQ,CACpD8B,CAAL,GACAA,CACA,CADS,CAAA,CACT,CAAIJ,CAAA,IAAJ,EACEA,CAAA,IAAA,CAAqBhB,CAAA,CAAeV,CAAf,CAArB,CAA4CA,CAA5C,CAHF,CADyD,CAA3D,CArEmD,CAlChD,CA9C6B,CAAZ,CAA1B,CA4SA9B,EAAA,CAAmB,aAAnB,CAAmC,EAAnC,CAAsC,WAAtC,CACAA;CAAA,CAAmB,cAAnB,CAAmC,CAAnC,CAAsC,YAAtC,CAtW2B,CAA1B,CAAD,CA0WGF,MA1WH,CA0WWA,MAAAC,QA1WX;", "sources": ["angular-touch.js"], "names": ["window", "angular", "makeSwipeDirective", "directiveName", "direction", "eventName", "ngTouch", "directive", "$parse", "$swipe", "scope", "element", "attr", "validSwipe", "coords", "startCoords", "deltaY", "Math", "abs", "y", "deltaX", "x", "valid", "MAX_VERTICAL_DISTANCE", "MIN_HORIZONTAL_DISTANCE", "MAX_VERTICAL_RATIO", "swi<PERSON><PERSON><PERSON><PERSON>", "pointerTypes", "isDefined", "push", "bind", "start", "event", "cancel", "end", "$apply", "<PERSON><PERSON><PERSON><PERSON>", "$event", "module", "info", "angularVersion", "factory", "getCoordinates", "originalEvent", "touches", "length", "e", "changedTouches", "clientX", "clientY", "getEvents", "eventType", "res", "for<PERSON>ach", "pointerType", "POINTER_EVENTS", "join", "move", "eventHandlers", "totalX", "totalY", "lastPos", "active", "on", "events", "MOVE_BUFFER_RADIUS", "preventDefault"]}