﻿@model AppTech.MSMS.Web.Models.ClientPermissionModel

@{
    Layout = "~/Views/Shared/_FormModal.cshtml";
}

@Html.Partial("_FormAction")
<div class="hr hr-dotted hr-24"></div>
<input type="checkbox" onClick="toggle(this)" />
<strong class="blue">أختر الكل</strong>
<br />

<input type="hidden" name="groupId" value="@Model.AccountID" />
<div class="table-responsive ">
    <table id="items-table" class="table table-hover table-striped">
        <thead class="">
            <tr style="background-color: gray; color: white">
                <th style="text-align: center;">
                    اختيار
                </th>
                <th style="text-align: center;">العنصر</th>
            </tr>
        </thead>

        <tbody id="table-body">
            @foreach (var item in Model.Actions)
            {


                <tr>
                    <td class="align-center">
                        <input id="chk@(item.Value)"
                               name="items"
                               type="checkbox"
                               class="action"
                               value="@item.Value"
                               checked="@item.IsAllowed" />

                    </td>
                    <td style="text-align: right;">
                        <strong>@item.Text</strong>

                    </td>
                </tr>
            }

        </tbody>
    </table>

</div>

<script>
    jQuery(document).ready(function ($) {

        try {
            var table = $("#items-table").DataTable({
                "paging": false,
                "order": [[0, "desc"]],
                "lengthChange": false,
                "searching": false,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                dom: "Bfrtip",

                "language": {
                    "decimal": ",",
                    "thousands": ".",
                    "sProcessing": "جارٍ التحميل...",
                    "sLengthMenu": "أظهر _MENU_ مدخلات",
                    "sZeroRecords": "لم يعثر على أية سجلات",
                    "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                    "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
                    "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
                    "sInfoPostFix": "",
                    "sSearch": "البحث:",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "الأول",
                        "sPrevious": "السابق",
                        "sNext": "التالي",
                        "sLast": "الأخير"
                    }
                }
            });


            formHelper.onBegin = function () {
                var query = table.search();
                i('query ' + query);
                if (query != '') {
                    ar('قم بحذف فلتره البحث اولا');
                    return false;
                }
                else {
                    return true;
                }
            }

        } catch (e) {
            i("data error: " + e);
        }
    });
</script>

<script>


    function toggle(source) {
        var checkboxes = $(".action");
        for (var i = 0, n = checkboxes.length; i < n; i++) {
            checkboxes[i].checked = source.checked;
        }
    }


</script>
