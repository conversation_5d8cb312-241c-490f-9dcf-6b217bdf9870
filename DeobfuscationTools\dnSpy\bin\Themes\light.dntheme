<theme guid="B3D71E74-C353-4206-9AD1-05847BE95CBE" name="light" menu-name="_Light" order="300" is-light="true" is-high-contrast="false">
	<colors>
		<color name="defaulttext" fg="Black" bg="White" />
		<color name="operator" fg="#000000" />
		<color name="punctuation" fg="#000000" />
		<color name="number" fg="#5B2DA8" />
		<color name="comment" fg="#008000" />
		<color name="keyword" fg="#0000FF" />
		<color name="string" fg="#A31515" />
		<color name="verbatimstring" fg="#800000" />
		<color name="char" fg="#A31515" />
		<color name="namespace" fg="#9E7E00" />
		<color name="type" fg="#2B91AF" />
		<color name="sealedtype" fg="#2B91AF" />
		<color name="statictype" fg="#1B5D70" />
		<color name="delegate" fg="#6666FF" />
		<color name="enum" fg="#336600" />
		<color name="interface" fg="#1E667A" />
		<color name="valuetype" fg="#009933" />
		<color name="module" fg="#1B5D70" />
		<color name="typegenericparameter" fg="#2B91AF" />
		<color name="methodgenericparameter" fg="#2B91AF" />
		<color name="instancemethod" fg="#880000" />
		<color name="staticmethod" fg="#880000" />
		<color name="extensionmethod" fg="#960000" italics="true" />
		<color name="instancefield" fg="#CC3399" />
		<color name="enumfield" fg="#6F008A" />
		<color name="literalfield" fg="#9900FF" />
		<color name="staticfield" fg="#990099" />
		<color name="instanceevent" fg="#990033" />
		<color name="staticevent" fg="#660033" />
		<color name="instanceproperty" fg="#996633" />
		<color name="staticproperty" fg="#7A5229" />
		<color name="local" fg="Black" />
		<color name="parameter" fg="Black" bold="true" />
		<color name="preprocessorkeyword" fg="#FF808080" />
		<color name="preprocessortext" fg="#FF000000" />
		<color name="label" fg="#663300" />
		<color name="opcode" fg="#993366" />
		<color name="ildirective" fg="#009900" />
		<color name="ilmodule" fg="#660033" />
		<color name="excludedcode" fg="#808080" />
		<color name="xmldoccommentattributename" fg="#FF808080" />
		<color name="xmldoccommentattributequotes" fg="#FF808080" />
		<color name="xmldoccommentattributevalue" fg="#FF808080" />
		<color name="xmldoccommentcdatasection" fg="#FF808080" />
		<color name="xmldoccommentcomment" fg="#FF808080" />
		<color name="xmldoccommentdelimiter" fg="#FF808080" />
		<color name="xmldoccommententityreference" fg="#FF008000" />
		<color name="xmldoccommentname" fg="#FF808080" />
		<color name="xmldoccommentprocessinginstruction" fg="#FF808080" />
		<color name="xmldoccommenttext" fg="#FF008000" />
		<color name="xmlliteralattributename" fg="#FFB96464" />
		<color name="xmlliteralattributequotes" fg="#FF555555" />
		<color name="xmlliteralattributevalue" fg="#FF6464B9" />
		<color name="xmlliteralcdatasection" fg="#FFC0C0C0" />
		<color name="xmlliteralcomment" fg="#FF629755" />
		<color name="xmlliteraldelimiter" fg="#FF6464B9" />
		<color name="xmlliteralembeddedexpression" fg="#FF555555" />
		<color name="xmlliteralentityreference" fg="#FFB96464" />
		<color name="xmlliteralname" fg="#FF844646" />
		<color name="xmlliteralprocessinginstruction" fg="#FFC0C0C0" />
		<color name="xmlliteraltext" fg="#FF555555" />
		<color name="xmlattribute" fg="#FFFF0000" />
		<color name="xmlattributequotes" fg="#FF000000" />
		<color name="xmlattributevalue" fg="#FF0000FF" />
		<color name="xmlcdatasection" fg="#FF808080" />
		<color name="xmlcomment" fg="#FF008000" />
		<color name="xmldelimiter" fg="#FF0000FF" />
		<color name="xmlkeyword" fg="#FF0000FF" />
		<color name="xmlname" fg="#FFA31515" />
		<color name="xmlprocessinginstruction" fg="#FF808080" />
		<color name="xmltext" fg="#FF000000" />
		<color name="xamlattribute" fg="#FFFF0000" />
		<color name="xamlattributequotes" fg="#FF000000" />
		<color name="xamlattributevalue" fg="#FF0000FF" />
		<color name="xamlcdatasection" fg="#FF808080" />
		<color name="xamlcomment" fg="#FF008000" />
		<color name="xamldelimiter" fg="#FF0000FF" />
		<color name="xamlkeyword" fg="#FF0000FF" />
		<color name="xamlmarkupextensionclass" fg="#FFA31515" />
		<color name="xamlmarkupextensionparametername" fg="#FFFF0000" />
		<color name="xamlmarkupextensionparametervalue" fg="#FF0000FF" />
		<color name="xamlname" fg="#FFA31515" />
		<color name="xamlprocessinginstruction" fg="#FF808080" />
		<color name="xamltext" fg="#FF000000" />
		<color name="xmldoctooltipheader" italics="true" bold="true" />
		<color name="assembly" fg="#414141" />
		<color name="assemblyexe" fg="#C17B19" />
		<color name="assemblymodule" fg="#642C8F" />
		<color name="directorypart" fg="Black" />
		<color name="filenamenoextension" fg="#990000" />
		<color name="fileextension" fg="#3366CC" />
		<color name="error" fg="Red" />
		<color name="tostringeval" fg="#2E387C" />
		<color name="linenumber" fg="#2B91AF" />
		<color name="repllinenumberinput1" fg="#A31515" />
		<color name="repllinenumberinput2" fg="#0000FF" />
		<color name="repllinenumberoutput" fg="#2B91AF" />
		<color name="visiblewhitespace" fg="#FF2B91AF" />
		<color name="selectedtext" bg="#FF0078D7" />
		<color name="inactiveselectedtext" bg="#FFBFCDDB" />
		<color name="highlightedreference" bg="#FFDBE0CC" />
		<color name="highlightedwrittenreference" bg="#FFDBE0CC" />
		<color name="highlighteddefinition" bg="#FFDBE0CC" fg="#FF808080" />
		<color name="currentstatement" fg="#FF000000" />
		<color name="currentstatementmarker" bg="#FFFFEE62" />
		<color name="callreturn" fg="#FF000000" />
		<color name="callreturnmarker" bg="#FFB4E4B4" />
		<color name="activestatementmarker" bg="#FFC0C0C0" />
		<color name="breakpointstatement" fg="#FFFFFFFF" />
		<color name="breakpointstatementmarker" bg="#FF963A46" />
		<color name="selectedbreakpointstatementmarker" bg="#FF963A46" fg="#FF0000FF" />
		<color name="disabledbreakpointstatementmarker" fg="#FF800000" />
		<color name="advancedbreakpointstatement" fg="#FFFFFFFF" />
		<color name="advancedbreakpointstatementmarker" bg="#FF963A46" />
		<color name="selectedadvancedbreakpointstatementmarker" bg="#FF963A46" fg="#FF0000FF" />
		<color name="disabledadvancedbreakpointstatement" fg="#FF800000" />
		<color name="disabledadvancedbreakpointstatementmarker" bg="#FFFFFFFF" />
		<color name="selecteddisabledadvancedbreakpointstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="breakpointwarningstatement" fg="#FFFFFFFF" />
		<color name="breakpointwarningstatementmarker" bg="#FF963A46" />
		<color name="selectedbreakpointwarningstatementmarker" bg="#FF963A46" fg="#FF0000FF" />
		<color name="breakpointerrorstatement" fg="#FFFFDBA3" />
		<color name="breakpointerrorstatementmarker" bg="#FF963A46" />
		<color name="selectedbreakpointerrorstatementmarker" bg="#FF963A46" fg="#FF0000FF" />
		<color name="advancedbreakpointwarningstatement" fg="#FFFFFFFF" />
		<color name="advancedbreakpointwarningstatementmarker" bg="#FF963A46" />
		<color name="selectedadvancedbreakpointwarningstatementmarker" bg="#FF963A46" fg="#FF0000FF" />
		<color name="advancedbreakpointerrorstatement" fg="#FFFFDBA3" />
		<color name="advancedbreakpointerrorstatementmarker" bg="#FF963A46" />
		<color name="selectedadvancedbreakpointerrorstatementmarker" bg="#FF963A46" fg="#FF0000FF" />
		<color name="tracepointstatement" fg="#FF800000" />
		<color name="tracepointstatementmarker" bg="#FFFFFFFF" />
		<color name="selectedtracepointstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="disabledtracepointstatement" fg="#FF800000" />
		<color name="disabledtracepointstatementmarker" bg="#FFFFFFFF" />
		<color name="selecteddisabledtracepointstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="advancedtracepointstatement" fg="#FF800000" />
		<color name="advancedtracepointstatementmarker" bg="#FFFFFFFF" />
		<color name="selectedadvancedtracepointstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="disabledadvancedtracepointstatement" fg="#FF800000" />
		<color name="disabledadvancedtracepointstatementmarker" bg="#FFFFFFFF" />
		<color name="selecteddisabledadvancedtracepointstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="tracepointwarningstatement" fg="#FF800000" />
		<color name="tracepointwarningstatementmarker" bg="#FFFFFFFF" />
		<color name="selectedtracepointwarningstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="tracepointerrorstatement" fg="#FF800000" />
		<color name="tracepointerrorstatementmarker" bg="#FFFFFFFF" />
		<color name="selectedtracepointerrorstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="advancedtracepointwarningstatement" fg="#FF800000" />
		<color name="advancedtracepointwarningstatementmarker" bg="#FFFFFFFF" />
		<color name="selectedadvancedtracepointwarningstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="advancedtracepointerrorstatement" fg="#FF800000" />
		<color name="advancedtracepointerrorstatementmarker" bg="#FFFFFFFF" />
		<color name="selectedadvancedtracepointerrorstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="bookmarkname" fg="#A31515" />
		<color name="activebookmarkname" fg="#A31515" bold="true" />
		<color name="currentline" fg="#EAEAF2" />
		<color name="currentlinenofocus" fg="#EAEAF2" />
		<color name="hextext" fg="Black" bg="White" />
		<color name="hexoffset" fg="#FF000000" />
		<color name="hexbyte0" fg="#FF3333CC" />
		<color name="hexbyte1" fg="#FF993366" />
		<color name="hexbyteerror" fg="#FFFF0000" />
		<color name="hexascii" fg="#FF000000" />
		<color name="hexcaret" bg="#66000000" />
		<color name="hexinactivecaret" bg="#FFFF0000" />
		<color name="hexselection" bg="#FF3399FF" />
		<color name="replprompt1" fg="Black" />
		<color name="replprompt2" fg="Black" />
		<color name="reploutputtext" fg="Black" />
		<color name="replscriptoutputtext" fg="Black" />
		<color name="black" fg="#FF000000" />
		<color name="blue" fg="#FF0000FF" />
		<color name="cyan" fg="#FF00C0C0" />
		<color name="darkblue" fg="#FF00007F" />
		<color name="darkcyan" fg="#FF007F7F" />
		<color name="darkgray" fg="#FF7F7F7F" />
		<color name="darkgreen" fg="#FF007F00" />
		<color name="darkmagenta" fg="#FF7F007F" />
		<color name="darkred" fg="#FF7F0000" />
		<color name="darkyellow" fg="#FF7F7F00" />
		<color name="gray" fg="#FFC0C0C0" />
		<color name="green" fg="#FF00C000" />
		<color name="magenta" fg="#FFFF00FF" />
		<color name="red" fg="#FFFF0000" />
		<color name="white" fg="#FF7F7F7F" />
		<color name="yellow" fg="#FFC0C020" />
		<color name="invblack" fg="White" bg="#FF000000" />
		<color name="invblue" fg="White" bg="#FF0000FF" />
		<color name="invcyan" fg="White" bg="#FF00C0C0" />
		<color name="invdarkblue" fg="White" bg="#FF00007F" />
		<color name="invdarkcyan" fg="White" bg="#FF007F7F" />
		<color name="invdarkgray" fg="White" bg="#FF7F7F7F" />
		<color name="invdarkgreen" fg="White" bg="#FF007F00" />
		<color name="invdarkmagenta" fg="White" bg="#FF7F007F" />
		<color name="invdarkred" fg="White" bg="#FF7F0000" />
		<color name="invdarkyellow" fg="White" bg="#FF7F7F00" />
		<color name="invgray" fg="White" bg="#FFC0C0C0" />
		<color name="invgreen" fg="White" bg="#FF00C000" />
		<color name="invmagenta" fg="White" bg="#FFFF00FF" />
		<color name="invred" fg="White" bg="#FFFF0000" />
		<color name="invwhite" fg="White" bg="#FF7F7F7F" />
		<color name="invyellow" fg="White" bg="#FFC0C020" />
		<color name="debuglogexceptionhandled" fg="#FFFF0000" />
		<color name="debuglogexceptionunhandled" fg="#FFFF0000" />
		<color name="debuglogstepfiltering" fg="#FFCC00CC" />
		<color name="debuglogloadmodule" fg="#FF996633" />
		<color name="debuglogunloadmodule" fg="#FF6600FF" />
		<color name="debuglogexitprocess" fg="#FF000000" />
		<color name="debuglogexitthread" fg="#FF006699" />
		<color name="debuglogprogramoutput" fg="#FF487C5F" />
		<color name="debuglogmda" fg="#FFFF0000" />
		<color name="debuglogtimestamp" fg="#FF6600CC" />
		<color name="glyphmargin" bg="#FFE6E7E8" />
		<color name="bracematching" bg="#FFDBE0CC" />
		<color name="lineseparator" fg="#FFA5A5A5" />
		<color name="findmatchhighlightmarker" bg="#FFF4A721" />
		<color name="blockstructurenamespace" fg="#FFB6B6B6" />
		<color name="blockstructuretype" fg="#FF7CCADD" />
		<color name="blockstructuremodule" fg="#FF7CCADD" />
		<color name="blockstructurevaluetype" fg="#FF7CCADD" />
		<color name="blockstructureinterface" fg="#FF7CCADD" />
		<color name="blockstructuremethod" fg="#FF879FFF" />
		<color name="blockstructureaccessor" fg="#FF879FFF" />
		<color name="blockstructureanonymousmethod" fg="#FF879FFF" />
		<color name="blockstructureconstructor" fg="#FF879FFF" />
		<color name="blockstructuredestructor" fg="#FF879FFF" />
		<color name="blockstructureoperator" fg="#FF879FFF" />
		<color name="blockstructureconditional" fg="#FF7CCC87" />
		<color name="blockstructureloop" fg="#FFFF00DC" />
		<color name="blockstructureproperty" fg="#FF879FFF" />
		<color name="blockstructureevent" fg="#FF879FFF" />
		<color name="blockstructuretry" fg="#FFFF776D" />
		<color name="blockstructurecatch" fg="#FFFF776D" />
		<color name="blockstructurefilter" fg="#FFFF776D" />
		<color name="blockstructurefinally" fg="#FFFF776D" />
		<color name="blockstructurefault" fg="#FFFF776D" />
		<color name="blockstructurelock" fg="#FFB6B6B6" />
		<color name="blockstructureusing" fg="#FFB6B6B6" />
		<color name="blockstructurefixed" fg="#FFB6B6B6" />
		<color name="blockstructureswitch" fg="#FF7CCC87" />
		<color name="blockstructurecase" fg="#FF7CCC87" />
		<color name="blockstructurelocalfunction" fg="#FF879FFF" />
		<color name="blockstructureother" fg="#FFB6B6B6" />
		<color name="blockstructurexml" fg="#FFB6B6B6" />
		<color name="blockstructurexaml" fg="#FFB6B6B6" />
		<color name="completionmatchhighlight" bg="#FFF4A721" />
		<color name="completionsuffix" fg="Black" />
		<color name="signaturehelpdocumentation" bold="true" />
		<color name="signaturehelpcurrentparameter" bold="true" bg="#FFF4A721" />
		<color name="signaturehelpparameter" bold="true" italic="true" />
		<color name="signaturehelpparameterdocumentation" italic="true" />
		<color name="url" fg="blue" />
		<color name="hexpedosheader" fg="#004A7F" />
		<color name="hexpefileheader" fg="#004A7F" />
		<color name="hexpeoptionalheader32" fg="#004A7F" />
		<color name="hexpeoptionalheader64" fg="#004A7F" />
		<color name="hexpesection" fg="#004A7F" />
		<color name="hexpesectionname" fg="#A31515" />
		<color name="hexcor20header" fg="#7F3300" />
		<color name="hexstoragesignature" fg="#7F3300" />
		<color name="hexstorageheader" fg="#7F3300" />
		<color name="hexstoragestream" fg="#7F3300" />
		<color name="hexstoragestreamname" fg="#A31515" />
		<color name="hexstoragestreamnameinvalid" fg="#FF0000" />
		<color name="hextablesstream" fg="#7F3300" />
		<color name="hextablename" fg="#A31515" />
		<color name="documentlistmatchhighlight" bg="#FFF4A721" />
		<color name="gacmatchhighlight" bg="#FFF4A721" />
		<color name="appsettingstreeviewnodematchhighlight" bg="#FFF4A721" />
		<color name="appsettingstextmatchhighlight" bg="#FFF4A721" />
		<color name="hexcurrentline" fg="#A0A0A0" />
		<color name="hexcurrentlinenofocus" fg="#A0A0A0" />
		<color name="hexinactiveselectedtext" bg="#FFBFCDDB" />
		<color name="hexcolumnline0" fg="#FF000000" />
		<color name="hexcolumnline1" fg="#FF000000" />
		<color name="hexcolumnlinegroup0" fg="#FF000000" />
		<color name="hexcolumnlinegroup1" fg="#FF000000" />
		<color name="hexhighlightedvaluescolumn" bg="#FFFCFCFC" />
		<color name="hexhighlightedasciicolumn" bg="#FFFCFCFC" />
		<color name="hexglyphmargin" bg="#FFE6E7E8" />
		<color name="hexcurrentvaluecell" bg="#FFDBE0CC" />
		<color name="hexcurrentasciicell" bg="#FFDBE0CC" />
		<color name="outputwindowtext" fg="Black" bg="White" />
		<color name="hexfindmatchhighlightmarker" bg="#FFF4A721" />
		<color name="hextooltipservicefield0" bg="#FFB3B6D3" fg="#FF888AA0" />
		<color name="hextooltipservicefield1" bg="#FFDDCCAF" fg="#FFA09480" />
		<color name="hextooltipservicecurrentfield" bg="#FFB8DB95" fg="#FF74895F" />
		<color name="listfindmatchhighlight" bg="#FFF4A721" />
		<color name="debuglogtrace" fg="#FF0000CC" />
		<color name="debuglogextensionmessage" fg="#FFFF3DE5" />
		<color name="debuggervaluechangedhighlight" bg="#FFF4A721" />
		<color name="debugexceptionname" fg="Black" />
		<color name="debugstowedexceptionname" fg="Black" />
		<color name="debugreturnvaluename" fg="Black" />
		<color name="debugvariablename" fg="Black" />
		<color name="debugobjectidname" fg="Black" />
		<color name="debuggerdisplayattributeeval" fg="#2E387C" />
		<color name="debuggernostringquoteseval" fg="#2E387C" />
		<color name="debugviewpropertyname" fg="#996633" />
		<color name="asmcomment" fg="#008000" />
		<color name="asmdirective" fg="#6F42C1" />
		<color name="asmprefix" fg="#D73A49" />
		<color name="asmmnemonic" fg="#D73A49" />
		<color name="asmkeyword" fg="#6F42C1" />
		<color name="asmoperator" fg="#000000" />
		<color name="asmpunctuation" fg="#000000" />
		<color name="asmnumber" fg="#5B2DA8" />
		<color name="asmregister" fg="#E36209" />
		<color name="asmselectorvalue" fg="#5B2DA8" />
		<color name="asmlabeladdress" fg="#5B2DA8" />
		<color name="asmfunctionaddress" fg="#5B2DA8" />
		<color name="asmlabel" fg="#663300" />
		<color name="asmfunction" fg="#339966" />
		<color name="asmdata" fg="#CC3399" />
		<color name="asmaddress" fg="#5B2DA8" />
		<color name="asmhexbytes" fg="#000000" />
	</colors>
</theme>
