﻿@model AppTech.MSMS.Domain.Reports.Models.VoucherModel
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Domain
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


<div class="form-horizontal">
    @{
        Html.RenderPartial("_DateControl");
    }

    <span class="lbl">نوع المستند</span>
    @Html.EnumDropDownListFor(model => model.Type)
    <div class="space-6"></div>

    <span class="lbl">الحساب </span>
    <select id="AccountID" name="AccountID" class="select2" placeholder="كافة الحسابات"></select>
    <div class="space-6"></div>

    <span class="lbl"> المستخدم</span>
    <select id="UserID" name="UserID" class="select2" placeholder="كافة المستخدمين"></select>
    <div class="space-6"></div>

    <span class="lbl">  العملة</span>
    @Html.DropDownListFor(model => model.CurrencyID, (SelectList)ViewBag.Currencies)
    <div class="space-6"></div>

    <div class="form-group">
        <div class="col-md-12">
            <span class="lbl">تجميع بالحساب </span>
            @Html.EditorFor(m => m.GroupByAccounts, (SelectList)ViewBag.Currencies)
        </div>
    </div>
    <div class="form-group">
        <div class="col-md-12">
            <span class="lbl">تجميع بالمستخدم </span>
            @Html.EditorFor(m => m.GroupByUsers, (SelectList)ViewBag.Currencies)
        </div>
    </div>
</div>


<script>
    $(function () {
        $("select#CurrencyID").prop('selectedIndex', 1);  
        $("select#Type").prop('selectedIndex', 1);  
        fillDataList('AccountID', '/Print/GetAccounts', false, 'كافة الحسابات');
        fillDataList('UserID', '/Print/GetUsers', false, 'كافة المستخدمين');


        $('.select2').css('width', '200px').select2({ allowClear: true });
        $('#select2-multiple-style .btn').on('click',
            function (e) {
                var target = $(this).find('input[type=radio]');
                var which = parseInt(target.val());
                if (which == 2) $('.select2').addClass('tag-input-style');
                else $('.select2').removeClass('tag-input-style');
            });
    });

</script>