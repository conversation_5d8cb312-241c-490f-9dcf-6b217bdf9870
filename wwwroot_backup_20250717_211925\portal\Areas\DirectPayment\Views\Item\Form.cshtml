﻿@model AppTech.MSMS.Domain.Models.Item
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    @Html.LabelFor(model => model.ServiceID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ServiceID, (SelectList)ViewBag.Services, new { })
        @Html.ValidationMessageFor(model => model.ServiceID, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.Label("نسبة التكلفة", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.CostPrice, new { htmlAttributes = new { } })
        @Html.ValidationMessageFor(model => model.CostPrice, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.Label("نسبة البيع", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.SalePrice, new { htmlAttributes = new { } })
        @Html.ValidationMessageFor(model => model.SalePrice, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.TextAreaFor(model => model.Note, new { htmlAttributes = new { @class = "form-control", rows = "3" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>