{"version": 3, "file": "push.js", "sources": ["../src/push/Messages.js", "../src/push/Permission.js", "../src/push/Util.js", "../src/agents/AbstractAgent.js", "../src/agents/DesktopAgent.js", "../src/agents/MobileChromeAgent.js", "../src/agents/MobileFirefoxAgent.js", "../src/agents/MSAgent.js", "../src/agents/WebKitAgent.js", "../src/push/Push.js", "../src/index.js"], "sourcesContent": ["// @flow\nconst errorPrefix = 'PushError:';\n\nexport default {\n    errors: {\n        incompatible: `${errorPrefix} Push.js is incompatible with browser.`,\n        invalid_plugin: `${errorPrefix} plugin class missing from plugin manifest (invalid plugin). Please check the documentation.`,\n        invalid_title: `${errorPrefix} title of notification must be a string`,\n        permission_denied: `${errorPrefix} permission request declined`,\n        sw_notification_error: `${errorPrefix} could not show a ServiceWorker notification due to the following reason: `,\n        sw_registration_error: `${errorPrefix} could not register the ServiceWorker due to the following reason: `,\n        unknown_interface: `${errorPrefix} unable to create notification: unknown interface`\n    }\n};\n", "// @flow\nimport type { Global } from 'types';\n\nexport default class Permission {\n    // Private members\n    _permissions: string[];\n    _win: Global;\n\n    // Public members\n    GRANTED: string;\n    DEFAULT: string;\n    DENIED: string;\n\n    constructor(win: Global) {\n        this._win = win;\n        this.GRANTED = 'granted';\n        this.DEFAULT = 'default';\n        this.DENIED = 'denied';\n        this._permissions = [this.GRANTED, this.DEFAULT, this.DENIED];\n    }\n\n    /**\n   * Requests permission for desktop notifications\n   * @param {Function} onGranted - Function to execute once permission is granted\n   * @param {Function} onDenied - Function to execute once permission is denied\n   * @return {void, Promise}\n   */\n    request(onGranted: () => void, onDenied: () => void) {\n        return arguments.length > 0\n            ? this._requestWithCallback(...arguments)\n            : this._requestAsPromise();\n    }\n\n    /**\n   * Old permissions implementation deprecated in favor of a promise based one\n   * @deprecated Since V1.0.4\n   * @param {Function} onGranted - Function to execute once permission is granted\n   * @param {Function} onDenied - Function to execute once permission is denied\n   * @return {void}\n   */\n    _requestWithCallback(onGranted: () => void, onDenied: () => void) {\n        const existing = this.get();\n\n        var resolved = false;\n        var resolve = (result = this._win.Notification.permission) => {\n            if (resolved) return;\n            resolved = true;\n            if (typeof result === 'undefined' && this._win.webkitNotifications)\n                result = this._win.webkitNotifications.checkPermission();\n            if (result === this.GRANTED || result === 0) {\n                if (onGranted) onGranted();\n            } else if (onDenied) onDenied();\n        };\n        var request;\n\n        /* Permissions already set */\n        if (existing !== this.DEFAULT) {\n            resolve(existing);\n        } else if (\n            this._win.webkitNotifications &&\n            this._win.webkitNotifications.checkPermission\n        ) {\n            /* Safari 6+, Legacy webkit browsers */\n            this._win.webkitNotifications.requestPermission(resolve);\n        } else if (\n            this._win.Notification &&\n            this._win.Notification.requestPermission\n        ) {\n            /* Safari 12+ */\n            /* This resolve argument will only be used in Safari */\n            /* CHrome, instead, returns a Promise */\n            request = this._win.Notification.requestPermission(resolve);\n            if (request && request.then) {\n                /* Chrome 23+ */\n                request.then(resolve).catch(function() {\n                    if (onDenied) onDenied();\n                });\n            }\n        } else if (onGranted) {\n            /* Let the user continue by default */\n            onGranted();\n        }\n    }\n\n    /**\n   * Requests permission for desktop notifications in a promise based way\n   * @return {Promise}\n   */\n    _requestAsPromise(): Promise<void> {\n        const existing = this.get();\n\n        let isGranted = result => result === this.GRANTED || result === 0;\n\n        /* Permissions already set */\n        var hasPermissions = existing !== this.DEFAULT;\n\n        /* Safari 6+, Chrome 23+ */\n        var isModernAPI =\n            this._win.Notification && this._win.Notification.requestPermission;\n\n        /* Legacy webkit browsers */\n        var isWebkitAPI =\n            this._win.webkitNotifications &&\n            this._win.webkitNotifications.checkPermission;\n\n        return new Promise((resolvePromise, rejectPromise) => {\n            var resolved = false;\n            var resolver = result => {\n                if (resolved) return;\n                resolved = true;\n                isGranted(result) ? resolvePromise() : rejectPromise();\n            };\n            var request;\n\n            if (hasPermissions) {\n                resolver(existing);\n            } else if (isWebkitAPI) {\n                this._win.webkitNotifications.requestPermission(result => {\n                    resolver(result);\n                });\n            } else if (isModernAPI) {\n                /* Safari 12+ */\n                /* This resolver argument will only be used in Safari */\n                /* CHrome, instead, returns a Promise */\n                request = this._win.Notification.requestPermission(resolver);\n                if (request && request.then) {\n                    /* Chrome 23+ */\n                    request.then(resolver).catch(rejectPromise);\n                }\n            } else resolvePromise();\n        });\n    }\n\n    /**\n   * Returns whether Push has been granted permission to run\n   * @return {Boolean}\n   */\n    has() {\n        return this.get() === this.GRANTED;\n    }\n\n    /**\n   * Gets the permission level\n   * @return {Permission} The permission level\n   */\n    get() {\n        let permission;\n\n        /* Safari 6+, Chrome 23+ */\n        if (this._win.Notification && this._win.Notification.permission)\n            permission = this._win.Notification.permission;\n        else if (\n            this._win.webkitNotifications &&\n            this._win.webkitNotifications.checkPermission\n        )\n            /* Legacy webkit browsers */\n            permission = this._permissions[\n                this._win.webkitNotifications.checkPermission()\n            ];\n        else if (navigator.mozNotification)\n            /* Firefox Mobile */\n            permission = this.GRANTED;\n        else if (this._win.external && this._win.external.msIsSiteMode)\n            /* IE9+ */\n            permission = this._win.external.msIsSiteMode()\n                ? this.GRANTED\n                : this.DEFAULT;\n        else permission = this.GRANTED;\n\n        return permission;\n    }\n}\n", "// @flow\nexport default class Util {\n    static isUndefined(obj) {\n        return obj === undefined;\n    }\n\n    static isNull(obs) {\n        return obj === null;\n    }\n\n    static isString(obj) {\n        return typeof obj === 'string';\n    }\n\n    static isFunction(obj) {\n        return obj && {}.toString.call(obj) === '[object Function]';\n    }\n\n    static isObject(obj) {\n        return typeof obj === 'object';\n    }\n\n    static objectMerge(target, source) {\n        for (var key in source) {\n            if (\n                target.hasOwnProperty(key) &&\n                this.isObject(target[key]) &&\n                this.isObject(source[key])\n            ) {\n                this.objectMerge(target[key], source[key]);\n            } else {\n                target[key] = source[key];\n            }\n        }\n    }\n}\n", "// @flow\nimport type { Global } from 'types';\n\nexport default class AbstractAgent {\n    _win: Global;\n\n    constructor(win: Global) {\n        this._win = win;\n    }\n}\n", "// @flow\nimport { AbstractAgent } from 'agents';\nimport { Util } from 'push';\nimport type { PushOptions, GenericNotification, Global } from 'types';\n\n/**\n * Notification agent for modern desktop browsers:\n * Safari 6+, Firefox 22+, Chrome 22+, Opera 25+\n */\nexport default class DesktopAgent extends AbstractAgent {\n    _win: Global;\n\n    /**\n     * Returns a boolean denoting support\n     * @returns {Boolean} boolean denoting whether webkit notifications are supported\n     */\n    isSupported() {\n        return this._win.Notification !== undefined;\n    }\n\n    /**\n     * Creates a new notification\n     * @param title - notification title\n     * @param options - notification options array\n     * @returns {Notification}\n     */\n    create(title: string, options: PushOptions) {\n        return new this._win.Notification(title, {\n            icon:\n                Util.isString(options.icon) ||\n                Util.isUndefined(options.icon) ||\n                Util.isNull(options.icon)\n                    ? options.icon\n                    : options.icon.x32,\n            body: options.body,\n            tag: options.tag,\n            requireInteraction: options.requireInteraction\n        });\n    }\n\n    /**\n     * Close a given notification\n     * @param notification - notification to close\n     */\n    close(notification: GenericNotification) {\n        notification.close();\n    }\n}\n", "// @flow\nimport { Util, Messages } from 'push';\nimport { AbstractAgent } from 'agents';\nimport type { Global, GenericNotification, PushOptions } from 'types';\n\n/**\n * Notification agent for modern desktop browsers:\n * Safari 6+, Firefox 22+, Chrome 22+, Opera 25+\n */\nexport default class MobileChromeAgent extends AbstractAgent {\n    _win: Global;\n\n    /**\n     * Returns a boolean denoting support\n     * @returns {Boolean} boolean denoting whether webkit notifications are supported\n     */\n    isSupported() {\n        return (\n            this._win.navigator !== undefined &&\n            this._win.navigator.serviceWorker !== undefined\n        );\n    }\n\n    /**\n     * Returns the function body as a string\n     * @param func\n     */\n    getFunctionBody(func: () => void) {\n        const str = func.toString().match(/function[^{]+{([\\s\\S]*)}$/);\n        return typeof str !== 'undefined' && str !== null && str.length > 1\n            ? str[1]\n            : null;\n    }\n\n    /**\n     * Creates a new notification\n     * @param id                ID of notification\n     * @param title             Title of notification\n     * @param options           Options object\n     * @param serviceWorker     ServiceWorker path\n     * @param callback          Callback function\n     */\n    create(\n        id: number,\n        title: string,\n        options: PushOptions,\n        serviceWorker: string,\n        callback: (GenericNotification[]) => void\n    ) {\n        /* Register ServiceWorker */\n        this._win.navigator.serviceWorker.register(serviceWorker);\n\n        this._win.navigator.serviceWorker.ready\n            .then(registration => {\n                /* Local data the service worker will use */\n                let localData = {\n                    id: id,\n                    link: options.link,\n                    origin: document.location.href,\n                    onClick: Util.isFunction(options.onClick)\n                        ? this.getFunctionBody(options.onClick)\n                        : '',\n                    onClose: Util.isFunction(options.onClose)\n                        ? this.getFunctionBody(options.onClose)\n                        : ''\n                };\n\n                /* Merge the local data with user-provided data */\n                if (options.data !== undefined && options.data !== null)\n                    localData = Object.assign(localData, options.data);\n\n                /* Show the notification */\n                registration\n                    .showNotification(title, {\n                        icon: options.icon,\n                        body: options.body,\n                        vibrate: options.vibrate,\n                        tag: options.tag,\n                        data: localData,\n                        requireInteraction: options.requireInteraction,\n                        silent: options.silent\n                    })\n                    .then(() => {\n                        registration.getNotifications().then(notifications => {\n                            /* Send an empty message so the ServiceWorker knows who the client is */\n                            registration.active.postMessage('');\n\n                            /* Trigger callback */\n                            callback(notifications);\n                        });\n                    })\n                    .catch(function(error) {\n                        throw new Error(\n                            Messages.errors.sw_notification_error +\n                                error.message\n                        );\n                    });\n            })\n            .catch(function(error) {\n                throw new Error(\n                    Messages.errors.sw_registration_error + error.message\n                );\n            });\n    }\n\n    /**\n     * Close all notification\n     */\n    close() {\n        // Can't do this with service workers\n    }\n}\n", "// @flow\nimport { AbstractAgent } from 'agents';\nimport type { Global, PushOptions } from 'types';\n\n/**\n * Notification agent for modern desktop browsers:\n * Safari 6+, Firefox 22+, Chrome 22+, Opera 25+\n */\nexport default class MobileFirefoxAgent extends AbstractAgent {\n    _win: Global;\n\n    /**\n     * Returns a boolean denoting support\n     * @returns {Boolean} boolean denoting whether webkit notifications are supported\n     */\n    isSupported() {\n        return this._win.navigator.mozNotification !== undefined;\n    }\n\n    /**\n     * Creates a new notification\n     * @param title - notification title\n     * @param options - notification options array\n     * @returns {Notification}\n     */\n    create(title: string, options: PushOptions) {\n        let notification = this._win.navigator.mozNotification.createNotification(\n            title,\n            options.body,\n            options.icon\n        );\n\n        notification.show();\n\n        return notification;\n    }\n}\n", "// @flow\nimport { AbstractAgent } from 'agents';\nimport { Util } from 'push';\nimport type { PushOptions, Global } from 'types';\n\n/**\n * Notification agent for IE9\n */\nexport default class MSAgent extends AbstractAgent {\n    _win: Global;\n\n    /**\n     * Returns a boolean denoting support\n     * @returns {Boolean} boolean denoting whether webkit notifications are supported\n     */\n    isSupported() {\n        return (\n            this._win.external !== undefined &&\n            this._win.external.msIsSiteMode !== undefined\n        );\n    }\n\n    /**\n     * Creates a new notification\n     * @param title - notification title\n     * @param options - notification options array\n     * @returns {Notification}\n     */\n    create(title: string, options: PushOptions) {\n        /* Clear any previous notifications */\n        this._win.external.msSiteModeClearIconOverlay();\n\n        this._win.external.msSiteModeSetIconOverlay(\n            Util.isString(options.icon) || Util.isUndefined(options.icon)\n                ? options.icon\n                : options.icon.x16,\n            title\n        );\n\n        this._win.external.msSiteModeActivate();\n\n        return null;\n    }\n\n    /**\n     * Close a given notification\n     * @param notification - notification to close\n     */\n    close() {\n        this._win.external.msSiteModeClearIconOverlay();\n    }\n}\n", "// @flow\nimport { AbstractAgent } from 'agents';\nimport type { Global, GenericNotification, PushOptions } from 'types';\n\n/**\n * Notification agent for old Chrome versions (and some) Firefox\n */\nexport default class WebKitAgent extends AbstractAgent {\n    _win: Global;\n\n    /**\n     * Returns a boolean denoting support\n     * @returns {Boolean} boolean denoting whether webkit notifications are supported\n     */\n    isSupported() {\n        return this._win.webkitNotifications !== undefined;\n    }\n\n    /**\n     * Creates a new notification\n     * @param title - notification title\n     * @param options - notification options array\n     * @returns {Notification}\n     */\n    create(title: string, options: PushOptions) {\n        let notification = this._win.webkitNotifications.createNotification(\n            options.icon,\n            title,\n            options.body\n        );\n\n        notification.show();\n\n        return notification;\n    }\n\n    /**\n     * Close a given notification\n     * @param notification - notification to close\n     */\n    close(notification: GenericNotification) {\n        notification.cancel();\n    }\n}\n", "// @flow\nimport { Messages, Permission, Util } from 'push';\nimport type { PluginManifest, GenericNotification, PushOptions } from 'types';\n\n/* Import notification agents */\nimport {\n    DesktopAgent,\n    MobileChromeAgent,\n    MobileFirefoxAgent,\n    MSAgent,\n    WebKitAgent\n} from 'agents';\n\nexport default class Push {\n    // Private members\n    _agents: {\n        desktop: DesktopAgent,\n        chrome: MobileChromeAgent,\n        firefox: MobileFirefoxAgent,\n        ms: MSAgent,\n        webkit: WebKitAgent\n    };\n    _configuration: {\n        serviceWorker: string,\n        fallback: ({}) => void\n    };\n    _currentId: number;\n    _notifications: {};\n    _win: {};\n\n    // Public members\n    Permission: Permission;\n\n    constructor(win: {}) {\n        /* Private variables */\n\n        /* ID to use for new notifications */\n        this._currentId = 0;\n\n        /* Map of open notifications */\n        this._notifications = {};\n\n        /* Window object */\n        this._win = win;\n\n        /* Public variables */\n        this.Permission = new Permission(win);\n\n        /* Agents */\n        this._agents = {\n            desktop: new DesktopAgent(win),\n            chrome: new MobileChromeAgent(win),\n            firefox: new MobileFirefoxAgent(win),\n            ms: new MSAgent(win),\n            webkit: new WebKitAgent(win)\n        };\n\n        this._configuration = {\n            serviceWorker: '/serviceWorker.min.js',\n            fallback: function(payload) {}\n        };\n    }\n\n    /**\n     * Closes a notification\n     * @param id            ID of notification\n     * @returns {boolean}   denotes whether the operation was successful\n     * @private\n     */\n    _closeNotification(id: number | string) {\n        let success = true;\n        const notification = this._notifications[id];\n\n        if (notification !== undefined) {\n            success = this._removeNotification(id);\n\n            /* Safari 6+, Firefox 22+, Chrome 22+, Opera 25+ */\n            if (this._agents.desktop.isSupported())\n                this._agents.desktop.close(notification);\n            else if (this._agents.webkit.isSupported())\n                /* Legacy WebKit browsers */\n                this._agents.webkit.close(notification);\n            else if (this._agents.ms.isSupported())\n                /* IE9 */\n                this._agents.ms.close();\n            else {\n                success = false;\n                throw new Error(Messages.errors.unknown_interface);\n            }\n\n            return success;\n        }\n\n        return false;\n    }\n\n    /**\n   * Adds a notification to the global dictionary of notifications\n   * @param {Notification} notification\n   * @return {Integer} Dictionary key of the notification\n   * @private\n   */\n    _addNotification(notification: GenericNotification) {\n        const id = this._currentId;\n        this._notifications[id] = notification;\n        this._currentId++;\n        return id;\n    }\n\n    /**\n   * Removes a notification with the given ID\n   * @param  {Integer} id - Dictionary key/ID of the notification to remove\n   * @return {Boolean} boolean denoting success\n   * @private\n   */\n    _removeNotification(id: number | string) {\n        let success = false;\n\n        if (this._notifications.hasOwnProperty(id)) {\n            /* We're successful if we omit the given ID from the new array */\n            delete this._notifications[id];\n            success = true;\n        }\n\n        return success;\n    }\n\n    /**\n   * Creates the wrapper for a given notification\n   *\n   * @param {Integer} id - Dictionary key/ID of the notification\n   * @param {Map} options - Options used to create the notification\n   * @returns {Map} wrapper hashmap object\n   * @private\n   */\n    _prepareNotification(id: number, options: PushOptions) {\n        let wrapper;\n\n        /* Wrapper used to get/close notification later on */\n        wrapper = {\n            get: () => {\n                return this._notifications[id];\n            },\n\n            close: () => {\n                this._closeNotification(id);\n            }\n        };\n\n        /* Autoclose timeout */\n        if (options.timeout) {\n            setTimeout(() => {\n                wrapper.close();\n            }, options.timeout);\n        }\n\n        return wrapper;\n    }\n\n    /**\n   * Find the most recent notification from a ServiceWorker and add it to the global array\n   * @param notifications\n   * @private\n   */\n    _serviceWorkerCallback(\n        notifications: GenericNotification[],\n        options: PushOptions,\n        resolve: ({} | null) => void\n    ) {\n        let id = this._addNotification(notifications[notifications.length - 1]);\n\n        /* Listen for close requests from the ServiceWorker */\n        if (navigator && navigator.serviceWorker) {\n            navigator.serviceWorker.addEventListener('message', event => {\n                const data = JSON.parse(event.data);\n\n                if (data.action === 'close' && Number.isInteger(data.id))\n                    this._removeNotification(data.id);\n            });\n\n            resolve(this._prepareNotification(id, options));\n        }\n\n        resolve(null);\n    }\n\n    /**\n   * Callback function for the 'create' method\n   * @return {void}\n   * @private\n   */\n    _createCallback(\n        title: string,\n        options: PushOptions,\n        resolve: ({} | null) => void\n    ) {\n        let notification = null;\n        let onClose;\n\n        /* Set empty settings if none are specified */\n        options = options || {};\n\n        /* onClose event handler */\n        onClose = id => {\n            /* A bit redundant, but covers the cases when close() isn't explicitly called */\n            this._removeNotification(id);\n            if (Util.isFunction(options.onClose)) {\n                options.onClose.call(this, notification);\n            }\n        };\n\n        /* Safari 6+, Firefox 22+, Chrome 22+, Opera 25+ */\n        if (this._agents.desktop.isSupported()) {\n            try {\n                /* Create a notification using the API if possible */\n                notification = this._agents.desktop.create(title, options);\n            } catch (e) {\n                const id = this._currentId;\n                const sw = this.config().serviceWorker;\n                const cb = notifications =>\n                    this._serviceWorkerCallback(\n                        notifications,\n                        options,\n                        resolve\n                    );\n                /* Create a Chrome ServiceWorker notification if it isn't supported */\n                if (this._agents.chrome.isSupported()) {\n                    this._agents.chrome.create(id, title, options, sw, cb);\n                }\n            }\n            /* Legacy WebKit browsers */\n        } else if (this._agents.webkit.isSupported())\n            notification = this._agents.webkit.create(title, options);\n        else if (this._agents.firefox.isSupported())\n            /* Firefox Mobile */\n            this._agents.firefox.create(title, options);\n        else if (this._agents.ms.isSupported())\n            /* IE9 */\n            notification = this._agents.ms.create(title, options);\n        else {\n            /* Default fallback */\n            options.title = title;\n            this.config().fallback(options);\n        }\n\n        if (notification !== null) {\n            const id = this._addNotification(notification);\n            const wrapper = this._prepareNotification(id, options);\n\n            /* Notification callbacks */\n            if (Util.isFunction(options.onShow))\n                notification.addEventListener('show', options.onShow);\n\n            if (Util.isFunction(options.onError))\n                notification.addEventListener('error', options.onError);\n\n            if (Util.isFunction(options.onClick))\n                notification.addEventListener('click', options.onClick);\n\n            notification.addEventListener('close', () => {\n                onClose(id);\n            });\n\n            notification.addEventListener('cancel', () => {\n                onClose(id);\n            });\n\n            /* Return the wrapper so the user can call close() */\n            resolve(wrapper);\n        }\n\n        /* By default, pass an empty wrapper */\n        resolve(null);\n    }\n\n    /**\n   * Creates and displays a new notification\n   * @param {Array} options\n   * @return {Promise}\n   */\n    create(title: string, options: {}): Promise<void> {\n        let promiseCallback;\n\n        /* Fail if no or an invalid title is provided */\n        if (!Util.isString(title)) {\n            throw new Error(Messages.errors.invalid_title);\n        }\n\n        /* Request permission if it isn't granted */\n        if (!this.Permission.has()) {\n            promiseCallback = (resolve: () => void, reject: string => void) => {\n                this.Permission\n                    .request()\n                    .then(() => {\n                        this._createCallback(title, options, resolve);\n                    })\n                    .catch(() => {\n                        reject(Messages.errors.permission_denied);\n                    });\n            };\n        } else {\n            promiseCallback = (resolve: () => void, reject: string => void) => {\n                try {\n                    this._createCallback(title, options, resolve);\n                } catch (e) {\n                    reject(e);\n                }\n            };\n        }\n\n        return new Promise(promiseCallback);\n    }\n\n    /**\n   * Returns the notification count\n   * @return {Integer} The notification count\n   */\n    count() {\n        let count = 0;\n        let key;\n\n        for (key in this._notifications)\n            if (this._notifications.hasOwnProperty(key)) count++;\n\n        return count;\n    }\n\n    /**\n   * Closes a notification with the given tag\n   * @param {String} tag - Tag of the notification to close\n   * @return {Boolean} boolean denoting success\n   */\n    close(tag: string) {\n        let key, notification;\n\n        for (key in this._notifications) {\n            if (this._notifications.hasOwnProperty(key)) {\n                notification = this._notifications[key];\n\n                /* Run only if the tags match */\n                if (notification.tag === tag) {\n                    /* Call the notification's close() method */\n                    return this._closeNotification(key);\n                }\n            }\n        }\n    }\n\n    /**\n   * Clears all notifications\n   * @return {Boolean} boolean denoting whether the clear was successful in closing all notifications\n   */\n    clear() {\n        let key,\n            success = true;\n\n        for (key in this._notifications)\n            if (this._notifications.hasOwnProperty(key))\n                success = success && this._closeNotification(key);\n\n        return success;\n    }\n\n    /**\n   * Denotes whether Push is supported in the current browser\n   * @returns {boolean}\n   */\n    supported() {\n        let supported = false;\n\n        for (var agent in this._agents)\n            if (this._agents.hasOwnProperty(agent))\n                supported = supported || this._agents[agent].isSupported();\n\n        return supported;\n    }\n\n    /**\n   * Modifies settings or returns all settings if no parameter passed\n   * @param settings\n   */\n    config(settings?: {}) {\n        if (\n            typeof settings !== 'undefined' ||\n            (settings !== null && Util.isObject(settings))\n        )\n            Util.objectMerge(this._configuration, settings);\n\n        return this._configuration;\n    }\n\n    /**\n   * Copies the functions from a plugin to the main library\n   * @param plugin\n   */\n    extend(manifest: PluginManifest) {\n        var plugin,\n            Plugin,\n            hasProp = {}.hasOwnProperty;\n\n        if (!hasProp.call(manifest, 'plugin')) {\n            throw new Error(Messages.errors.invalid_plugin);\n        } else {\n            if (\n                hasProp.call(manifest, 'config') &&\n                Util.isObject(manifest.config) &&\n                manifest.config !== null\n            ) {\n                this.config(manifest.config);\n            }\n\n            Plugin = manifest.plugin;\n            plugin = new Plugin(this.config());\n\n            for (var member in plugin) {\n                if (\n                    hasProp.call(plugin, member) &&\n                    Util.isFunction(plugin[member])\n                )\n                    // $FlowFixMe\n                    this[member] = plugin[member];\n            }\n        }\n    }\n}\n", "// @flow\nimport { Push } from 'push';\n\nexport default new Push(typeof window !== 'undefined' ? window : global);\n"], "names": ["errorPrefix", "errors", "incompatible", "invalid_plugin", "invalid_title", "permission_denied", "sw_notification_error", "sw_registration_error", "unknown_interface", "Permission", "win", "_win", "GRANTED", "DEFAULT", "DENIED", "_permissions", "onGranted", "onDenied", "arguments", "length", "_requestWithCallback", "_requestAsPromise", "existing", "get", "resolved", "resolve", "result", "Notification", "permission", "webkitNotifications", "checkPermission", "request", "requestPermission", "then", "catch", "isGranted", "hasPermissions", "isModernAPI", "isWebkitAPI", "Promise", "resolvePromise", "rejectPromise", "resolver", "navigator", "mozNotification", "external", "msIsSiteMode", "<PERSON><PERSON>", "obj", "undefined", "obs", "toString", "call", "target", "source", "key", "hasOwnProperty", "isObject", "objectMerge", "AbstractAgent", "DesktopAgent", "title", "options", "icon", "isString", "isUndefined", "isNull", "x32", "body", "tag", "requireInteraction", "notification", "close", "MobileChromeAgent", "serviceWorker", "func", "str", "match", "id", "callback", "register", "ready", "registration", "localData", "link", "origin", "document", "location", "href", "onClick", "isFunction", "getFunctionBody", "onClose", "data", "Object", "assign", "showNotification", "vibrate", "silent", "getNotifications", "notifications", "active", "postMessage", "error", "Error", "Messages", "message", "MobileFirefoxAgent", "createNotification", "show", "MSAgent", "msSiteModeClearIconOverlay", "msSiteModeSetIconOverlay", "x16", "msSiteModeActivate", "WebKitAgent", "cancel", "<PERSON><PERSON>", "_currentId", "_notifications", "_agents", "desktop", "chrome", "firefox", "ms", "webkit", "_configuration", "fallback", "payload", "success", "_removeNotification", "isSupported", "wrapper", "_closeNotification", "timeout", "setTimeout", "_addNotification", "addEventListener", "event", "JSON", "parse", "action", "Number", "isInteger", "_prepareNotification", "create", "e", "sw", "config", "cb", "_serviceWorkerCallback", "onShow", "onError", "promise<PERSON>allback", "has", "reject", "_createCallback", "count", "supported", "agent", "settings", "manifest", "plugin", "Plugin", "hasProp", "member", "window", "global"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IACA,IAAMA,WAAW,GAAG,YAApB;AAEA,mBAAe;IACXC,EAAAA,MAAM,EAAE;IACJC,IAAAA,YAAY,YAAKF,WAAL,2CADR;IAEJG,IAAAA,cAAc,YAAKH,WAAL,iGAFV;IAGJI,IAAAA,aAAa,YAAKJ,WAAL,4CAHT;IAIJK,IAAAA,iBAAiB,YAAKL,WAAL,iCAJb;IAKJM,IAAAA,qBAAqB,YAAKN,WAAL,+EALjB;IAMJO,IAAAA,qBAAqB,YAAKP,WAAL,wEANjB;IAOJQ,IAAAA,iBAAiB,YAAKR,WAAL;IAPb;IADG,CAAf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QCAqBS;;;IACjB;IAIA;IAKA,sBAAYC,GAAZ,EAAyB;IAAA;;IACrB,SAAKC,IAAL,GAAYD,GAAZ;IACA,SAAKE,OAAL,GAAe,SAAf;IACA,SAAKC,OAAL,GAAe,SAAf;IACA,SAAKC,MAAL,GAAc,QAAd;IACA,SAAKC,YAAL,GAAoB,CAAC,KAAKH,OAAN,EAAe,KAAKC,OAApB,EAA6B,KAAKC,MAAlC,CAApB;IACH;IAED;;;;;;;;;;gCAMQE,WAAuBC,UAAsB;IACjD,aAAOC,SAAS,CAACC,MAAV,GAAmB,CAAnB,GACD,KAAKC,oBAAL,aAA6BF,SAA7B,CADC,GAED,KAAKG,iBAAL,EAFN;IAGH;IAED;;;;;;;;;;6CAOqBL,WAAuBC,UAAsB;IAAA;;IAC9D,UAAMK,QAAQ,GAAG,KAAKC,GAAL,EAAjB;IAEA,UAAIC,QAAQ,GAAG,KAAf;;IACA,UAAIC,OAAO,GAAG,SAAVA,OAAU,GAAgD;IAAA,YAA/CC,MAA+C,uEAAtC,KAAI,CAACf,IAAL,CAAUgB,YAAV,CAAuBC,UAAe;IAC1D,YAAIJ,QAAJ,EAAc;IACdA,QAAAA,QAAQ,GAAG,IAAX;IACA,YAAI,OAAOE,MAAP,KAAkB,WAAlB,IAAiC,KAAI,CAACf,IAAL,CAAUkB,mBAA/C,EACIH,MAAM,GAAG,KAAI,CAACf,IAAL,CAAUkB,mBAAV,CAA8BC,eAA9B,EAAT;;IACJ,YAAIJ,MAAM,KAAK,KAAI,CAACd,OAAhB,IAA2Bc,MAAM,KAAK,CAA1C,EAA6C;IACzC,cAAIV,SAAJ,EAAeA,SAAS;IAC3B,SAFD,MAEO,IAAIC,QAAJ,EAAcA,QAAQ;IAChC,OARD;;IASA,UAAIc,OAAJ;IAEA;;IACA,UAAIT,QAAQ,KAAK,KAAKT,OAAtB,EAA+B;IAC3BY,QAAAA,OAAO,CAACH,QAAD,CAAP;IACH,OAFD,MAEO,IACH,KAAKX,IAAL,CAAUkB,mBAAV,IACA,KAAKlB,IAAL,CAAUkB,mBAAV,CAA8BC,eAF3B,EAGL;IACE;IACA,aAAKnB,IAAL,CAAUkB,mBAAV,CAA8BG,iBAA9B,CAAgDP,OAAhD;IACH,OANM,MAMA,IACH,KAAKd,IAAL,CAAUgB,YAAV,IACA,KAAKhB,IAAL,CAAUgB,YAAV,CAAuBK,iBAFpB,EAGL;IACE;;IACA;;IACA;IACAD,QAAAA,OAAO,GAAG,KAAKpB,IAAL,CAAUgB,YAAV,CAAuBK,iBAAvB,CAAyCP,OAAzC,CAAV;;IACA,YAAIM,OAAO,IAAIA,OAAO,CAACE,IAAvB,EAA6B;IACzB;IACAF,UAAAA,OAAO,CAACE,IAAR,CAAaR,OAAb,EAAsBS,KAAtB,CAA4B,YAAW;IACnC,gBAAIjB,QAAJ,EAAcA,QAAQ;IACzB,WAFD;IAGH;IACJ,OAdM,MAcA,IAAID,SAAJ,EAAe;IAClB;IACAA,QAAAA,SAAS;IACZ;IACJ;IAED;;;;;;;4CAImC;IAAA;;IAC/B,UAAMM,QAAQ,GAAG,KAAKC,GAAL,EAAjB;;IAEA,UAAIY,SAAS,GAAG,SAAZA,SAAY,CAAAT,MAAM;IAAA,eAAIA,MAAM,KAAK,MAAI,CAACd,OAAhB,IAA2Bc,MAAM,KAAK,CAA1C;IAAA,OAAtB;IAEA;;;IACA,UAAIU,cAAc,GAAGd,QAAQ,KAAK,KAAKT,OAAvC;IAEA;;IACA,UAAIwB,WAAW,GACX,KAAK1B,IAAL,CAAUgB,YAAV,IAA0B,KAAKhB,IAAL,CAAUgB,YAAV,CAAuBK,iBADrD;IAGA;;IACA,UAAIM,WAAW,GACX,KAAK3B,IAAL,CAAUkB,mBAAV,IACA,KAAKlB,IAAL,CAAUkB,mBAAV,CAA8BC,eAFlC;IAIA,aAAO,IAAIS,OAAJ,CAAY,UAACC,cAAD,EAAiBC,aAAjB,EAAmC;IAClD,YAAIjB,QAAQ,GAAG,KAAf;;IACA,YAAIkB,QAAQ,GAAG,SAAXA,QAAW,CAAAhB,MAAM,EAAI;IACrB,cAAIF,QAAJ,EAAc;IACdA,UAAAA,QAAQ,GAAG,IAAX;IACAW,UAAAA,SAAS,CAACT,MAAD,CAAT,GAAoBc,cAAc,EAAlC,GAAuCC,aAAa,EAApD;IACH,SAJD;;IAKA,YAAIV,OAAJ;;IAEA,YAAIK,cAAJ,EAAoB;IAChBM,UAAAA,QAAQ,CAACpB,QAAD,CAAR;IACH,SAFD,MAEO,IAAIgB,WAAJ,EAAiB;IACpB,UAAA,MAAI,CAAC3B,IAAL,CAAUkB,mBAAV,CAA8BG,iBAA9B,CAAgD,UAAAN,MAAM,EAAI;IACtDgB,YAAAA,QAAQ,CAAChB,MAAD,CAAR;IACH,WAFD;IAGH,SAJM,MAIA,IAAIW,WAAJ,EAAiB;IACpB;;IACA;;IACA;IACAN,UAAAA,OAAO,GAAG,MAAI,CAACpB,IAAL,CAAUgB,YAAV,CAAuBK,iBAAvB,CAAyCU,QAAzC,CAAV;;IACA,cAAIX,OAAO,IAAIA,OAAO,CAACE,IAAvB,EAA6B;IACzB;IACAF,YAAAA,OAAO,CAACE,IAAR,CAAaS,QAAb,EAAuBR,KAAvB,CAA6BO,aAA7B;IACH;IACJ,SATM,MASAD,cAAc;IACxB,OAzBM,CAAP;IA0BH;IAED;;;;;;;8BAIM;IACF,aAAO,KAAKjB,GAAL,OAAe,KAAKX,OAA3B;IACH;IAED;;;;;;;8BAIM;IACF,UAAIgB,UAAJ;IAEA;;IACA,UAAI,KAAKjB,IAAL,CAAUgB,YAAV,IAA0B,KAAKhB,IAAL,CAAUgB,YAAV,CAAuBC,UAArD,EACIA,UAAU,GAAG,KAAKjB,IAAL,CAAUgB,YAAV,CAAuBC,UAApC,CADJ,KAEK,IACD,KAAKjB,IAAL,CAAUkB,mBAAV,IACA,KAAKlB,IAAL,CAAUkB,mBAAV,CAA8BC,eAF7B;IAID;IACAF,QAAAA,UAAU,GAAG,KAAKb,YAAL,CACT,KAAKJ,IAAL,CAAUkB,mBAAV,CAA8BC,eAA9B,EADS,CAAb,CALC,KAQA,IAAIa,SAAS,CAACC,eAAd;IACD;IACAhB,QAAAA,UAAU,GAAG,KAAKhB,OAAlB,CAFC,KAGA,IAAI,KAAKD,IAAL,CAAUkC,QAAV,IAAsB,KAAKlC,IAAL,CAAUkC,QAAV,CAAmBC,YAA7C;IACD;IACAlB,QAAAA,UAAU,GAAG,KAAKjB,IAAL,CAAUkC,QAAV,CAAmBC,YAAnB,KACP,KAAKlC,OADE,GAEP,KAAKC,OAFX,CAFC,KAKAe,UAAU,GAAG,KAAKhB,OAAlB;IAEL,aAAOgB,UAAP;IACH;;;;;;QCzKgBmB;;;;;;;;;oCACEC,KAAK;IACpB,aAAOA,GAAG,KAAKC,SAAf;IACH;;;+BAEaC,KAAK;IACf,aAAOF,GAAG,KAAK,IAAf;IACH;;;iCAEeA,KAAK;IACjB,aAAO,OAAOA,GAAP,KAAe,QAAtB;IACH;;;mCAEiBA,KAAK;IACnB,aAAOA,GAAG,IAAI,GAAGG,QAAH,CAAYC,IAAZ,CAAiBJ,GAAjB,MAA0B,mBAAxC;IACH;;;iCAEeA,KAAK;IACjB,aAAO,QAAOA,GAAP,MAAe,QAAtB;IACH;;;oCAEkBK,QAAQC,QAAQ;IAC/B,WAAK,IAAIC,GAAT,IAAgBD,MAAhB,EAAwB;IACpB,YACID,MAAM,CAACG,cAAP,CAAsBD,GAAtB,KACA,KAAKE,QAAL,CAAcJ,MAAM,CAACE,GAAD,CAApB,CADA,IAEA,KAAKE,QAAL,CAAcH,MAAM,CAACC,GAAD,CAApB,CAHJ,EAIE;IACE,eAAKG,WAAL,CAAiBL,MAAM,CAACE,GAAD,CAAvB,EAA8BD,MAAM,CAACC,GAAD,CAApC;IACH,SAND,MAMO;IACHF,UAAAA,MAAM,CAACE,GAAD,CAAN,GAAcD,MAAM,CAACC,GAAD,CAApB;IACH;IACJ;IACJ;;;;;;QC/BgBI,gBAGjB,uBAAYjD,GAAZ,EAAyB;IAAA;;IACrB,OAAKC,IAAL,GAAYD,GAAZ;IACH;;ICHL;;;;QAIqBkD;;;;;;;;;;;;;;IAGjB;;;;sCAIc;IACV,aAAO,KAAKjD,IAAL,CAAUgB,YAAV,KAA2BsB,SAAlC;IACH;IAED;;;;;;;;;+BAMOY,OAAeC,SAAsB;IACxC,aAAO,IAAI,KAAKnD,IAAL,CAAUgB,YAAd,CAA2BkC,KAA3B,EAAkC;IACrCE,QAAAA,IAAI,EACAhB,IAAI,CAACiB,QAAL,CAAcF,OAAO,CAACC,IAAtB,KACAhB,IAAI,CAACkB,WAAL,CAAiBH,OAAO,CAACC,IAAzB,CADA,IAEAhB,IAAI,CAACmB,MAAL,CAAYJ,OAAO,CAACC,IAApB,CAFA,GAGMD,OAAO,CAACC,IAHd,GAIMD,OAAO,CAACC,IAAR,CAAaI,GANc;IAOrCC,QAAAA,IAAI,EAAEN,OAAO,CAACM,IAPuB;IAQrCC,QAAAA,GAAG,EAAEP,OAAO,CAACO,GARwB;IASrCC,QAAAA,kBAAkB,EAAER,OAAO,CAACQ;IATS,OAAlC,CAAP;IAWH;IAED;;;;;;;8BAIMC,cAAmC;IACrCA,MAAAA,YAAY,CAACC,KAAb;IACH;;;;MArCqCb;;ICJ1C;;;;QAIqBc;;;;;;;;;;;;;;IAGjB;;;;sCAIc;IACV,aACI,KAAK9D,IAAL,CAAUgC,SAAV,KAAwBM,SAAxB,IACA,KAAKtC,IAAL,CAAUgC,SAAV,CAAoB+B,aAApB,KAAsCzB,SAF1C;IAIH;IAED;;;;;;;wCAIgB0B,MAAkB;IAC9B,UAAMC,GAAG,GAAGD,IAAI,CAACxB,QAAL,GAAgB0B,KAAhB,CAAsB,2BAAtB,CAAZ;IACA,aAAO,OAAOD,GAAP,KAAe,WAAf,IAA8BA,GAAG,KAAK,IAAtC,IAA8CA,GAAG,CAACzD,MAAJ,GAAa,CAA3D,GACDyD,GAAG,CAAC,CAAD,CADF,GAED,IAFN;IAGH;IAED;;;;;;;;;;;+BASIE,IACAjB,OACAC,SACAY,eACAK,UACF;IAAA;;IACE;IACA,WAAKpE,IAAL,CAAUgC,SAAV,CAAoB+B,aAApB,CAAkCM,QAAlC,CAA2CN,aAA3C;;IAEA,WAAK/D,IAAL,CAAUgC,SAAV,CAAoB+B,aAApB,CAAkCO,KAAlC,CACKhD,IADL,CACU,UAAAiD,YAAY,EAAI;IAClB;IACA,YAAIC,SAAS,GAAG;IACZL,UAAAA,EAAE,EAAEA,EADQ;IAEZM,UAAAA,IAAI,EAAEtB,OAAO,CAACsB,IAFF;IAGZC,UAAAA,MAAM,EAAEC,QAAQ,CAACC,QAAT,CAAkBC,IAHd;IAIZC,UAAAA,OAAO,EAAE1C,IAAI,CAAC2C,UAAL,CAAgB5B,OAAO,CAAC2B,OAAxB,IACH,KAAI,CAACE,eAAL,CAAqB7B,OAAO,CAAC2B,OAA7B,CADG,GAEH,EANM;IAOZG,UAAAA,OAAO,EAAE7C,IAAI,CAAC2C,UAAL,CAAgB5B,OAAO,CAAC8B,OAAxB,IACH,KAAI,CAACD,eAAL,CAAqB7B,OAAO,CAAC8B,OAA7B,CADG,GAEH;IATM,SAAhB;IAYA;;IACA,YAAI9B,OAAO,CAAC+B,IAAR,KAAiB5C,SAAjB,IAA8Ba,OAAO,CAAC+B,IAAR,KAAiB,IAAnD,EACIV,SAAS,GAAGW,MAAM,CAACC,MAAP,CAAcZ,SAAd,EAAyBrB,OAAO,CAAC+B,IAAjC,CAAZ;IAEJ;;IACAX,QAAAA,YAAY,CACPc,gBADL,CACsBnC,KADtB,EAC6B;IACrBE,UAAAA,IAAI,EAAED,OAAO,CAACC,IADO;IAErBK,UAAAA,IAAI,EAAEN,OAAO,CAACM,IAFO;IAGrB6B,UAAAA,OAAO,EAAEnC,OAAO,CAACmC,OAHI;IAIrB5B,UAAAA,GAAG,EAAEP,OAAO,CAACO,GAJQ;IAKrBwB,UAAAA,IAAI,EAAEV,SALe;IAMrBb,UAAAA,kBAAkB,EAAER,OAAO,CAACQ,kBANP;IAOrB4B,UAAAA,MAAM,EAAEpC,OAAO,CAACoC;IAPK,SAD7B,EAUKjE,IAVL,CAUU,YAAM;IACRiD,UAAAA,YAAY,CAACiB,gBAAb,GAAgClE,IAAhC,CAAqC,UAAAmE,aAAa,EAAI;IAClD;IACAlB,YAAAA,YAAY,CAACmB,MAAb,CAAoBC,WAApB,CAAgC,EAAhC;IAEA;;IACAvB,YAAAA,QAAQ,CAACqB,aAAD,CAAR;IACH,WAND;IAOH,SAlBL,EAmBKlE,KAnBL,CAmBW,UAASqE,KAAT,EAAgB;IACnB,gBAAM,IAAIC,KAAJ,CACFC,QAAQ,CAACxG,MAAT,CAAgBK,qBAAhB,GACIiG,KAAK,CAACG,OAFR,CAAN;IAIH,SAxBL;IAyBH,OA7CL,EA8CKxE,KA9CL,CA8CW,UAASqE,KAAT,EAAgB;IACnB,cAAM,IAAIC,KAAJ,CACFC,QAAQ,CAACxG,MAAT,CAAgBM,qBAAhB,GAAwCgG,KAAK,CAACG,OAD5C,CAAN;IAGH,OAlDL;IAmDH;IAED;;;;;;gCAGQ;IAEP;;;;MArG0C/C;;ICL/C;;;;QAIqBgD;;;;;;;;;;;;;;IAGjB;;;;sCAIc;IACV,aAAO,KAAKhG,IAAL,CAAUgC,SAAV,CAAoBC,eAApB,KAAwCK,SAA/C;IACH;IAED;;;;;;;;;+BAMOY,OAAeC,SAAsB;IACxC,UAAIS,YAAY,GAAG,KAAK5D,IAAL,CAAUgC,SAAV,CAAoBC,eAApB,CAAoCgE,kBAApC,CACf/C,KADe,EAEfC,OAAO,CAACM,IAFO,EAGfN,OAAO,CAACC,IAHO,CAAnB;;IAMAQ,MAAAA,YAAY,CAACsC,IAAb;IAEA,aAAOtC,YAAP;IACH;;;;MA3B2CZ;;ICHhD;;;QAGqBmD;;;;;;;;;;;;;;IAGjB;;;;sCAIc;IACV,aACI,KAAKnG,IAAL,CAAUkC,QAAV,KAAuBI,SAAvB,IACA,KAAKtC,IAAL,CAAUkC,QAAV,CAAmBC,YAAnB,KAAoCG,SAFxC;IAIH;IAED;;;;;;;;;+BAMOY,OAAeC,SAAsB;IACxC;IACA,WAAKnD,IAAL,CAAUkC,QAAV,CAAmBkE,0BAAnB;;IAEA,WAAKpG,IAAL,CAAUkC,QAAV,CAAmBmE,wBAAnB,CACIjE,IAAI,CAACiB,QAAL,CAAcF,OAAO,CAACC,IAAtB,KAA+BhB,IAAI,CAACkB,WAAL,CAAiBH,OAAO,CAACC,IAAzB,CAA/B,GACMD,OAAO,CAACC,IADd,GAEMD,OAAO,CAACC,IAAR,CAAakD,GAHvB,EAIIpD,KAJJ;;IAOA,WAAKlD,IAAL,CAAUkC,QAAV,CAAmBqE,kBAAnB;;IAEA,aAAO,IAAP;IACH;IAED;;;;;;;gCAIQ;IACJ,WAAKvG,IAAL,CAAUkC,QAAV,CAAmBkE,0BAAnB;IACH;;;;MA1CgCpD;;ICJrC;;;QAGqBwD;;;;;;;;;;;;;;IAGjB;;;;sCAIc;IACV,aAAO,KAAKxG,IAAL,CAAUkB,mBAAV,KAAkCoB,SAAzC;IACH;IAED;;;;;;;;;+BAMOY,OAAeC,SAAsB;IACxC,UAAIS,YAAY,GAAG,KAAK5D,IAAL,CAAUkB,mBAAV,CAA8B+E,kBAA9B,CACf9C,OAAO,CAACC,IADO,EAEfF,KAFe,EAGfC,OAAO,CAACM,IAHO,CAAnB;;IAMAG,MAAAA,YAAY,CAACsC,IAAb;IAEA,aAAOtC,YAAP;IACH;IAED;;;;;;;8BAIMA,cAAmC;IACrCA,MAAAA,YAAY,CAAC6C,MAAb;IACH;;;;MAnCoCzD;;QCMpB0D;;;IACjB;IAgBA;IAGA,mBAAY3G,GAAZ,EAAqB;IAAA;;IACjB;;IAEA;IACA,SAAK4G,UAAL,GAAkB,CAAlB;IAEA;;IACA,SAAKC,cAAL,GAAsB,EAAtB;IAEA;;IACA,SAAK5G,IAAL,GAAYD,GAAZ;IAEA;;IACA,SAAKD,UAAL,GAAkB,IAAIA,UAAJ,CAAeC,GAAf,CAAlB;IAEA;;IACA,SAAK8G,OAAL,GAAe;IACXC,MAAAA,OAAO,EAAE,IAAI7D,eAAJ,CAAiBlD,GAAjB,CADE;IAEXgH,MAAAA,MAAM,EAAE,IAAIjD,oBAAJ,CAAsB/D,GAAtB,CAFG;IAGXiH,MAAAA,OAAO,EAAE,IAAIhB,qBAAJ,CAAuBjG,GAAvB,CAHE;IAIXkH,MAAAA,EAAE,EAAE,IAAId,UAAJ,CAAYpG,GAAZ,CAJO;IAKXmH,MAAAA,MAAM,EAAE,IAAIV,cAAJ,CAAgBzG,GAAhB;IALG,KAAf;IAQA,SAAKoH,cAAL,GAAsB;IAClBpD,MAAAA,aAAa,EAAE,uBADG;IAElBqD,MAAAA,QAAQ,EAAE,kBAASC,OAAT,EAAkB;IAFV,KAAtB;IAIH;IAED;;;;;;;;;;2CAMmBlD,IAAqB;IACpC,UAAImD,OAAO,GAAG,IAAd;IACA,UAAM1D,YAAY,GAAG,KAAKgD,cAAL,CAAoBzC,EAApB,CAArB;;IAEA,UAAIP,YAAY,KAAKtB,SAArB,EAAgC;IAC5BgF,QAAAA,OAAO,GAAG,KAAKC,mBAAL,CAAyBpD,EAAzB,CAAV;IAEA;;IACA,YAAI,KAAK0C,OAAL,CAAaC,OAAb,CAAqBU,WAArB,EAAJ,EACI,KAAKX,OAAL,CAAaC,OAAb,CAAqBjD,KAArB,CAA2BD,YAA3B,EADJ,KAEK,IAAI,KAAKiD,OAAL,CAAaK,MAAb,CAAoBM,WAApB,EAAJ;IACD;IACA,eAAKX,OAAL,CAAaK,MAAb,CAAoBrD,KAApB,CAA0BD,YAA1B,EAFC,KAGA,IAAI,KAAKiD,OAAL,CAAaI,EAAb,CAAgBO,WAAhB,EAAJ;IACD;IACA,eAAKX,OAAL,CAAaI,EAAb,CAAgBpD,KAAhB,GAFC,KAGA;IACDyD,UAAAA,OAAO,GAAG,KAAV;IACA,gBAAM,IAAIzB,KAAJ,CAAUC,QAAQ,CAACxG,MAAT,CAAgBO,iBAA1B,CAAN;IACH;IAED,eAAOyH,OAAP;IACH;;IAED,aAAO,KAAP;IACH;IAED;;;;;;;;;yCAMiB1D,cAAmC;IAChD,UAAMO,EAAE,GAAG,KAAKwC,UAAhB;IACA,WAAKC,cAAL,CAAoBzC,EAApB,IAA0BP,YAA1B;IACA,WAAK+C,UAAL;IACA,aAAOxC,EAAP;IACH;IAED;;;;;;;;;4CAMoBA,IAAqB;IACrC,UAAImD,OAAO,GAAG,KAAd;;IAEA,UAAI,KAAKV,cAAL,CAAoB/D,cAApB,CAAmCsB,EAAnC,CAAJ,EAA4C;IACxC;IACA,eAAO,KAAKyC,cAAL,CAAoBzC,EAApB,CAAP;IACAmD,QAAAA,OAAO,GAAG,IAAV;IACH;;IAED,aAAOA,OAAP;IACH;IAED;;;;;;;;;;;6CAQqBnD,IAAYhB,SAAsB;IAAA;;IACnD,UAAIsE,OAAJ;IAEA;;IACAA,MAAAA,OAAO,GAAG;IACN7G,QAAAA,GAAG,EAAE,eAAM;IACP,iBAAO,KAAI,CAACgG,cAAL,CAAoBzC,EAApB,CAAP;IACH,SAHK;IAKNN,QAAAA,KAAK,EAAE,iBAAM;IACT,UAAA,KAAI,CAAC6D,kBAAL,CAAwBvD,EAAxB;IACH;IAPK,OAAV;IAUA;;IACA,UAAIhB,OAAO,CAACwE,OAAZ,EAAqB;IACjBC,QAAAA,UAAU,CAAC,YAAM;IACbH,UAAAA,OAAO,CAAC5D,KAAR;IACH,SAFS,EAEPV,OAAO,CAACwE,OAFD,CAAV;IAGH;;IAED,aAAOF,OAAP;IACH;IAED;;;;;;;;+CAMIhC,eACAtC,SACArC,SACF;IAAA;;IACE,UAAIqD,EAAE,GAAG,KAAK0D,gBAAL,CAAsBpC,aAAa,CAACA,aAAa,CAACjF,MAAd,GAAuB,CAAxB,CAAnC,CAAT;IAEA;;;IACA,UAAIwB,SAAS,IAAIA,SAAS,CAAC+B,aAA3B,EAA0C;IACtC/B,QAAAA,SAAS,CAAC+B,aAAV,CAAwB+D,gBAAxB,CAAyC,SAAzC,EAAoD,UAAAC,KAAK,EAAI;IACzD,cAAM7C,IAAI,GAAG8C,IAAI,CAACC,KAAL,CAAWF,KAAK,CAAC7C,IAAjB,CAAb;IAEA,cAAIA,IAAI,CAACgD,MAAL,KAAgB,OAAhB,IAA2BC,MAAM,CAACC,SAAP,CAAiBlD,IAAI,CAACf,EAAtB,CAA/B,EACI,MAAI,CAACoD,mBAAL,CAAyBrC,IAAI,CAACf,EAA9B;IACP,SALD;IAOArD,QAAAA,OAAO,CAAC,KAAKuH,oBAAL,CAA0BlE,EAA1B,EAA8BhB,OAA9B,CAAD,CAAP;IACH;;IAEDrC,MAAAA,OAAO,CAAC,IAAD,CAAP;IACH;IAED;;;;;;;;wCAMIoC,OACAC,SACArC,SACF;IAAA;;IACE,UAAI8C,YAAY,GAAG,IAAnB;IACA,UAAIqB,OAAJ;IAEA;;IACA9B,MAAAA,OAAO,GAAGA,OAAO,IAAI,EAArB;IAEA;;IACA8B,MAAAA,OAAO,GAAG,iBAAAd,EAAE,EAAI;IACZ;IACA,QAAA,MAAI,CAACoD,mBAAL,CAAyBpD,EAAzB;;IACA,YAAI/B,IAAI,CAAC2C,UAAL,CAAgB5B,OAAO,CAAC8B,OAAxB,CAAJ,EAAsC;IAClC9B,UAAAA,OAAO,CAAC8B,OAAR,CAAgBxC,IAAhB,CAAqB,MAArB,EAA2BmB,YAA3B;IACH;IACJ,OAND;IAQA;;;IACA,UAAI,KAAKiD,OAAL,CAAaC,OAAb,CAAqBU,WAArB,EAAJ,EAAwC;IACpC,YAAI;IACA;IACA5D,UAAAA,YAAY,GAAG,KAAKiD,OAAL,CAAaC,OAAb,CAAqBwB,MAArB,CAA4BpF,KAA5B,EAAmCC,OAAnC,CAAf;IACH,SAHD,CAGE,OAAOoF,CAAP,EAAU;IACR,cAAMpE,EAAE,GAAG,KAAKwC,UAAhB;IACA,cAAM6B,EAAE,GAAG,KAAKC,MAAL,GAAc1E,aAAzB;;IACA,cAAM2E,EAAE,GAAG,SAALA,EAAK,CAAAjD,aAAa;IAAA,mBACpB,MAAI,CAACkD,sBAAL,CACIlD,aADJ,EAEItC,OAFJ,EAGIrC,OAHJ,CADoB;IAAA,WAAxB;IAMA;;;IACA,cAAI,KAAK+F,OAAL,CAAaE,MAAb,CAAoBS,WAApB,EAAJ,EAAuC;IACnC,iBAAKX,OAAL,CAAaE,MAAb,CAAoBuB,MAApB,CAA2BnE,EAA3B,EAA+BjB,KAA/B,EAAsCC,OAAtC,EAA+CqF,EAA/C,EAAmDE,EAAnD;IACH;IACJ;IACD;;IACH,OAnBD,MAmBO,IAAI,KAAK7B,OAAL,CAAaK,MAAb,CAAoBM,WAApB,EAAJ,EACH5D,YAAY,GAAG,KAAKiD,OAAL,CAAaK,MAAb,CAAoBoB,MAApB,CAA2BpF,KAA3B,EAAkCC,OAAlC,CAAf,CADG,KAEF,IAAI,KAAK0D,OAAL,CAAaG,OAAb,CAAqBQ,WAArB,EAAJ;IACD;IACA,aAAKX,OAAL,CAAaG,OAAb,CAAqBsB,MAArB,CAA4BpF,KAA5B,EAAmCC,OAAnC,EAFC,KAGA,IAAI,KAAK0D,OAAL,CAAaI,EAAb,CAAgBO,WAAhB,EAAJ;IACD;IACA5D,QAAAA,YAAY,GAAG,KAAKiD,OAAL,CAAaI,EAAb,CAAgBqB,MAAhB,CAAuBpF,KAAvB,EAA8BC,OAA9B,CAAf,CAFC,KAGA;IACD;IACAA,QAAAA,OAAO,CAACD,KAAR,GAAgBA,KAAhB;IACA,aAAKuF,MAAL,GAAcrB,QAAd,CAAuBjE,OAAvB;IACH;;IAED,UAAIS,YAAY,KAAK,IAArB,EAA2B;IACvB,YAAMO,GAAE,GAAG,KAAK0D,gBAAL,CAAsBjE,YAAtB,CAAX;;IACA,YAAM6D,OAAO,GAAG,KAAKY,oBAAL,CAA0BlE,GAA1B,EAA8BhB,OAA9B,CAAhB;IAEA;;;IACA,YAAIf,IAAI,CAAC2C,UAAL,CAAgB5B,OAAO,CAACyF,MAAxB,CAAJ,EACIhF,YAAY,CAACkE,gBAAb,CAA8B,MAA9B,EAAsC3E,OAAO,CAACyF,MAA9C;IAEJ,YAAIxG,IAAI,CAAC2C,UAAL,CAAgB5B,OAAO,CAAC0F,OAAxB,CAAJ,EACIjF,YAAY,CAACkE,gBAAb,CAA8B,OAA9B,EAAuC3E,OAAO,CAAC0F,OAA/C;IAEJ,YAAIzG,IAAI,CAAC2C,UAAL,CAAgB5B,OAAO,CAAC2B,OAAxB,CAAJ,EACIlB,YAAY,CAACkE,gBAAb,CAA8B,OAA9B,EAAuC3E,OAAO,CAAC2B,OAA/C;IAEJlB,QAAAA,YAAY,CAACkE,gBAAb,CAA8B,OAA9B,EAAuC,YAAM;IACzC7C,UAAAA,OAAO,CAACd,GAAD,CAAP;IACH,SAFD;IAIAP,QAAAA,YAAY,CAACkE,gBAAb,CAA8B,QAA9B,EAAwC,YAAM;IAC1C7C,UAAAA,OAAO,CAACd,GAAD,CAAP;IACH,SAFD;IAIA;;IACArD,QAAAA,OAAO,CAAC2G,OAAD,CAAP;IACH;IAED;;;IACA3G,MAAAA,OAAO,CAAC,IAAD,CAAP;IACH;IAED;;;;;;;;+BAKOoC,OAAeC,SAA4B;IAAA;;IAC9C,UAAI2F,eAAJ;IAEA;;IACA,UAAI,CAAC1G,IAAI,CAACiB,QAAL,CAAcH,KAAd,CAAL,EAA2B;IACvB,cAAM,IAAI2C,KAAJ,CAAUC,QAAQ,CAACxG,MAAT,CAAgBG,aAA1B,CAAN;IACH;IAED;;;IACA,UAAI,CAAC,KAAKK,UAAL,CAAgBiJ,GAAhB,EAAL,EAA4B;IACxBD,QAAAA,eAAe,GAAG,yBAAChI,OAAD,EAAsBkI,MAAtB,EAAiD;IAC/D,UAAA,MAAI,CAAClJ,UAAL,CACKsB,OADL,GAEKE,IAFL,CAEU,YAAM;IACR,YAAA,MAAI,CAAC2H,eAAL,CAAqB/F,KAArB,EAA4BC,OAA5B,EAAqCrC,OAArC;IACH,WAJL,EAKKS,KALL,CAKW,YAAM;IACTyH,YAAAA,MAAM,CAAClD,QAAQ,CAACxG,MAAT,CAAgBI,iBAAjB,CAAN;IACH,WAPL;IAQH,SATD;IAUH,OAXD,MAWO;IACHoJ,QAAAA,eAAe,GAAG,yBAAChI,OAAD,EAAsBkI,MAAtB,EAAiD;IAC/D,cAAI;IACA,YAAA,MAAI,CAACC,eAAL,CAAqB/F,KAArB,EAA4BC,OAA5B,EAAqCrC,OAArC;IACH,WAFD,CAEE,OAAOyH,CAAP,EAAU;IACRS,YAAAA,MAAM,CAACT,CAAD,CAAN;IACH;IACJ,SAND;IAOH;;IAED,aAAO,IAAI3G,OAAJ,CAAYkH,eAAZ,CAAP;IACH;IAED;;;;;;;gCAIQ;IACJ,UAAII,KAAK,GAAG,CAAZ;IACA,UAAItG,GAAJ;;IAEA,WAAKA,GAAL,IAAY,KAAKgE,cAAjB;IACI,YAAI,KAAKA,cAAL,CAAoB/D,cAApB,CAAmCD,GAAnC,CAAJ,EAA6CsG,KAAK;IADtD;;IAGA,aAAOA,KAAP;IACH;IAED;;;;;;;;8BAKMxF,KAAa;IACf,UAAId,GAAJ,EAASgB,YAAT;;IAEA,WAAKhB,GAAL,IAAY,KAAKgE,cAAjB,EAAiC;IAC7B,YAAI,KAAKA,cAAL,CAAoB/D,cAApB,CAAmCD,GAAnC,CAAJ,EAA6C;IACzCgB,UAAAA,YAAY,GAAG,KAAKgD,cAAL,CAAoBhE,GAApB,CAAf;IAEA;;IACA,cAAIgB,YAAY,CAACF,GAAb,KAAqBA,GAAzB,EAA8B;IAC1B;IACA,mBAAO,KAAKgE,kBAAL,CAAwB9E,GAAxB,CAAP;IACH;IACJ;IACJ;IACJ;IAED;;;;;;;gCAIQ;IACJ,UAAIA,GAAJ;IAAA,UACI0E,OAAO,GAAG,IADd;;IAGA,WAAK1E,GAAL,IAAY,KAAKgE,cAAjB;IACI,YAAI,KAAKA,cAAL,CAAoB/D,cAApB,CAAmCD,GAAnC,CAAJ,EACI0E,OAAO,GAAGA,OAAO,IAAI,KAAKI,kBAAL,CAAwB9E,GAAxB,CAArB;IAFR;;IAIA,aAAO0E,OAAP;IACH;IAED;;;;;;;oCAIY;IACR,UAAI6B,SAAS,GAAG,KAAhB;;IAEA,WAAK,IAAIC,KAAT,IAAkB,KAAKvC,OAAvB;IACI,YAAI,KAAKA,OAAL,CAAahE,cAAb,CAA4BuG,KAA5B,CAAJ,EACID,SAAS,GAAGA,SAAS,IAAI,KAAKtC,OAAL,CAAauC,KAAb,EAAoB5B,WAApB,EAAzB;IAFR;;IAIA,aAAO2B,SAAP;IACH;IAED;;;;;;;+BAIOE,UAAe;IAClB,UACI,OAAOA,QAAP,KAAoB,WAApB,IACCA,QAAQ,KAAK,IAAb,IAAqBjH,IAAI,CAACU,QAAL,CAAcuG,QAAd,CAF1B,EAIIjH,IAAI,CAACW,WAAL,CAAiB,KAAKoE,cAAtB,EAAsCkC,QAAtC;IAEJ,aAAO,KAAKlC,cAAZ;IACH;IAED;;;;;;;+BAIOmC,UAA0B;IAC7B,UAAIC,MAAJ;IAAA,UACIC,MADJ;IAAA,UAEIC,OAAO,GAAG,GAAG5G,cAFjB;;IAIA,UAAI,CAAC4G,OAAO,CAAChH,IAAR,CAAa6G,QAAb,EAAuB,QAAvB,CAAL,EAAuC;IACnC,cAAM,IAAIzD,KAAJ,CAAUC,QAAQ,CAACxG,MAAT,CAAgBE,cAA1B,CAAN;IACH,OAFD,MAEO;IACH,YACIiK,OAAO,CAAChH,IAAR,CAAa6G,QAAb,EAAuB,QAAvB,KACAlH,IAAI,CAACU,QAAL,CAAcwG,QAAQ,CAACb,MAAvB,CADA,IAEAa,QAAQ,CAACb,MAAT,KAAoB,IAHxB,EAIE;IACE,eAAKA,MAAL,CAAYa,QAAQ,CAACb,MAArB;IACH;;IAEDe,QAAAA,MAAM,GAAGF,QAAQ,CAACC,MAAlB;IACAA,QAAAA,MAAM,GAAG,IAAIC,MAAJ,CAAW,KAAKf,MAAL,EAAX,CAAT;;IAEA,aAAK,IAAIiB,MAAT,IAAmBH,MAAnB,EAA2B;IACvB,cACIE,OAAO,CAAChH,IAAR,CAAa8G,MAAb,EAAqBG,MAArB,KACAtH,IAAI,CAAC2C,UAAL,CAAgBwE,MAAM,CAACG,MAAD,CAAtB,CAFJ;IAKI,iBAAKA,MAAL,IAAeH,MAAM,CAACG,MAAD,CAArB;IACP;IACJ;IACJ;;;;;;ACpaL,gBAAe,IAAIhD,OAAJ,CAAS,OAAOiD,MAAP,KAAkB,WAAlB,GAAgCA,MAAhC,GAAyCC,MAAlD,CAAf;;;;;;;;"}