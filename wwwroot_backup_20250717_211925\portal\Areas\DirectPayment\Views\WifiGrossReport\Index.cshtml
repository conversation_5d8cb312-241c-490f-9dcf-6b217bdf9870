﻿@model AppTech.MSMS.Domain.Reports.Models.WifiModel
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Domain
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


    <div class="form-horizontal">
        @{
            Html.RenderPartial("_DateControl");
        }
        <span class="lbl"> المزود</span>
        @Html.DropDownListFor(model => model.ProviderID, (SelectList)ViewBag.Providers)
        <div class="space-6"></div>

        <span class="lbl">الفئة</span>
        @Html.DropDownListFor(model => model.FactionID, (SelectList)ViewBag.Factions)
        <div class="space-6"></div>

        <span class="lbl"> اسم الحساب</span>
        @Html.DropDownListFor(model => model.AccountID, (SelectList)ViewBag.Accounts)
        <div class="space-6"></div>


        <span class="lbl">الحالة</span>
        @Html.DropDownListFor(model => model.State, new[]
           {
               new SelectListItem {Text = "الكل", Value = "-1"},
               new SelectListItem {Text = "لم يتم البيع", Value = "0"},
               new SelectListItem {Text = "تم البيع ", Value = "1"}
           })
        <div class="space-6"></div>

        <div class="form-group">
            <div class="col-md-12">
                <span class="lbl">تجميع بالفئة </span>
                @Html.EditorFor(m => m.GroupByFactions, (SelectList)ViewBag.Currencies)
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-12">
                <span class="lbl">تجميع بالمزود </span>
                @Html.EditorFor(m => m.GroupByProviders, (SelectList)ViewBag.Currencies)
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-12">
                <span class="lbl">تجميع بالحساب </span>
                @Html.EditorFor(m => m.GroupByAccounts, (SelectList)ViewBag.Currencies)
            </div>
        </div>
    </div>

