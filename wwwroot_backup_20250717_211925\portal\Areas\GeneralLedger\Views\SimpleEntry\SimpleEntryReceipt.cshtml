﻿@model AppTech.MSMS.Web.Models.ReceiptModel
@Styles.Render("~/Content/print")

<div class=" col-sm-12" style="border: 1px solid black;">
    <div class="table table-responsive">
        <table id="simple-table" class="table table-condensed no-border" style="border-bottom: 1px solid black;margin: 1px 0 0 0;">
            <tr>
                <td class="align-center col-sm-3  no-border">
                    التاريخ  <br />
                    <div style="border: 1px solid black;  font: bold 18px Helvetica, Sans-Serif;padding: 7px 0;">
                        @Html.DisplayFor(model => model.Date)
                    </div>
                </td>
                <td class="align-center col-sm-6  no-border">
                    <div style="border: 1px solid black;  font: bold 18px Helvetica, Sans-Serif;font-size: 38px;padding: 7px 0;">
                        @Html.DisplayFor(model => model.Type)
                    </div>
                </td>
                <td class="align-center col-sm-3  no-border">
                    رقم السند  <br />
                    <div style="border: 1px solid black;  font: bold 18px Helvetica, Sans-Serif;color: red;padding: 7px 0;">
                        @Html.DisplayFor(model => model.Number)
                    </div>
                </td>
            </tr>
        </table>

        <table id="simple-table" class="table table-condensed">
            <tr>
                <td class="no-border">
                    <b>
                        السيد :
                    </b>
                </td>
                <td colspan="3" class="no-border">
                    @Html.DisplayFor(model => model.FundName)
                </td>
            </tr>
            <tr>
                <td class="no-border"></td>
                <td colspan="3" class="no-border">
                    نــــود اشعـــاركــــــــــم اننــا خصمنــــا من حســــابــكــم لدينــا
                </td>
            </tr>
            <tr>
                <td>
                    <b>
                        مبلغ وقدرة :
                    </b>
                </td>
                <td style="color:blue">
                    #
                    @Html.DisplayFor(model => model.Amount)
                    #
                </td>
                <td>
                    @Html.DisplayFor(model => model.CurrencyName)
                </td>
                <td>
                    @Html.DisplayFor(model => model.AmountInText) لاغير
                </td>
            </tr>
            <tr>
                <td class="no-border">
                    <b>
                        ملاحظات  :
                    </b>
                </td>
                <td colspan="3" class="no-border">
                    @Html.DisplayFor(model => model.Note)
                </td>
            </tr>
        </table>

        <table id="simple-table" class="table table-condensed">
            <tr>
                <td class="no-border">
                    <b>
                        السيد :
                    </b>
                </td>
                <td colspan="3" class="no-border">
                    @Html.DisplayFor(model => model.AccountName)
                </td>
            </tr>
            <tr>
                <td class="no-border"></td>
                <td colspan="3" class="no-border">
                    نــــود اشعـــاركــــــــــم اننــا اضفنـــــا الى حســــابــكــم لدينــا
                </td>
            </tr>
            <tr>
                <td>
                    <b>
                        مبلغ وقدرة :
                    </b>
                </td>
                <td style="color:blue">
                    #
                    @Html.DisplayFor(model => model.Amount)
                    #
                </td>
                <td>
                    @Html.DisplayFor(model => model.CurrencyName)
                </td>
                <td>
                    @Html.DisplayFor(model => model.AmountInText) لاغير
                </td>
            </tr>
        </table>

        <table id="simple-table" class="table no-border">
            <tr class="no-border">
                <td class="no-border">
                    <b>
                        اسم المحاسب :
                    </b>
                    ..........................
                    <br />
                    <b>التوقيع</b>
                    <div style="border: 1px solid black;padding: 20px 0;display: inline-block;width: 85%;"><br /></div>
                </td>
                <td class="no-border">
                    <b>
                        المدير :
                    </b>
                    ...........................
                    <br />
                        <b>التوقيع</b>
                        <div style="border: 1px solid black;padding: 20px 0;display: inline-block;width: 85%;"><br /></div>
                </td>
            </tr>
        </table>
    </div>
</div>