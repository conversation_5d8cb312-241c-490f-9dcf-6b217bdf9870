﻿@{
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}
<div>
    <input type="hidden" id="page" name="page" value="@Model.Page" />
    @{ Html.RenderPartial("_CrudHeader"); }
    <section>
        @RenderBody()
    </section>

    <span class="alert"></span>
</div>
<div>
    <div style="margin-top: 5px">
        <div class="table-header">
        <span>عدد السجلات</span>
        <select id="pageSize">
            <option>10</option>
            <option>30</option>
            <option>50</option>
            <option>80</option>
            <option>100</option>
            <option>200</option>
        </select>
</div>
        <div id="list">
            @Html.Partial("_Indicator")
        </div>
    </div>
</div>
@section Scripts
{

    <script>

        hideLoading()
        $(document).ready(function (e) {
            pageHelper.init(true);
            $("#refresh").on('click',
                function () {
                    CrudHelper.hideSearchBox();
                    fetchData();
                });
        });

        var pageHelper = {
            init: function (firstInit) {
                fetchData(firstInit);
            }
        };

        function fetchData(firstInit, refreshWithCondition) {
            i('fetchData from: ' + $("#Controller").val());

            var condition = '';

            if (refreshWithCondition) {
                i('refreshWithCondition');
                    condition = $("#QryCondition").val();
            }
        
            var options = {
                url: "/" + $("#Controller").val() + "/Index",
                data: {
                    pageSize: $('#pageSize').val(),
                    condition : condition
                }
            };
            $.ajax(options).done(function (data) {
                i('data loaded');
                try {
                    $("#list").replaceWith(data);
                }
                catch (e) {
                    i('لم  يتمكن  من عرض البيانات : ' + e);
                }
                var pager = Patterns.Art.Pager;
                if (firstInit)
                    pager.start();
                else {
                    pager.activateList();
                    resetButton();
                }
            }).fail(function (xhr, textStatus, errorThrown) {
                resetButton();
                parseAndShowError(xhr, textStatus, errorThrown);
            });
        }


    </script>

}