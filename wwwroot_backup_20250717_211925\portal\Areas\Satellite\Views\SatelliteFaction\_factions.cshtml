﻿@model IEnumerable<AppTech.MSMS.Domain.Models.SatelliteFaction>
<table class="table table-hover" id="tableDetailSatellitePayed">
    <thead>
        <tr>
            <th>اسم الفئة</th>
            <th >سعر البيع</th>
            <th>ملاحظات</th>
            <th></th>

        </tr>
    </thead>
    @foreach (var Faction in Model)
    {
        <tbody>
            <tr>
                @if (Faction != null)
                {
                    <td>@Html.DisplayFor(modelItem => Faction.Name)</td>
                    <td>@Html.DisplayFor(modelItem => Faction.Price)</td>
                    <td>@Html.DisplayFor(modelItem => Faction.Note)</td>
                    <td style="text-align:right">
                        @*<Button class="btn btn-link" onclick="openCards('@Faction.ID')">
                            <i class="ace-icon fa fa-eye bigger-110"></i>
                            عرض الكروت
                        </Button>*@
                        <Button class="btn btn-link" onclick="openEditModal('@Faction.ID','@Faction.SatelliteProviderID')">
                            <i class="ace-icon fa fa-edit bigger-110"></i>
                            تعديل
                        </Button>
                    </td>
                }
            </tr>
        </tbody>
    }
</table>
<script>
    function openModal(id) {
        i('open modal id' + id);
        openViewAsModal('Satellite/SatelliteFaction/AddOrEditFaction?ID=' + id, " جديد");
    }
      function openEditModal(id,SatelliteProviderID) {
        i('open modal id' + id);
        openViewAsModal('Satellite/SatelliteFaction/AddOrEditFaction?ID=' + id +'&SatelliteProviderID='+SatelliteProviderID);
    }
</script>
