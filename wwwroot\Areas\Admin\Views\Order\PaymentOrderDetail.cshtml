﻿@model AppTech.MSMS.Domain.Models.TopupOrder

<div class="space-6"></div>
<span class="label label-info"> تفاصيل الطلب</span>
<div class="space-6"></div>


<div class="profile-info-row">
    <div class="profile-info-name"> رقم المشترك </div>

    <div class="profile-info-value">
        <span class="editable" id="age"> @Html.DisplayFor(model => model.SubscriberNumber) </span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> المبلغ </div>

    <div class="profile-info-value">
        <span class="editable" id="signup"> @Html.DisplayFor(model => model.Amount)</span>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> الباقة / الفئة </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.ImageName)</span>
    </div>
</div>
<div class="profile-info-row">
    <div class="profile-info-name"> التفاصيل </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Description)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> الملاحظات </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Note)</span>
    </div>
</div>


@*<div class="profile-info-row">
    <div class="profile-info-name"> الصورة الملحقة </div>

    <div class="profile-info-value">
        @if (!string.IsNullOrEmpty(Model.ImageName))
        {
            <img id="avatar" class="editable img-responsive" alt="Alex's Avatar" src="@Url.Content(Model.ImageName)" />
        }

    </div>
</div>*@