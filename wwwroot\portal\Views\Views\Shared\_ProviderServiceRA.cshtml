﻿@using AppTech.MSMS.Web.Security
@{
    var userPermission = CurrentUser.UserPermissions.UserPermissions.SingleOrDefault(x => x.Page.PageName.Equals(ViewBag.PageName));
    if (userPermission != null)
    {
        if (userPermission.PageActions.Any(x => x.Name.Equals("ACTIVATE") && x.IsAllow))
        {
            <li>
                @if (!Model.Row["الحالة"].ToString().Equals("مفعل"))
                {
                    <a href="" onclick="process(@Model.ID), '1'">تفعيل</a>
                }
                else
                {
                    <a href="" onclick="process(@Model.ID), '0'">توقيف</a>
                }
            </li>
            <li class="divider"></li>
        }
    }
}

<script>
    function process(id, action) {

        var url = '@Url.Action("Activate", "LiveTopup")';
        if (action === "0")
            url = '@Url.Action("Deactivate", "LiveTopup")';


        $.ajax({
            url: url,
            data: { id: id },
            success: function(data) {
                var result = data;
                handleResult(result);
            },
            error: function(xhr, ajaxOptions, thrownError) {
                handleXhr(xhr);

            }
        });
    }

</script>