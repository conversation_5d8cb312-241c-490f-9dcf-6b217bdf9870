﻿@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
    <ul class="nav ace-nav">

        @if (CurrentUser.IsAuthenticated)
        {
            <li class="dark dropdown-modal">
                <a data-toggle="dropdown" href="javascript;:" class="dropdown-toggle">

                    <span class="user-info">

                        @CurrentUser.CurrentSession.User.Name
                    </span>

                    <i class="ace-icon fa fa-caret-down"></i>
                </a>

                <ul class="user-menu dropdown-menu-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
                    <li>
                        <a href="/#!/account/Account">
                            <i class="ace-icon fa fa-users"></i>
                            الملف الشخصي
                        </a>
                    </li>
                    <li class="divider"></li>

                    <li>
                        <a href="/account/logout">
                            <i class="ace-icon fa fa-power-off"></i>
                            تسجيل خروج
                        </a>
                    </li>
                </ul>
            </li>

            if (CurrentUser.Type == UserType.Admin)
            {
                <li class="dark dropdown-modal">
                    <button data-toggle="dropdown" class="btn btn-link dropdown-toggle">
                        <i class="ace-icon fa fa-tasks"></i>
                        <span class="badge badge-important"></span>
                    </button>

                    <ul class="dropdown-menu-right dropdown-navbar dropdown-menu dropdown-caret dropdown-close">
                        <li class="dropdown-header">
                            <i class="ace-icon fa fa-check"></i>
                        </li>

                        <li class="dropdown-content">
                            <ul class="dropdown-menu dropdown-navbar">
                                <li>

                                    <a href="/#!/route/DirectPayment/Topup">
                                        <div class="clearfix">
                                            <span class="pull-right">
                                                التحصيلات

                                            </span>
                                            <i class="btn btn-xs no-hover fa fa-users pull-left"></i>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/#!/route/GeneralLedger/CurrencyRate">
                                        <div class="clearfix">
                                            <span class="pull-right">
                                                أسعار العملات

                                            </span>
                                            <i class="btn btn-xs no-hover fa fa-money pull-left"></i>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/#!/route/GeneralLedger/AccountSheet">
                                        <div class="clearfix">
                                            <span class="pull-right">
                                                كشف حساب

                                            </span>
                                            <i class="btn btn-xs no-hover fa fa-pie-chart pull-left"></i>
                                        </div>
                                    </a>
                                </li>
                                @*<li>
                                    <a href="/#!/route/clients/Charging">
                                        <div class="clearfix">
                                            <span class="pull-right">

                                                الشحن الفوري والباقات

                                            </span>
                                            <i class="btn btn-xs no-hover fa fa-mobile pull-left"></i>
                                        </div>
                                    </a>
                                </li>*@

                                <li>
                                    <a href="/#!/route/DirectPayment/ProviderReport">
                                        <div class="clearfix">
                                            <span class="pull-right">
                                                تقرير التحصيلات
                                            </span>
                                            <i class="btn btn-xs no-hover fa fa-bar-chart pull-left"></i>
                                        </div>
                                    </a>
                                </li>

                            </ul>
                        </li>

                        <li class="dropdown-footer">
                            @*<a href="/#!/route/Admin/Order">
                                الانتقال الى واجهة الطلبات
                                <i class="ace-icon fa fa-arrow-right"></i>
                            </a>*@
                        </li>
                    </ul>
                </li>



                <li class="dark dropdown-modal">


                    <button data-toggle="dropdown" class="btn btn-link dropdown-toggle">
                        <i class="ace-icon fa fa-bell icon-animated-bell"></i>
                        <span class="badge badge-important pending-orders" id="orders"></span>
                    </button>

                    <ul class="dropdown-menu-right dropdown-navbar navbar-pink dropdown-menu dropdown-caret dropdown-close">
                        <li class="dropdown-header">
                            <i class="ace-icon fa fa-exclamation-triangle"></i>

                        </li>

                        <li class="dropdown-content">
                            <ul class="dropdown-menu dropdown-navbar navbar-pink">


                                <li>
                                    <a href="/#!/route/Admin/Order">
                                        <div class="clearfix">
                                            <span class="pull-right">
                                                الطلبات
                                            </span>
                                            <span class="pull-left badge badge-success pending-orders">+0</span>
                                        </div>
                                    </a>
                                </li>

                                <li>
                                    <a href="/#!/route/client/registeration">
                                        <div class="clearfix">
                                            <span class="pull-right">

                                                طلبات فتح حساب
                                            </span>
                                            <i class="btn btn-xs no-hover btn-success fa fa-user-plus pull-left"></i>
                                            <span class="pull-left badge badge-success pending-users "></span>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="dropdown-footer">
                            <a href="/#!/route/Admin/Order">
                                الانتقال الى واجهة الطلبات
                                <i class="ace-icon fa fa-arrow-right"></i>
                            </a>
                        </li>
                    </ul>



                </li>
            }

            <li class="dark dropdown-modal">
                <button data-toggle="dropdown" class="btn btn-link dropdown-toggle">
                    <i class="ace-icon fa fa-envelope icon-animated-vertical"></i>
                    <span class="badge badge-success"></span>
                </button>

                <ul class="dropdown-menu-right dropdown-navbar dropdown-menu dropdown-caret dropdown-close">
                    <li class="dropdown-header">
                        <i class="ace-icon fa fa-envelope-o"></i>
                    </li>

                    <li class="dropdown-content">
                        <ul class="dropdown-menu dropdown-navbar">
                     
                        </ul>
                    </li>

                    <li class="dropdown-footer">
                      
                    </li>
                </ul>
            </li>

            <li class="dark dropdown-modal">
                @Html.Partial("_Loader")

            </li>

        }
        else
        {
            <li>
                <a href="/account/admin">تسجيل الدخول</a>
            </li>
        }
    </ul>