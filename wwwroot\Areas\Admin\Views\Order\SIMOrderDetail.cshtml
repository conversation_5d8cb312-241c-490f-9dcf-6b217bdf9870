﻿@model AppTech.MSMS.Domain.Models.SimCardOrder

<div class="space-6"></div>
<span> بيانات الشريحة</span>
<div class="space-6"></div>





<div class="profile-info-row">
    <div class="profile-info-name"> المشغل  </div>

    <div class="profile-info-value">
        <span class="editable">  @Html.DisplayFor(model => model.NetworkID)	</span>
    </div>
</div>




<div class="profile-info-row">
    <div class="profile-info-name"> نوع العملية </div>

    <div class="profile-info-value">
        <span class="editable">
            @Html.DisplayFor(model => model.ActionType) @if (Model.SimAction == 1)
            {<span> | مع رقم مميز</span>}
        </span>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> نوع الأشتراك  </div>

    <div class="profile-info-value">
        <span class="editable">  @Html.DisplayFor(model => model.SimType)	</span>
    </div>
</div>




<div class="profile-info-row">
    <div class="profile-info-name"> رقم الشريحة </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.SimNumber) </span>
        <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.SimNumber)">
            <i class="ace-icon fa fa-copy"></i> نسخ
        </button>

    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> نوع الخط </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.LineType) </span>

    </div>
</div>

@*<div class="profile-info-row">
        <div class="profile-info-name"> رقم العقد </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.ContractNumber) </span>
        </div>
    </div>*@

@if (Model.SimAction == 1)
{
    <div class="profile-info-row">
        <div class="profile-info-name"> رقم المميز </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.ESDN)</span>
        </div>
    </div>
}

<div class="profile-info-row">
    <div class="profile-info-name"> المبلغ </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Amount)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> رقم المشترك /الخط </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.SubscriberNumber) </span>
    </div>
</div>




<div class="profile-info-row">
    <div class="profile-info-name"> اسم المشترك </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.CustomerName) </span>
        <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.CustomerName)">
            <i class="ace-icon fa fa-copy"></i> نسخ
        </button>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> تاريخ الميلاد </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.BirthDate) </span>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name">نوع الهوية </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.PersonalCardType) </span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name">رقم الهوية </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.CardNumber) </span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> تاريخ الأصدار </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.IssueDate) </span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> الملاحظات </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Note)</span>
    </div>
</div>

@*<div class="profile-info-row">
        <div class="profile-info-name"> مكان الأصدار </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.CardIssuePlace) </span>
        </div>
    </div>
    <div class="profile-info-row">
        <div class="profile-info-name"> تاريخ الأنتهاء </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.IssueDate) </span>
        </div>
    </div>*@




<div class="profile-info-row">
    <div class="profile-info-name"> صوره ملحقة 1</div>

    <div class="profile-info-value">
        @if (!string.IsNullOrEmpty(Model.FrontCardImage) && Model.BackCardImage.StartsWith("~/Photos/"))
        {
            <img id="avatar" class="editable img-responsive" alt="attached image" src="@Url.Content(Model.FrontCardImage)" />
        }
    </div>
</div>
<div class="profile-info-row">
    <div class="profile-info-name">صوره ملحقة 2 </div>

    <div class="profile-info-value">
        @if (!string.IsNullOrEmpty(Model.BackCardImage) && Model.BackCardImage.StartsWith("~/Photos/"))
        {
            <img id="avatar" class="editable img-responsive" alt="attached image" src="@Url.Content(Model.BackCardImage)" />
        }

    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> صوره ملحقة 3 </div>

    <div class="profile-info-value">
        @if (!string.IsNullOrEmpty(Model.MSISDN) && Model.BackCardImage.StartsWith("~/Photos/"))
        {
            <img id="avatar" class="editable img-responsive" alt="attached image" src="@Url.Content(Model.MSISDN)" />
        }

    </div>
</div>


@*<div class="profile-info-row">
    <div class="profile-info-name"> صوره ملحقة 4 </div>

    <div class="profile-info-value">
        @if (!string.IsNullOrEmpty(Model.CardIssuePlace))
        {
            <img id="avatar" class="editable img-responsive" alt="attached image" src="@Url.Content(Model.CardIssuePlace)" />
        }

    </div>
</div>*@

