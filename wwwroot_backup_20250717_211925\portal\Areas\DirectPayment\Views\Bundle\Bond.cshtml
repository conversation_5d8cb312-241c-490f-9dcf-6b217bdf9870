﻿@model AppTech.MSMS.Domain.Models.Bundle

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
@*<input type="hidden" name="FactionID" value="@ViewBag.FactionID" />*@

        @Html.HiddenFor(model => model.FactionID)
<div class="form-group">
    @Html.Label("رقم المزود", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.ProviderNumber)
        @Html.ValidationMessageFor(model => model.ProviderNumber)
    </div>
</div>

<div class="form-group">
    @Html.Label("الكود", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Code)
        @Html.ValidationMessageFor(model => model.Code)
    </div>
</div>

<div class="form-group">
    @Html.Label("سعر التكلفة", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.CostPrice)
        @Html.ValidationMessageFor(model => model.CostPrice)
    </div>
</div>

<script>
    hideLoading();
</script>
<script>

    $(function () {
        try {
            formHelper.onSuccess = function (data) {
                log('user party form');
                i('data> ' + data);
                $("#modal").modal('hide');
                $("#list").html(data);
            }
            formHelper.onBegin = function (context) {
                return true;
            };
        } catch (e) {
        }
    });


</script>