﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.Party
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.LabelFor(model => model.AccountID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.Obout(new ComboBox("AccountID")
        {
            SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains
        })

        @Html.ValidationMessageFor(model => model.AccountID, "", new {@class = "text-danger"})
    </div>
</div>



<div class="form-group">
    @Html.LabelFor(model => model.SyncAccountID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SyncAccountID, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.SyncAccountID, "", new {@class = "text-danger"})
    </div>
</div>
