# AppTech Connection String Decryption Script
# سكريبت فك تشفير سلاسل الاتصال

param(
    [string]$SourcePath = "C:\inetpub",
    [string]$OutputPath = "C:\inetpub\DecryptedConnections"
)

Write-Host "=== AppTech Connection String Decryption Tool ===" -ForegroundColor Green

# إنشاء مجلد الإخراج
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
}

# البحث عن ملفات التكوين
Write-Host "`nSearching for configuration files..." -ForegroundColor Cyan
$ConfigFiles = @()
$ConfigFiles += Get-ChildItem -Path $SourcePath -Filter "web.config" -Recurse
$ConfigFiles += Get-ChildItem -Path $SourcePath -Filter "app.config" -Recurse
$ConfigFiles += Get-ChildItem -Path $SourcePath -Filter "*.config" -Recurse | Where-Object { $_.Name -like "*connection*" }

Write-Host "Found $($ConfigFiles.Count) configuration files:" -ForegroundColor Yellow
foreach ($config in $ConfigFiles) {
    Write-Host "  - $($config.FullName)" -ForegroundColor White
}

# دالة لفك تشفير AES
function Decrypt-AES {
    param(
        [string]$EncryptedText,
        [string]$Key = "8080808080808080",
        [string]$IV = "8080808080808080"
    )
    
    try {
        # تحويل المفتاح و IV إلى bytes
        $keyBytes = [System.Text.Encoding]::UTF8.GetBytes($Key)
        $ivBytes = [System.Text.Encoding]::UTF8.GetBytes($IV)
        
        # فك تشفير Base64
        $encryptedBytes = [System.Convert]::FromBase64String($EncryptedText)
        
        # إنشاء AES decryptor
        $aes = [System.Security.Cryptography.Aes]::Create()
        $aes.Key = $keyBytes
        $aes.IV = $ivBytes
        $aes.Mode = [System.Security.Cryptography.CipherMode]::CBC
        $aes.Padding = [System.Security.Cryptography.PaddingMode]::PKCS7
        
        $decryptor = $aes.CreateDecryptor()
        
        # فك التشفير
        $decryptedBytes = $decryptor.TransformFinalBlock($encryptedBytes, 0, $encryptedBytes.Length)
        $decryptedText = [System.Text.Encoding]::UTF8.GetString($decryptedBytes)
        
        $aes.Dispose()
        return $decryptedText
    } catch {
        Write-Host "AES Decryption failed: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# دالة لتحليل ملفات التكوين
function Analyze-ConfigFile {
    param(
        [string]$ConfigFile,
        [string]$OutputFile
    )
    
    try {
        Write-Host "`nAnalyzing: $ConfigFile" -ForegroundColor Cyan
        
        # قراءة محتوى الملف
        $content = Get-Content $ConfigFile -Raw
        
        # تحليل XML
        [xml]$xmlContent = $content
        
        $report = @()
        $report += "=== Configuration File Analysis ==="
        $report += "File: $ConfigFile"
        $report += "Date: $(Get-Date)"
        $report += ""
        
        # البحث عن إعدادات التشفير
        $appSettings = $xmlContent.configuration.appSettings.add
        if ($appSettings) {
            $report += "=== App Settings ==="
            foreach ($setting in $appSettings) {
                $key = $setting.key
                $value = $setting.value
                
                # فحص الإعدادات المهمة
                if ($key -like "*connection*" -or $key -like "*encrypt*" -or $key -like "*key*" -or $key -like "*password*") {
                    $report += "Key: $key"
                    $report += "Value: $value"
                    
                    # محاولة فك التشفير إذا كان النص مشفراً
                    if ($value -and $value.Length -gt 20 -and $value -match "^[A-Za-z0-9+/=]+$") {
                        $decrypted = Decrypt-AES -EncryptedText $value
                        if ($decrypted) {
                            $report += "Decrypted: $decrypted"
                        }
                    }
                    $report += ""
                }
            }
        }
        
        # البحث عن سلاسل الاتصال
        $connectionStrings = $xmlContent.configuration.connectionStrings.add
        if ($connectionStrings) {
            $report += "=== Connection Strings ==="
            foreach ($conn in $connectionStrings) {
                $report += "Name: $($conn.name)"
                $report += "Connection String: $($conn.connectionString)"
                $report += "Provider: $($conn.providerName)"
                $report += ""
            }
        }
        
        # البحث عن مفاتيح التشفير المكشوفة
        $report += "=== Security Analysis ==="
        if ($content -match "recaptcha") {
            $report += "⚠️  reCAPTCHA keys found (potentially exposed)"
        }
        if ($content -match "8080808080808080") {
            $report += "⚠️  Hardcoded encryption key found"
        }
        if ($content -match "EncrptedFile") {
            $report += "✅ Connection strings are encrypted"
        }
        
        # حفظ التقرير
        $report | Out-File -FilePath $OutputFile -Encoding UTF8
        Write-Host "Analysis saved to: $OutputFile" -ForegroundColor Green
        
        return $true
    } catch {
        Write-Host "Error analyzing config file: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# معالجة ملفات التكوين
Write-Host "`n=== Starting Configuration Analysis ===" -ForegroundColor Green

$successCount = 0
$failCount = 0

foreach ($configFile in $ConfigFiles) {
    $relativePath = $configFile.FullName.Substring($SourcePath.Length + 1)
    $outputFile = Join-Path $OutputPath ($relativePath -replace "\.(config|xml)$", "_analysis.txt")
    $outputDir = Split-Path $outputFile -Parent
    
    # إنشاء مجلد الإخراج
    if (!(Test-Path $outputDir)) {
        New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    }
    
    if (Analyze-ConfigFile -ConfigFile $configFile.FullName -OutputFile $outputFile) {
        $successCount++
    } else {
        $failCount++
    }
}

Write-Host "`n=== Configuration Analysis Complete ===" -ForegroundColor Green
Write-Host "Successfully analyzed: $successCount files" -ForegroundColor Green
Write-Host "Failed to analyze: $failCount files" -ForegroundColor Red
Write-Host "Output directory: $OutputPath" -ForegroundColor Yellow
