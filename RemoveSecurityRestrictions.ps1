# Remove All Security Restrictions Temporarily
# إزالة جميع قيود الحماية مؤقتاً

Write-Host @"
╔══════════════════════════════════════════════════════════════╗
║            Remove Security Restrictions                     ║
║              إزالة قيود الحماية مؤقتاً                      ║
╚══════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Red

Write-Host "`nRemoving all security restrictions temporarily..." -ForegroundColor Yellow

# الخطوة 1: إزالة SSL/HTTPS Requirements
Write-Host "`n=== Step 1: Removing SSL/HTTPS Requirements ===" -ForegroundColor Cyan

try {
    Import-Module WebAdministration -ErrorAction SilentlyContinue
    
    # إزالة متطلبات SSL
    Set-WebConfigurationProperty -Filter "system.webServer/security/access" -Name "sslFlags" -Value "None" -PSPath "IIS:\" -Location "Default Web Site" -ErrorAction SilentlyContinue
    Write-Host "✅ SSL requirements removed" -ForegroundColor Green
    
    # إزالة HTTPS redirects
    Remove-WebConfigurationProperty -Filter "system.webServer/httpRedirect" -Name "enabled" -PSPath "IIS:\" -Location "Default Web Site" -ErrorAction SilentlyContinue
    Write-Host "✅ HTTPS redirects removed" -ForegroundColor Green
    
} catch {
    Write-Host "⚠️ WebAdministration module not available" -ForegroundColor Yellow
}

# الخطوة 2: إنشاء web.config بدون قيود أمنية
Write-Host "`n=== Step 2: Creating Security-Free Web.config ===" -ForegroundColor Cyan

$noSecurityWebConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="false" />
    <add key="UnobtrusiveJavaScriptEnabled" value="false" />
    <add key="vs:EnableBrowserLink" value="false" />
    <add key="SecurityEnabled" value="false" />
    <add key="AuthenticationRequired" value="false" />
    <add key="SSLRequired" value="false" />
    <add key="CertificateValidation" value="false" />
  </appSettings>
  
  <system.web>
    <compilation debug="true" targetFramework="4.8" tempDirectory="~/App_Data/Temp/" />
    <httpRuntime targetFramework="4.8" maxRequestLength="51200" executionTimeout="300" />
    <customErrors mode="Off" />
    <trust level="Full" />
    <authentication mode="None" />
    <authorization>
      <allow users="*" />
    </authorization>
    <httpModules>
      <clear />
    </httpModules>
    <httpHandlers>
      <clear />
      <add verb="*" path="*" type="System.Web.DefaultHttpHandler" />
    </httpHandlers>
    <pages validateRequest="false" enableEventValidation="false" viewStateEncryptionMode="Never" />
    <sessionState mode="Off" />
    <trace enabled="false" />
    <globalization requestEncoding="utf-8" responseEncoding="utf-8" />
  </system.web>
  
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <modules runAllManagedModulesForAllRequests="false">
      <clear />
    </modules>
    <handlers>
      <clear />
      <add name="StaticFile" path="*" verb="*" modules="StaticFileModule,DefaultDocumentModule,DirectoryListingModule" resourceType="Either" requireAccess="Read" />
    </handlers>
    <defaultDocument>
      <files>
        <clear />
        <add value="index.html" />
        <add value="default.html" />
        <add value="default.aspx" />
        <add value="index.aspx" />
      </files>
    </defaultDocument>
    <directoryBrowse enabled="false" />
    <httpErrors errorMode="Detailed" />
    <security>
      <authentication>
        <anonymousAuthentication enabled="true" />
        <windowsAuthentication enabled="false" />
        <basicAuthentication enabled="false" />
      </authentication>
      <authorization>
        <clear />
        <add accessType="Allow" users="*" />
      </authorization>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="52428800" />
      </requestFiltering>
    </security>
    <httpProtocol>
      <customHeaders>
        <clear />
      </customHeaders>
    </httpProtocol>
  </system.webServer>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="********" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
"@

# تطبيق web.config بدون حماية على جميع التطبيقات
$apps = @("portal", "api", "client", "apinewAN")
foreach ($app in $apps) {
    $appPath = "C:\inetpub\wwwroot\$app"
    if (Test-Path $appPath) {
        Write-Host "Removing security from $app..." -ForegroundColor Yellow
        
        # نسخ احتياطي من web.config الحالي
        if (Test-Path "$appPath\web.config") {
            Copy-Item "$appPath\web.config" "$appPath\web.config.secure.backup" -Force -ErrorAction SilentlyContinue
        }
        
        # تطبيق web.config بدون حماية
        $noSecurityWebConfig | Out-File -FilePath "$appPath\web.config" -Encoding UTF8 -Force
        Write-Host "✅ $app security restrictions removed" -ForegroundColor Green
    }
}

# الخطوة 3: إزالة شهادات SSL من IIS
Write-Host "`n=== Step 3: Removing SSL Certificates ===" -ForegroundColor Cyan

try {
    # إزالة bindings SSL
    $sslBindings = Get-WebBinding -Protocol "https" -ErrorAction SilentlyContinue
    foreach ($binding in $sslBindings) {
        Remove-WebBinding -Protocol "https" -Port $binding.bindingInformation.Split(':')[1] -ErrorAction SilentlyContinue
        Write-Host "✅ Removed SSL binding on port $($binding.bindingInformation.Split(':')[1])" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ Could not remove SSL bindings automatically" -ForegroundColor Yellow
}

# الخطوة 4: تعطيل Windows Firewall مؤقتاً
Write-Host "`n=== Step 4: Temporarily Disabling Windows Firewall ===" -ForegroundColor Cyan

try {
    netsh advfirewall set allprofiles state off >$null 2>&1
    Write-Host "✅ Windows Firewall temporarily disabled" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Could not disable Windows Firewall" -ForegroundColor Yellow
}

# الخطوة 5: إزالة قيود UAC مؤقتاً
Write-Host "`n=== Step 5: Reducing UAC Restrictions ===" -ForegroundColor Cyan

try {
    # تقليل مستوى UAC (يتطلب إعادة تشغيل)
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "EnableLUA" -Value 0 -ErrorAction SilentlyContinue
    Write-Host "✅ UAC restrictions reduced (requires restart)" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Could not modify UAC settings" -ForegroundColor Yellow
}

# الخطوة 6: إعطاء صلاحيات كاملة لجميع المجلدات
Write-Host "`n=== Step 6: Granting Full Permissions ===" -ForegroundColor Cyan

$pathsToOpen = @(
    "C:\inetpub",
    "C:\inetpub\wwwroot",
    "C:\Windows\Temp",
    "C:\Windows\Microsoft.NET"
)

foreach ($path in $pathsToOpen) {
    if (Test-Path $path) {
        Write-Host "Opening permissions for $path..." -ForegroundColor Yellow
        
        # إعطاء صلاحيات كاملة للجميع
        icacls $path /grant "Everyone:(OI)(CI)F" /T >$null 2>&1
        icacls $path /grant "Users:(OI)(CI)F" /T >$null 2>&1
        icacls $path /grant "IIS_IUSRS:(OI)(CI)F" /T >$null 2>&1
        icacls $path /grant "IUSR:(OI)(CI)F" /T >$null 2>&1
        icacls $path /grant "Authenticated Users:(OI)(CI)F" /T >$null 2>&1
        
        Write-Host "✅ Full permissions granted for $path" -ForegroundColor Green
    }
}

# الخطوة 7: تعطيل Windows Defender مؤقتاً
Write-Host "`n=== Step 7: Temporarily Disabling Windows Defender ===" -ForegroundColor Cyan

try {
    # تعطيل Real-time protection
    Set-MpPreference -DisableRealtimeMonitoring $true -ErrorAction SilentlyContinue
    Write-Host "✅ Windows Defender real-time protection disabled" -ForegroundColor Green
    
    # إضافة استثناءات
    Add-MpPreference -ExclusionPath "C:\inetpub" -ErrorAction SilentlyContinue
    Add-MpPreference -ExclusionPath "C:\Windows\Microsoft.NET" -ErrorAction SilentlyContinue
    Write-Host "✅ Defender exclusions added" -ForegroundColor Green
    
} catch {
    Write-Host "⚠️ Could not modify Windows Defender settings" -ForegroundColor Yellow
}

# الخطوة 8: إعادة تشغيل IIS مع إعدادات مفتوحة
Write-Host "`n=== Step 8: Restarting IIS with Open Settings ===" -ForegroundColor Cyan

try {
    # إيقاف IIS
    iisreset /stop >$null 2>&1
    Start-Sleep -Seconds 2
    
    # بدء IIS مع إعدادات مفتوحة
    iisreset /start >$null 2>&1
    Start-Sleep -Seconds 3
    
    Write-Host "✅ IIS restarted with open security settings" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Please restart IIS manually" -ForegroundColor Yellow
}

# النتائج النهائية
Write-Host "`n" + "="*60 -ForegroundColor Red
Write-Host "SECURITY RESTRICTIONS REMOVED" -ForegroundColor Red
Write-Host "="*60 -ForegroundColor Red

Write-Host "`n⚠️ WARNING: Security has been temporarily disabled!" -ForegroundColor Red
Write-Host "`n🔓 What has been disabled:" -ForegroundColor Yellow
Write-Host "  ❌ SSL/HTTPS requirements" -ForegroundColor Red
Write-Host "  ❌ Authentication requirements" -ForegroundColor Red
Write-Host "  ❌ Certificate validation" -ForegroundColor Red
Write-Host "  ❌ Windows Firewall" -ForegroundColor Red
Write-Host "  ❌ Windows Defender real-time protection" -ForegroundColor Red
Write-Host "  ❌ UAC restrictions" -ForegroundColor Red
Write-Host "  ❌ File permission restrictions" -ForegroundColor Red

Write-Host "`n✅ System should now run without security restrictions" -ForegroundColor Green
Write-Host "`n🌐 Test your applications now:" -ForegroundColor Yellow
Write-Host "  • Portal: http://localhost/portal" -ForegroundColor Cyan
Write-Host "  • API: http://localhost/api" -ForegroundColor Cyan
Write-Host "  • Client: http://localhost/client" -ForegroundColor Cyan
Write-Host "  • API New: http://localhost/apinewAN" -ForegroundColor Cyan

Write-Host "`n⚠️ IMPORTANT: Re-enable security after testing!" -ForegroundColor Red
Write-Host "Run 'RestoreSecuritySettings.ps1' when done testing" -ForegroundColor Yellow

Write-Host "`n🔓 All security restrictions have been temporarily removed! 🔓" -ForegroundColor Red
