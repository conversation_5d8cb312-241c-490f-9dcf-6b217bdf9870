﻿@{
    Layout = "~/Views/Shared/_CrudLayout.cshtml";
}
@*using (Ajax.BeginForm("Find",new AjaxOptions
{
//    OnBegin = "return OnFormBegin()",
  //  OnSuccess = "onCrudSuccess",
   // OnFailure = "onCrudFailure",
   // LoadingElementId = "formloader",
    UpdateTargetId = "list",
    InsertionMode = InsertionMode.ReplaceWith
}))
{

}*@
<div class="row" style="margin-top: 10px">

    <div class="form-group">
        @*<label class="control-label col-xs-12 col-sm-3 no-padding-right"> العملاء </label>*@

        <div class="col-md-12">
            <select id="id" name="id" class="select2" data-placeholder="بحث عن عميل..."></select>
        </div>

    </div>

</div>
<script>
    $(function() {
        i('')
        AjaxCall('/Client/Client/GetClients').done(function(response) {
            if (response.length > 0) {
                $('#id').html('');
                var options = '';
                for (var i = 0; i < response.length; i++) {
                    options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
                }
                $('#id').append(options);

            }
        }).fail(function (xhr, textStatus, errorThrown) {
            parseAndShowError(xhr, textStatus, errorThrown);
            
        });


        //$('#find').on('click',
        $('#id').on('change',
            function() {
                i('onclick find');
                var id = $('#id').val();
                i('id ' + id);

                var data = { id: id };
                showLoading();
                AjaxCall('/Client/Client/Findy', data).done(function(response) {
                    hideLoading();
                    if (response.length > 0) {
                        $("#list").replaceWith(response);
                        var pager = Patterns.Art.Pager;
                        pager.activateList();
                    }
                }).fail(function (xhr, textStatus, errorThrown) {
                    hideLoading();
                    parseAndShowError(xhr, textStatus, errorThrown);
                  

                });
            });
        //select2
        $('.select2').css('width', '200px').select2({ allowClear: true });
        $('#select2-multiple-style .btn').on('click',
            function(e) {
                var target = $(this).find('input[type=radio]');
                var which = parseInt(target.val());
                if (which == 2) $('.select2').addClass('tag-input-style');
                else $('.select2').removeClass('tag-input-style');
            });

    });

</script>
@*<script>


    function OnFormBegin(context) {
        return true;
    }

    function onCrudSuccess(data) {

    }

    function onCrudFailure(xhr, textStatus, errorThrown) {


        hideFormLoading();
        i('textStatus ' + textStatus);
        i('errorThrown ' + errorThrown);

        var msg = parseXhr(xhr);
        log('onCrudFailure xhr msg:' + msg);
        alert(msg);
    }
    //try {
    ////   ace.data = new ace.data_storage();
    //    var name = ace.data.get('namez');
    //    if (name===null) {
    //        alert('null');

    //    }
    //    else
    //        alert(name);

    //} catch (e) {
    //    alert(e);
    //} 
</script>*@