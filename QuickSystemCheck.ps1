# Quick System Check Script
# سكريبت فحص سريع للأنظمة

Write-Host @"
╔══════════════════════════════════════════════════════════════╗
║                    Quick System Check                       ║
║                   فحص سريع للأنظمة                         ║
╚══════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Green

Write-Host "`n$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray

# فحص الخدمات
Write-Host "`n=== Services Status ===" -ForegroundColor Yellow
$services = @("W3SVC", "MSSQLSERVER", "MySQL")
foreach ($service in $services) {
    $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
    if ($svc) {
        $status = if ($svc.Status -eq "Running") { "✅" } else { "❌" }
        Write-Host "$status $service`: $($svc.Status)" -ForegroundColor White
    } else {
        Write-Host "❓ $service`: Not Found" -ForegroundColor Gray
    }
}

# فحص العمليات
Write-Host "`n=== Processes ===" -ForegroundColor Yellow
$processes = @("httpd", "mysqld", "w3wp")
foreach ($proc in $processes) {
    $process = Get-Process -Name $proc -ErrorAction SilentlyContinue
    if ($process) {
        Write-Host "✅ $proc`: Running (PID: $($process.Id -join ', '))" -ForegroundColor Green
    } else {
        Write-Host "❌ $proc`: Not Running" -ForegroundColor Red
    }
}

# فحص المنافذ
Write-Host "`n=== Ports ===" -ForegroundColor Yellow
$ports = @("80", "443", "3306", "5700", "8080")
foreach ($port in $ports) {
    $portCheck = netstat -an | findstr ":$port.*LISTENING"
    if ($portCheck) {
        Write-Host "✅ Port $port`: Active" -ForegroundColor Green
    } else {
        Write-Host "❌ Port $port`: Inactive" -ForegroundColor Red
    }
}

# اختبار التطبيقات
Write-Host "`n=== Applications Test ===" -ForegroundColor Yellow
$apps = @(
    @{ Name = "AppTech Portal"; URL = "http://localhost/portal" },
    @{ Name = "AppTech API"; URL = "http://localhost/api" },
    @{ Name = "Collections System"; URL = "http://localhost/collections_system" }
)

foreach ($app in $apps) {
    try {
        $response = Invoke-WebRequest -Uri $app.URL -TimeoutSec 3 -ErrorAction Stop
        Write-Host "✅ $($app.Name)`: Accessible" -ForegroundColor Green
    } catch {
        if ($_.Exception.Message -match "500") {
            Write-Host "⚠️ $($app.Name)`: Server Error (DB needed)" -ForegroundColor Yellow
        } elseif ($_.Exception.Message -match "403") {
            Write-Host "⚠️ $($app.Name)`: Access Forbidden" -ForegroundColor Yellow
        } else {
            Write-Host "❌ $($app.Name)`: Not Accessible" -ForegroundColor Red
        }
    }
}

# ملخص سريع
Write-Host "`n=== Quick Summary ===" -ForegroundColor Cyan
Write-Host "🌐 Web Server (IIS/Apache)`: " -NoNewline
if (Get-Process -Name "httpd" -ErrorAction SilentlyContinue) {
    Write-Host "Running" -ForegroundColor Green
} else {
    Write-Host "Stopped" -ForegroundColor Red
}

Write-Host "🗄️ Database (MySQL)`: " -NoNewline
if (Get-Process -Name "mysqld" -ErrorAction SilentlyContinue) {
    Write-Host "Running" -ForegroundColor Green
} else {
    Write-Host "Stopped" -ForegroundColor Red
}

Write-Host "🔧 AppTech System`: " -NoNewline
try {
    Invoke-WebRequest -Uri "http://localhost/portal" -TimeoutSec 2 -ErrorAction Stop | Out-Null
    Write-Host "Accessible" -ForegroundColor Green
} catch {
    if ($_.Exception.Message -match "500") {
        Write-Host "Needs Database" -ForegroundColor Yellow
    } else {
        Write-Host "Not Working" -ForegroundColor Red
    }
}

Write-Host "`n📋 For detailed report, see: SystemStatus_Final.md" -ForegroundColor Gray
Write-Host "🛠️ For tools, go to: C:\inetpub\DeobfuscationTools\" -ForegroundColor Gray

Write-Host "`n✨ Quick check completed! ✨" -ForegroundColor Green
