﻿

function refreshData(data) {
    $("#list").replaceWith(data);
}

function PrintReceipt(title,data) {
    localStorage["REPORT_title"] = title;
    localStorage["REPORT"] = data;
     window.open("/Print/PrintDialog");
}
function print(id) {

    var data = { id: id };
    var title = "";
    var controller = $("#Controller").val();
    i('print record controller: ' + controller);
    var url = "/" + controller + "/" + "Print";
    try {
        $.ajax({
            url: url,
            data: data,
            success: function (response) {
                var result = response;
                if (result !== undefined) {

                    localStorage["REPORT_title"] = title;
                    localStorage["REPORT"] = result;
                     window.open("/Print/PrintDialog");
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                alert(xhr.responseText);
            }
        });

    } catch (err) {
        alert(err);
    }
}
function printPath(id,path) {

    var data = { id: id };
    var title = "";
    var controller = $("#Controller").val();
    i('print record controller: ' + controller);
    var url = "/" + controller + "/" + path;
    try {
        $.ajax({
            url: url,
            data: data,
            success: function (response) {
                var result = response;
                i('print path response ' + result);
                if (result !== undefined) {

                    localStorage["REPORT_title"] = title;
                    localStorage["REPORT"] = result;
                    i('print path open PrintDialog ');
                    window.open("/Print/PrintDialog");
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                alert(xhr.responseText);
            }
        });

    } catch (err) {
        alert(err);
    }
}
//end Crud
function showSecondInput(self) {

    var selectedField = self.options[self.selectedIndex].text;
    if (selectedField === "خلال فترة") {
        $("#end_date").show();
    } else {

        $("#end_date").hide();
    }
}


$("#report-form").submit(function (e) {
    alert("report-form");
    showLoading();
    var $table = $("#simple-table");
    try {
        $table.bootstrapTable("showLoading");
    } catch (e) {
        alert("cant show loading " + e);
    }

    var form = $(this);

    var url = "";
    alert("url" + url);
    $.ajax({
        type: "POST",
        url: url,
        data: form.serialize(),
        success: function (data) {
            hideLoading();
            console.log("success: " + data);
            $table.bootstrapTable("hideLoading");
            if (!data.startsWith("error:")) {
                $("#list").replaceWith(data);
            } else {
                showError(data);
            }
        }
    });

    e.preventDefault(); // avoid to execute the actual submit of the form.
});

function printGrid() {
    i("print report");
    var title = $("#Title").val();
    var listDiv = document.getElementById("list");
    localStorage["REPORT_title"] = title;
    localStorage["REPORT"] = listDiv.innerHTML;
   window.open("/Print/PrintDialog");

}

