﻿@model AppTech.MSMS.Web.Areas.Remittance.Models.RemittanceReceiptModel
<style>
    noborde { border-collapse: collapse; }

    td { border: none; }


    .rightpane {
        width: 70%;
        float: right;
        border-collapse: collapse;
        border: 1px solid black;
    }

    .leftpane {
        width: 30%;
        height: 29%;
        float: left;
        border-collapse: collapse;
        border: 1px solid black;
        padding-top: 60px;
    }
   


    .toppane {
        width: 100%;
        height: 100px;
        border-collapse: collapse;
        background-color: #4da6ff;
    }
</style>
<div class="">
    <div class="">
        <div style="border: 1px solid black;">

            <div class="col-sm-6 pull-right" style="margin-top: 10px">
                التاريخ : @DateTime.Now.ToString("yyyy/MM/dd")
            </div>


            <div class="col-sm-6 align-left" style="margin-top: 10px">
                رقم الحوالة : @Html.DisplayFor(model => model.RemittanceNumber)
            </div>

            <div class="col-sm-12 align-center" style="border-bottom: 1px solid black; margin-top: 1px; margin-bottom: 20px; width: 100%; padding: 8px 0px; height: 30px; font: bold 18px Helvetica, Sans-Serif;">
                @Html.DisplayFor(model => model.Title)
            </div>
        </div>


        <div class="rightpane">
            <div >

                <table id="" class="table noborde">

                    <tr>
                        <td>
                            <strong> انا الموقع أدناه: </strong>
                            @Html.DisplayFor(model => model.BenficiaryName)
                        </td>

                    </tr>
                    <tr>
                        <td class=" col-sm-12">
                            <strong> استلمت مبلغا وقدره: </strong>
                            @Html.DisplayFor(model => model.AmountInText)

                        </td>

                    </tr>

                    <tr>
                        <td>
                            <strong> المحولة لي من المرسل :</strong>

                            @Html.DisplayFor(model => model.SenderName)
                        </td>

                    </tr>

                    <tr>
                        <td>
                            <strong> عن طريق:</strong>

                            @Html.DisplayFor(model => model.TargetName)
                            <strong> تاريخ الحوالة :</strong>

                            @Html.DisplayFor(model => model.Date)
                        </td>

                    </tr>
                    <tr>
                        <td>
                            <strong> ملاحظات : </strong>

                            @Html.DisplayFor(model => model.Note)
                        </td>

                    </tr>
                </table>

                <table class="table">


                    <tr>
                        <td class=" col-sm-4">
                            اسم المستلم
                        </td>
                        <td class=" col-sm-8">
                            .....................................................
                        </td>
                    </tr>
                    <tr>
                        <td class=" col-sm-4">
                            هاتف المستلم
                        </td>
                        <td class=" col-sm-8">
                            .....................................................
                        </td>
                    </tr>

                    <tr>
                        <td class=" col-sm-4">
                            التوقيع
                        </td>
                        <td class=" col-sm-8">
                            .....................................................
                        </td>
                    </tr>

                </table>

                <span>استلمت المبلغ المذكور اعلاه كاملا وفي حالة جيدة</span>
            </div>
        </div>
        <div class="leftpane">
            @if (!string.IsNullOrEmpty(Model.BenficiaryCard))
            {
                <img class="img-thumbnail center-block bottom" width="150" height="150" id="preview"
                     src="@Url.Content(Model.BenficiaryCard)"/>
            }
        </div>
    </div>
</div>