﻿@model AppTech.MSMS.Domain.Models.DepositOrder

@{
    Layout = "~/Views/Shared/_MultiLayout.cshtml";
}


<input type="hidden" name="Device" value="Web"/>
<input type="hidden" name="ServiceID" value="@ViewBag.ServiceID"/>


<div class="form-group form-inline">
    @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Amount, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label no-padding-right" for="form-field-currency">العملة</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.CurrencyID, new[]
        {
            new SelectListItem {Text = "يمني", Value = "1"},
            new SelectListItem {Text = "دولار", Value = "2"},
            new SelectListItem {Text = "سعودي", Value = "3"}
        })


    </div>
    @Html.ValidationMessageFor(model => model.CurrencyID)
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note)
        @Html.ValidationMessageFor(model => model.Note)
    </div>
</div>


<div class="space-8"></div>

<div class="form-group">
    <label class="col-sm-2 control-label">صوره الأشعار</label>
    <div class="col-sm-10">
        <div style="position: relative;">
            <input type="file" name="ImageData" size="40" onchange="showImg(this)">
        </div>

        <div>
            <img class="img-thumbnail" id="preview" height="300" width="300" style="border: solid"/>
        </div>
    </div>
</div>
@*<div class="form-group">
    @Html.LabelFor(model => model.ImageName, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <input type="file"  name="ImageName"/>
    </div>
</div>*@

<script type="text/javascript">

    function show(input) {
        if (input.files && input.files[0]) {
            var filerdr = new FileReader();
            filerdr.onload = function(e) {
                $('#user_img').attr('src', e.target.result);
            };
            filerdr.readAsDataURL(input.files[0]);
        }
    }
</script>