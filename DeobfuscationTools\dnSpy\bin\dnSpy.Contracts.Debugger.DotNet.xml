<?xml version="1.0"?>
<doc>
    <assembly>
        <name>dnSpy.Contracts.Debugger.DotNet</name>
    </assembly>
    <members>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointFactory">
            <summary>
            Creates breakpoints and tracepoints
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointFactory.Create(dnSpy.Contracts.Metadata.ModuleId,System.UInt32,System.UInt32)">
            <summary>
            Creates an enabled breakpoint. If there's already a breakpoint at the location, null is returned.
            </summary>
            <param name="module">Module</param>
            <param name="token">Token of a method within the module</param>
            <param name="offset">IL offset of the breakpoint within the method body</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointFactory.CreateTracepoint(dnSpy.Contracts.Metadata.ModuleId,System.UInt32,System.UInt32,System.String)">
            <summary>
            Creates an enabled tracepoint. If there's already a breakpoint at the location, null is returned.
            </summary>
            <param name="module">Module</param>
            <param name="token">Token of a method within the module</param>
            <param name="offset">IL offset of the tracepoint within the method body</param>
            <param name="message">Message</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointFactory.Create(dnSpy.Contracts.Metadata.ModuleId,System.UInt32,System.UInt32,dnSpy.Contracts.Debugger.Breakpoints.Code.DbgCodeBreakpointSettings)">
            <summary>
            Creates a breakpoint or a tracepoint. If there's already a breakpoint at the location, null is returned.
            </summary>
            <param name="module">Module</param>
            <param name="token">Token of a method within the module</param>
            <param name="offset">IL offset of the breakpoint within the method body</param>
            <param name="settings">Breakpoint settings</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointFactory.Create(dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointInfo[])">
            <summary>
            Creates breakpoints or tracepoints. Duplicate breakpoints are ignored.
            </summary>
            <param name="breakpoints">Breakpoint infos</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointFactory.TryGetBreakpoint(dnSpy.Contracts.Metadata.ModuleId,System.UInt32,System.UInt32)">
            <summary>
            Returns an existing breakpoint or null if none exists
            </summary>
            <param name="module">Module</param>
            <param name="token">Token of a method within the module</param>
            <param name="offset">IL offset of the breakpoint within the method body</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointInfo">
            <summary>
            Contains all required data to create a breakpoint
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointInfo.Module">
            <summary>
            Module
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointInfo.Token">
            <summary>
            Token of a method within the module
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointInfo.Offset">
            <summary>
            IL offset of the breakpoint within the method body
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointInfo.Settings">
            <summary>
            Breakpoint settings
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Breakpoints.Code.DbgDotNetBreakpointInfo.#ctor(dnSpy.Contracts.Metadata.ModuleId,System.UInt32,System.UInt32,dnSpy.Contracts.Debugger.Breakpoints.Code.DbgCodeBreakpointSettings)">
            <summary>
            Constructor
            </summary>
            <param name="module">Module</param>
            <param name="token">Token of a method within the module</param>
            <param name="offset">IL offset of the breakpoint within the method body</param>
            <param name="settings">Breakpoint settings</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncMethodDebugInfo">
            <summary>
            Asynchronous method debug info
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncMethodDebugInfo.StepInfos">
            <summary>
            Async step infos
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncMethodDebugInfo.BuilderField">
            <summary>
            Async method builder field or null
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncMethodDebugInfo.CatchHandlerOffset">
            <summary>
            Catch handler offset or <see cref="F:System.UInt32.MaxValue"/>. Only used if it's an async void method
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncMethodDebugInfo.SetResultOffset">
            <summary>
            Offset of SetResult() call, or <see cref="F:System.UInt32.MaxValue"/> if it's unknown
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncMethodDebugInfo.#ctor(dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncStepInfo[],dnlib.DotNet.FieldDef,System.UInt32,System.UInt32)">
            <summary>
            Constructor
            </summary>
            <param name="stepInfos">Async step infos</param>
            <param name="builderField">Async method builder field or null if it's unknown</param>
            <param name="catchHandlerOffset">Catch handler offset or <see cref="F:System.UInt32.MaxValue"/>. Only used if it's a async void method</param>
            <param name="setResultOffset">Offset of SetResult() call, or <see cref="F:System.UInt32.MaxValue"/> if it's unknown</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncStepInfo">
            <summary>
            Async method step info
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncStepInfo.YieldOffset">
            <summary>
            Offset in method where it starts waiting for the result
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncStepInfo.ResumeMethod">
            <summary>
            Resume method
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncStepInfo.ResumeOffset">
            <summary>
            Offset in <see cref="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncStepInfo.ResumeMethod"/> where it resumes after the result is available
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncStepInfo.#ctor(System.UInt32,dnlib.DotNet.MethodDef,System.UInt32)">
            <summary>
            Constructor
            </summary>
            <param name="yieldOffset">Offset in <see cref="M:System.Runtime.CompilerServices.IAsyncStateMachine.MoveNext"/> where it starts waiting for the result</param>
            <param name="resumeMethod">Resume method</param>
            <param name="resumeOffset">Offset in <paramref name="resumeMethod"/> where it resumes after the result is available</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgCodeRange">
            <summary>
            Code range
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgCodeRange.Start">
            <summary>
            Gets the start offset relative to the start of the method
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgCodeRange.End">
            <summary>
            Gets the end method offset (exclusive) relative to the start of the method
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgCodeRange.Length">
            <summary>
            Gets the length of the range
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgCodeRange.#ctor(System.UInt32,System.UInt32)">
            <summary>
            Constructor
            </summary>
            <param name="start">Start offset relative to the start of the method</param>
            <param name="end">End method offset (exclusive) relative to the start of the method</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgCodeRange.Contains(System.UInt32)">
            <summary>
            Checks whether <paramref name="offset"/> is within this range
            </summary>
            <param name="offset">Offset</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgCompilerKind">
            <summary>
            Compilers
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgCompilerKind.Unknown">
            <summary>
            Unknown compiler
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgCompilerKind.MicrosoftCSharp">
            <summary>
            Microsoft C# compiler
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgCompilerKind.MicrosoftVisualBasic">
            <summary>
            Microsoft Visual Basic compiler
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgCompilerKind.MonoCSharp">
            <summary>
            Mono C# compiler
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocation">
            <summary>
            .NET code location
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocation.Module">
            <summary>
            Gets the module
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocation.Token">
            <summary>
            Gets the token of a method within the module
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocation.Offset">
            <summary>
            Gets the IL offset within the method body
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocation.ILOffsetMapping">
            <summary>
            Gets the IL offset mapping
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocation.DbgModule">
            <summary>
            Gets the debugger module or null
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocation.NativeAddress">
            <summary>
            Gets the native address
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocationFactory">
            <summary>
            Creates <see cref="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocation"/> instances
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocationFactory.Create(dnSpy.Contracts.Metadata.ModuleId,System.UInt32,System.UInt32)">
            <summary>
            Creates a new <see cref="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocation"/> instance
            </summary>
            <param name="module">Module</param>
            <param name="token">Token of a method within the module</param>
            <param name="offset">IL offset of the location within the method body</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocationFactory.Create(dnSpy.Contracts.Metadata.ModuleId,System.UInt32,System.UInt32,dnSpy.Contracts.Debugger.DotNet.Code.DbgILOffsetMapping)">
            <summary>
            Creates a new <see cref="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetCodeLocation"/> instance
            </summary>
            <param name="module">Module</param>
            <param name="token">Token of a method within the module</param>
            <param name="offset">IL offset of the location within the method body</param>
            <param name="ilOffsetMapping">IL offset mapping</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetDecompilerGuidProvider">
            <summary>
            Converts <see cref="T:dnSpy.Contracts.Debugger.Evaluation.DbgLanguage"/>s to decompiler GUIDs. Use <see cref="T:dnSpy.Contracts.Debugger.DotNet.Code.ExportDbgDotNetDecompilerGuidProviderAttribute"/>
            to export an instance
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetDecompilerGuidProvider.GetDecompilerGuid(dnSpy.Contracts.Debugger.Evaluation.DbgLanguage)">
            <summary>
            Gets the decompiler GUID or null
            </summary>
            <param name="language">Language</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.IDbgDotNetDecompilerGuidProviderMetadata">
            <summary>Metadata</summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.IDbgDotNetDecompilerGuidProviderMetadata.Order">
            <summary>See <see cref="P:dnSpy.Contracts.Debugger.DotNet.Code.ExportDbgDotNetDecompilerGuidProviderAttribute.Order"/></summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.ExportDbgDotNetDecompilerGuidProviderAttribute">
            <summary>
            Exports a <see cref="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetDecompilerGuidProvider"/> instance
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.ExportDbgDotNetDecompilerGuidProviderAttribute.#ctor(System.Double)">
            <summary>
            Constructor
            </summary>
            <param name="order">Order</param>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.ExportDbgDotNetDecompilerGuidProviderAttribute.Order">
            <summary>
            Order
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetDecompilerService">
            <summary>
            Returns the decompiler that should be used by code that needs to use a decompiler to format methods.
            The decompiler gets updated when the <see cref="T:dnSpy.Contracts.Debugger.Evaluation.DbgLanguage"/> gets changed.
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetDecompilerService.Decompiler">
            <summary>
            Gets the decompiler
            </summary>
        </member>
        <member name="E:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetDecompilerService.DecompilerChanged">
            <summary>
            Raised after <see cref="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetDecompilerService.Decompiler"/> is changed
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetInstructionOffsetConstants">
            <summary>
            Constants
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetInstructionOffsetConstants.EPILOG">
            <summary>
            The offset is in an epilog
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetInstructionOffsetConstants.PROLOG">
            <summary>
            The offset is in the prolog
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgILInstruction">
            <summary>
            IL instruction
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgILInstruction.Offset">
            <summary>
            Gets the offset
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgILInstruction.OpCode">
            <summary>
            Gets the opcode, <c>0x00XX</c> or <c>0xFEXX</c>
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgILInstruction.Operand">
            <summary>
            Gets the operand
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgILInstruction.#ctor(System.UInt32,System.UInt16,System.UInt32)">
            <summary>
            Constructor
            </summary>
            <param name="offset">Offset of instruction</param>
            <param name="opCode">IL opcode</param>
            <param name="operand">Integer operand</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgILInstruction.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgILOffsetMapping">
            <summary>
            IL offset mapping result. This enum is similar to <c>CorDebugMappingResult</c>
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgILOffsetMapping.Unknown">
            <summary>
            Unknown
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgILOffsetMapping.Prolog">
            <summary>
            The native code is in the prolog
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgILOffsetMapping.Epilog">
            <summary>
            The native code is in an epilog
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgILOffsetMapping.Exact">
            <summary>
            Either the method maps exactly to MSIL code or the frame has been interpreted, so the value of the IP is accurate
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgILOffsetMapping.Approximate">
            <summary>
            The method was successfully mapped, but the value of the IP may be approximate
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgILOffsetMapping.NoInfo">
            <summary>
            No mapping information is available for the method
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgILOffsetMapping.UnmappedAddress">
            <summary>
            Although there is mapping information for the method, the current address cannot be mapped to Microsoft intermediate language (MSIL) code
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan">
            <summary>
            IL span
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.Start">
            <summary>
            Start offset
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.End">
            <summary>
            End offset, exclusive
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.Length">
            <summary>
            Length (<see cref="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.End"/> - <see cref="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.Start"/>)
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.IsEmpty">
            <summary>
            true if it's empty (<see cref="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.Length"/> is 0)
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.#ctor(System.UInt32,System.UInt32)">
            <summary>
            Constructor
            </summary>
            <param name="start">Start offset</param>
            <param name="length">Length</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.FromBounds(System.UInt32,System.UInt32)">
            <summary>
            Creates a new instance
            </summary>
            <param name="start">Start offset</param>
            <param name="end">End offset</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.op_Equality(dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan,dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan)">
            <summary>
            operator ==()
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.op_Inequality(dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan,dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan)">
            <summary>
            operator !=()
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.Equals(dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgLocal">
            <summary>
            Method local info
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgLocal.Index">
            <summary>
            Gets the local index or &lt; 0 if it's not in the metadata
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgLocal.Name">
            <summary>
            Gets the name of the local
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgLocal.HoistedField">
            <summary>
            Gets the hoisted field or null if it's not a hoisted local
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgLocal.Flags">
            <summary>
            Gets the flags
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgLocal.IsDecompilerGenerated">
            <summary>
            true if this is a decompiler generated local
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgLocal.IsDebuggerHidden">
            <summary>
            true if this is a debugger hidden local
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgLocal.#ctor(System.Int32,System.String,dnlib.DotNet.FieldDef,dnSpy.Contracts.Debugger.DotNet.Code.DbgLocalFlags)">
            <summary>
            Constructor
            </summary>
            <param name="index">Index of local or &lt; 0 if it's not in the metadata</param>
            <param name="name">Name of the local</param>
            <param name="hoistedField">Hoisted field or null if it's not a hoisted local/parameter</param>
            <param name="flags">Local flags</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgLocalFlags">
            <summary>
            Locals flags
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgLocalFlags.None">
            <summary>
            No bit is set
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgLocalFlags.DecompilerGenerated">
            <summary>
            Decompiler generated local
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgLocalFlags.DebuggerHidden">
            <summary>
            Debugger hidden local
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo">
            <summary>
            Method debug info
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.Compiler">
            <summary>
            Compiler used to compile the code
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.DebugInfoVersion">
            <summary>
            Version number of this method debug info. If it gets incremented, any older instances with a different
            version should not be used again.
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.Method">
            <summary>
            Gets the method
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.Parameters">
            <summary>
            Gets the parameters. There could be missing parameters, in which case use <see cref="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.Method"/>. This array isn't sorted.
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.Statements">
            <summary>
            Gets all statements, sorted by <see cref="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan.Start"/>
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.AsyncInfo">
            <summary>
            Gets the async method debug info or null if it's not an async method
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.Scope">
            <summary>
            Gets the root scope
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.#ctor(dnSpy.Contracts.Debugger.DotNet.Code.DbgCompilerKind,System.Int32,dnlib.DotNet.MethodDef,dnSpy.Contracts.Debugger.DotNet.Code.DbgParameter[],dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement[],dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugScope,dnSpy.Contracts.Debugger.DotNet.Code.DbgAsyncMethodDebugInfo)">
            <summary>
            Constructor
            </summary>
            <param name="compiler">Compiler</param>
            <param name="debugInfoVersion">Debug info version</param>
            <param name="method">Method</param>
            <param name="parameters">Parameters or null</param>
            <param name="statements">Statements</param>
            <param name="scope">Root scope</param>
            <param name="asyncMethodDebugInfo">Async info or null</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.GetRanges(dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan[])">
            <summary>
            Gets step ranges
            </summary>
            <param name="sourceILSpans">Source statement spans</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.GetUnusedRanges">
            <summary>
            Gets unused step ranges
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.GetSourceStatementByCodeOffset(System.UInt32)">
            <summary>
            Gets a <see cref="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement"/>
            </summary>
            <param name="ilOffset">IL offset</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo.GetILSpansOfStatement(dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan)">
            <summary>
            Gets all ILSpans of a statement
            </summary>
            <param name="statementSpan">Statement span</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugScope">
            <summary>
            Method scope
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugScope.Span">
            <summary>
            Gets the span of this scope
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugScope.Scopes">
            <summary>
            Gets all child scopes
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugScope.Locals">
            <summary>
            Gets all new locals in the scope
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugScope.Imports">
            <summary>
            Gets all new imports in the scope
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugScope.#ctor(dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan,dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugScope[],dnSpy.Contracts.Debugger.DotNet.Code.DbgLocal[],dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfo[])">
            <summary>
            Constructor
            </summary>
            <param name="span">Scope span</param>
            <param name="scopes">Child scopes</param>
            <param name="locals">Locals</param>
            <param name="imports">Imports</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfoKind">
            <summary>
            Import kind
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfoKind.Namespace">
            <summary>
            Namespace import
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfoKind.Type">
            <summary>
            Type import
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfoKind.NamespaceOrType">
            <summary>
            Namespace or type import
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfoKind.Assembly">
            <summary>
            C#: extern alias
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfoKind.XmlNamespace">
            <summary>
            VB: XML import
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfoKind.MethodToken">
            <summary>
            VB: token of method with imports
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfoKind.CurrentNamespace">
            <summary>
            VB: containing namespace
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfoKind.DefaultNamespace">
            <summary>
            VB: root namespace
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgVBImportScopeKind">
            <summary>
            Visual Basic import scope kind
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgVBImportScopeKind.None">
            <summary>
            Unspecified scope
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgVBImportScopeKind.File">
            <summary>
            File scope
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgVBImportScopeKind.Project">
            <summary>
            Project scope
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfo">
            <summary>
            Import info
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfo.TargetKind">
            <summary>
            Target kind
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfo.VBImportScopeKind">
            <summary>
            Gets the VB import scope kind
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfo.Target">
            <summary>
            Target
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfo.Alias">
            <summary>
            Alias
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfo.ExternAlias">
            <summary>
            Extern alias
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfo.#ctor(dnSpy.Contracts.Debugger.DotNet.Code.DbgImportInfoKind,System.String,System.String,System.String,dnSpy.Contracts.Debugger.DotNet.Code.DbgVBImportScopeKind)">
            <summary>
            Constructor
            </summary>
            <param name="targetKind">Target kind</param>
            <param name="target">Target string</param>
            <param name="alias">Alias</param>
            <param name="externAlias">Extern alias</param>
            <param name="importScopeKind">VB import scope kind</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgParameter">
            <summary>
            Method parameter info
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgParameter.Index">
            <summary>
            Gets the parameter index
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgParameter.Name">
            <summary>
            Gets the parameter name
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgParameter.#ctor(System.Int32,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="index">Parameter index</param>
            <param name="name">Parameter name</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement">
            <summary>
            Source statement
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement.ILSpan">
            <summary>
            IL span
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement.TextSpan">
            <summary>
            Text span
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement.#ctor(dnSpy.Contracts.Debugger.DotNet.Code.DbgILSpan,dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan)">
            <summary>
            Constructor
            </summary>
            <param name="ilSpan">IL span</param>
            <param name="textSpan">Text span</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement.op_Equality(dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement,dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement)">
            <summary>
            operator ==()
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement.op_Inequality(dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement,dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement)">
            <summary>
            operator !=()
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement.Equals(dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgSourceStatement.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan">
            <summary>
            Text span
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.Start">
            <summary>
            Start offset
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.End">
            <summary>
            End offset, exclusive
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.Length">
            <summary>
            Length (<see cref="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.End"/> - <see cref="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.Start"/>)
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.IsEmpty">
            <summary>
            true if it's empty (<see cref="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.Length"/> is 0)
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.#ctor(System.Int32,System.Int32)">
            <summary>
            Constructor
            </summary>
            <param name="start">Start offset</param>
            <param name="length">Length</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.FromBounds(System.Int32,System.Int32)">
            <summary>
            Creates a new instance
            </summary>
            <param name="start">Start offset</param>
            <param name="end">End offset</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.op_Equality(dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan,dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan)">
            <summary>
            operator ==()
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.op_Inequality(dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan,dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan)">
            <summary>
            operator !=()
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.Equals(dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgTextSpan.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.IDbgDotNetCodeLocation">
            <summary>
            .NET code location
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.IDbgDotNetCodeLocation.Module">
            <summary>
            Gets the module
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.IDbgDotNetCodeLocation.Token">
            <summary>
            Gets the token of a method within the module
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.IDbgDotNetCodeLocation.Offset">
            <summary>
            Gets the IL offset within the method body
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.IDbgDotNetCodeLocation.ILOffsetMapping">
            <summary>
            Gets the IL offset mapping
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.IDbgDotNetCodeLocation.DbgModule">
            <summary>
            Gets the debugger module or null
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.IDbgDotNetCodeLocation.NativeAddress">
            <summary>
            Gets the native address
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetNativeFunctionAddress">
            <summary>
            Native address info
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetNativeFunctionAddress.None">
            <summary>
            No address
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetNativeFunctionAddress.Address">
            <summary>
            Gets the address or 0 if it's not available
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetNativeFunctionAddress.Offset">
            <summary>
            Gets the offset relative to <see cref="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetNativeFunctionAddress.Address"/>
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetNativeFunctionAddress.IP">
            <summary>
            Gets the instruction pointer
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Code.DbgDotNetNativeFunctionAddress.#ctor(System.UInt64,System.UInt64)">
            <summary>
            Constructor
            </summary>
            <param name="address">Address or 0 if it's not available</param>
            <param name="offset">Offset relative to <paramref name="address"/></param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.DbgDotNetInternalAppDomain">
            <summary>
            Base class of a .NET app domain object implemented by the .NET debug engine
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.DbgDotNetInternalAppDomain.ReflectionAppDomain">
            <summary>
            Gets the reflection app domain
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.DbgDotNetInternalModule">
            <summary>
            Base class of a .NET module object implemented by the .NET debug engine
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.DbgDotNetInternalModule.ReflectionModule">
            <summary>
            Gets the reflection module or null if this isn't a managed module
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.DbgDotNetInternalRuntime">
            <summary>
            Base class of a .NET runtime object implemented by the .NET debug engine. It must implement <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime"/>
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.DbgDotNetInternalRuntime.ReflectionRuntime">
            <summary>
            Gets the reflection runtime
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCode">
            <summary>
            Contains the code that will be disassembled
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCode.Kind">
            <summary>
            Gets the code kind
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCode.Optimization">
            <summary>
            Gets the optimization kind
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCode.Blocks">
            <summary>
            All blocks to disassemble
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCode.CodeInfo">
            <summary>
            Extra optional info, or null if none
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCode.MethodName">
            <summary>
            Method name or null
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCode.ShortMethodName">
            <summary>
            Short method name or null
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCode.ModuleName">
            <summary>
            Module name or null
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCode.#ctor(dnSpy.Contracts.Disassembly.NativeCodeKind,dnSpy.Contracts.Disassembly.NativeCodeOptimization,dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCodeBlock[],dnSpy.Contracts.Disassembly.NativeCodeInfo,System.String,System.String,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="kind">Code kind</param>
            <param name="optimization">Optimization kind</param>
            <param name="blocks">All blocks to disassemble</param>
            <param name="codeInfo">Extra code info or null</param>
            <param name="methodName">Method name or null</param>
            <param name="shortMethodName">Short method name or null</param>
            <param name="moduleName">Module name or null</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCodeBlock">
            <summary>
            A block of native code
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCodeBlock.Kind">
            <summary>
            Gets the kind
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCodeBlock.Address">
            <summary>
            Gets the address of the code
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCodeBlock.Code">
            <summary>
            Gets the raw code
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCodeBlock.ILOffset">
            <summary>
            IL offset or -1 if unknown
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCodeBlock.#ctor(dnSpy.Contracts.Disassembly.NativeCodeBlockKind,System.UInt64,System.ArraySegment{System.Byte},System.Int32)">
            <summary>
            Constructor
            </summary>
            <param name="kind">Code kind</param>
            <param name="address">Address of block</param>
            <param name="code">Raw code</param>
            <param name="ilOffset">IL offset or -1 if unknown</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetAliasInfo">
            <summary>
            Alias
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetAliasInfo.Kind">
            <summary>
            Alias kind
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetAliasInfo.Type">
            <summary>
            Alias type
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetAliasInfo.Id">
            <summary>
            Alias id
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetAliasInfo.CustomTypeInfo">
            <summary>
            Custom type info understood by the EE or null
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetAliasInfo.CustomTypeInfoId">
            <summary>
            Custom type info ID
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetAliasInfo.#ctor(dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetAliasInfoKind,dnSpy.Debugger.DotNet.Metadata.DmdType,System.UInt32,System.Guid,System.Collections.ObjectModel.ReadOnlyCollection{System.Byte})">
            <summary>
            Constructor
            </summary>
            <param name="kind">Alias kind</param>
            <param name="type">Alias type</param>
            <param name="id">Alias id</param>
            <param name="customTypeInfoId">Custom type info ID</param>
            <param name="customTypeInfo">Custom type info understood by the EE or null</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetAliasInfoKind">
            <summary>
            Alias kind
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetAliasInfoKind.Exception">
            <summary>
            An exception, eg. "$exception"
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetAliasInfoKind.StowedException">
            <summary>
            A stowed exception, eg. "$stowedexception"
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetAliasInfoKind.ReturnValue">
            <summary>
            A return value, eg. "$ReturnValue1"
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetCustomTypeInfo">
            <summary>
            Extra custom type info provided by the expression compiler and used by language formatters
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetCustomTypeInfo.CustomTypeInfoId">
            <summary>
            Gets the custom type info ID
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetCustomTypeInfo.CustomTypeInfo">
            <summary>
            Gets the custom type info
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetCustomTypeInfo.#ctor(System.Guid,System.Collections.ObjectModel.ReadOnlyCollection{System.Byte})">
            <summary>
            Constructor
            </summary>
            <param name="customTypeInfoId">Custom type info ID</param>
            <param name="customTypeInfo">Custom type info</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetDispatcher">
            <summary>
            Invokes code on another thread.
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetDispatcher.VerifyAccess">
            <summary>
            Throws if the current thread isn't the dispatcher thread
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetDispatcher.CheckAccess">
            <summary>
            Checks whether the current thread is the dispatcher thread
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetDispatcher.BeginInvoke(System.Action)">
            <summary>
            Executes code asynchronously on the dispatcher thread. This method returns immediately even if
            it happens to be called on the dispatcher thread.
            </summary>
            <param name="callback">Code to execute</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetDispatcher.TryBeginInvoke(System.Action)">
            <summary>
            Executes code asynchronously on the dispatcher thread. This method returns immediately even if
            it happens to be called on the dispatcher thread.
            </summary>
            <param name="callback">Code to execute</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetDispatcher.Invoke``1(System.Func{``0})">
            <summary>
            Executes code on the dispatcher thread
            </summary>
            <typeparam name="T">Return type</typeparam>
            <param name="callback">Code to execute</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetDispatcher.TryInvoke``1(System.Func{``0},``0@)">
            <summary>
            Executes code on the dispatcher thread
            </summary>
            <typeparam name="T">Return type</typeparam>
            <param name="callback">Code to execute</param>
            <param name="result">Result if successful</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetDispatcher.Invoke(System.Action)">
            <summary>
            Executes code on the dispatcher thread
            </summary>
            <param name="callback">Code to execute</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetDispatcher.TryInvoke(System.Action)">
            <summary>
            Executes code on the dispatcher thread
            </summary>
            <param name="callback">Code to execute</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetExceptionInfo">
            <summary>
            Exception info
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetExceptionInfo.Flags">
            <summary>
            Gets the flags
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetExceptionInfo.IsStowedException">
            <summary>
            true if it's a stowed exception
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetExceptionInfo.Id">
            <summary>
            Gets the exception id
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetExceptionInfo.Value">
            <summary>
            Gets the exception instance. There's no guarantee that it derives from <see cref="T:System.Exception"/>.
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetExceptionInfo.#ctor(dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,System.UInt32,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetExceptionInfoFlags)">
            <summary>
            Constructor
            </summary>
            <param name="value">Exception value</param>
            <param name="id">Exception id</param>
            <param name="flags">Flags</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetExceptionInfoFlags">
            <summary>
            Exception info flags
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetExceptionInfoFlags.None">
            <summary>
            No bit is set
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetExceptionInfoFlags.StowedException">
            <summary>
            If set, it's a stowed exception, else it's an exception
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetLanguageGuids">
            <summary>
            .NET language guids
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetLanguageGuids.CSharp">
            <summary>
            C# language
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetLanguageGuids.VisualBasic">
            <summary>
            Visual Basic language
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetObjectId">
            <summary>
            References a value in the debugged process
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetObjectId.Id">
            <summary>
            Gets the unique id in the runtime
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetObjectId.Dispose">
            <summary>
            Called when its owner (<see cref="T:dnSpy.Contracts.Debugger.Engine.Evaluation.DbgEngineObjectId"/>) gets closed
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetReturnValueInfo">
            <summary>
            Contains a method and its return value
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetReturnValueInfo.Id">
            <summary>
            Gets the return value id
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetReturnValueInfo.Method">
            <summary>
            Gets the method
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetReturnValueInfo.Value">
            <summary>
            Gets the value returned by <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetReturnValueInfo.Method"/>
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetReturnValueInfo.#ctor(System.UInt32,dnSpy.Debugger.DotNet.Metadata.DmdMethodBase,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue)">
            <summary>
            Constructor
            </summary>
            <param name="id">Return value id</param>
            <param name="method">Method</param>
            <param name="value">Value returned by <paramref name="method"/></param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue">
            <summary>
            Result of evaluating an expression. All values are automatically closed when the runtime continues
            but they implement <see cref="T:System.IDisposable"/> and should be disposed of earlier if possible.
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.Type">
            <summary>
            Gets the type of the value
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.IsNull">
            <summary>
            true if this is a null value
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.LoadIndirect">
            <summary>
            Gets the referenced value if it's a by-ref or a pointer
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.StoreIndirect(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.Object)">
            <summary>
            Writes to the referenced value (by-ref or pointer). The return value is null or an error message.
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="value">Value to store: A <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue"/> or a primitive number or a string or arrays of primitive numbers / strings</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.GetArrayCount(System.UInt32@)">
            <summary>
            Gets the number of elements of the array
            </summary>
            <param name="elementCount">Total number of elements in the array</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.GetArrayInfo(System.UInt32@,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetArrayDimensionInfo[]@)">
            <summary>
            Gets array information if it's an array or returns false
            </summary>
            <param name="elementCount">Total number of elements in the array</param>
            <param name="dimensionInfos">Dimension base indexes and lengths</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.GetArrayElementAddressAt(System.UInt32)">
            <summary>
            Gets the address of the element at <paramref name="index"/> in the array or null if it's not supported.
            This method can be called even if it's a multi-dimensional array.
            </summary>
            <param name="index">Zero-based index of the element</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.GetArrayElementAt(System.UInt32)">
            <summary>
            Gets the element at <paramref name="index"/> in the array. This method can be called even if it's
            a multi-dimensional array.
            </summary>
            <param name="index">Zero-based index of the element</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.SetArrayElementAt(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32,System.Object)">
            <summary>
            Stores a value at <paramref name="index"/> in the array. This method can be called even if it's
            a multi-dimensional array.
            The return value is null or an error message.
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="index">Zero-based index of the element</param>
            <param name="value">Value to store: A <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue"/> or a primitive number or a string or arrays of primitive numbers / strings</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.Box(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo)">
            <summary>
            Boxes the value type, returns null on failure
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.GetRawAddressValue(System.Boolean)">
            <summary>
            Gets the address of the value or null if there's no address available.
            The returned address gets invalid when the runtime continues.
            </summary>
            <param name="onlyDataAddress">If true and if it's a supported type (eg. a simple type such as integers,
            floating point values, strings or byte arrays) the returned object contains the address of the actual
            value, else the returned address and length covers the whole object including vtable, method table or other
            special data.</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.GetRawValue">
            <summary>
            Gets the raw value
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.TryGetDotNetRuntime">
            <summary>
            Returns the <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime"/> instance or null if it's unknown
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.Dispose">
            <summary>
            Called when its owner (<see cref="T:dnSpy.Contracts.Debugger.Engine.Evaluation.DbgEngineValue"/>) gets closed
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetArrayDimensionInfo">
            <summary>
            Contains base index and length of an array dimension
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetArrayDimensionInfo.BaseIndex">
            <summary>
            Base index
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetArrayDimensionInfo.Length">
            <summary>
            Number of elements in this dimension
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetArrayDimensionInfo.#ctor(System.Int32,System.UInt32)">
            <summary>
            Constructor
            </summary>
            <param name="baseIndex">Base index</param>
            <param name="length">Number of elements in this dimension</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawValue">
            <summary>
            Raw value
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawValue.ValueType">
            <summary>
            Type of the value
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawValue.HasRawValue">
            <summary>
            true if <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawValue.RawValue"/> is valid
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawValue.RawValue">
            <summary>
            The value. It's only valid if <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawValue.HasRawValue"/> is true. A null value is a valid value.
            If it's an enum value, it's stored as the enum's underlying type (eg. <see cref="T:System.Int32"/>)
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawValue.#ctor(dnSpy.Contracts.Debugger.Evaluation.DbgSimpleValueType)">
            <summary>
            Constructor
            </summary>
            <param name="valueType">Type</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawValue.#ctor(dnSpy.Contracts.Debugger.Evaluation.DbgSimpleValueType,System.Object)">
            <summary>
            Constructor
            </summary>
            <param name="valueType">Type</param>
            <param name="rawValue">Value</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult">
            <summary>
            Return value of methods creating <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue"/>s
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult.Value">
            <summary>
            Gets the value or null if there was an error (<see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult.ErrorMessage"/>).
            If <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult.ValueIsException"/> is true, this is the thrown exception value.
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult.ValueIsException">
            <summary>
            true if <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult.Value"/> contains the thrown exception instead of the expected return value / field value
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult.ErrorMessage">
            <summary>
            Gets the error message or null if there was no error
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult.HasError">
            <summary>
            true if there was an error, see <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult.ErrorMessage"/>
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult.IsNormalResult">
            <summary>
            true if there's no error and no exception was thrown
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult.Create(dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue)">
            <summary>
            Creates a normal result
            </summary>
            <param name="value">Value</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult.CreateException(dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue)">
            <summary>
            Creates an exception result
            </summary>
            <param name="value">Exception value</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValueResult.CreateError(System.String)">
            <summary>
            Creates an error result
            </summary>
            <param name="errorMessage">Error message</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfo">
            <summary>
            Method debug info used by a .NET debugger language. An instance of this class is attached to
            a <see cref="T:dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationContext"/>, see <see cref="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfoExtensions.TryGetLanguageDebugInfo(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationContext)"/>
            and <see cref="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfoExtensions.GetLanguageDebugInfo(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationContext)"/>
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfo.MethodDebugInfo">
            <summary>
            Gets the method debug info
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfo.MethodToken">
            <summary>
            Gets the method token
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfo.LocalVarSigTok">
            <summary>
            Gets the method local variables signature token
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfo.MethodVersion">
            <summary>
            Gets the method version number, a 1-based number
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfo.ILOffset">
            <summary>
            Gets the IL offset
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfo.#ctor(dnSpy.Contracts.Debugger.DotNet.Code.DbgMethodDebugInfo,System.Int32,System.Int32,System.Int32,System.UInt32)">
            <summary>
            Constructor
            </summary>
            <param name="methodDebugInfo">Method debug info</param>
            <param name="methodToken">Method token</param>
            <param name="localVarSigTok">Method local variables signature token</param>
            <param name="methodVersion">Method version number, a 1-based number</param>
            <param name="ilOffset">IL offset</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfoExtensions">
            <summary>
            Extension methods
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfoExtensions.TryGetLanguageDebugInfo(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationContext)">
            <summary>
            Gets the debug info or null if there's none
            </summary>
            <param name="context">Context</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfoExtensions.GetLanguageDebugInfo(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationContext)">
            <summary>
            Gets the debug info and throws if there is none
            </summary>
            <param name="context">Context</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfoExtensions.SetLanguageDebugInfo(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationContext,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgLanguageDebugInfo)">
            <summary>
            Attaches <paramref name="debugInfo"/> to <paramref name="context"/>
            </summary>
            <param name="context">Context</param>
            <param name="debugInfo">Debug info</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.Engine.DbgDotNetEngineObjectIdFactory">
            <summary>
            A .NET <see cref="T:dnSpy.Contracts.Debugger.Engine.Evaluation.DbgEngineObjectIdFactory"/>.
            Use <see cref="T:dnSpy.Contracts.Debugger.Engine.Evaluation.ExportDbgEngineObjectIdFactoryAttribute"/> to export an instance.
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Engine.DbgDotNetEngineObjectIdFactory.#ctor(System.Guid,dnSpy.Contracts.Debugger.DotNet.Evaluation.Engine.DbgDotNetLanguageService)">
            <summary>
            Constructor
            </summary>
            <param name="runtimeGuid">Runtime guid, see <see cref="T:dnSpy.Contracts.Debugger.PredefinedDbgRuntimeGuids"/></param>
            <param name="dbgDotNetLanguageService">.NET language service instance</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Engine.DbgDotNetEngineObjectIdFactory.CanCreateObjectId(dnSpy.Contracts.Debugger.Engine.Evaluation.DbgEngineValue)">
            <summary>
            Returns true if it's possible to create an object id
            </summary>
            <param name="value">Value created by this runtime</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Engine.DbgDotNetEngineObjectIdFactory.CreateObjectId(dnSpy.Contracts.Debugger.Engine.Evaluation.DbgEngineValue,System.UInt32)">
            <summary>
            Creates an object id or returns null
            </summary>
            <param name="value">Value created by this runtime</param>
            <param name="id">Unique id</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Engine.DbgDotNetEngineObjectIdFactory.Equals(dnSpy.Contracts.Debugger.Engine.Evaluation.DbgEngineObjectId,dnSpy.Contracts.Debugger.Engine.Evaluation.DbgEngineValue)">
            <summary>
            Checks if an object id and a value refer to the same data
            </summary>
            <param name="objectId">Object id created by this class</param>
            <param name="value">Value created by this runtime</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Engine.DbgDotNetEngineObjectIdFactory.GetHashCode(dnSpy.Contracts.Debugger.Engine.Evaluation.DbgEngineObjectId)">
            <summary>
            Gets the hash code of an object id
            </summary>
            <param name="objectId">Object id created by this class</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Engine.DbgDotNetEngineObjectIdFactory.GetHashCode(dnSpy.Contracts.Debugger.Engine.Evaluation.DbgEngineValue)">
            <summary>
            Gets the hash code of a value created by this runtime
            </summary>
            <param name="value">Value created by this runtime</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.Engine.DbgDotNetLanguageService">
            <summary>
            Used by a <see cref="T:dnSpy.Contracts.Debugger.Engine.Evaluation.DbgEngineObjectIdFactory"/> to create object id factories
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Engine.DbgDotNetLanguageService.GetEngineObjectIdFactory(System.Guid)">
            <summary>
            Creates a <see cref="T:dnSpy.Contracts.Debugger.Engine.Evaluation.DbgEngineObjectIdFactory"/>
            </summary>
            <param name="runtimeGuid">Runtime guid, see <see cref="T:dnSpy.Contracts.Debugger.PredefinedDbgRuntimeGuids"/></param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAliasKind">
            <summary>
            Alias kind
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAliasKind.Exception">
            <summary>
            An exception, eg. "$exception"
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAliasKind.StowedException">
            <summary>
            A stowed exception, eg. "$stowedexception"
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAliasKind.ReturnValue">
            <summary>
            A return value, eg. "$ReturnValue", "$ReturnValue1"
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAliasKind.Variable">
            <summary>
            A variable created by the user that doesn't exist in code
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAliasKind.ObjectId">
            <summary>
            An object ID, eg. "$1", "$3"
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAlias">
            <summary>
            An alias (eg. return value, object id, etc)
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAlias.Kind">
            <summary>
            Alias kind
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAlias.CustomTypeInfo">
            <summary>
            Custom type info understood by the EE or null
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAlias.CustomTypeInfoId">
            <summary>
            Custom type info ID
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAlias.Name">
            <summary>
            Name, eg. "$ReturnValue", "$1"
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAlias.Type">
            <summary>
            Serialized type name, see <see cref="P:System.Type.AssemblyQualifiedName"/>
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAlias.#ctor(dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAliasKind,System.String,System.String,System.Guid,System.Collections.ObjectModel.ReadOnlyCollection{System.Byte})">
            <summary>
            Constructor
            </summary>
            <param name="kind">Alias kind</param>
            <param name="type">Serialized type name, see <see cref="P:System.Type.AssemblyQualifiedName"/></param>
            <param name="name">Name, eg. "$ReturnValue", "$1"</param>
            <param name="customTypeInfoId">Custom type info ID</param>
            <param name="customTypeInfo">Custom type info understood by the EE or null</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompilationResult">
            <summary>
            Contains the compiled assembly and info on which method to evaluate to get the result of an expression
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompilationResult.IsError">
            <summary>
            true if it has an error message (<see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompilationResult.ErrorMessage"/>)
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompilationResult.ErrorMessage">
            <summary>
            Gets the error message or null if there was no error
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompilationResult.Assembly">
            <summary>
            Gets the .NET assembly bytes or null if there was an error (<see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompilationResult.ErrorMessage"/>). It's
            empty if <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompilationResult.CompiledExpressions"/> is empty.
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompilationResult.CompiledExpressions">
            <summary>
            Gets the result of all compiled expressions or null if there was an error (<see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompilationResult.ErrorMessage"/>)
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompilationResult.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="errorMessage">Error message</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompilationResult.#ctor(System.Byte[],dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult[])">
            <summary>
            Constructor
            </summary>
            <param name="assembly">.NET assembly bytes</param>
            <param name="compiledExpressions">Compiled expressions info</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResultFlags">
            <summary>
            Compiled expression result flags
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResultFlags.None">
            <summary>
            No bit is set
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResultFlags.CompilerGenerated">
            <summary>
            Compiler generated variable
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult">
            <summary>
            Compiled expression result
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.ErrorMessage">
            <summary>
            Error message or null if no error. See also <see cref="T:dnSpy.Contracts.Debugger.Engine.Evaluation.PredefinedEvaluationErrorMessages"/>
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.TypeName">
            <summary>
            Name of the type that contains the method (<see cref="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.MethodName"/>) that should be evaluated
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.MethodName">
            <summary>
            Name of the method that should be evaluated. The declaring type is <see cref="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.TypeName"/>
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.Expression">
            <summary>
            Gets the expression that was compiled. This is eg. a C# or Visual Basic expression.
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.Name">
            <summary>
            Display name shown in the UI
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.Flags">
            <summary>
            Gets the evaluation result flags
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.ImageName">
            <summary>
            Gets the image, see <see cref="T:dnSpy.Contracts.Debugger.Evaluation.PredefinedDbgValueNodeImageNames"/>
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.CustomTypeInfo">
            <summary>
            Gets extra custom type info or null if none
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.FormatSpecifiers">
            <summary>
            Gets the format specifiers or null
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.Index">
            <summary>
            Parameter/local index or -1 if unknown
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.ResultFlags">
            <summary>
            Gets the compiled expression flags
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.Create(System.String,System.String,System.String,dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText,dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationResultFlags,System.String,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetCustomTypeInfo,System.Collections.ObjectModel.ReadOnlyCollection{System.String},dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResultFlags,System.Int32)">
            <summary>
            Creates a successful compiled expression with no error
            </summary>
            <param name="typeName">Name of type that contains the method to evaluate</param>
            <param name="methodName">Name of the method to evaluate</param>
            <param name="expression">Original expression</param>
            <param name="name">Display name shown in the UI</param>
            <param name="flags">Evaluation result flags</param>
            <param name="imageName">Image, see <see cref="T:dnSpy.Contracts.Debugger.Evaluation.PredefinedDbgValueNodeImageNames"/></param>
            <param name="customTypeInfo">Optional custom type info known by the language expression compiler and the language value formatter</param>
            <param name="formatSpecifiers">Format specifiers</param>
            <param name="resultFlags">Result flags</param>
            <param name="index">Parameter/local index or -1 if unknown</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetCompiledExpressionResult.CreateError(System.String,dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText,System.String)">
            <summary>
            Creates an error
            </summary>
            <param name="expression">Expression</param>
            <param name="name">Display name shown in the UI</param>
            <param name="errorMessage">Error message, see also <see cref="T:dnSpy.Contracts.Debugger.Engine.Evaluation.PredefinedEvaluationErrorMessages"/></param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetExpressionCompiler">
            <summary>
            A .NET expression compiler. Use <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute"/>
            to export an instance.
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetExpressionCompiler.CompileExpression(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgModuleReference[],dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAlias[],System.String,dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationOptions)">
            <summary>
            Compiles an expression
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="references">.NET module references</param>
            <param name="aliases">Aliases</param>
            <param name="expression">Expression</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetExpressionCompiler.CompileTypeExpression(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Debugger.DotNet.Metadata.DmdType,dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgModuleReference[],dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAlias[],System.String,dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationOptions)">
            <summary>
            Compiles a type expression (compiles <see cref="T:System.Diagnostics.DebuggerDisplayAttribute"/> expressions)
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="type">Type</param>
            <param name="references">.NET module references</param>
            <param name="aliases">Aliases</param>
            <param name="expression">Expression</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetExpressionCompiler.CompileGetLocals(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgModuleReference[],dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationOptions)">
            <summary>
            Creates an assembly that is used to get all the locals
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="references">.NET module references</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetExpressionCompiler.CompileAssignment(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgModuleReference[],dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAlias[],System.String,System.String,dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationOptions)">
            <summary>
            Compiles an assignment
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="references">.NET module references</param>
            <param name="aliases">Aliases</param>
            <param name="target">Target expression (lhs)</param>
            <param name="expression">Expression (rhs)</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetExpressionCompiler.TryGetAliasInfo(System.String,dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetParsedAlias@)">
            <summary>
            Gets alias info
            </summary>
            <param name="aliasName">Alias name</param>
            <param name="aliasInfo">Updated with alias info</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.IDbgDotNetExpressionCompilerMetadata">
            <summary>Metadata</summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.IDbgDotNetExpressionCompilerMetadata.LanguageGuid">
            <summary>See <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute.LanguageGuid"/></summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.IDbgDotNetExpressionCompilerMetadata.LanguageName">
            <summary>See <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute.LanguageName"/></summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.IDbgDotNetExpressionCompilerMetadata.LanguageDisplayName">
            <summary>See <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute.LanguageDisplayName"/></summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.IDbgDotNetExpressionCompilerMetadata.DecompilerGuid">
            <summary>See <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute.DecompilerGuid"/></summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.IDbgDotNetExpressionCompilerMetadata.Order">
            <summary>See <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute.Order"/></summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute">
            <summary>
            Exports a <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetExpressionCompiler"/> instance
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute.#ctor(System.String,System.String,System.String,System.String,System.Double)">
            <summary>
            Constructor
            </summary>
            <param name="languageGuid">Language GUID, see <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetLanguageGuids"/></param>
            <param name="languageName">Language name, see <see cref="T:dnSpy.Contracts.Debugger.Evaluation.PredefinedDbgLanguageNames"/></param>
            <param name="languageDisplayName">Language's display name (shown in the UI)</param>
            <param name="decompilerGuid">Decompiler GUID, see <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.PredefinedDecompilerGuids"/> or one of the decompiler GUIDs (<see cref="T:dnSpy.Contracts.Decompiler.DecompilerConstants"/>)</param>
            <param name="order">Order, see <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.PredefinedDbgDotNetExpressionCompilerOrders"/></param>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute.LanguageGuid">
            <summary>
            Language GUID, see <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetLanguageGuids"/>
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute.LanguageName">
            <summary>
            Gets the language name, see <see cref="T:dnSpy.Contracts.Debugger.Evaluation.PredefinedDbgLanguageNames"/>
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute.LanguageDisplayName">
            <summary>
            Gets the language's display name (shown in the UI)
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute.DecompilerGuid">
            <summary>
            Gets the decompiler GUID, see <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.PredefinedDecompilerGuids"/> or one of the decompiler GUIDs (<see cref="T:dnSpy.Contracts.Decompiler.DecompilerConstants"/>)
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.ExportDbgDotNetExpressionCompilerAttribute.Order">
            <summary>
            Order
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.PredefinedDbgDotNetExpressionCompilerOrders">
            <summary>
            Order of known expression compilers
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.PredefinedDbgDotNetExpressionCompilerOrders.CSharp">
            <summary>
            Order of C# expression compiler
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.PredefinedDbgDotNetExpressionCompilerOrders.VisualBasic">
            <summary>
            Order of Visual Basic expression compiler
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetParsedAlias">
            <summary>
            Alias info
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetParsedAlias.Kind">
            <summary>
            Alias kind
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetParsedAlias.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetParsedAlias.#ctor(dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgDotNetAliasKind,System.UInt32)">
            <summary>
            Constructor
            </summary>
            <param name="kind">Alias kind</param>
            <param name="id">Id</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgModuleReference">
            <summary>
            A reference to module metadata used by .NET expression compilers
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgModuleReference.MetadataAddress">
            <summary>
            Gets the address of the .NET metadata (BSJB header)
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgModuleReference.MetadataSize">
            <summary>
            Gets the size of the metadata
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgModuleReference.ModuleVersionId">
            <summary>
            Gets the module version id
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.DbgModuleReference.GenerationId">
            <summary>
            Gets the module generation id
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.PredefinedDecompilerGuids">
            <summary>
            Predefined decompiler guids
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.PredefinedDecompilerGuids.CSharp">
            <summary>
            C# decompiler
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.ExpressionCompiler.PredefinedDecompilerGuids.VisualBasic">
            <summary>
            Visual Basic decompiler
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter">
            <summary>
            Formats values, types, names. Use <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.ExportDbgDotNetFormatterAttribute"/>
            to export an instance.
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter.FormatExceptionName(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationContext,dnSpy.Contracts.Debugger.Text.IDbgTextWriter,System.UInt32)">
            <summary>
            Formats an exception name
            </summary>
            <param name="context">Evaluation context</param>
            <param name="output">Output</param>
            <param name="id">Exception id</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter.FormatStowedExceptionName(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationContext,dnSpy.Contracts.Debugger.Text.IDbgTextWriter,System.UInt32)">
            <summary>
            Formats a stowed exception name
            </summary>
            <param name="context">Evaluation context</param>
            <param name="output">Output</param>
            <param name="id">Stowed exception id</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter.FormatReturnValueName(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationContext,dnSpy.Contracts.Debugger.Text.IDbgTextWriter,System.UInt32)">
            <summary>
            Formats a return value name
            </summary>
            <param name="context">Evaluation context</param>
            <param name="output">Output</param>
            <param name="id">Return value id</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter.FormatObjectIdName(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationContext,dnSpy.Contracts.Debugger.Text.IDbgTextWriter,System.UInt32)">
            <summary>
            Formats an object ID name
            </summary>
            <param name="context">Evaluation context</param>
            <param name="output">Output</param>
            <param name="id">Object id</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter.FormatValue(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.Text.IDbgTextWriter,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,dnSpy.Contracts.Debugger.Evaluation.DbgValueFormatterOptions,System.Globalization.CultureInfo)">
            <summary>
            Formats a value
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="output">Output</param>
            <param name="value">Value to format</param>
            <param name="options">Options</param>
            <param name="cultureInfo">Culture or null to use invariant culture</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter.FormatType(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.Text.IDbgTextWriter,dnSpy.Debugger.DotNet.Metadata.DmdType,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,dnSpy.Contracts.Debugger.Evaluation.DbgValueFormatterTypeOptions,System.Globalization.CultureInfo)">
            <summary>
            Formats a type
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="output">Output</param>
            <param name="type">Type to format</param>
            <param name="value">Value or null</param>
            <param name="options">Options</param>
            <param name="cultureInfo">Culture or null to use invariant culture</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter.FormatFrame(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.Text.IDbgTextWriter,dnSpy.Contracts.Debugger.Evaluation.DbgStackFrameFormatterOptions,dnSpy.Contracts.Debugger.Evaluation.DbgValueFormatterOptions,System.Globalization.CultureInfo)">
            <summary>
            Formats a stack frame
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="output">Output</param>
            <param name="options">Stack frame options</param>
            <param name="valueOptions">Value option</param>
            <param name="cultureInfo">Culture or null to use invariant culture</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.IDbgDotNetFormatterMetadata">
            <summary>Metadata</summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.IDbgDotNetFormatterMetadata.LanguageGuid">
            <summary>See <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.ExportDbgDotNetFormatterAttribute.LanguageGuid"/></summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.IDbgDotNetFormatterMetadata.Order">
            <summary>See <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.ExportDbgDotNetFormatterAttribute.Order"/></summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.ExportDbgDotNetFormatterAttribute">
            <summary>
            Exports a <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter"/> instance
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.ExportDbgDotNetFormatterAttribute.#ctor(System.String,System.Double)">
            <summary>
            Constructor
            </summary>
            <param name="languageGuid">Language GUID, see <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetLanguageGuids"/></param>
            <param name="order">Order</param>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.ExportDbgDotNetFormatterAttribute.LanguageGuid">
            <summary>
            Language GUID, see <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetLanguageGuids"/>
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.ExportDbgDotNetFormatterAttribute.Order">
            <summary>
            Order
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime">
            <summary>
            Implemented by a .NET engine, see <see cref="P:dnSpy.Contracts.Debugger.DbgRuntime.InternalRuntime"/>
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.Dispatcher">
            <summary>
            Gets the dispatcher
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.Features">
            <summary>
            Gets the feature flags
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetModuleId(dnSpy.Contracts.Debugger.DbgModule)">
            <summary>
            Gets the module id
            </summary>
            <param name="module">Module</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetRawModuleBytes(dnSpy.Contracts.Debugger.DbgModule)">
            <summary>
            Gets the module data or <see cref="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawModuleBytes.None"/>
            </summary>
            <param name="module">Module</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.TryGetMethodToken(dnSpy.Contracts.Debugger.DbgModule,System.Int32,System.Int32@,System.Int32@)">
            <summary>
            Translates a method token from the original dynamic module's metadata to the saved module metadata used by the expression compiler
            </summary>
            <param name="module">Module</param>
            <param name="methodToken">Method token</param>
            <param name="metadataMethodToken">New method token</param>
            <param name="metadataLocalVarSigTok">New method body local variables signature token</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetFrameMethod(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo)">
            <summary>
            Gets the current method or null if it's not a normal IL frame
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.LoadFieldAddress(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo)">
            <summary>
            Loads the address of an instance or a static field or returns null if it's not supported
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="obj">Instance object or null if it's a static field</param>
            <param name="field">Field</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.LoadField(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo)">
            <summary>
            Loads an instance or a static field
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="obj">Instance object or null if it's a static field</param>
            <param name="field">Field</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.StoreField(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo,System.Object)">
            <summary>
            Stores a value in a field. Returns null or an error message
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="obj">Instance object or null if it's a static field</param>
            <param name="field">Field</param>
            <param name="value">Value to store: A <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue"/> or a primitive number or a string or arrays of primitive numbers / strings</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.Call(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,dnSpy.Debugger.DotNet.Metadata.DmdMethodBase,System.Object[],dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetInvokeOptions)">
            <summary>
            Calls an instance or a static method
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="obj">Instance object or null if it's a static method</param>
            <param name="method">Method</param>
            <param name="arguments">Arguments: A <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue"/> or a primitive number or a string or arrays of primitive numbers / strings</param>
            <param name="invokeOptions">Invoke options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.CreateInstance(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo,System.Object[],dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetInvokeOptions)">
            <summary>
            Creates a new instance of a type by calling its constructor
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="ctor">Constructor</param>
            <param name="arguments">Arguments: A <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue"/> or a primitive number or a string or arrays of primitive numbers / strings</param>
            <param name="invokeOptions">Invoke options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.CreateInstanceNoConstructor(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Creates a new instance of a type. All fields are initialized to 0 or null. The constructor isn't called.
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="type">Type to create</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.CreateSZArray(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Debugger.DotNet.Metadata.DmdType,System.Int32)">
            <summary>
            Creates an SZ array
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="elementType">Element type</param>
            <param name="length">Length of the array</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.CreateArray(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Debugger.DotNet.Metadata.DmdType,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetArrayDimensionInfo[])">
            <summary>
            Creates a multi-dimensional array
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="elementType">Element type</param>
            <param name="dimensionInfos">Dimension infos</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetAliases(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo)">
            <summary>
            Gets aliases
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetExceptions(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo)">
            <summary>
            Gets all exceptions
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetReturnValues(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo)">
            <summary>
            Gets all return values
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetException(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32)">
            <summary>
            Gets an exception or null
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="id">Exception id, eg. <see cref="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeConstants.ExceptionId"/></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetStowedException(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32)">
            <summary>
            Gets a stowed exception or null
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="id">Stowed exception id, eg. <see cref="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeConstants.StowedExceptionId"/></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetReturnValue(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32)">
            <summary>
            Gets a return value or null
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="id">Return value id, eg. <see cref="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeConstants.LastReturnValueId"/></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetLocalValue(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32)">
            <summary>
            Gets a local value
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="index">Metadata index of local</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetParameterValue(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32)">
            <summary>
            Gets a parameter value
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="index">Metadata index of parameter</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.SetLocalValue(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32,dnSpy.Debugger.DotNet.Metadata.DmdType,System.Object)">
            <summary>
            Writes a new local value. Returns an error message or null.
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="index">Metadata index of parameter</param>
            <param name="targetType">Type of the local</param>
            <param name="value">New value: A <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue"/> or a primitive number or a string or arrays of primitive numbers / strings</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.SetParameterValue(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32,dnSpy.Debugger.DotNet.Metadata.DmdType,System.Object)">
            <summary>
            Writes a new parameter value. Returns an error message or null.
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="index">Metadata index of parameter</param>
            <param name="targetType">Type of the parameter</param>
            <param name="value">New value: A <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue"/> or a primitive number or a string or arrays of primitive numbers / strings</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetLocalValueAddress(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32,dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Gets the address of a local value or null if it's not supported
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="index">Metadata index of local</param>
            <param name="targetType">Type of the local</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetParameterValueAddress(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32,dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Gets the address of a parameter value or null if it's not supported
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="index">Metadata index of local</param>
            <param name="targetType">Type of the parameter</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.CreateValue(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.Object)">
            <summary>
            Creates a simple value (a primitive number or a string, or arrays of those types)
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="value">A <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue"/> or a primitive number or a string or arrays of primitive numbers / strings</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.Box(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.Object)">
            <summary>
            Boxes the value type
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="value">Value to box</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.CanCreateObjectId(dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue)">
            <summary>
            Returns true if it's possible to create an object id
            </summary>
            <param name="value">Value created by this runtime</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.CreateObjectId(dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,System.UInt32)">
            <summary>
            Creates an object id or returns null
            </summary>
            <param name="value">Value created by this runtime</param>
            <param name="id">Unique id</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.Equals(dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetObjectId,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue)">
            <summary>
            Checks if an object id and a value refer to the same data
            </summary>
            <param name="objectId">Object id created by this class</param>
            <param name="value">Value created by this runtime</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetHashCode(dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetObjectId)">
            <summary>
            Gets the hash code of an object id
            </summary>
            <param name="objectId">Object id created by this class</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetHashCode(dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue)">
            <summary>
            Gets the hash code of a value created by this runtime
            </summary>
            <param name="value">Value created by this runtime</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.GetValue(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetObjectId)">
            <summary>
            Gets an object ID's value or null if there was an error
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="objectId">Object id created by this class</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.Equals(dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue)">
            <summary>
            Checks if two values are equal. Returns null if it's unknown.
            </summary>
            <param name="a">Value #1</param>
            <param name="b">Value #2</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.TryGetNativeCode(dnSpy.Contracts.Debugger.CallStack.DbgStackFrame,dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCode@)">
            <summary>
            Tries to get the native code
            </summary>
            <param name="frame">Frame</param>
            <param name="nativeCode">Updated with the native code if successful</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.TryGetNativeCode(dnSpy.Debugger.DotNet.Metadata.DmdMethodBase,dnSpy.Contracts.Debugger.DotNet.Disassembly.DbgDotNetNativeCode@)">
            <summary>
            Tries to get the native code
            </summary>
            <param name="method">Method</param>
            <param name="nativeCode">Updated with the native code if successful</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime.TryGetSymbol(System.UInt64,dnSpy.Contracts.Disassembly.SymbolResolverResult@)">
            <summary>
            Tries to get a symbol
            </summary>
            <param name="address">Address</param>
            <param name="result">Updated with the symbol if successful</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetInvokeOptions">
            <summary>
            Invoke options
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetInvokeOptions.None">
            <summary>
            No bit is set
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetInvokeOptions.NonVirtual">
            <summary>
            Non-virtual call
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeFeatures">
            <summary>
            .NET runtime features
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeFeatures.None">
            <summary>
            No bit is set
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeFeatures.ObjectIds">
            <summary>
            Object IDs are supported
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeFeatures.NoGenericMethods">
            <summary>
            Calling generic methods isn't supported
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeFeatures.NoDereferencePointers">
            <summary>
            <see cref="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.LoadIndirect"/> and <see cref="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue.StoreIndirect(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.Object)"/>
            isn't supported for pointers.
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeFeatures.NoAsyncStepObjectId">
            <summary>
            Async step with object ids isn't supported
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeFeatures.NativeMethodBodies">
            <summary>
            It's possible to get the native code of jitted managed methods
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeConstants">
            <summary>
            Constants
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeConstants.ExceptionId">
            <summary>
            Exception ID
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeConstants.StowedExceptionId">
            <summary>
            Stowed exception ID
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRuntimeConstants.LastReturnValueId">
            <summary>
            ID of last return value
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawModuleBytes">
            <summary>
            Contains .NET module data information
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawModuleBytes.None">
            <summary>
            No .NET module data is available
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawModuleBytes.IsFileLayout">
            <summary>
            true if it's file layout, false if it's memory layout
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawModuleBytes.RawBytes">
            <summary>
            Raw bytes of the .NET module
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetRawModuleBytes.#ctor(System.Byte[],System.Boolean)">
            <summary>
            Constructor
            </summary>
            <param name="rawBytes">Raw bytes of the .NET module</param>
            <param name="isFileLayout">true if it's file layout, false if it's memory layout</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode">
            <summary>
            A .NET value node
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.ExpectedType">
            <summary>
            Gets the expected type or null
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.ActualType">
            <summary>
            Gets the actual type or null
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.ErrorMessage">
            <summary>
            Gets the error message or null
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.Value">
            <summary>
            Gets the value or null
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.Name">
            <summary>
            Gets the name
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.Expression">
            <summary>
            Gets the expression
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.ImageName">
            <summary>
            Image name, see <see cref="T:dnSpy.Contracts.Debugger.Evaluation.PredefinedDbgValueNodeImageNames"/>
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.IsReadOnly">
            <summary>
            true if this is a read-only value
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.CausesSideEffects">
            <summary>
            true if the expression causes side effects
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.FormatSpecifiers">
            <summary>
            Gets the format specifiers or null
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.HasChildren">
            <summary>
            Returns true if it has children, false if it has no children and null if it's unknown (eg. it's too expensive to calculate it now).
            UI code can use this property to decide if it shows the treeview node expander ("|>").
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.GetChildCount(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo)">
            <summary>
            Number of children. This property is called as late as possible and can be lazily initialized.
            It's assumed to be 0 if <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.HasChildren"/> is false.
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.GetChildren(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt64,System.Int32,dnSpy.Contracts.Debugger.Evaluation.DbgValueNodeEvaluationOptions)">
            <summary>
            Creates new children
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="index">Index of first child</param>
            <param name="count">Max number of children to return</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.FormatName(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.Text.IDbgTextWriter,dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter,dnSpy.Contracts.Debugger.Evaluation.DbgValueFormatterOptions,System.Globalization.CultureInfo)">
            <summary>
            Formats the name. Returns false if nothing was written to <paramref name="output"/>
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="output">Output</param>
            <param name="formatter">Formatter</param>
            <param name="options">Options</param>
            <param name="cultureInfo">Culture or null to use invariant culture</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.FormatValue(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.Text.IDbgTextWriter,dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter,dnSpy.Contracts.Debugger.Evaluation.DbgValueFormatterOptions,System.Globalization.CultureInfo)">
            <summary>
            Formats the value column. Returns false if nothing was written to <paramref name="output"/>
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="output">Output</param>
            <param name="formatter">Formatter</param>
            <param name="options">Options</param>
            <param name="cultureInfo">Culture or null to use invariant culture</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.FormatExpectedType(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.Text.IDbgTextWriter,dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter,dnSpy.Contracts.Debugger.Evaluation.DbgValueFormatterTypeOptions,dnSpy.Contracts.Debugger.Evaluation.DbgValueFormatterOptions,System.Globalization.CultureInfo)">
            <summary>
            Formats the expected type. Returns false if nothing was written to <paramref name="output"/>
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="output">Output</param>
            <param name="formatter">Formatter</param>
            <param name="options">Options</param>
            <param name="valueOptions">Value options</param>
            <param name="cultureInfo">Culture or null to use invariant culture</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNode.FormatActualType(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.Text.IDbgTextWriter,dnSpy.Contracts.Debugger.DotNet.Evaluation.Formatters.DbgDotNetFormatter,dnSpy.Contracts.Debugger.Evaluation.DbgValueFormatterTypeOptions,dnSpy.Contracts.Debugger.Evaluation.DbgValueFormatterOptions,System.Globalization.CultureInfo)">
            <summary>
            Formats the actual type. Returns false if nothing was written to <paramref name="output"/>
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="output">Output</param>
            <param name="formatter">Formatter</param>
            <param name="options">Options</param>
            <param name="valueOptions">Value options</param>
            <param name="cultureInfo">Culture or null to use invariant culture</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNodeFactory">
            <summary>
            Creates value nodes. Use <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.ExportDbgDotNetValueNodeFactoryAttribute"/>
            to export an instance.
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNodeFactory.Create(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,System.Collections.ObjectModel.ReadOnlyCollection{System.String},dnSpy.Contracts.Debugger.Evaluation.DbgValueNodeEvaluationOptions,System.String,System.String,System.Boolean,System.Boolean,dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Creates a value node
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="name">Name</param>
            <param name="value">Value</param>
            <param name="formatSpecifiers">Format specifiers or null</param>
            <param name="options">Options</param>
            <param name="expression">Expression</param>
            <param name="imageName">Image name, see <see cref="T:dnSpy.Contracts.Debugger.Evaluation.PredefinedDbgValueNodeImageNames"/></param>
            <param name="isReadOnly">true if it's a read-only value</param>
            <param name="causesSideEffects">true if the expression causes side effects</param>
            <param name="expectedType">Expected type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNodeFactory.CreateException(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,System.Collections.ObjectModel.ReadOnlyCollection{System.String},dnSpy.Contracts.Debugger.Evaluation.DbgValueNodeEvaluationOptions)">
            <summary>
            Creates an exception value node
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="id">Exception id</param>
            <param name="value">Value</param>
            <param name="formatSpecifiers">Format specifiers or null</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNodeFactory.CreateStowedException(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,System.Collections.ObjectModel.ReadOnlyCollection{System.String},dnSpy.Contracts.Debugger.Evaluation.DbgValueNodeEvaluationOptions)">
            <summary>
            Creates an exception value node
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="id">Stowed exception id</param>
            <param name="value">Value</param>
            <param name="formatSpecifiers">Format specifiers or null</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNodeFactory.CreateReturnValue(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,System.UInt32,dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetValue,System.Collections.ObjectModel.ReadOnlyCollection{System.String},dnSpy.Contracts.Debugger.Evaluation.DbgValueNodeEvaluationOptions,dnSpy.Debugger.DotNet.Metadata.DmdMethodBase)">
            <summary>
            Creates a return value node
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="id">Return value id</param>
            <param name="value">Value</param>
            <param name="formatSpecifiers">Format specifiers or null</param>
            <param name="options">Options</param>
            <param name="method">Method</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNodeFactory.CreateError(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText,System.String,System.String,System.Boolean)">
            <summary>
            Creates an error value node
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="name">Name</param>
            <param name="errorMessage">Error message</param>
            <param name="expression">Expression</param>
            <param name="causesSideEffects">true if the expression causes side effects</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNodeFactory.CreateTypeVariables(dnSpy.Contracts.Debugger.Evaluation.DbgEvaluationInfo,dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetTypeVariableInfo[])">
            <summary>
            Creates type variables value node
            </summary>
            <param name="evalInfo">Evaluation info</param>
            <param name="typeVariableInfos">Type variables</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.IDbgDotNetValueNodeFactoryMetadata">
            <summary>Metadata</summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.IDbgDotNetValueNodeFactoryMetadata.LanguageGuid">
            <summary>See <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.ExportDbgDotNetValueNodeFactoryAttribute.LanguageGuid"/></summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.IDbgDotNetValueNodeFactoryMetadata.Order">
            <summary>See <see cref="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.ExportDbgDotNetValueNodeFactoryAttribute.Order"/></summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.ExportDbgDotNetValueNodeFactoryAttribute">
            <summary>
            Exports a <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetValueNodeFactory"/> instance
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.ExportDbgDotNetValueNodeFactoryAttribute.#ctor(System.String,System.Double)">
            <summary>
            Constructor
            </summary>
            <param name="languageGuid">Language GUID, see <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetLanguageGuids"/></param>
            <param name="order">Order</param>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.ExportDbgDotNetValueNodeFactoryAttribute.LanguageGuid">
            <summary>
            Language GUID, see <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.DbgDotNetLanguageGuids"/>
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.ExportDbgDotNetValueNodeFactoryAttribute.Order">
            <summary>
            Order
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetTypeVariableInfo">
            <summary>
            Contains the generic parameter and type
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetTypeVariableInfo.GenericParameterType">
            <summary>
            Gets the generic parameter type
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetTypeVariableInfo.GenericArgumentType">
            <summary>
            Gets the generic argument type
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Evaluation.ValueNodes.DbgDotNetTypeVariableInfo.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdType,dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Constructor
            </summary>
            <param name="genericParameterType">Generic parameter type</param>
            <param name="genericArgumentType">Generic argument type</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgAssemblyInfoProvider">
            <summary>
            Assembly info provider
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgAssemblyInfoProvider.GetManifestModule(dnSpy.Contracts.Debugger.DbgModule)">
            <summary>
            Returns the manifest module (first module) or null if it's not part of an assembly
            </summary>
            <param name="module">A module in some assembly</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgAssemblyInfoProvider.GetAssemblyModules(dnSpy.Contracts.Debugger.DbgModule)">
            <summary>
            Gets all modules in an assembly or an empty array if it's not part of an assembly.
            The manifest module is always the first module.
            </summary>
            <param name="module">Module</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgAssemblyInfoProviderFactory">
            <summary>
            Creates <see cref="T:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgAssemblyInfoProvider"/> instances
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgAssemblyInfoProviderFactory.Create(dnSpy.Contracts.Debugger.DbgRuntime)">
            <summary>
            Creates a <see cref="T:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgAssemblyInfoProvider"/> or returns null
            </summary>
            <param name="runtime">Runtime</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProvider">
            <summary>
            Loads and creates dynamic modules (they can get extra classses and members at runtime)
            </summary>
        </member>
        <member name="E:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProvider.ClassLoaded">
            <summary>
            Raised when a new class has been loaded in a dynamic assembly
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProvider.BeginInvoke(System.Action)">
            <summary>
            Executes <paramref name="action"/> asynchronously on the thread required to load dynamic modules.
            </summary>
            <param name="action">Code to execute</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProvider.GetDynamicMetadata(dnSpy.Contracts.Debugger.DbgModule,dnSpy.Contracts.Metadata.ModuleId@)">
            <summary>
            Gets the dynamic module's metadata or null if none is available
            </summary>
            <param name="module">Module</param>
            <param name="moduleId">Module id</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProvider.GetModifiedTypes(dnSpy.Contracts.Debugger.DbgModule)">
            <summary>
            Gets all modified types. This method is called on the engine thread (see <see cref="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProvider.BeginInvoke(System.Action)"/>)
            </summary>
            <param name="module">Module</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProvider.InitializeNonLoadedClasses(dnSpy.Contracts.Debugger.DbgModule,System.UInt32[])">
            <summary>
            Initializes new classes that haven't gotten a load-class event yet. This method is called on the engine thread (see <see cref="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProvider.BeginInvoke(System.Action)"/>)
            </summary>
            <param name="module">Module</param>
            <param name="nonLoadedTokens">Sorted tokens of classes that haven't been loaded but are still present in the metadata</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProvider.LoadEverything(dnSpy.Contracts.Debugger.DbgModule[],System.Boolean)">
            <summary>
            Called on the engine thread just before and just after all types and members are force loaded
            </summary>
            <param name="modules">Modules that will be loaded</param>
            <param name="started">true if we're about to load all modules, false if we're done</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Metadata.ClassLoadedEventArgs">
            <summary>
            Class loaded event args
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Metadata.ClassLoadedEventArgs.Module">
            <summary>
            Module
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Metadata.ClassLoadedEventArgs.LoadedClassToken">
            <summary>
            Token of loaded class
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.ClassLoadedEventArgs.#ctor(dnSpy.Contracts.Debugger.DbgModule,System.UInt32)">
            <summary>
            Constructor
            </summary>
            <param name="module">Module</param>
            <param name="loadedClassToken">Token of loaded class</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProviderFactory">
            <summary>
            Creates <see cref="T:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProvider"/>s
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProviderFactory.Create(dnSpy.Contracts.Debugger.DbgRuntime)">
            <summary>
            Creates a <see cref="T:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgDynamicModuleProvider"/> or returns null
            </summary>
            <param name="runtime">Runtime</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgMetadataService">
            <summary>
            Provides .NET metadata
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgMetadataService.TryGetMetadata(dnSpy.Contracts.Debugger.DbgModule,dnSpy.Contracts.Debugger.DotNet.Metadata.DbgLoadModuleOptions)">
            <summary>
            Returns a <see cref="T:dnlib.DotNet.ModuleDef"/> or null if none could be loaded
            </summary>
            <param name="module">Module</param>
            <param name="options">Load options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgMetadataService.TryGetMetadata(dnSpy.Contracts.Metadata.ModuleId,dnSpy.Contracts.Debugger.DotNet.Metadata.DbgLoadModuleOptions)">
            <summary>
            Returns a <see cref="T:dnlib.DotNet.ModuleDef"/> or null if none could be loaded
            </summary>
            <param name="moduleId">Module id</param>
            <param name="options">Load options</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgLoadModuleOptions">
            <summary>
            Options used when loading modules
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgLoadModuleOptions.None">
            <summary>
            No bit is set
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgLoadModuleOptions.AutoLoaded">
            <summary>
            The module load was caused by a non-user action
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgLoadModuleOptions.ForceMemory">
            <summary>
            Always load the module from the process' address space instead of from the module's file on disk
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgModuleIdProvider">
            <summary>
            Provides <see cref="T:dnSpy.Contracts.Metadata.ModuleId"/>s
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.DbgModuleIdProvider.GetModuleId(dnSpy.Contracts.Debugger.DbgModule)">
            <summary>
            Gets the <see cref="T:dnSpy.Contracts.Metadata.ModuleId"/> or null
            </summary>
            <param name="module">Module</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata">
            <summary>
            Raw .NET metadata stored in some memory location
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata.IsFileLayout">
            <summary>
            true if it's file layout, false if it's memory layout
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata.IsMemoryLayout">
            <summary>
            true if it's memory layout, false if it's file layout
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata.Address">
            <summary>
            Gets the address of the data (first byte of the PE file)
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata.Size">
            <summary>
            Gets the size of the data (size of the PE file in memory)
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata.MetadataAddress">
            <summary>
            Gets the address of the .NET metadata (BSJB header)
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata.MetadataSize">
            <summary>
            Gets the size of the metadata
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata.AddRef">
            <summary>
            Increments the reference count and returns the same instance
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata.Release">
            <summary>
            Decrements the reference count
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata.UpdateMemory">
            <summary>
            Re-reads the memory if possible
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadataService">
            <summary>
            Creates <see cref="T:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata"/> instances
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadataService.Create(dnSpy.Contracts.Debugger.DbgRuntime,System.Boolean,System.UInt64,System.Int32)">
            <summary>
            Creates a <see cref="T:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata"/>
            </summary>
            <param name="runtime">Runtime</param>
            <param name="isFileLayout">true if it's file layout, false if it's memory layout</param>
            <param name="moduleAddress">Address of .NET module in the process' address space</param>
            <param name="moduleSize">Size of module</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadataService.Create(dnSpy.Contracts.Debugger.DbgRuntime,System.Boolean,System.Byte[])">
            <summary>
            Creates a <see cref="T:dnSpy.Contracts.Debugger.DotNet.Metadata.Internal.DbgRawMetadata"/>
            </summary>
            <param name="runtime">Runtime</param>
            <param name="isFileLayout">true if it's file layout, false if it's memory layout</param>
            <param name="moduleBytes">Raw module bytes</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.PredefinedDotNetDbgRuntimeTags">
            <summary>
            Predefined <see cref="T:dnSpy.Contracts.Debugger.DbgRuntime"/> tags (<see cref="P:dnSpy.Contracts.Debugger.DbgRuntime.Tags"/>)
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.PredefinedDotNetDbgRuntimeTags.DotNetBase">
            <summary>
            .NET runtime (any)
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.PredefinedDotNetDbgRuntimeTags.DotNetFramework">
            <summary>
            .NET Framework runtime
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.PredefinedDotNetDbgRuntimeTags.DotNet">
            <summary>
            .NET runtime
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.PredefinedDotNetDbgRuntimeTags.DotNetMono">
            <summary>
            .NET Mono runtime
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.PredefinedDotNetDbgRuntimeTags.DotNetUnity">
            <summary>
            .NET Unity runtime
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper">
            <summary>
            .NET stepper
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.maxReturnValues">
            <summary>
            Max number of return values to save
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.SessionBase">
            <summary>
            Session
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.SessionBase.Tag">
            <summary>
            Gets the tag
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.SessionBase.#ctor(System.Object)">
            <summary>
            Constructor
            </summary>
            <param name="tag">Tag</param>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.Session">
            <summary>
            Gets/sets the session. It's null if there's no step operation in progress.
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.CreateSession(System.Object)">
            <summary>
            Creates a new <see cref="T:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.SessionBase"/>
            </summary>
            <param name="tag">Tag</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.IsRuntimePaused">
            <summary>
            true if the runtime is paused
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.ContinueCounter">
            <summary>
            Gets the countinue counter. It's incremented each time the process is continued.
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.TryGetFrameInfo(dnSpy.Contracts.Debugger.DbgThread)">
            <summary>
            Gets frame info or null if none is available
            </summary>
            <param name="thread">Thread</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.Continue">
            <summary>
            Lets the process run
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.StepOutAsync(dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepperFrameInfo)">
            <summary>
            Steps out
            </summary>
            <param name="frame">Frame info</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.StepIntoAsync(dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepperFrameInfo,dnSpy.Contracts.Debugger.DotNet.Code.DbgCodeRange[])">
            <summary>
            Steps into
            </summary>
            <param name="frame">Frame info</param>
            <param name="ranges">Statement ranges</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.StepOverAsync(dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepperFrameInfo,dnSpy.Contracts.Debugger.DotNet.Code.DbgCodeRange[])">
            <summary>
            Steps over
            </summary>
            <param name="frame">Frame info</param>
            <param name="ranges">Statement ranges</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.CancelLastStep">
            <summary>
            Cancels last step operation
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.CollectReturnValues(dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepperFrameInfo,dnSpy.Contracts.Debugger.DotNet.Code.DbgILInstruction[][])">
            <summary>
            Prepares collecting return values
            </summary>
            <param name="frame">Frame info</param>
            <param name="statementInstructions">Statement instructions</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.ClearReturnValues">
            <summary>
            Clears all return values
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.CreateBreakpoint(dnSpy.Contracts.Debugger.DbgThread,dnSpy.Contracts.Debugger.DbgModule,System.UInt32,System.UInt32)">
            <summary>
            Creates a breakpoint
            </summary>
            <param name="thread">Thread or null to match any thread</param>
            <param name="module">Module</param>
            <param name="token">Method token</param>
            <param name="offset">IL offset</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.RemoveBreakpoints(dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetStepperBreakpoint[])">
            <summary>
            Removes breakpoints
            </summary>
            <param name="breakpoints">Breakpoints to remove</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.OnStepComplete">
            <summary>
            Called when the step is complete
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.OnCanceled(dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.SessionBase)">
            <summary>
            Called when it gets canceled
            </summary>
            <param name="session">Session</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.IgnoreException(System.Exception)">
            <summary>
            Returns true if the exception should be ignored eg. because the process has exited
            </summary>
            <param name="exception">Thrown exception</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper.Close(dnSpy.Contracts.Debugger.DbgDispatcher)">
            <summary>
            Cleans up
            </summary>
            <param name="dispatcher">Dispatcher</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetStepperBreakpoint">
            <summary>
            A code breakpoint used by the .NET stepper
            </summary>
        </member>
        <member name="E:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetStepperBreakpoint.Hit">
            <summary>
            Raised when the breakpoint is hit
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetStepperBreakpointEventArgs">
            <summary>
            Stepper breakpoint event args
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetStepperBreakpointEventArgs.Thread">
            <summary>
            Gets the thread
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetStepperBreakpointEventArgs.Pause">
            <summary>
            Set to true by the event handler to pause the process
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetStepperBreakpointEventArgs.#ctor(dnSpy.Contracts.Debugger.DbgThread)">
            <summary>
            Constructor
            </summary>
            <param name="thread">Current thread</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepperFrameInfo">
            <summary>
            Frame info needed by the stepper
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepperFrameInfo.SupportsReturnValues">
            <summary>
            true if return values are supported
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepperFrameInfo.Thread">
            <summary>
            Gets the thread
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepperFrameInfo.TryGetLocation(dnSpy.Contracts.Debugger.DbgModule@,System.UInt32@,System.UInt32@)">
            <summary>
            Gets the location
            </summary>
            <param name="module">Module</param>
            <param name="token">Method token</param>
            <param name="offset">IL offset</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepperFrameInfo.Equals(dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepperFrameInfo)">
            <summary>
            Checks if this frame is the same as another frame <paramref name="other"/>
            </summary>
            <param name="other">Other frame</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgEngineStepperFactory">
            <summary>
            Creates a .NET stepper
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgEngineStepperFactory.Create(dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime,dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.DbgDotNetEngineStepper,dnSpy.Contracts.Debugger.DbgThread)">
            <summary>
            Creates a .NET stepper
            </summary>
            <param name="runtime">Runtime</param>
            <param name="stepper">Stepper</param>
            <param name="thread">Thread</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.StepErrorException">
            <summary>
            Thrown when there was an error stepping
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.StepErrorException.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="message">Message</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.ForciblyCanceledException">
            <summary>
            Thrown when the stepper was forcibly canceled
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Steppers.Engine.ForciblyCanceledException.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="message">Message</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText">
            <summary>
            Contains text and color
            </summary>
        </member>
        <member name="F:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText.Empty">
            <summary>
            Gets the empty instance
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText.Parts">
            <summary>
            Gets all colors and text parts
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText.#ctor(dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetTextPart)">
            <summary>
            Constructor
            </summary>
            <param name="text">Text</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText.#ctor(dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetTextPart[])">
            <summary>
            Constructor
            </summary>
            <param name="parts">Color and text</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText.WriteTo(dnSpy.Contracts.Debugger.Text.IDbgTextWriter)">
            <summary>
            Writes all text and colors to <paramref name="output"/>
            </summary>
            <param name="output">Output</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText.ToString">
            <summary>
            Gets all text
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetTextPart">
            <summary>
            Color and text
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetTextPart.Color">
            <summary>
            Gets the color
            </summary>
        </member>
        <member name="P:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetTextPart.Text">
            <summary>
            Gets the text
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetTextPart.#ctor(dnSpy.Contracts.Debugger.Text.DbgTextColor,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="color">Color</param>
            <param name="text">Text</param>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetTextOutput">
            <summary>
            Creates <see cref="T:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText"/>
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetTextOutput.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetTextOutput.Write(dnSpy.Contracts.Debugger.Text.DbgTextColor,System.String)">
            <summary>
            Writes text
            </summary>
            <param name="color">Color</param>
            <param name="text">Text</param>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetTextOutput.Create">
            <summary>
            Creates a <see cref="T:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetTextOutput.CreateAndReset">
            <summary>
            Creates a <see cref="T:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetText"/> and clears this instance so it can be reused
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DotNet.Text.DbgDotNetTextOutput.Clear">
            <summary>
            Clears this instance
            </summary>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DbgDotNetRuntimeExtensions">
            <summary>
            Extension methods
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DbgDotNetRuntimeExtensions.GetDotNetRuntime(dnSpy.Contracts.Debugger.DbgRuntime)">
            <summary>
            Gets the <see cref="T:dnSpy.Contracts.Debugger.DotNet.Evaluation.IDbgDotNetRuntime"/> instance or throws if it's not a .NET runtime
            </summary>
            <param name="runtime">Runtime</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DbgAppDomainExtensions">
            <summary>
            Extension methods
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DbgAppDomainExtensions.GetReflectionAppDomain(dnSpy.Contracts.Debugger.DbgAppDomain)">
            <summary>
            Gets the reflection app domain or null if this isn't a managed app domain
            </summary>
            <param name="appDomain">Debugger app domain</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DbgAppDomainExtensions.GetDotNetInternalAppDomain(dnSpy.Contracts.Debugger.DbgAppDomain)">
            <summary>
            Gets the internal .NET app domain or null if it's not a managed app domain
            </summary>
            <param name="appDomain"></param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DbgModuleExtensions">
            <summary>
            Extension methods
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DbgModuleExtensions.GetReflectionModule(dnSpy.Contracts.Debugger.DbgModule)">
            <summary>
            Gets the reflection module or null if this isn't a managed module
            </summary>
            <param name="module">Debugger module</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DbgModuleExtensions.GetDotNetInternalModule(dnSpy.Contracts.Debugger.DbgModule)">
            <summary>
            Gets the internal .NET module or null if it's not a managed module
            </summary>
            <param name="module"></param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DbgRuntimeExtensions">
            <summary>
            Extension methods
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DbgRuntimeExtensions.GetReflectionRuntime(dnSpy.Contracts.Debugger.DbgRuntime)">
            <summary>
            Gets the reflection runtime or null if this isn't a managed runtime
            </summary>
            <param name="runtime">Debugger runtime</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DbgRuntimeExtensions.GetDotNetInternalRuntime(dnSpy.Contracts.Debugger.DbgRuntime)">
            <summary>
            Gets the internal .NET runtime or null if it's not a managed runtime
            </summary>
            <param name="runtime"></param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.Evaluation.DbgValueExtensions">
            <summary>
            Extension methods
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.Evaluation.DbgValueExtensions.GetDotNetValue(dnSpy.Contracts.Debugger.Evaluation.DbgValue)">
            <summary>
            Gets the .NET engine value
            </summary>
            <param name="value">Value</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DbgModuleDotNetExtensions">
            <summary>
            <see cref="T:dnSpy.Contracts.Debugger.DbgModule"/> extension methods
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DbgModuleDotNetExtensions.IsDotNetModule(dnSpy.Contracts.Debugger.DbgModule)">
            <summary>
            Returns true if <paramref name="module"/> is a .NET module
            </summary>
            <param name="module">Module</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Contracts.Debugger.DbgRuntimeDotNetExtensions">
            <summary>
            <see cref="T:dnSpy.Contracts.Debugger.DbgRuntime"/> extension methods
            </summary>
        </member>
        <member name="M:dnSpy.Contracts.Debugger.DbgRuntimeDotNetExtensions.IsDotNetRuntime(dnSpy.Contracts.Debugger.DbgRuntime)">
            <summary>
            Returns true if <paramref name="runtime"/> is a .NET runtime
            </summary>
            <param name="runtime">Runtime</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdAppDomainExtensions">
            <summary>
            Extension methods
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomainExtensions.GetDebuggerAppDomain(dnSpy.Debugger.DotNet.Metadata.DmdAppDomain)">
            <summary>
            Gets the debugger AppDomain object
            </summary>
            <param name="appDomain">Debugger metadata AppDomain object</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdModuleExtensions">
            <summary>
            Extension methods
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModuleExtensions.GetDebuggerModule(dnSpy.Debugger.DotNet.Metadata.DmdModule)">
            <summary>
            Gets the debugger module object or returns null if there is none (eg. it's a synthetic module)
            </summary>
            <param name="module">Debugger metadata module object</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdRuntimeExtensions">
            <summary>
            Extension methods
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdRuntimeExtensions.GetDebuggerRuntime(dnSpy.Debugger.DotNet.Metadata.DmdRuntime)">
            <summary>
            Gets the debugger runtime object
            </summary>
            <param name="runtime">Debugger metadata runtime object</param>
            <returns></returns>
        </member>
    </members>
</doc>
