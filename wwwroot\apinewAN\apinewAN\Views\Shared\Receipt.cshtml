﻿@model AppTech.MSMS.Web.Models.ReceiptModel
@Styles.Render("~/Content/print")


<div class=" col-sm-12" style="border: 1px solid black;">

    <div class="col-sm-6 pull-right" style="margin-top: 10px">
        التاريخ : @Html.DisplayFor(model => model.Date)
    </div>

    <div class="col-sm-6 align-left" style="margin-top: 10px">
        رقم السند : @Html.DisplayFor(model => model.Number)
    </div>

    <div class="col-sm-12 align-center" style="border-bottom: 1px solid black; margin-top: 1px; margin-bottom: 20px; width: 100%; padding: 8px 0px; height: 30px; font: bold 18px Helvetica, Sans-Serif;">
        @Html.DisplayFor(model => model.Type)
    </div>


    @*<div id="" class="reportheadertitle col-sm-6 col-xs-6 " >@Html.DisplayFor(model => model.Type)</div>*@

    <div class="table table-responsive">
        <table id="simple-table" class="table table-condensed">

            <tr>
                <td class=" col-sm-2">
                    اسم الحساب
                </td>
                <td class=" col-sm-10">
                    @Html.DisplayFor(model => model.AccountName)
                </td>
            </tr>


        </table>
        <table id="simple-table" class="table">
            <tr>
                <td>
                    المبلغ
                </td>
                <td>
                    @Html.DisplayFor(model => model.Amount)
                </td>

                <td>
                    العملة
                </td>
                <td>
                    @Html.DisplayFor(model => model.CurrencyName)
                </td>
            </tr>

        </table>

        <table id="" class="table">
            <tr>
                <td class=" col-sm-2">
                    مبلغ وقدرة
                </td>
                <td class=" col-sm-10">
                    @Html.DisplayFor(model => model.AmountInText)
                </td>
            </tr>

        </table>

        <table id="simple-table" class="table">
            @if (!string.IsNullOrEmpty(Model.DeliveryTitle))
            {
                <tr>
                    <td>
                        @Html.DisplayFor(model => model.DeliveryTitle)
                    </td>
                    <td>
                        @Html.DisplayFor(model => model.Delivery)
                    </td>
                </tr>
            }


            <tr>
                <td>
                    وذلك مقابل
                </td>
                <td>
                    @Html.DisplayFor(model => model.Note)
                </td>
            </tr>


            <tr>
                <td>
                    التوقيع

                </td>

                <td>


                    <div>
                        <textarea></textarea>
                    </div>
                </td>

            </tr>


        </table>
    </div>
</div>