﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.CashOut

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}



    <div class="row">



        <div class="col-xs-12 col-sm-6">

            <div class="form-group">
                <label class="col-md-2 control-label">طريقة الصرف</label>

                <div class="col-md-10">
                    @Html.DropDownListFor(model => model.Method, new[]
                    {
                        new SelectListItem {Text = "نقد", Value = "نقد", Selected = true},
                        new SelectListItem {Text = "بنك", Value = "بنك"},
                        new SelectListItem {Text = "صراف", Value = "صراف"},
                    })
                </div>
                @Html.ValidationMessageFor(model => model.Method)
            </div>



            <div class="form-group form-inline">
                @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
                <div class="col-md-10 form-inline">
                    @Html.EditorFor(model => model.Amount, new { @class = "col-md-8" })
                    <label id="words" class="red"></label>
                    @Html.ValidationMessageFor(model => model.Amount)
                </div>
            </div>


            <div class="form-group">
                @Html.LabelFor(model => model.DebitorAccountID, new { @class = "control-label col-md-2" })
                <div class="col-sm-10">
                    @Html.Obout(new ComboBox("DebitorAccountID")
                    {
                        Width = 250,
                        SelectedValue = Model.DebitorAccountID == 0 ? null : Model.DebitorAccountID.ToString(),
                        FilterType = ComboBoxFilterType.Contains
                    })
                    @Html.ValidationMessageFor(model => model.DebitorAccountID)
                </div>
            </div>



            <div class="form-group">
                @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
                    @Html.ValidationMessageFor(model => model.Note)
                </div>
            </div>



        </div>









        <div class="col-xs-12 col-sm-6">



            <div class="form-group">
                @Html.Label("الصندوق", new { @id = "exchange_name", @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <select id="CreditorAccountID" name="CreditorAccountID"></select>
                    @Html.ValidationMessageFor(model => model.CreditorAccountID, "", new { @class = "text-danger" })
                </div>
            </div>


            <div class="form-group">
                <div class="col-md-12">
                    @Html.LabelFor(model => model.CurrencyID, new { @class = "control-label col-md-2" })
                    @Html.DropDownListFor(m => m.CurrencyID, (SelectList)ViewBag.Currencies)
                </div>
            </div>


            <div class="form-group">

                @Html.LabelFor(model => model.Date, new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.Date, new { htmlAttributes = new { @class = "date-picker" } })
                    @Html.ValidationMessageFor(model => model.Date, "", new { @class = "text-danger" })
                </div>
            </div>



            <div class="form-group">
                <label class="col-sm-2 control-label no-padding-right" for="form-field-delvry">مناولة</label>

                <div class="col-sm-10">
                    @Html.EditorFor(model => model.Delivery, new { @class = "col-xs-12 col-sm-10", id = "form-field-delvry", placeholder = "Username" })
                    @Html.ValidationMessageFor(model => model.Delivery)
                </div>
            </div>


            <div class="form-group">
                <label class="col-sm-2 control-label no-padding-right" for="form-field-ref">رقم المرجع</label>

                <div class="col-sm-10">
                    @Html.EditorFor(model => model.RefNumber, new { @class = "col-sm-10", id = "form-field-ref" })
                </div>
            </div>



        </div>
        
        


    </div>

@*<script>

        $(document).ready(function() {
            $("#AccountName").autocomplete({
                source: function(request, response) {
                    $.ajax({
                        url: '@Url.Action("GetVisitCustomer", "CashOut")',
                        datatype: "json",
                        data: {
                            Areas: 'Sales',
                            term: request.term
                        },
                        success: function(data) {
                            response($.map(data,
                                function(val, item) {
                                    return {
                                        label: val.Name,
                                        value: val.Name,
                                        clientId: val.ID
                                    };
                                }));
                        }
                    });
                },
                select: function(event, ui) {
                    $("#AccountID").val(ui.item.clientId);
                }
            });
        });

    </script>*@
<script>



    $(function () {

        loadDataList();
        $('#Method').on("change",
            function () {
                i('change');
                loadDataList();
            });

        function loadDataList() {
            var method = $('#Method').val();
            i('Method>' + method);
            var path = "GetFunds";
            var title = "اختر الصندوق";

            if (method === 'بنك') {
                title = "اختر البنك";
                $("#exchange_name").text("اسم البنك")
                path = "GetBanks";

            } else if (method === 'صراف') {
                title = "اختر صراف";
                $("#exchange_name").text("اسم صراف")
                path = "GetExchangers";

            }
            else {
                $("#exchange_name").text("اسم الصندوق")
            }

            fillListWithSelected('CreditorAccountID',@Model.CreditorAccountID, '/GeneralLedger/Account/' + path, true, title)

        }
        //function fillList(response, element, title) {

        //    $('#' + element).html('');
        //    var options = '';
        //    options += '<option value="Select">' + title + '  </option>';
        //    for (var i = 0; i < response.length; i++) {
        //        options += '<option value="' + response[i].AccountID + '">' + response[i].Name + '</option>';
        //    }
        //    $('#' + element).append(options);
        //}
    });

</script>