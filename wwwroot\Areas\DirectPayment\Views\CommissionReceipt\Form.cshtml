﻿@model AppTech.MSMS.Domain.Models.CommissionReceipt
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="row">
    <div class="search-area well well-sm">
        <div class="search-filter-header bg-primary">
            <h5 class="smaller no-margin-bottom">
                <i class="ace-icon fa fa-sliders light-green bigger-130"></i>&nbsp; البحث خلال فتره
            </h5>
        </div>
        <div class="space-10"></div>
        <div class="hr hr-dotted"></div>
        <div class="col-xs-12 col-sm-6">
            <div class="form-group" id="group">
                @Html.Label("المجموعة", new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DropDownListFor(model => model.AccountGroupID, (SelectList)ViewBag.Groups, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.AccountGroupID, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.ServiceID, new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DropDownListFor(m => m.ServiceID, (SelectList)ViewBag.Services)
                    @Html.ValidationMessageFor(model => model.ServiceID)
                </div>
            </div>

        </div>
        <div class="col-xs-12 col-sm-6">
            <div class="form-group">
                @Html.LabelFor(model => model.StartDate, new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.StartDate)
                    @Html.ValidationMessageFor(model => model.StartDate, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.EndDate, new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.EndDate)
                    @Html.ValidationMessageFor(model => model.EndDate, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.Percentage, new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.Percentage)
                    @Html.ValidationMessageFor(model => model.Percentage, "", new { @class = "text-danger" })
                </div>
            </div>


            <div class="form-group">
                @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.Note)
                    @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger", @readonly = "readonly" })
                </div>
            </div>


        </div>
        <div class="space-6"></div>
        <div class="space-6"></div>
        <div class="hr hr-dotted"></div>
        @*<div class="text-right">
            @Ajax.ActionLink(
                "أستعلام",
                "GetGrossReceipt",
                null,
                new AjaxOptions
                {
                    LoadingElementId = "loader",
                    OnSuccess = "onSuccess",
                    HttpMethod = "GET" // <-- HTTP method
                },
                    new {@class = "btn btn-default btn-round btn-white", onclick = "this.href = '/directpayment/commissionReceipt/GetGrossReceipt?aid=' + document.getElementById('AccountGroupID').value +'&sid=' + document.getElementById('ServiceID').value +'&percentage=' + document.getElementById('Percentage').value +'&frdate=' + document.getElementById('StartDate').value +'&todate=' + document.getElementById('EndDate').value;"}
                )
            @Html.ValidationMessageFor(model => model.RefNumber)
        </div>*@
        <button type="button" id="buttonID" class="btn btn-default btn-round btn-white loading" data-loading-text="<i class='fa fa-spinner fa-spin '></i> جاري الاستعلام...">أستعلام</button>

        <div class="space-4"></div>
    </div>
</div>

<div id="Commissionlist">
    @*@RenderBody()*@

    @*@Html.Partial("_Commissionlist")*@

    @*<table class="table table-hover">
            <thead>
                <tr>
                    <th>اسم الفئة</th>
                    <th>سعر التكلفة</th>
                    <th>سعر البيع</th>
                    <th>الصلاحية</th>
                    <th>ملاحظات</th>
                    <th></th>
                </tr>
            </thead>
            @foreach (var Line in Model.CommissionReceiptLines)
            {
                <tbody>
                    <tr>
                        @if (Line != null)
                        {
                        <td>
                            <input type='number' value="@Math.Abs(Line.Amount)" name='Amount' id='Amount' />
                        </td>
                            <td>@Html.DisplayFor(modelItem => Line.TotalTopup)</td>
                            <td>@Html.DisplayFor(modelItem => Line.Amount)</td>
                            <td>@Html.DisplayFor(modelItem => Line.Note)</td>
                            <td>@Html.DisplayFor(modelItem => Line.Note)</td>

                        }
                    </tr>
                </tbody>
            }
        </table>*@






    @*@Html.Partial("_Commissionlist")*@

    @*<div class="form-group">
            @Html.Label("عدد العمليات", new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                <div class="checkbox">
                    @Html.EditorFor(model => model.OpsCount)
                    @Html.ValidationMessageFor(model => model.OpsCount, "", new { @class = "text-danger", @readonly = "readonly" })
                </div>
            </div>
        </div>

        <div class="form-group">
            @Html.Label("إجمالي المبلغ", new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                <input type="text" id="RefNumber" name="RefNumber" readonly="readonly" />
            </div>
        </div>




        <div class="form-group">
            @Html.Label("إحمالي العمولة", new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Amount)
                @Html.ValidationMessageFor(model => model.Amount, "", new { @class = "text-danger", @readonly = "readonly" })
            </div>
        </div>*@


</div>
@*<div id="totalAmount">
    
    <input type="text" name="Amountss" value="@ViewBag.Amount" />
    <input type="hidden" name="Amount" value="@ViewBag.Amount" />

</div>*@

<script>
    

    $(function() {
        $("#ServiceID", "#AccountID", "#StartDate", "EndDate").on('change',
            function() {
                $("#RefNumber").val('');
                $("#OpsCount").val('');
                $("#Percentage").val('');
                $("#Amount").val('');
            });
    });

    $('#OpsCount').prop('readonly', true);
    $('#Amount').prop('readonly', true);
    $("#form").hide();
    $("#totalAmount").hide();

    $('#Percentage').on('input',
        function () {
            i('perc input ');
            var rate = Number($('#Percentage').val());
            var refNumber = Number($('#RefNumber').val().replace(',', ''));
            var commission = refNumber * (rate / 100);
            i('commission ' + commission);
            $('#Amount').val(commission);
        });

    function onSuccess(data) {
        if (data.Success) {

            $("#form").show();
            $("#RefNumber").val(data.total);
            $("#OpsCount").val(data.num);
        } else {
            alert(data.Message);
        }
    }

    $("#buttonID").on('click',
        function () {
             var data = JSON.stringify({
                AccountGroupID: $("#AccountGroupID").val(),
                ServiceID: $("#ServiceID").val(),
                StartDate: $("#StartDate").val(),
                EndDate: $("#EndDate").val(),
                Percentage: $("#Percentage").val(),
                Note: $("#Note").val(),
             });
            i('perc data '+data);

            $.ajax({
                    method: "POST",
                    url: 'DirectPayment/CommissionReceipt/GetGrossReceipts',
                   contentType: "application/json",
                    data: data ,
                success: function (data) {
                         i('perc data '+data);
                    console.log(data);
                         resetButton();
                        //showSuccess(data.Message);
                        //     $("#modal").modal('hide');
                         i('perc data '+data);
                        $("#Commissionlist").html(data);
                        $("#totalAmount").show();
                            //fetchData(false);
                        },
                error: function (xhr, textStatus, errorThrown) {
                    parseAndShowError(xhr, textStatus, errorThrown)
                }
                });

        });
</script>