﻿$(function () {
    $("#btnLogin").click(function () {
        console.log("btnLogin clicked");

        var passwordInput = $("#Password");
        var password = passwordInput.val();
        if (password !== "" && password !== "undefined") {

            var seed = $("#hdrandomSeed").val();
           
            var hashedPass = enc(password);
            passwordInput.val(hashedPass);

            return Tokenize(seed);
        }
        $("#loginform").submit();
    });

    $("#UUID").val(finguard());
 
});

function enc(text) {
    console.log("start enc");
    var encryptedpassword = "";
    try {

        var key = CryptoJS.enc.Utf8.parse('8080808080808080');
        var iv = CryptoJS.enc.Utf8.parse('8080808080808080');

        //var encryptedlogin = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(text), key,
        //    {
        //        keySize: 128 / 8,
        //        iv: iv,
        //        mode: CryptoJS.mode.CBC,
        //        padding: CryptoJS.pad.Pkcs7
        //    });


         encryptedpassword = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(text), key,
            {
                keySize: 128 / 8,
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            });

        //alert('encrypted login :' + encryptedlogin);
        //alert('encrypted password :' + encryptedpassword); 

        console.log('encrypted ' + encryptedpassword);
    } catch (e) {
        console.log(e);
    } 
    return encryptedpassword;
}

function Tokenize(seed) {
    try {
        console.log("start Tokenize");
        var username = $("#Email").val();
        var hash = md5(seed + md5(username).toUpperCase());

        console.log("set token val: "+hash);
    
        $("#Token").val(hash);
        return true;
    } catch (e) {
        console.log("error Tokeniz "+e);
    } 
}
function hashing(seed) {
    console.log("start hash");
    var passwordInput = $("#Password");
    var password = passwordInput.val();
    //    var md5 = require('md5');
    var hash = md5(seed + md5(password).toUpperCase());

    console.log(hash);
    passwordInput.val(hash);
    return true;
}