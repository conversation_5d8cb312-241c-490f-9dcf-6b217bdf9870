﻿@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.BagatPayment


<div class="col-sm-offset-1 col-sm-10">

    @using (Ajax.BeginForm("MakeBagat", new AjaxOptions {LoadingElementId = "loader", OnSuccess = "onSuccess", OnFailure = "OnFailure", OnBegin = "return OnBagatBegin($(this))"}))
    {
        @Html.AntiForgeryToken()

        <div class="form-horizontal">

            <div class="tab-content">
                <h4> @ViewBag.FormTitle</h4>
                <hr />
                @Html.ValidationSummary(true, "", new { @class = "text-danger" })

                <input type="hidden" name="Device" value="Web" />

                @if (CurrentUser.Type == UserType.Admin)
                {
                    <div class="form-group">
                        @Html.Label("اسم الحساب", new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.Obout(new ComboBox("AccountID")
                            {
                                Width = 300,
                                SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
                                FilterType = ComboBoxFilterType.Contains,
                                LoadingText = "Loading"
                            })
                            @*<select id="AccountID" name="AccountID" class="select2" placeholder="أختر حساب"></select>*@
                            @Html.ValidationMessageFor(model => model.AccountID, "", new { @class = "text-danger" })
                        </div>
                    </div>
                }
                <div class="form-group">
                    @Html.LabelFor(model => model.SubscriberNumber, new { @class = "control-label col-md-2" })
                    <div class="col-md-10 form-inline">
                        @Html.EditorFor(model => model.SubscriberNumber)


                        <a id="search" href="" onclick=" queryBalance() " class="btn btn-white btn-info btn-round">
                            <i class="ace-icon fa fa-search bigger-110"></i>
                            الأستعلام
                        </a>
                        @Html.ValidationMessageFor(model => model.SubscriberNumber)
                    </div>
                </div>


                <div id="sub">
                   
                </div>


            </div>
        </div>

        <div class="clearfix form-actions">
            <div class="col-md-offset-3 col-md-9">


            </div>

        </div>
    }
</div>

<script type="text/javascript">

    $(function() {
        $('#loader').hide();
        $('#SubscriberNumber').on('input',
            function() {
                i('key down sid:');
                var sno = $("#SubscriberNumber").val();
                if (sno.length === 9 && sno.match("^77")) {
                    i('sno.length === 9 && sno.match("^77")');
                    queryBalance();
                }
            });
    });

    var title = $('#FormTitle').val();
    $("#page-title").text(title);


    function OnBagatBegin(value) {

        //i('OnBagatBegin');
        //var buttonValue = getParameterByName(value[0].data, "submitButton");

        //i('buttonValue: ' + buttonValue);
        //switch (buttonValue) {
        //case 'onlybagat':
        //    //Do your stuff here
        //    break;
        //case 'topupandBagat':
        //    //Do your stuff here
        //    break;
        //default:
        //    //default code block
        //}



             var sno = $("#SubscriberNumber").val();
        if (sno.length !== 9 || !sno.match("^77")) {
            ar('رقم المشترك غير صحيح');
            return false;
        }

        var amount = $("#Amount").val();
        //if (amount === "") {
        //    ar('قم بأدخال  مبلغ التسديد');
        //    return false;
        //}

         var selectedText = $("#OfferCode option:selected").html();
        amount = ' باقة ' + selectedText +' مبلغ ' +amount ;

        var msg = "سوف يتم تفعيل لرقم " + sno+ amount + ' هل انت متأكد ';
        if (!confirm(msg)) {
            i('not confirmed');
            return false;
        } else {

            i('confirmed');
            return true;
        }
        return true;

    }

    function getParameterByName(data, name) {

        name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");

        name = '?' + name; //add ? on front of string for a simplified RegEx search

        var regex = new RegExp("[\\?&]" + name + "=([^&#]*)"), results = regex.exec(data);

        return results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
    }

    function onSuccess(data) {

        if (data.Success) {
            alert(data.Message);
            //   $('#search').trigger('click');
        } else {

            alert(data.Message);
        }
    }

    function OnFailure(xhr, status) {
        hideLoading();
        log('onCrudFailure ymoffers ');
        //  hideLoading();
        var msg = parseXhr(xhr);
        log('onCrudFailure xhr msg:' + msg);
        alert(msg);
        //   showError(msg);
    }

    function onTopupSuccess(data) {

        if (data.Success) {
            alert("تم التسديد بنجاح");
            $("#Amount").val("");
        } else {
            alert(data.Message);
        }

    }

    function onTopupFailure(xhr, status) {

        var msg = parseXhr(xhr);
        log('onCrudFailure xhr msg:' + msg);
        alert(msg);
    }


    function onTopupBegin(context) {
        var sno = $("#SubscriberNumber").val();
        if (sno.length !== 9 || !sno.match("^77")) {
            ar('رقم المشترك غير صحيح');
            return false;
        }

        var amount = $("#Amount").val();
        if (amount === "") {
            ar('قم بأدخال  مبلغ التسديد');
            return false;
        }

         var selectedText = $("#OfferCode option:selected").html();
        amount = ' باقة ' + selectedText +' مبلغ ' +amount ;

        var msg = "سوف يتم تفعيل لرقم " + sno+ amount + ' هل انت متأكد ';
        if (!confirm(msg)) {
            i('not confirmed');
            return false;
        } else {

            i('confirmed');
            return true;

        }
        return true;
    }

    function queryBalance() {
        showLoading();
        var sno = $("#SubscriberNumber").val();

        var url = '/Clients/YMOfferPayment/QueryBalance';
        $.ajax({
            url: url,
            data: { sno: sno, sid: "" },
            success: function(data) {
                hideLoading();
                $("#sub").replaceWith(data);
            },
            error: function(xhr, ajaxOptions, thrownError) {
                hideLoading();
                var msg = parseXhr(xhr);
                log('onCrudFailure xhr msg:' + msg);
                alert(msg);
            }
        });
    }

</script>

<script>
    function renew_offer(id, name, oa) {

        showLoading();
        var sno = $("#SubscriberNumber").val();
        var lineType = $("#lineType").val();
        $.ajax({
            url: '/Clients/YMOfferPayment/ChangeOffer',
            data: { sno: sno, offercode: id, offerAction: oa, lineType: lineType, name: name },
            success: function(data) {
                hideLoading();
                $("#loader").hide();
                var result = data;
                if (result.Success) {
                    alert("تمت العملية بنجاح , سوف يتم اعاده الاستعلام ");
                    queryBalance();
                    //$('#search').trigger('click');
                } else {
                    alert(result.Message);
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                hideLoading();
                handleXhr(xhr);
            }
        });
    }


</script>