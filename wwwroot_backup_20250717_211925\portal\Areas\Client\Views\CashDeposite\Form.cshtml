﻿@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.CashDeposit
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="row">
    
    
    
    <div class="col-xs-12 col-sm-6">
        
        
        
        

        <div class="form-group">
            <label class="col-md-2 control-label">طريقة الإيداع</label>
            <div class="col-md-10">
                @Html.DropDownListFor(model => model.Attachments, new[] {new SelectListItem {Text = "نقد", Value = "نقد", Selected = true}, new SelectListItem {Text = "بنك", Value = "بنك"}, new SelectListItem {Text = "صراف", Value = "صراف"},})
            </div>
            @Html.ValidationMessageFor(model => model.Attachments)
        </div>
        
        
        
        <div class="form-group form-inline">
            @Html.LabelFor(model => model.Amount, new {@class = "control-label col-md-2"})
            <div class="col-md-10 form-inline">
                @Html.EditorFor(model => model.Amount, new {@class = "col-md-8"})
                <label id="words" class="red"></label>
                @Html.ValidationMessageFor(model => model.Amount)
            </div>
        </div>
        
        
        
        <div class="form-group">
            <div class="col-md-12">
                @Html.Label("اسم العميل", new {@class = "control-label col-md-2"})
                @Html.Obout(new ComboBox("CreditorAccountID") { Width = 300, SelectedValue = Model.CreditorAccountID == 0 ? null : Model.CreditorAccountID.ToString(), FilterType = ComboBoxFilterType.Contains, LoadingText = "Loading" })

                @Html.ValidationMessageFor(model => model.CreditorAccountID)
            </div>
        </div>
        
        
        
        
        <div class="form-group">
            @Html.Label("ملاحظات", new {@class = "control-label col-md-2"})
            <div class="col-md-10">
                @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control", id = "form-field-note"}})
                @Html.ValidationMessageFor(model => model.Note)
            </div>
        </div>
        
        
        
        

    </div>
    
    
    
    
    <div class="col-xs-12 col-sm-6">
        
        
        
        
        <div class="form-group">
            @Html.Label("الصندوق", new {@id = "exchange_name", @class = "control-label col-md-2"})
            <div class="col-md-10">
                <select id="DebitorAccountID" name="DebitorAccountID"></select>
                @Html.ValidationMessageFor(model => model.DebitorAccountID, "", new {@class = "text-danger"})
            </div>
        </div>

        
        
        
        
        <div class="form-group">
            <div class="col-md-12">
                @Html.LabelFor(model => model.CurrencyID, new {@class = "control-label col-md-2"})
                @Html.DropDownListFor(m => m.CurrencyID, (SelectList) ViewBag.Currencies)
            </div>
        </div>
        
        
        
        
        <div class="form-group">

            <div class="col-md-12">
                @Html.Label("التاريخ", new {@class = "control-label col-md-2"})
                @Html.EditorFor(model => model.Date, new {htmlAttributes = new {@class = "date-picker"}})

            </div>
        </div>

        
        
        
        
        <div class="form-group">
            <div class="col-md-12">
                @Html.Label("المودع", new {@class = "control-label col-md-2"})
                @Html.EditorFor(model => model.Depositor)
                @Html.ValidationMessageFor(model => model.Depositor)
            </div>
        </div>
    </div>
    

  

    


 

   
   


   


    
</div>

<script>

    $(function () {
        $("#submit-button").text('إيداع نقدي');
        loadDataList();
        var exchangeAccountId =@Model.DebitorAccountID;
        i('exchangeAccountId ' + exchangeAccountId);
        if (Number(exchangeAccountId) > 0) {
            i('set exchangeAccountId val');
            $('#DebitorAccountID').val(exchangeAccountId);
        }

        $('#Attachments').on("change",
            function () {
                i('change');
                loadDataList();
            });

        function loadDataList() {
            var method = $('#Attachments').val();
            i('Method>' + method);
            var path = "GetFunds";
            var title = "اختر الصندوق";

            if (method === 'بنك') {
                title = "اختر البنك";
                $("#exchange_name").text("اسم البنك")
                path = "GetBanks";

            }
            else if (method === 'صراف') {
                title = "اختر صراف";
                $("#exchange_name").text("اسم صراف")
                path = "GetExchangers";

            }
            else {
                $("#exchange_name").text("اسم الصندوق")
            }
            fillListWithSelected('DebitorAccountID',@Model.DebitorAccountID, '/GeneralLedger/Account/' + path, true, title)
        }

    });

</script>

