﻿@using AppTech.Common.Extensions
@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@model AppTech.MSMS.Domain.Models.ExternalBranch


<div class="hr dotted"></div>
<span class="alert"></span>

<div id="user-profile-1" class="user-profile row">
<div class="col-xs-12 col-sm-3 center">


    <div>
        <span class="profile-picture">
            @if (!string.IsNullOrEmpty(Model.ImageName) && Model.ImageName.StartsWith("~"))
            {

                <img class="img-thumbnail editable img-responsive" alt="image"  id="preview"
                     src="@Url.Action("GetImage", "ExternalBranch",
                                  new {Model.ID})" />
            }
        </span>

        <div class="space-4"></div>

        <div class="width-80 label label-info label-xlg arrowed-in arrowed-in-right">
            <div class="inline position-relative">
                <a href="" class="user-title-label dropdown-toggle" data-toggle="dropdown">
                    
                        <i class="ace-icon fa fa-circle red"></i>


                    &nbsp;
                    <span class="white">@Model.Name</span>
                </a>

                
            </div>
        </div>
    </div>

    <div class="space-6"></div>

    @*<div class="profile-contact-info">
        <input type="hidden" id="id" value="@Model.ID"/>
        <div class="profile-contact-links align-right">

            <a class="btn btn-link " id="permission" href="">
                <i class="ace-icon fa fa-user-secret bigger-120 green"></i>
                الصلاحيات
            </a>
            <a class="btn btn-link " id="documents" href="">
                <i class="ace-icon fa fa-user-secret bigger-120 green"></i>
                الوثائق

            </a>
           

        </div>

        <div class="space-6"></div>

    </div>*@

    <div class="hr hr12 dotted"></div>
    <div class="clearfix">
        <div class="grid2">
            <span class="bigger-175 blue">@ViewBag.CurrentBalance</span>

            <br/>
            الرصيد
        </div>

        <div class="grid2">
            <span class="bigger-175 blue">@ViewBag.SlatingAmount</span>

            <br />
            مبلغ التسقيف
        </div>
    </div>

    <div class="hr hr16 dotted"></div>
</div>

<div class="col-xs-12 col-sm-9">

    <div class="space-12"></div>


    <div class="profile-user-info profile-user-info-striped">

        <div class="profile-info-row">
            <div class="profile-info-name"> رقم الفرع </div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @Html.DisplayFor(model => model.Number) </span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> اسم الفرع </div>

            <div class="profile-info-value">
                <span class="editable" id="username"> @Html.DisplayFor(model => model.Name)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> رقم الهاتف </div>

            <div class="profile-info-value">
                <span class="editable" id="login"> @Html.DisplayFor(model => model.PhoneNumber)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> العنوان </div>

            <div class="profile-info-value">
                <i class="fa fa-map-marker light-orange bigger-110"></i>
                <span class="editable" id="country">اليمن</span>
                <span class="editable" id="city"> @Html.DisplayFor(model => model.Address)</span>
            </div>
        </div>
        <div class="profile-info-row">
            <div class="profile-info-name">الفرع </div>

            <div class="profile-info-value">
                <span class="editable" id="login"> @Html.DisplayFor(model => model.Branch.Name)</span>
            </div>
        </div>

      
        <div class="profile-info-row">
            <div class="profile-info-name"> أرقام تواصل أخرى </div>

            <div class="profile-info-value">
                <span class="editable" id="login"> @Html.DisplayFor(model => model.ContactNumbers)</span>
            </div>
        </div>
      


        <div class="profile-info-row">
            <div class="profile-info-name"> تاريخ التسجيل </div>

            <div class="profile-info-value">
                <span class="editable" id="about"> @Html.DisplayFor(model => model.CreatedTime)</span>
            </div>
        </div>



        @*<div class="profile-info-row">
            <div class="profile-info-name"> صورة البطاقة </div>

            <div class="profile-info-value">
                @if (!string.IsNullOrEmpty(Model.ImageName) && Model.ImageName.StartsWith("~"))
                {
                    <img id="avatar" class="editable img-responsive" alt="Alex's Avatar" src="@Url.Content(Model.ImageName)"/>
                }

            </div>
        </div>*@


    </div>

 
</div>
</div>