﻿@model AppTech.MSMS.Web.Models.BalanceSheetModel

@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


<div class="form-horizontal">


    @{

        Html.RenderPartial("_DateControl");
    }


    @*<span class="lbl"> العملة</span>
    <select name="CurrencyID">

        <option value="0">كافة العملات</option>
        <option value="1">يمني</option>
        <option value="2">دولار</option>
        <option value="3">سعودي</option>
    </select>
    <div class="hr hr-dotted hr-24"></div>
    <div class="space-6"></div>*@
    <span class="lbl"> نوع التقرير</span>
    @Html.DropDownListFor(m => m.Type, (SelectList) ViewBag.ReportType)

</div>