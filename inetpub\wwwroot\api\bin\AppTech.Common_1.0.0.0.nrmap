<<type>>
<Module>
<Module>
<Module>
<Module>
<<type>>
uoPBbUW8esHcTlmAm2.tTDIo2vwAZrlM8Q4Db
gURRuBEKCwMP6T6X1M
tTDIo2vwAZrlM8Q4Db
gURRuBEKCwMP6T6X1M
<<type>>
AppTech.Security.Licensing.AppLicenseProvider
AppTech.Security.Licensing.AppLicenseProvider
AppLicenseProvider
AppLicenseProvider
ENOjYBwfa
VerifyFromLicFile
t3lva68sM
customerLicense
EnTWDIo2w
_currentInstance
RZr0lM8Q4
_type
nbuBoPBbU
_licensed
<<type>>
AppTech.Security.Licensing.CertificateHelper
AppTech.Security.Licensing.CertificateHelper
CertificateHelper
CertificateHelper
<<type>>
AppTech.Security.Licensing.CustomerFiles
AppTech.Security.Licensing.CustomerFiles
CustomerFiles
CustomerFiles
<<type>>
AppTech.Security.Licensing.CustomerInfo
AppTech.Security.Licensing.CustomerInfo
CustomerInfo
CustomerInfo
zesxHcTlm
<ID>k__BackingField
Rm2bif2ZR
<Name>k__BackingField
fQSQsdw6v
<Number>k__BackingField
wsLIn0iSW
<EnglishName>k__BackingField
qqctCXtDL
<CompanyName>k__BackingField
qFSUt2BhJ
<Contacts>k__BackingField
cFNrZSITQ
<Address>k__BackingField
THUV6NXxo
<Description>k__BackingField
<<type>>
AppTech.Security.Licensing.CustormTypeTypeConverter
AppTech.Security.Licensing.CustormTypeTypeConverter
CustormTypeTypeConverter
CustormTypeTypeConverter
<<type>>
AppTech.Security.Licensing.CustomerLicense
AppTech.Security.Licensing.CustomerLicense
CustomerLicense
CustomerLicense
BjK6oae9e
set_State
fc3DSIvHQ
set_LicenseSetting
RxIJHBMYa
SetLicenseSetting
FKeH7NjCp
<State>k__BackingField
xrkSqBAqv
<LicenseSetting>k__BackingField
wQNaY6vN1
type
<<type>>
AppTech.Security.Licensing.LicenseState
AppTech.Security.Licensing.LicenseState
LicenseState
LicenseState
<<type>>
diSWaqBcCXtDLaFSt2.Wf2ZRx0QSsdw6vysLn
AppTech.Security.Licensing.DesigntimeLicense
Wf2ZRx0QSsdw6vysLn
DesigntimeLicense
<<type>>
AppTech.Security.Licensing.LicenseTypes
AppTech.Security.Licensing.LicenseTypes
LicenseTypes
LicenseTypes
<<type>>
AppTech.Security.Licensing.LicenseStatus
AppTech.Security.Licensing.LicenseStatus
LicenseStatus
LicenseStatus
<<type>>
AppTech.Security.Licensing.VersionType
AppTech.Security.Licensing.VersionType
VersionType
VersionType
<<type>>
AppTech.Security.Licensing.SystemLevel
AppTech.Security.Licensing.SystemLevel
SystemLevel
SystemLevel
<<type>>
AppTech.Security.Licensing.HardwareInfo
AppTech.Security.Licensing.HardwareInfo
HardwareInfo
HardwareInfo
jR4Ko0GDp
GetDiskVolumeSerialNumber
PEvLCWsLK
GetProcessorId
g7SGeIt2c
GetMotherboardID
zH4nmThdL
SplitInParts
<<type>>
AppTech.Security.Licensing.IdentifierManager
AppTech.Security.Licensing.IdentifierManager
IdentifierManager
IdentifierManager
ccvuyPo5x
GetHash
NR02inVq4
GetHexString
s4QP7iB0W
identifier
XB1yr0osq
identifier
oEhe71M5j
cpuId
Cxh9BDlj6
biosId
KmpgnBAFn
diskId
kIe8pnBek
baseId
FyqMvn4qP
videoId
LMLCQ0DID
macId
tmgOJfGHZ
fingerPrint
<<type>>
AppTech.Security.Licensing.ShowInLicenseInfoAttribute
AppTech.Security.Licensing.ShowInLicenseInfoAttribute
ShowInLicenseInfoAttribute
ShowInLicenseInfoAttribute
<<type>>
AppTech.Security.Licensing.LicenseEntity
AppTech.Security.Licensing.LicenseEntity
LicenseEntity
LicenseEntity
LuA5gREyk
<AppName>k__BackingField
cvOmV4Oer
<UID>k__BackingField
lkek8SYlH
<Type>k__BackingField
Tr6X31oC6
<VersionType>k__BackingField
YkffhbShV
<ExpiryDate>k__BackingField
Q3g3kFBWB
<CreateDateTime>k__BackingField
<<type>>
AppTech.Security.Licensing.LicenseHelper
AppTech.Security.Licensing.LicenseHelper
LicenseHelper
LicenseHelper
<<type>>
AppTech.Security.Licensing.LicenseInfo
AppTech.Security.Licensing.LicenseInfo
LicenseInfo
LicenseInfo
pQJFpAMGd
<SystemLevel>k__BackingField
WZOE5hTXl
<MaxUsers>k__BackingField
TEPqjNwWx
<MaxBranches>k__BackingField
NW2TXGIWd
<Modules>k__BackingField
oxU4VhCsM
<CustomerNumber>k__BackingField
jtulUObFb
<CustomerName>k__BackingField
<<type>>
AppTech.Security.Licensing.LicenseSetting
AppTech.Security.Licensing.LicenseSetting
LicenseSetting
LicenseSetting
IuUN5MOGP
<CustomerInfo>k__BackingField
FKGYvGrVv
<SystemLevel>k__BackingField
dhthFZwQs
<VersionType>k__BackingField
cHDwROZEl
<MaxUsers>k__BackingField
KrV7ob3Su
<MaxBranches>k__BackingField
SfBAfHmuW
<Modules>k__BackingField
hlLZ4EgpV
<Properties>k__BackingField
<<type>>
AppTech.Security.Licensing.LicenseVerifier
AppTech.Security.Licensing.LicenseVerifier
LicenseVerifier
LicenseVerifier
UnwsqmWxK
VerifyXml
<<type>>
AppTech.Security.Licensing.Controls.LicenseActivateControl
AppTech.Security.Licensing.Controls.LicenseActivateControl
LicenseActivateControl
LicenseActivateControl
g5fdpT6Ij
get_CertificatePublicKeyData
QfIoC0Ww3
lnkCopy_LinkClicked
h6r1mO6LG
InitializeComponent
c5OiqFQKY
<AppName>k__BackingField
CBPR9H96H
<CertificatePublicKeyData>k__BackingField
dwKpwR9Qr
<ShowMessageAfterValidation>k__BackingField
mEiz0bBFb
<LicenseObjectType>k__BackingField
gymjcIaar8
components
KYKjjydUUg
grpbxLicense
BaujvncasC
txtLicense
Ly4jWLsT84
grpbxUID
htSj0nIon6
lblUIDTip
ChBjBXuFPO
lnkCopy
VyRjxSulYI
txtUID
<<type>>
AppTech.Common.AppFiles
AppTech.Common.AppFiles
AppFiles
AppFiles
<<type>>
AppTech.Common.AppSetting
AppTech.Common.AppSetting
AppSetting
AppSetting
gCxjbHJRXW
<ConnectionStringFile>k__BackingField
ORljQSGZaM
<AppLicenseName>k__BackingField
NsfjIAZPRs
appType
<<type>>
AppTech.Common.DbProvider
AppTech.Common.DbProvider
DbProvider
DbProvider
<<type>>
AppTech.Common.ConnnectionStringSource
AppTech.Common.ConnnectionStringSource
ConnnectionStringSource
ConnnectionStringSource
<<type>>
AppTech.Common.SessionStatus
AppTech.Common.SessionStatus
SessionStatus
SessionStatus
<<type>>
AppTech.Common.Channel
AppTech.Common.Channel
Channel
Channel
<<type>>
AppTech.Common.FileLogger
AppTech.Common.FileLogger
FileLogger
FileLogger
<<type>>
AppTech.Common.RandomGenerator
AppTech.Common.RandomGenerator
RandomGenerator
RandomGenerator
<<type>>
AppTech.Common.Utils.Reflector
AppTech.Common.Utils.Reflector
Reflector
Reflector
<<type>>
AppTech.Common.Utils.DataFormat
AppTech.Common.Utils.DataFormat
DataFormat
DataFormat
<<type>>
fo7xIHbBMYaMjKoae9.ehJWFNxZSITQsHU6NX
AppTech.Common.Utils.Excel
ehJWFNxZSITQsHU6NX
Excel
pogjtA4LNe
ReadExcel
<<type>>
AppTech.Common.Utils.ImageConverter
AppTech.Common.Utils.ImageConverter
ImageConverter
ImageConverter
<<type>>
AppTech.Common.Utils.RegistryManager
AppTech.Common.Utils.RegistryManager
RegistryManager
RegistryManager
<<type>>
AppTech.Common.Utils.Validator
AppTech.Common.Utils.Validator
Validator
Validator
<<type>>
AppTech.Common.Helpers.Money
AppTech.Common.Helpers.Money
Money
Money
H4ojU8ckta
Ahad
vvZjrrLsxi
Degree_One
KInjVn6lgk
Degree_Two
nhwjJN7jNy
Hundredth
UUWj6bhlc2
ToFractions
mJMjDGCoe2
ReadThreeDigits
yrFjHpVwUc
Tenth
n8WjSH5w5O
Tenth_Ahad
<<type>>
AppTech.Common.Helpers.TypeHelper
AppTech.Common.Helpers.TypeHelper
TypeHelper
TypeHelper
<<type>>
xrkqBAIqvPQNY6vN1U.xbc3SIQvHQkKe7NjCp
AppTech.Common.Helpers.SettingHelper
xbc3SIQvHQkKe7NjCp
SettingHelper
NvhjaDeSK7
GetCurrentComputerName
vnmjKeTvJf
GetDbNameFromConnectionString
FxxjLbCvcd
GetAppSetting
unRjGVx4em
SetAppSetting
<<type>>
AppTech.Common.Helpers.XMLHelper
AppTech.Common.Helpers.XMLHelper
XMLHelper
XMLHelper
vTmjnLaRTe
get_FileName
Wxkj2paPQo
xmlDocument
YNXjuKLvsT
FileName
<<type>>
AppTech.Common.Extensions.DatatableExtension
AppTech.Common.Extensions.DatatableExtension
DatatableExtension
DatatableExtension
<<type>>
AppTech.Common.Extensions.DateExtension
AppTech.Common.Extensions.DateExtension
DateExtension
DateExtension
<<type>>
AppTech.Common.Extensions.EnumExtensions
AppTech.Common.Extensions.EnumExtensions
EnumExtensions
EnumExtensions
<<type>>
AppTech.Common.Extensions.Extensions
AppTech.Common.Extensions.Extensions
Extensions
Extensions
<<type>>
AppTech.Common.Extensions.BrowserCapabilitiesExtension
AppTech.Common.Extensions.BrowserCapabilitiesExtension
BrowserCapabilitiesExtension
BrowserCapabilitiesExtension
<<type>>
AppTech.Common.Extensions.CollectionExtension
AppTech.Common.Extensions.CollectionExtension
CollectionExtension
CollectionExtension
p4OjPyjT7F
GetProperty
<<type>>
AppTech.Common.Extensions.EnumExtension
AppTech.Common.Extensions.EnumExtension
EnumExtension
EnumExtension
<<type>>
AppTech.Common.Extensions.QueryStringExtension
AppTech.Common.Extensions.QueryStringExtension
QueryStringExtension
QueryStringExtension
<<type>>
AppTech.Common.Extensions.SerializationExtension
AppTech.Common.Extensions.SerializationExtension
SerializationExtension
SerializationExtension
<<type>>
AppTech.Common.Extensions.StringExtension
AppTech.Common.Extensions.StringExtension
StringExtension
StringExtension
<<type>>
AppTech.Common.Extensions.NullExtension
AppTech.Common.Extensions.NullExtension
NullExtension
NullExtension
<<type>>
AppTech.Common.Extensions.StreamExtensions
AppTech.Common.Extensions.StreamExtensions
StreamExtensions
StreamExtensions
<<type>>
AppTech.Common.Extensions.XmlDocumentExtensions
AppTech.Common.Extensions.XmlDocumentExtensions
XmlDocumentExtensions
XmlDocumentExtensions
<<type>>
AppTech.Common.Events.EventsHelper
AppTech.Common.Events.EventsHelper
EventsHelper
EventsHelper
wf1jyCaiaA
InvokeDelegate
<<type>>
AppTech.Common.Events.GenericEventHandler
AppTech.Common.Events.GenericEventHandler
GenericEventHandler
GenericEventHandler
<<type>>
AppTech.Common.Events.GenericEventHandler`1
AppTech.Common.Events.GenericEventHandler`1
GenericEventHandler`1
GenericEventHandler`1
<<type>>
AppTech.Common.Events.GenericEventHandler`2
AppTech.Common.Events.GenericEventHandler`2
GenericEventHandler`2
GenericEventHandler`2
<<type>>
AppTech.Common.Events.GenericEventHandler`3
AppTech.Common.Events.GenericEventHandler`3
GenericEventHandler`3
GenericEventHandler`3
<<type>>
AppTech.Common.Events.GenericEventHandler`4
AppTech.Common.Events.GenericEventHandler`4
GenericEventHandler`4
GenericEventHandler`4
<<type>>
AppTech.Common.Events.GenericEventHandler`5
AppTech.Common.Events.GenericEventHandler`5
GenericEventHandler`5
GenericEventHandler`5
<<type>>
AppTech.Common.Events.GenericEventHandler`6
AppTech.Common.Events.GenericEventHandler`6
GenericEventHandler`6
GenericEventHandler`6
<<type>>
AppTech.Common.Events.GenericEventHandler`7
AppTech.Common.Events.GenericEventHandler`7
GenericEventHandler`7
GenericEventHandler`7
<<type>>
AppTech.Common.DTO.ResponseInfo
AppTech.Common.DTO.ResponseInfo
ResponseInfo
ResponseInfo
O8hje2NJyL
<ReqType>k__BackingField
GF2j9I5srw
<Success>k__BackingField
niHjgXCNXF
<Result>k__BackingField
lB7j8A45X7
<Error>k__BackingField
<<type>>
AppTech.Common.DTO.MasterRecord
AppTech.Common.DTO.MasterRecord
MasterRecord
MasterRecord
fHtjMYMMwm
<ID>k__BackingField
jSdjCIMlWZ
<DetailRecords>k__BackingField
<<type>>
AppTech.Common.DTO.RequestInfo
AppTech.Common.DTO.RequestInfo
RequestInfo
RequestInfo
EBcjObAb3a
<Domain>k__BackingField
cxPj5iVgYS
<Target>k__BackingField
pbGjmhYXxT
<Channel>k__BackingField
qy4jk7ta0a
<ReqType>k__BackingField
QNyjXHdHB2
<AsJson>k__BackingField
lL5jf1g9ir
<SID>k__BackingField
UEUj3Osaba
<Entity>k__BackingField
vaijFFj0nB
<Query>k__BackingField
jILjEroPlD
<Method>k__BackingField
yIWjqV7nS8
<Extra>k__BackingField
<<type>>
AppTech.Common.DTO.ExtraInfo
AppTech.Common.DTO.ExtraInfo
ExtraInfo
ExtraInfo
Lg1jTrYRjv
<Extras>k__BackingField
<<type>>
AppTech.Common.DTO.Extra
AppTech.Common.DTO.Extra
Extra
Extra
xwlj4yfijR
<Key>k__BackingField
Xb9jlqVFCA
<Value>k__BackingField
<<type>>
AppTech.Common.DTO.QueryInfo
AppTech.Common.DTO.QueryInfo
QueryInfo
QueryInfo
fZvjNkTuGl
<Name>k__BackingField
v7SjYpZoLa
<Page>k__BackingField
tiqjh1SJia
<PageSize>k__BackingField
lsQjwTLGmC
<Where>k__BackingField
QuWj7wuBDB
<OrderBy>k__BackingField
DvNjAsvV1Z
<All>k__BackingField
<<type>>
AppTech.Common.DTO.MethodInfo
AppTech.Common.DTO.MethodInfo
MethodInfo
MethodInfo
qERjZKPEXy
<Args>k__BackingField
OVJjsoage3
<Name>k__BackingField
<<type>>
AppTech.Common.DTO.ReqType
AppTech.Common.DTO.ReqType
ReqType
ReqType
<<type>>
AppTech.Common.Data.Data
AppTech.Common.Data.Data
Data
Data
pF1jovF0aO
<Id>k__BackingField
QrFj14Ct6Q
<Name>k__BackingField
fPwjdEEapS
<ExtraId>k__BackingField
<<type>>
AppTech.Common.Data.DataResponse
AppTech.Common.Data.DataResponse
DataResponse
DataResponse
btKjioE26V
<Ref>k__BackingField
gDwjRruRaX
<DataList>k__BackingField
<<type>>
AppTech.Common.Data.DataTableHelper
AppTech.Common.Data.DataTableHelper
DataTableHelper
DataTableHelper
oqjjp8tphs
GetValue
nTljzboS4M
IsDataContract
E7dvcr3ujk
MatchingTableRow
<<type>>
AppTech.Common.Data.DataContractSerializer`1
AppTech.Common.Data.DataContractSerializer`1
DataContractSerializer`1
DataContractSerializer`1
M5LvjDqUqY
m_DataContractSerializer
<<type>>
XB0WRBV1r0osqHEh71.wPo5xlrR0inVq4G4Q7
AppTech.Common.Security.EncrytionHelper
wPo5xlrR0inVq4G4Q7
EncrytionHelper
YOivm5nPgv
get_Content
oVZvkLfvkF
set_Content
sW7vfdmhAs
get_CryptoException
lhYv3TtvMb
set_CryptoException
oVTvE0hjoW
get_Encoding
whZvqn9Jba
set_Encoding
lrJv45QRCi
get_EncryptionAlgorithm
jBqvlAChmO
set_EncryptionAlgorithm
AZ8vYinP3q
get_IsHashAlgorithm
SLwvwFihT3
get_Key
nJSv78DbJj
set_Key
WjGvvEerfC
_Decrypt
oPUvWVQFYn
_Decrypt
OXLv0KhCi9
_Encrypt
mXxvBRvBFI
_Encrypt
BjPvx6iv20
BytesToHex
LJVvbMb32h
Clear
eMOvQHSYvp
ClearBuffer
rC0vI1v3fG
ComputeHash
DRmvtolaBV
ComputeHash
tC6vUBQ329
ComputeHash
VA1vr5OjQj
Decrypt
CI8vVCjsZB
Decrypt
DDmvJveEUl
DecryptFile
gs8v6RCr2W
DecryptFile
r7NvDhvScC
DecryptFile
XkbvHYq1VX
DecryptString
DfGvS0wpVo
DerivePassword
X1YvauKBpN
Encrypt
CyxvKZal3Y
Encrypt
qJ1vLKB6Zp
EncryptFile
YCSvGBXDrm
EncryptFile
GekvneWAQi
EncryptFile
MZbvuqdETH
EncryptString
favv2vpLsV
GenerateHash
lmkvPS88Nv
GenerateSalt
EYMvyhPei0
GetTextFromFile
dB7veXriZJ
HexToBytes
T3Qv9VUQqJ
Init
x7fvgJDbLi
Init
QO6v8lMW5y
smethod_0
LuJvMvQYmD
smethod_1
msPvCnlhsG
SymmetricDecrypt
e7tvOVXvfc
SymmetricEncrypt
OG2v5TXPMy
ValidateRSAKeys
MUJvAX9iAg
IV_8
lpmvZ1Mhbr
IV_16
RYwvs0gCZJ
IV_24
V8KvofjZ4p
IV_32
HGMv1yV1HJ
SALT_BYTES
YXUvd2g0Y3
<Content>k__BackingField
nV3viFEvl5
<CryptoException>k__BackingField
qxtvRA32WE
<Encoding>k__BackingField
e11vp1cSji
<EncryptionAlgorithm>k__BackingField
ypIvzSGQHi
<Key>k__BackingField
C2FvXvC9xA
Content
x2BvFW10iy
CryptoException
pbpvT59xYL
Encoding
kTuvNZHm3J
EncryptionAlgorithm
pPnvhClWWG
IsHashAlgorithm
<<type>>
AppTech.Common.Security.BASE36
AppTech.Common.Security.BASE36
BASE36
BASE36
M1nWcEv2gr
Reverse
QKNWjVcFo0
_charArray
<<type>>
AppTech.Common.Security.KeyManager
AppTech.Common.Security.KeyManager
KeyManager
KeyManager
HAaWv4QMJM
_androidKey
<<type>>
AppTech.Common.Security.DataSecurity
AppTech.Common.Security.DataSecurity
DataSecurity
DataSecurity
WK4WWwPQWU
Encrypt
flvW0M9rxx
Decrypt
ofLWB8nwif
GetRijndaelManaged
m0KWxXgqs1
bytes
<<type>>
AppTech.Common.Properties.Resources
AppTech.Common.Properties.Resources
Resources
Resources
QA7WbfCnAr
get_ResourceManager
UowWIQtpcL
get_Culture
PKyWtufeXD
set_Culture
nq4Wrp7EWr
get_AK
qhLWVcrb1L
resourceMan
T4HWJImVRP
resourceCulture
uGwWQET1w8
ResourceManager
jRqWUgRgKU
Culture
<<type>>
AppTech.Common.Exceptions.AuthenticationException
AppTech.Common.Exceptions.AuthenticationException
AuthenticationException
AuthenticationException
<<type>>
AppTech.Common.Exceptions.AppTechException
AppTech.Common.Exceptions.AppTechException
AppTechException
AppTechException
<<type>>
AppTech.Common.Exceptions.ExceptionManager
AppTech.Common.Exceptions.ExceptionManager
ExceptionManager
ExceptionManager
<<type>>
AppTech.Common.Exceptions.ReportException
AppTech.Common.Exceptions.ReportException
ReportException
ReportException
<<type>>
AppTech.Common.Exceptions.SessionException
AppTech.Common.Exceptions.SessionException
SessionException
SessionException
<<type>>
AppTech.Common.Entities.IBranchable
AppTech.Common.Entities.IBranchable
IBranchable
IBranchable
<<type>>
AppTech.Common.Entities.Result
AppTech.Common.Entities.Result
Result
Result
IDXW6y7tFG
<Success>k__BackingField
tGoWDujZfl
<Message>k__BackingField
<<type>>
AppTech.Common.Entities.IAuditableEntity
AppTech.Common.Entities.IAuditableEntity
IAuditableEntity
IAuditableEntity
<<type>>
AppTech.Common.Entities.IEntity
AppTech.Common.Entities.IEntity
IEntity
IEntity
<<type>>
AppTech.Common.Entities.Error
AppTech.Common.Entities.Error
Error
Error
OaaWHCC3NK
<Code>k__BackingField
TKuWSQ7O1E
<Message>k__BackingField
YwqWaZ0uUH
<Detail>k__BackingField
rWQWKT35Z0
<Type>k__BackingField
<<type>>
AppTech.Common.Entities.ErrorType
AppTech.Common.Entities.ErrorType
ErrorType
ErrorType
<<type>>
AppTech.Common.Db.DbConnectionHelper
AppTech.Common.Db.DbConnectionHelper
DbConnectionHelper
DbConnectionHelper
zQ3WL87IkK
SaveToEncryptedFile
<<type>>
AppTech.Common.Db.DBConnection
AppTech.Common.Db.DBConnection
DBConnection
DBConnection
K72WGIxNYh
set_ConnectionString
d5rWnCQnoB
set_DbName
syXWuvgEOX
<ConnectionString>k__BackingField
NL0W2RAGBa
<DbName>k__BackingField
<<type>>
AppTech.Common.Db.XmlDbConnection
AppTech.Common.Db.XmlDbConnection
XmlDbConnection
XmlDbConnection
<<type>>
AppTech.Common.Db.EncryptedDbConnection
AppTech.Common.Db.EncryptedDbConnection
EncryptedDbConnection
EncryptedDbConnection
RMsWP3YCIX
ExtractConnectionString
AFsWy6DE8s
_password
tP2WelOWGf
<UserName>k__BackingField
<<type>>
AppTech.Common.Abstracts.IError
AppTech.Common.Abstracts.IError
IError
IError
<<type>>
AppTech.Common.Abstracts.ILogger
AppTech.Common.Abstracts.ILogger
ILogger
ILogger
<<type>>
<PrivateImplementationDetails>
<PrivateImplementationDetails>
<PrivateImplementationDetails>
<PrivateImplementationDetails>
<<type>>
AppTech.Security.Licensing.HardwareInfo/<SplitInParts>d__3
AppTech.Security.Licensing.HardwareInfo/<SplitInParts>d__3
<SplitInParts>d__3
<SplitInParts>d__3
<<type>>
AppTech.Security.Licensing.ShowInLicenseInfoAttribute/FormatType
AppTech.Security.Licensing.ShowInLicenseInfoAttribute/FormatType
FormatType
FormatType
<<type>>
xrkqBAIqvPQNY6vN1U.xbc3SIQvHQkKe7NjCp/<>c__DisplayClass2_0
AppTech.Common.Helpers.SettingHelper/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
xrkqBAIqvPQNY6vN1U.xbc3SIQvHQkKe7NjCp/<>c__DisplayClass3_0
AppTech.Common.Helpers.SettingHelper/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.Common.Extensions.CollectionExtension/<>c__DisplayClass5_0
AppTech.Common.Extensions.CollectionExtension/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.Common.Extensions.EnumExtension/<ToEnumerable>d__0
AppTech.Common.Extensions.EnumExtension/<ToEnumerable>d__0
<ToEnumerable>d__0
<ToEnumerable>d__0
<<type>>
AppTech.Common.Events.EventsHelper/B5jOxhJBDlj6qmpnBA
AppTech.Common.Events.EventsHelper/AsyncFire
B5jOxhJBDlj6qmpnBA
AsyncFire
<<type>>
AppTech.Common.Events.EventsHelper/<>c
AppTech.Common.Events.EventsHelper/<>c
<>c
<>c
<<type>>
AppTech.Common.Data.DataTableHelper/<>c
AppTech.Common.Data.DataTableHelper/<>c
<>c
<>c
<<type>>
XB0WRBV1r0osqHEh71.wPo5xlrR0inVq4G4Q7/InVIep6nBekkyqvn4q
AppTech.Common.Security.EncrytionHelper/Algorithm
InVIep6nBekkyqvn4q
Algorithm
<<type>>
XB0WRBV1r0osqHEh71.wPo5xlrR0inVq4G4Q7/w4MLQ0DDIDnmgJfGHZ
AppTech.Common.Security.EncrytionHelper/EncodingType
w4MLQ0DDIDnmgJfGHZ
EncodingType
<<type>>
XB0WRBV1r0osqHEh71.wPo5xlrR0inVq4G4Q7/LuAgREHykWvOV4OerV
AppTech.Common.Security.EncrytionHelper/EncryptAlgorithmEnum
LuAgREHykWvOV4OerV
EncryptAlgorithmEnum
<<type>>
XB0WRBV1r0osqHEh71.wPo5xlrR0inVq4G4Q7/Fe8SYlSHsr631oC6pk
AppTech.Common.Security.EncrytionHelper/HashAlgorithmEnum
Fe8SYlSHsr631oC6pk
HashAlgorithmEnum
<<type>>
XB0WRBV1r0osqHEh71.wPo5xlrR0inVq4G4Q7/HhbShVaw3gkFBWBSQJ
AppTech.Common.Security.EncrytionHelper/KeySize
HhbShVaw3gkFBWBSQJ
KeySize
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=6
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=6
__StaticArrayInitTypeSize=6
__StaticArrayInitTypeSize=6
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=11
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=11
__StaticArrayInitTypeSize=11
__StaticArrayInitTypeSize=11
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=16
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=24
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=24
__StaticArrayInitTypeSize=24
__StaticArrayInitTypeSize=24
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=32
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
<<type>>
<Module>{693F001A-6852-4781-AB4D-8E9BC1A39359}
<Module>{693F001A-6852-4781-AB4D-8E9BC1A39359}
<Module>{693F001A-6852-4781-AB4D-8E9BC1A39359}
<Module>{693F001A-6852-4781-AB4D-8E9BC1A39359}
<<type>>
NwWxNWL2XGIWdHxUVh.YAMGdiKZO5hTXlsEPj
SOj3wtG2Ob7xEudvw7.CDCWSn7SaPjUwoq2Cc
YAMGdiKZO5hTXlsEPj
CDCWSn7SaPjUwoq2Cc
sIcW9XNqxU
TWp4PNnQc
<<type>>
NwWxNWL2XGIWdHxUVh.YAMGdiKZO5hTXlsEPj/SFU4mbT3GMret7THonf
SOj3wtG2Ob7xEudvw7.CDCWSn7SaPjUwoq2Cc/SFU4mbT3GMret7THonf
SFU4mbT3GMret7THonf
SFU4mbT3GMret7THonf
<<type>>
sPXKGvnGrVvWhtFZwQ.BsMttuGUObFbFuU5MO
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il
BsMttuGUObFbFuU5MO
DyyVDbaRvM1YfIq9il
rvWWgR8Tlp
creoiNvd7
LyWW8FC37h
jZiU8kt7k
v6IWMIpPw9
yIEeUuogE
nycWCvn5rB
HNMMnrD0K
bqWWOSSNpG
U6ZIpjiMV
KFxW5RlBAP
TYIaeXNeW
CYYWmPA6V2
rI3lmZ9FL
cjUWkf5ttx
SuhhReBcy
fjdWX4yKas
QWOOk18h0
Fy1WfQygQ7
BjkXsyRir
RIcW3kAhZA
mCC9ZT9yx
nWiWFdxLVT
b82VQ34LR
ONYWEqAUr9
P4kZBQ8Uk
MqhWq13ELE
KX0HrYNeb
oYrWTBda6Q
pvQ2Nvbv9
VR4W4HNHlv
gVU0QeojF
tfvWl0ybDg
HK2JaffxR
WQFWNLWci7
ubITRqgdO
CZaWYnvFgn
vZF7RiFiF
rZSWhDTZ1m
eM2t2dfoT
QbRWwFmomm
vDfq2bW1V
apmW7US0GH
B3XRfqih9
z4UWAUUKOM
sVk5WFvVV
vvgWZqJjE1
E3GryunuI
IZ9WsBt8TV
yxOcIGI9u
nc8WoXtGh1
Oihu8LNHm
RylW1vqPpe
ifqQyNVWS
BpBWd4mdlN
hcDmskCdX
JLHWiZELhH
mKgSOTjDj
cWQWRdVj8A
aYTwtN0c5
GiUWpKplPb
udfDaXdkp
fXQWzyFKGP
NrL10qsNW
kEk0cKKen6
j8hgmZJ7n
Kfy0jXEj5r
M6EKmwjSJ
zJZ0v8bknM
PVVpfAGtG
P8M0Wsd88V
cQCd71PIW
FLW00nwh7I
lodECQQVs
ofJ0BMc0C3
VvPxdPh3O
KWD0xyQIBP
hIsn23p8h
iDi0bdaUVU
dKMLoMpMs
maI0QggCyQ
ghLACNa05
Ltt0InqEUl
c9FNce5cf
xIH0tYl71l
diL3t0peo
YIm0Un5VYV
sMgC0o5PW
OdQ0rMFkig
S0FvrGWpN
KbP0V0Ue5K
hSjGubHK9
agc0JTZ74K
d1uknJpcW
pnM06d6bBi
uS9zmJ6WC
wKD0D4amt7
i244bikuos
KdI0HBHH1O
bFB44BUGlg
dFC0SHrhl0
x3c4o2PyTx
ILL0aBIepn
phV4Uu6SUx
hB60KLRPpu
Qwp4ejR7FG
ES10L9GYMJ
TWn4MujlZv
Sp60GxoOns
NFL4IGyoc7
jim0nYWA1K
WS94a0Vnlv
YtL0ugwGqF
XtL4lyIIgx
VBY02j2Y96
firstrundone
XXa0PIteXp
IBe4hEip2A
<<type>>
sPXKGvnGrVvWhtFZwQ.BsMttuGUObFbFuU5MO/TvHDROuZElTrVob3Su
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/AXBrnIFfMAfABnJrF9
TvHDROuZElTrVob3Su
AXBrnIFfMAfABnJrF9
<<type>>
sPXKGvnGrVvWhtFZwQ.BsMttuGUObFbFuU5MO/TvHDROuZElTrVob3Su/SfBfHm2uWIlL4EgpVo`1
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/AXBrnIFfMAfABnJrF9/z0oyxsqySXMDuI4ZyY`1
SfBfHm2uWIlL4EgpVo`1
z0oyxsqySXMDuI4ZyY`1
<<type>>
sPXKGvnGrVvWhtFZwQ.BsMttuGUObFbFuU5MO/kmO6LGyb5fpT6Ijv5O
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/ay67rn8SHAWRagidNL
kmO6LGyb5fpT6Ijv5O
ay67rn8SHAWRagidNL
f7n0yIwqph
D4r4O0AxSI
<<type>>
sPXKGvnGrVvWhtFZwQ.BsMttuGUObFbFuU5MO/KFQKYCeBP9H96H0wKw
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/rL2N9N6wh7IWY3IC3G
KFQKYCeBP9H96H0wKw
rL2N9N6wh7IWY3IC3G
<<type>>
sPXKGvnGrVvWhtFZwQ.BsMttuGUObFbFuU5MO/X9QrJE9i0bBFbbymIa
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/LhmiV9AUoOr1v5yhIs
X9QrJE9i0bBFbbymIa
LhmiV9AUoOr1v5yhIs
<<type>>
sPXKGvnGrVvWhtFZwQ.BsMttuGUObFbFuU5MO/qr8qYKgydUUgCaunca
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/Lk7BwHKFmNJY32ZC3n
qr8qYKgydUUgCaunca
Lk7BwHKFmNJY32ZC3n
yqH0eB7qLt
bV44XU8KQo
Ovt09dFrQT
Uu349Vtr47
<<type>>
sPXKGvnGrVvWhtFZwQ.BsMttuGUObFbFuU5MO/TC4y4L8sT84ItSnIon
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/WDRJe2H6E4HVV6PGZs
TC4y4L8sT84ItSnIon
WDRJe2H6E4HVV6PGZs
<<type>>
gCxHJRCXWNRlSGZaMN.mChBXuMFPO1yRSulYI
cH8IXcwQY4Peh2qpAn.xrUtBVoaXtCT6B0w6a
mChBXuMFPO1yRSulYI
xrUtBVoaXtCT6B0w6a
kg40gwadnU
ywq4VEynyU
<<type>>
U8ckta5PvZrLsxiqIn.TfAZPROsSogA4LNef4
c5uHW3cSW8ou55rAF3.KKr6hZkjvwWjdm9A4Z
TfAZPROsSogA4LNef4
KKr6hZkjvwWjdm9A4Z
lBs08PyS5I
Uur4ZuAaiM
<<type>>
Plc26JkMGCoe2erFpV.t6lgkDmhwN7jNyoUWb
CvXDWVQi7y2eO3gBAC.OsyMlHJSvCHNZySQs6
t6lgkDmhwN7jNyoUWb
OsyMlHJSvCHNZySQs6
<<type>>
r72nmefTvJfkxxbCvc.PUcD8WXH5w5OlvhDeS
zsd5DaYg9lOJGmlIO4.R2mIapWar4cwoqqx6Q
PUcD8WXH5w5OlvhDeS
R2mIapWar4cwoqqx6Q
YrR0MOQyWB
HNM4YkXJs5
Xnm0CcokBe
pfJ40gjxwv
iKN0ONFfq5
eBxqprrF8
iq105RW66u
Ypf4J7ba8u
crn0mJqR9D
CCw4Tb9h3V
yd90khJogm
n3x46T2MQ2
RZL0XDK9Uy
WP947UZNwy
FJ90ftlbf8
Fko4i7KTuh
<<type>>
r72nmefTvJfkxxbCvc.PUcD8WXH5w5OlvhDeS/VunRVx34emPTmLaRTe
zsd5DaYg9lOJGmlIO4.R2mIapWar4cwoqqx6Q/dde9wksVEKdElHkEKH
VunRVx34emPTmLaRTe
dde9wksVEKdElHkEKH
<<type>>
r72nmefTvJfkxxbCvc.PUcD8WXH5w5OlvhDeS/YNXKLvFsTixkpaPQoH
zsd5DaYg9lOJGmlIO4.R2mIapWar4cwoqqx6Q/T9eZG8XLTT9vNo3j18
YNXKLvFsTixkpaPQoH
T9eZG8XLTT9vNo3j18
NIn03KhXIP
IWZ4FNxMCV
qYL0FETmmP
X4o4BaXNNW
uhM0EaNsGK
ReR4PkWY9i
MWU0q7kaeK
XZO4yOqtpA
cEs0T0lQt8
pcT48wm9UY
Ieh04RRGsq
Y9l4jroko9
lpK0lJQSN9
OY84tBcMwd
lv50N7VYNE
JrQ4qkE5mX
WZA0Y3DppD
iRM4R10ean
JaS0hKOfMH
AGe45CEX5X
p6H0wg5qSj
Goe4rkO7Su
Svi07RdjUi
Tt04cJf5Ud
mP80A7WvoW
wDU4ucXGpO
Thm0ZqN5fS
HGp4Q5R9ww
dcj0siesHe
FvC4mE2qIR
kSg0odyt8Y
iv04SsOrFF
QW101eekiX
zBi4wdjAN2
XhL0dPE6q5
PN14D93Kyx
qD20i8bT4Z
ulr41vALu8
kZd0RvrmSp
lQp4gbkEqU
BW30p93cuY
IRA4KTlYfd
<<type>>
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}
<<type>>
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=256
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=256
__StaticArrayInitTypeSize=256
__StaticArrayInitTypeSize=256
<<type>>
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=40
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=40
__StaticArrayInitTypeSize=40
__StaticArrayInitTypeSize=40
<<type>>
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=30
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=30
__StaticArrayInitTypeSize=30
__StaticArrayInitTypeSize=30
<<type>>
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=32
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
<<type>>
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=16
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
<<type>>
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=64
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=64
__StaticArrayInitTypeSize=64
__StaticArrayInitTypeSize=64
<<type>>
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=18
<PrivateImplementationDetails>{372577E6-D7CB-4016-A079-25BDCE77A0A4}/__StaticArrayInitTypeSize=18
__StaticArrayInitTypeSize=18
__StaticArrayInitTypeSize=18
