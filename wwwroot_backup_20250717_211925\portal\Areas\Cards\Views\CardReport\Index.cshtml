﻿@model AppTech.MSMS.Domain.Reports.Models.CardModel
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Domain
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


<div class="form-horizontal">
    @{
        Html.RenderPartial("_DateControl");
    }
    <span class="lbl"> النوع</span>
    @*@Html.DropDownListFor(model => model.CardTypeID, (SelectList)ViewBag.CardTypes)*@
    <select id="CardTypeID" name="CardTypeID" class="select2" placeholder="كافة الانواع"></select>
    <div class="space-6"></div>

    <span class="lbl">الفئة</span>
    @*@Html.DropDownListFor(model => model.CardFactionID, (SelectList)ViewBag.CardFactions)*@
    <select id="CardFactionID" name="CardFactionID" class="select2" style="width: 110px;" placeholder="اختر فئه" required></select>
    <div class="space-6"></div>

    <span class="lbl"> اسم الحساب</span>
    @Html.DropDownListFor(model => model.AccountID, (SelectList)ViewBag.Accounts)
    <div class="space-6"></div>


    <span class="lbl">الحالة</span>
    @Html.DropDownListFor(model => model.State, new[]
       {
           new SelectListItem {Text = "لم يتم البيع", Value = "0"},
           new SelectListItem {Text = "تم البيع ", Value = "1"}
       })
    <div class="space-6"></div>

</div>

<script>
    $("#CardTypeID").on("change", function () {
        var id = $("#CardTypeID").val();
        if (id > 0) {
            loadFactionList(id);
        }
        i('loadDataList' + id);

    });
    function loadFirstList() {
        i('loadDataList');
        fillDataList('CardTypeID', '/Cards/CardGrossReport/GetCardTypes', false, 'كافة الانواع');
    }
    function loadFactionList(id) {
        i('loadDataList');
        fillDataList('CardFactionID', '/Cards/CardGrossReport/GetCardFactions?id=' + id, false, 'كافة الفئات');
    }
    loadFirstList();
    //loadFactionList(1);
    initAutoSearch();

    function initAutoSearch() {
        i('initAutoSearch');
        $('.select2').css('width', '200px').select2({ allowClear: true });
        $('#select2-multiple-style .btn').on('click',
            function (e) {
                var target = $(this).find('input[type=radio]');
                var which = parseInt(target.val());
                if (which == 2) $('.select2').addClass('tag-input-style');
                else $('.select2').removeClass('tag-input-style');
            });
    }
</script>