﻿@{
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}
<div class="col-sm-offset-1 col-sm-10">
    <form id="multiform" name="multiform" class="form-horizontal" method="POST" enctype="multipart/form-data">
        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new {@class = "text-danger"})
        <div class="form-horizontal">
            <section>
                @RenderBody()
            </section>
        </div>
        <span class="alert"></span>
        @Html.Partial("_FormAction");

    </form>
</div>

<script type="text/javascript">

    var formHelper = {
        onSuccess: function(data) {
            onSubmitSuccess(data);
        },
        onBegin: function() {
            showLoading();
            return true;
        }
    }

    function onSubmitSuccess(data) {
        hideLoading();
        if (data.Success) {
            showSuccess("تمت العملية بنجاح");
            $('#multiform')[0].reset();
        } else {

            alert(data.Message);
        }
    }

    function onCrudSuccess(data) {
        resetButton();
        formHelper.onSuccess(data);
    }

    function onCrudFailure(xhr, status) {
        resetButton();
        hideLoading();
        var msg = parseXhr(xhr);
      //  alert(msg);
        showError(msg);
    }

    function OnFormBegin(context) {
        return formHelper.onBegin(context);
    }

    $(function() {
        i('on multiLayout load');
        if ($("#Amount")[0]) {
            $('#Amount').on('input',
                function () {
                    var words = tafqeet($('#Amount').val());
                    $('#words').text(words);
                    $('#Amount').title(words);
                });
        }
        $("#multiform").submit(function(event) {
            i('onsubmit multi');

            if (!OnFormBegin()) {
                return;
            }
            var dataString;
            event.preventDefault();
            var controller = $("#Controller").val();

            var action = controller + "/addoredit";
            i("url: " + action);
            if ($("#multiform").attr("enctype") === "multipart/form-data") {
                dataString = new FormData($("#multiform").get(0));
                contentType = false;
                processData = false;
            } else {
                log('not multipart');
            }

            showLoading();
            $.ajax({
                type: "POST",
                url: action,
                data: dataString,
                dataType: "json",
                contentType: contentType,
                processData: processData,
                success: function(data) {
                    onCrudSuccess(data);
                    //handleSuccessFunctionHERE(data);
                },
                error: function(xhr, textStatus, errorThrown) {
                    onCrudFailure(xhr);
                }
            });
        });
    });
</script>
<script type="text/javascript">

    var title = $('#Title').val();
    $("#page-title").text(title);

    function show(input) {


        if (input.files && input.files[0]) {
            var filerdr = new FileReader();
            filerdr.onload = function(e) {
                $('#preview').attr('src', e.target.result);
            };
            filerdr.readAsDataURL(input.files[0]);
        }
    }


</script>