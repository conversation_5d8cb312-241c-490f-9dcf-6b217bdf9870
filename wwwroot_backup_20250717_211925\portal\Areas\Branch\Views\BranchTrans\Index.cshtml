﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Reports.Models.TopupModel

@{
    ViewBag.Title = "تقرير عمليات الفروع";
    Layout = "~/Views/Shared/_Report.cshtml";
}

<div class="form-horizontal">

    @{
        Html.RenderPartial("_DateControl");
    }
    <span class="lbl">اسم الفرع </span>
    <div class="form-group">
        <div class="col-md-10">
            @Html.Obout(new ComboBox("AccountID")
            {
                Width = 230
            })

        </div>
    </div>
    <div class="hr hr-dotted hr-24"></div>
    <div class="space-6"></div>
    <span class="lbl"> الخدمة</span>
    @Html.DropDownListFor(model => model.ServiceID, (SelectList) ViewBag.Services)

    <div class="space-6"></div>
    <span class="lbl"> نوع التقرير</span>
    @Html.EnumDropDownListFor(m => m.Type)
    @*<div class="space-6"></div>
    <div class="checkbox">
        <label>
            @Html.EditorFor(model => model.IncludeCommission)
            @Html.Label("حساب العمولة")
        </label>
    </div>*@

</div>