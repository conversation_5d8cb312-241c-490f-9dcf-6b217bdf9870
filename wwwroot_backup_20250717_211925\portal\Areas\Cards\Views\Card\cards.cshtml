﻿@model IEnumerable<AppTech.MSMS.Domain.Models.Card>
@{
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}

<p>
    <a class="blue" href="" onclick="openModal('@ViewBag.CardFactionID','@ViewBag.CardTypeID')">
        <i class="ace-icon fa fa-plus bigger-150"></i>
        <span>إضافة كرت جديد</span>
    </a>
</p>
<div id="list">
    @Html.Partial("_cards")
</div>
    @Html.Partial("_Modal")

    <script>
        function openModal(id,CardTypeID) {
            i('open modal id' + id);
            openViewAsModal('Cards/Card/AddOrEditCards?ID=' + id +'&CardTypeID='+CardTypeID);
        }
    </script>
