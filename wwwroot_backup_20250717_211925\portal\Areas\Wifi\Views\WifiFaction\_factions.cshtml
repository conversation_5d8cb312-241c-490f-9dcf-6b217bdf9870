﻿@model IEnumerable<AppTech.MSMS.Domain.Models.WifiFaction>
<table class="table table-hover" id="tableDetailWifiPayed">
    <thead>
        <tr>
            <th>اسم الفئة</th>
            <th >سعر البيع</th>
            <th>الصلاحية</th>
            <th >الكروت المتبقية</th>
            <th>الكروت المباعة</th>
            <th>إجمالي الكروت </th>
            <th>ملاحظات</th>
            <th></th>

        </tr>
    </thead>
    @foreach (var Faction in Model)
    {
        <tbody>
            <tr>
                @if (Faction != null)
                {
                    <td>@Html.DisplayFor(modelItem => Faction.Name)</td>
                    <td>@Html.DisplayFor(modelItem => Faction.Price)</td>
                    <td>@Html.DisplayFor(modelItem => Faction.Description)</td>
                    <td class="cardNotPayed ">@Html.DisplayFor(modelItem => Faction.TotalSold)</td>
                    <td class="cardPayed ">@Html.DisplayFor(modelItem => Faction.TotalRemian)</td>
                    <td class="cardAll">@Html.DisplayFor(modelItem => Faction.TotalAll)</td>
                    <td>@Html.DisplayFor(modelItem => Faction.Note)</td>
                    <td style="text-align:right">
                        <Button class="btn btn-link" onclick="openCards('@Faction.ID')">
                            <i class="ace-icon fa fa-eye bigger-110"></i>
                            عرض الكروت
                        </Button>
                        <Button class="btn btn-link" onclick="openEditModal('@Faction.ID','@Faction.WifiProviderID')">
                            <i class="ace-icon fa fa-edit bigger-110"></i>
                            تعديل
                        </Button>
                    </td>
                }
            </tr>
        </tbody>
    }
</table>
<style>
    .cardNotPayed {
        color: #00bb00;
        font-size: 16px;
    }
    #tableDetailWifiPayed thead tr th ,
    #tableDetailWifiPayed tbody tr td{
        text-align:center;
    }
    .cardPayed {
        color:  #009cad;
        font-size: 16px;
    }
       .cardAll{
        color:#00009c;
        font-size: 16px;
    }
</style>
<script>
    function openModal(id) {
        i('open modal id' + id);
        openViewAsModal('Wifi/WifiFaction/AddOrEditFaction?ID=' + id, " جديد");
    }
      function openEditModal(id,WifiProviderID) {
        i('open modal id' + id);
        openViewAsModal('Wifi/WifiFaction/AddOrEditFaction?ID=' + id +'&WifiProviderID='+WifiProviderID);
    }
      function openCards(id) {
          i('open modal id' + id);
           window.location.href = "/#!/route/Wifi/WifiCard/Cards/" + id;
        }
      $(function () {
        $.each($("#tableDetailWifiPayed tbody tr"), function () {
           var cardNotP = parseInt($(this).find("td:eq(3)").text());
            var cardN = parseInt($(this).find("td:eq(4)").text());
            var cardA = parseInt($(this).find("td:eq(5)").text());

            if (cardNotP == "0") 
                $(this).find("td:eq(3)").css("color", "red");
            if (cardN == "0") 
                $(this).find("td:eq(4)").css("color", "red");
            if (cardA == "0") 
                $(this).find("td:eq(5)").css("color", "red");
        })
    });
</script>
