﻿@using AppTech.MSMS.Web.Models
@Html.Partial("_RecordAction", new ActionModel {ID = Model.ID})
<button class="btn btn-link" onclick="show(@Model.ID)">الرصيد</button>


<script>
    function show(id) {

        showLoading();
        $.ajax({
            url: '@Url.Action("QueryBalance", "TopupProvider")',
            data: { id: id },
            success: function (data) {
                hideLoading();
                alert(data);
            },
            error: function(xhr, ajaxOptions, thrownError) {
                hideLoading();
                alert(xhr.responseText);

            }
        });
    }

</script>