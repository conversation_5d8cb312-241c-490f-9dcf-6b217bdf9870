﻿@model AppTech.MSMS.Domain.Models.Bagat

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<div class="form-group">
    @Html.LabelFor(model => model.OrderNo, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.OrderNo)
        @Html.ValidationMessageFor(model => model.OrderNo)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Code, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Code, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.Code, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    <label class="col-sm-2 control-label">نوع الخط</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.LineType, new[]
        {
            new SelectListItem {Text = "دفع مسبق", Value = "دفع مسبق", Selected = true},
            new SelectListItem {Text = "فوترة", Value = "فوترة"}
        })


    </div>
    @Html.ValidationMessageFor(model => model.LineType)
</div>


<div class="form-group">
    <label class="col-sm-2 control-label">نوع الشريحة</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.SimType, new[]
        {
            new SelectListItem {Text = "شريحة", Value = "شريحة", Selected = true},
            new SelectListItem {Text = "برمجة", Value = "برمجة"},
            new SelectListItem {Text = "برمجة + شريحة", Value = "برمجة + شريحة"}
        })


    </div>
    @Html.ValidationMessageFor(model => model.SimType)
</div>


<div class="form-group">
    <label class="col-sm-2 control-label">التصنيف</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.Mode, new[]
        {
            new SelectListItem {Text = "الكل", Value = "both", Selected = true},
            new SelectListItem {Text = "3G", Value = "3G"},
            new SelectListItem {Text = "1x", Value = "1X"},
            new SelectListItem {Text = "مودم", Value = "Modem"},
            new SelectListItem {Text = "أخرى", Value = "أخرى"},
        })


    </div>
    @Html.ValidationMessageFor(model => model.SimType)
</div>

@*<div class="form-group">
        @Html.LabelFor(model => model.ProviderPrice, new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EditorFor(model => model.ProviderPrice, new {htmlAttributes = new {@class = ""}})
            @Html.ValidationMessageFor(model => model.ProviderPrice, "", new {@class = "text-danger"})
        </div>
    </div>*@

<div class="form-group">
    @Html.Label("الكمية", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Price, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.Price, "", new { @class = "text-danger" })
    </div>
</div>



}