﻿@model AppTech.MSMS.Domain.Models.AccountApi

<div>
    <h4>Account Api Details for </h4>
    <hr />
    <dl class="dl-horizontal">

        <dt>
            @Html.DisplayName("اسم الحساب")
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Account.Name)
            <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.Account.Name)">
                <i class="ace-icon fa fa-copy"></i> نسخ
            </button>
        </dd>

        <dt>
            @Html.DisplayName("Account-ID")
        </dt>

        <dd>
            @Html.DisplayFor(model => model.GuidNumber)
            <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.GuidNumber)">
                <i class="ace-icon fa fa-copy"></i> نسخ
            </button>
                </dd>

        
        <dt>
            @Html.DisplayName("Token")
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Token)
            <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.Token)">
                <i class="ace-icon fa fa-copy"></i> نسخ
            </button>
                </dd>

