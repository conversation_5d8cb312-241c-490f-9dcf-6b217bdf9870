﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Reports.Models.TransferReportModel

@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


<div class="form-horizontal">


    @{

        Html.RenderPartial("_DateControl");
    }
    <span class="lbl">اسم العميل المرسل </span>
    <div class="form-group">
        <div class="col-md-10">
            @Html.Obout(new ComboBox("SenderClientID")
            {
                Width = 230
            })

        </div>
    </div>
    <div class="hr hr-dotted hr-24"></div>
    <div class="space-6"></div>

    <span class="lbl">اسم العميل المستفيد </span>
    <div class="form-group">
        <div class="col-md-10">
            @Html.Obout(new ComboBox("ReceiverClientID")
            {
                Width = 230
            })

        </div>
    </div>

    <div class="hr hr-dotted hr-24"></div>
    <div class="space-6"></div>


</div>