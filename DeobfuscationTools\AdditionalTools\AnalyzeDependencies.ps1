﻿# Assembly Dependency Analyzer
param([string]$AssemblyPath)

try {
    Add-Type -AssemblyName System.Reflection
    $assembly = [System.Reflection.Assembly]::LoadFile($AssemblyPath)
    $references = $assembly.GetReferencedAssemblies()
    
    Write-Host "Assembly: $($assembly.FullName)" -ForegroundColor Yellow
    Write-Host "Dependencies ($($references.Count)):" -ForegroundColor Green
    
    foreach ($ref in $references) {
        Write-Host "  - $($ref.Name) v$($ref.Version)" -ForegroundColor White
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
