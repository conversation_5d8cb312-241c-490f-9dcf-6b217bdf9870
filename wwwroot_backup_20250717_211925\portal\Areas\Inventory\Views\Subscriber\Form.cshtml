﻿@model AppTech.MSMS.Domain.Models.Subscriber
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<div class="form-group">
    @Html.LabelFor(model => model.Name, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Name, "", new {@class = "text-danger"})
    </div>
</div>

@*<div class="form-group">
            @Html.LabelFor(model => model.SubscriberNo, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.SubscriberNo, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.SubscriberNo, "", new { @class = "text-danger" })
            </div>
        </div>*@

<div class="form-group">
    @Html.LabelFor(model => model.ProviderID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.ProviderID, (SelectList) ViewBag.Providers, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.ProviderID, "", new {@class = "text-danger"})
    </div>
</div>

@*<div class="form-group">
            @Html.LabelFor(model => model.ServiceID, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownListFor(model => model.ServiceID, (SelectList)ViewBag.Services, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.ServiceID, "", new { @class = "text-danger" })
            </div>
        </div>*@
@*<div class="form-group">
    @Html.LabelFor(model => model.OpeningBalance, htmlAttributes: new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.OpeningBalance, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.OpeningBalance, "", new { @class = "text-danger" })
    </div>
</div>*@

<div class="form-group">
    @Html.LabelFor(model => model.PhoneNumber, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.PhoneNumber, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.PhoneNumber, "", new {@class = "text-danger"})
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.Address, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Address, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Address, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
    </div>
</div>