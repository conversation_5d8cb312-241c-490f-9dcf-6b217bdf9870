﻿@model AppTech.MSMS.Domain.Models.WifiFaction

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<input type="hidden" name="ID" value="@Model.ID" />
<input type="hidden" name="WifiProviderID" value="@ViewBag.WifiProviderID" />

@*@if (Model.ID == 0)
{*@
    <div>
        <div class="form-group">
            @Html.LabelFor(model => model.Number, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Number)
                @Html.ValidationMessageFor(model => model.Number)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.OrderNO, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.OrderNO)
                @Html.ValidationMessageFor(model => model.OrderNO)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Name)
                @Html.ValidationMessageFor(model => model.Name)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Price, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Price)
                @Html.ValidationMessageFor(model => model.Price)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Description)
                @Html.ValidationMessageFor(model => model.Description)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Note)
                @Html.ValidationMessageFor(model => model.Note)
            </div>
        </div>
    </div>
@*}
else
{
    <p> else</p>
}*@



<script>
    hideLoading();
</script>
<script>

    $(function () {
        try {
            formHelper.onSuccess = function (data) {
                log('user party form');
                i('data> ' + data);
                $("#modal").modal('hide');
                $("#list").html(data);
            }
            formHelper.onBegin = function (context) {
                return true;
            };
        } catch (e) {
        }
    });
    $(function () {
        i('on load');
        $("#SyncAccountID").on('change',
            function () {
                i('on change sync account select');
                var name = $("#SyncAccountID option:selected").text();
                $("#OwnerName").val(name);
            });
    });

</script>