﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="DefaultConnection" connectionString="Data Source=:memory:;Version=3;" providerName="System.Data.SQLite" />
  </connectionStrings>
  
  <appSettings>
    <add key="DemoMode" value="true" />
    <add key="ConnectionStringSource" value="ConfigFile" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
  </appSettings>
  
  <system.web>
    <compilation debug="true" targetFramework="4.8" />
    <httpRuntime targetFramework="4.8" maxRequestLength="51200" />
    <customErrors mode="Off" />
    <trust level="Full" />
  </system.web>
  
  <system.webServer>
    <defaultDocument>
      <files>
        <clear />
        <add value="index.html" />
        <add value="default.aspx" />
        <add value="default.htm" />
      </files>
    </defaultDocument>
    <directoryBrowse enabled="true" />
  </system.webServer>
</configuration>
