﻿@model AppTech.MSMS.Domain.Models.MerchantPayment

@{
    Layout = "~/Views/Shared/_FormLayout.cshtml";
}


<input type="hidden" name="Device" value="Web" />
<div class="form-group">
    @Html.LabelFor(model => model.MerchantID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.MerchantID, (SelectList)ViewBag.Merchants)
        @Html.ValidationMessageFor(model => model.MerchantID)
    </div>
</div>
<div class="form-group form-inline">
    @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Amount, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>

<div class="form-group">
        <label class="control-label col-md-2">العملة</label>

        <div class="col-sm-10">
            @Html.DropDownListFor(model => model.CurrencyID, new[]
            {
                new SelectListItem {Text = "يمني", Value = "1"},
                new SelectListItem {Text = "دولار", Value = "2"},
                new SelectListItem {Text = "سعودي", Value = "3"}
            })

        </div>
        @Html.ValidationMessageFor(model => model.CurrencyID)
    </div>


<div class="form-group">
    @Html.LabelFor(model => model.InvoiceNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.InvoiceNumber)
        @Html.ValidationMessageFor(model => model.InvoiceNumber)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note)
        @Html.ValidationMessageFor(model => model.Note)
    </div>
</div>

