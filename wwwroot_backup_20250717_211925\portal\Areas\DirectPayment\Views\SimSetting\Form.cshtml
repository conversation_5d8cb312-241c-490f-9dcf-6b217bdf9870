﻿@model AppTech.MSMS.Domain.Settings.SimSetting

<div class="row">
    <div class="col-xs-12 col-sm-6">
        @using (Ajax.BeginForm(new AjaxOptions
        {
            OnBegin = "return OnFormBegin()",
            OnSuccess = "onCrudSuccess",
            OnFailure = "onCrudFailure",
            LoadingElementId = "formloader"
        }))
        {
            foreach (var property in ViewData.ModelMetadata.Properties)
            {
                <div class="checkbox">
                    <label>
                        @Html.Editor(property.PropertyName)
                        @Html.Label(property.DisplayName)
                    </label>
                </div>
            }


            <div class="space-10"></div>

            <div class="space-32"></div>
            <div class="space-32"></div>
            <div class="hr hr32 hr-dotted"></div>
            @Html.Partial("_FormAction")
        }
    </div>


</div>