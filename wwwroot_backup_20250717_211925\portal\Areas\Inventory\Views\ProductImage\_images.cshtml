﻿@model IEnumerable<AppTech.MSMS.Domain.Models.ProductImage>
    <table class="table table-hover">
        <thead>
            <tr>
                <th>الصورة</th>
                <th>العنوان</th>
                <th>الوصف</th>
                <th>معلومات اضافية</th>

            </tr>
        </thead>
        @foreach (var image in Model)
        {
            <tbody>
                <tr>
                    @if (image != null)
                    {

                        <td> <img src="@image.ImageName.TrimStart('~')" style="width:100px;height:auto;margin:0 40px" /></td>
                        <td>@Html.DisplayFor(modelItem =>image.Title)</td>
                        <td>@Html.DisplayFor(modelItem =>image.Description)</td>
                        <td>@Html.DisplayFor(modelItem =>image.Extainfo)</td>


                    }
                </tr>
            </tbody>
                }
        </table>
    <script>
        function openModal(id) {
            i('open modal id' + id);
            openViewAsModal('Inventory/ProductImage/AddOrEditImage?productID=' + id, "صورة جديدة);
        }
    </script>
