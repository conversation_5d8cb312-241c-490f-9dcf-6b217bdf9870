﻿@model AppTech.MSMS.Domain.Models.TrailToupOrder
@using Obout.Mvc.ComboBox
@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security

@{
    Layout = "~/Views/Shared/_FormAsync.cshtml";
}
<input type="hidden" name="Device" value="Web" />

@if (CurrentUser.Type == UserType.Admin)
{
    <div class="form-group">
        @Html.Label("اسم الحساب", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.Obout(new ComboBox("AccountID")
            {
                Width = 300,
                SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
                FilterType = ComboBoxFilterType.Contains,
                LoadingText = "Loading"
            })
            @Html.ValidationMessageFor(model => model.AccountID, "", new { @class = "text-danger" })
        </div>
    </div>
}
else
{
<input type="hidden" name="AccountID" id="AccountID" value="0" />
}
<div class="form-group">
    @Html.LabelFor(model => model.MobileNetworkID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.MobileNetworkID, new[]
            {
                new SelectListItem {Text = "سبأفون جملة", Value = "3"},
                new SelectListItem {Text = "MTN جملة", Value = "2"},
                new SelectListItem {Text = "يمن موبايل جملة", Value = "1"},
            })
        </div>
        @Html.ValidationMessageFor(model => model.MobileNetworkID, "", new { @class = "text-danger" })
    </div>
</div>




<div class="form-group">
    @Html.LabelFor(model => model.SubscriberNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.SubscriberNumber, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.SubscriberNumber, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Amount, new { htmlAttributes = new { @class = "" } })
        @Ajax.ActionLink(
            "احتساب المبلغ",
            "CalcAmount",
            null,
            //new {number= " getNumber()" },

            new AjaxOptions
            {
                // UpdateTargetId="CustomerList", // <-- DOM element ID to update
                //InsertionMode = InsertionMode.Replace, // <-- Replace the content of DOM element
                LoadingElementId = "loader",
                OnSuccess = "onSuccess",
                OnFailure = "onFailure",
                HttpMethod = "GET" // <-- HTTP method
            },
            new { @class = "btn btn-primary btn-round", onclick = "this.href = '/clients/TrailToupOrder/CalcAmount?amount=' + $('#Amount').val() + '&networkId=' +$('#MobileNetworkID').val()+ '&accountId=' +$('#AccountID').val()" }
            )
        @Html.ValidationMessageFor(model => model.Amount, "", new { @class = "text-danger" })
    </div>

</div>


<div class="form-group" id="ex-amount-row">
    @Html.LabelFor(model => model.ExchangeAmount, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.ExchangeAmount, new { htmlAttributes = new { @class = "", @readonly = "readonly" } })
        @Html.ValidationMessageFor(model => model.ExchangeAmount, "", new { @class = "text-danger" })
    </div>
</div>
<script>
    $(function () {
        $("#cancel-button").hide();
    });
    $("#ex-amount-row").hide();

    function onSuccess(data) {

        if (data.Success) {
            $("#ex-amount-row").show();
            $("#ExchangeAmount").val(data.Message);
        } else {

            alert(data.Message);
        }
    }

    function onFailure(xhr, status) {
        hideLoading();
        log('on trial calc topup Failure');
        //  hideLoading();
        var msg = parseXhr(xhr);
        log('on Failure xhr msg:' + msg);
        alert(msg);

    }
</script>