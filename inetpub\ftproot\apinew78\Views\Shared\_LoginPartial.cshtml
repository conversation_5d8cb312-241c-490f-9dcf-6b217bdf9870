﻿@using Microsoft.AspNet.Identity
@if (Request.IsAuthenticated)
{
    using (Html.BeginForm("LogOff", "Auth", FormMethod.Post, new {id = "logoutForm", @class = "navbar-right"}))
    {
        @Html.AntiForgeryToken()

        <ul class="nav navbar-nav navbar-right">
            <li>
                @Html.ActionLink("Hello " + User.Identity.GetUserName() + "!", "Manage", "Auth", null, new {title = "Manage"})
            </li>
            <li>
                <a href="javascript:document.getElementById('logoutForm').submit()">Log off</a>
            </li>
        </ul>
    }
}
else
{
    <ul class="nav navbar-nav navbar-right">
        <li>@Html.ActionLink("Register", "Signup", "Auth", null, new {id = "registerLink"})</li>
        <li>@Html.ActionLink("Log in", "Login", "Auth", null, new {id = "loginLink"})</li>
    </ul>
}