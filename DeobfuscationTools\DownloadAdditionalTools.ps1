# Download Additional Deobfuscation Tools
# تحميل أدوات إضافية لفك التشفير

param(
    [string]$ToolsPath = "C:\inetpub\DeobfuscationTools"
)

Write-Host "=== Downloading Additional Tools ===" -ForegroundColor Green

# إنشاء مجلد الأدوات الإضافية
$AdditionalToolsPath = "$ToolsPath\AdditionalTools"
if (!(Test-Path $AdditionalToolsPath)) {
    New-Item -ItemType Directory -Path $AdditionalToolsPath -Force | Out-Null
}

# دالة للتحميل مع معالجة الأخطاء
function Download-Tool {
    param(
        [string]$Url,
        [string]$OutputPath,
        [string]$ToolName
    )
    
    try {
        Write-Host "Downloading $ToolName..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $Url -OutFile $OutputPath -ErrorAction Stop
        Write-Host "✅ $ToolName downloaded successfully" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ Failed to download $ToolName : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# قائمة الأدوات للتحميل
$tools = @(
    @{
        Name = "Reflexil"
        Url = "https://github.com/sailro/Reflexil/releases/download/v2.5/Reflexil.for.Reflector.v8.5.v10.0.AIO.zip"
        Output = "$AdditionalToolsPath\Reflexil.zip"
        Description = "Assembly editor for .NET"
    },
    @{
        Name = "ConfuserEx Unpacker"
        Url = "https://github.com/NotPrab/.NET-Deobfuscator/archive/refs/heads/master.zip"
        Output = "$AdditionalToolsPath\NET-Deobfuscator.zip"
        Description = "Multiple .NET deobfuscators"
    }
)

Write-Host "`nAttempting to download additional tools..." -ForegroundColor Cyan

$successCount = 0
$totalCount = $tools.Count

foreach ($tool in $tools) {
    if (Download-Tool -Url $tool.Url -OutputPath $tool.Output -ToolName $tool.Name) {
        $successCount++
        Write-Host "  Description: $($tool.Description)" -ForegroundColor Gray
    }
    Write-Host ""
}

# تحميل أدوات محلية مفيدة
Write-Host "Creating additional utility scripts..." -ForegroundColor Cyan

# سكريبت لتحليل التوقيعات الرقمية
$signatureAnalyzer = @"
# Digital Signature Analyzer
param([string]`$FilePath)

try {
    `$signature = Get-AuthenticodeSignature `$FilePath
    Write-Host "File: `$FilePath" -ForegroundColor Yellow
    Write-Host "Status: `$(`$signature.Status)" -ForegroundColor White
    Write-Host "Certificate: `$(`$signature.SignerCertificate.Subject)" -ForegroundColor White
    Write-Host "Timestamp: `$(`$signature.TimeStamperCertificate.NotAfter)" -ForegroundColor White
} catch {
    Write-Host "Error analyzing signature: `$(`$_.Exception.Message)" -ForegroundColor Red
}
"@

$signatureAnalyzer | Out-File -FilePath "$AdditionalToolsPath\AnalyzeSignature.ps1" -Encoding UTF8

# سكريبت لاستخراج الموارد
$resourceExtractor = @"
# Resource Extractor for .NET Assemblies
param([string]`$AssemblyPath, [string]`$OutputDir = ".\Resources")

try {
    Add-Type -AssemblyName System.Reflection
    `$assembly = [System.Reflection.Assembly]::LoadFile(`$AssemblyPath)
    `$resources = `$assembly.GetManifestResourceNames()
    
    if (!(Test-Path `$OutputDir)) {
        New-Item -ItemType Directory -Path `$OutputDir -Force | Out-Null
    }
    
    Write-Host "Found `$(`$resources.Count) resources in assembly" -ForegroundColor Green
    
    foreach (`$resource in `$resources) {
        try {
            `$stream = `$assembly.GetManifestResourceStream(`$resource)
            `$outputFile = Join-Path `$OutputDir `$resource
            `$fileStream = [System.IO.File]::Create(`$outputFile)
            `$stream.CopyTo(`$fileStream)
            `$fileStream.Close()
            `$stream.Close()
            Write-Host "Extracted: `$resource" -ForegroundColor Cyan
        } catch {
            Write-Host "Failed to extract: `$resource" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "Error: `$(`$_.Exception.Message)" -ForegroundColor Red
}
"@

$resourceExtractor | Out-File -FilePath "$AdditionalToolsPath\ExtractResources.ps1" -Encoding UTF8

# سكريبت لتحليل الاعتماديات
$dependencyAnalyzer = @"
# Assembly Dependency Analyzer
param([string]`$AssemblyPath)

try {
    Add-Type -AssemblyName System.Reflection
    `$assembly = [System.Reflection.Assembly]::LoadFile(`$AssemblyPath)
    `$references = `$assembly.GetReferencedAssemblies()
    
    Write-Host "Assembly: `$(`$assembly.FullName)" -ForegroundColor Yellow
    Write-Host "Dependencies (`$(`$references.Count)):" -ForegroundColor Green
    
    foreach (`$ref in `$references) {
        Write-Host "  - `$(`$ref.Name) v`$(`$ref.Version)" -ForegroundColor White
    }
} catch {
    Write-Host "Error: `$(`$_.Exception.Message)" -ForegroundColor Red
}
"@

$dependencyAnalyzer | Out-File -FilePath "$AdditionalToolsPath\AnalyzeDependencies.ps1" -Encoding UTF8

# إنشاء ملف README للأدوات الإضافية
$additionalReadme = @"
# Additional Deobfuscation Tools
## أدوات إضافية لفك التشفير

### الأدوات المحملة:
1. **Reflexil** - محرر التجميعات لـ .NET
2. **NET-Deobfuscator** - مجموعة أدوات فك التشويش

### السكريبتات المساعدة:
1. **AnalyzeSignature.ps1** - تحليل التوقيعات الرقمية
2. **ExtractResources.ps1** - استخراج الموارد من التجميعات
3. **AnalyzeDependencies.ps1** - تحليل اعتماديات التجميعات

### الاستخدام:
```powershell
# تحليل التوقيع الرقمي
.\AnalyzeSignature.ps1 -FilePath "path\to\assembly.dll"

# استخراج الموارد
.\ExtractResources.ps1 -AssemblyPath "path\to\assembly.dll" -OutputDir ".\ExtractedResources"

# تحليل الاعتماديات
.\AnalyzeDependencies.ps1 -AssemblyPath "path\to\assembly.dll"
```

### ملاحظات:
- تأكد من تشغيل PowerShell كمدير
- بعض الأدوات قد تحتاج إلى استخراج يدوي
- استخدم هذه الأدوات بمسؤولية وفقاً للقوانين المحلية
"@

$additionalReadme | Out-File -FilePath "$AdditionalToolsPath\README.md" -Encoding UTF8

Write-Host "=== Download Summary ===" -ForegroundColor Green
Write-Host "Successfully downloaded: $successCount/$totalCount tools" -ForegroundColor White
Write-Host "Additional scripts created: 3" -ForegroundColor White
Write-Host "Tools location: $AdditionalToolsPath" -ForegroundColor Yellow

Write-Host "`nAdditional tools and scripts are ready!" -ForegroundColor Green
