﻿@model AppTech.MSMS.Domain.Models.ServiceInfo
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    @Html.Label("اسم الخدمة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name)
        @Html.ValidationMessageFor(model => model.Name)
    </div>
</div>


<div class="form-group">
    @Html.Label("الوصف", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Description)
        @Html.ValidationMessageFor(model => model.Description)
    </div>
</div>


<input type="hidden" name="Type" id="Type" value="Direct"/>

@*<div class="form-group">
    @Html.Label(model => model.FixedPrice, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.FixedPrice, new[]{
            new SelectListItem(){Text = "نعم",Value = bool.TrueString},
            new SelectListItem(){Text = "لا",Value = bool.FalseString}
        }, new { @onchange = "price(this)" })
    </div>
</div>*@


<div class="form-group">
    @Html.Label("سعر الخدمة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Prices)
        @Html.ValidationMessageFor(model => model.Prices)
    </div>
</div>

<div class="form-group">
    @Html.Label("الحالة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.PartyTarget, new[]
        {
            new SelectListItem {Text = "نشط", Value = bool.TrueString},
            new SelectListItem {Text = "موقف", Value = bool.FalseString}
        })
    </div>
</div>

<div class="form-group">
    @Html.Label("ملاحظات", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note)
        @Html.ValidationMessageFor(model => model.Note)
    </div>
</div>

<script>
    $(function () {
        var priceable =@Model.FixedPrice;
        if (priceable === true) {
            $("#Prices").show();
        }
        else {
            $("#Prices").hide();
        }
    })
</script>