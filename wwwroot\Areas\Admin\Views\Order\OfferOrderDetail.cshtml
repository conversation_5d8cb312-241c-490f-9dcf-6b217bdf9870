﻿@using AppTech.MSMS.Domain
@model AppTech.MSMS.Domain.Models.OfferOrder

<div class="space-6"></div>
<span class="label label-info"> بيانات التسديد</span>
<div class="space-6"></div>


<div class="profile-info-row">
    <div class="profile-info-name"> رقم المشترك </div>

    <div class="profile-info-value">
        <span class="editable" id="age"> @Html.DisplayFor(model => model.SubscriberNo) </span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> نوع الخط </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.SimType)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> نوع العرض </div>

    <div class="profile-info-value">
        @if (Model.OfferType == (byte) OfferType.G3)
        {
            <span class="editable" id="about"> 3G تفيعل </span>
        }
        else
        {
            <span class="editable" id="about"> 1X تفيعل </span>
        }

    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> الملاحظات </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Note)</span>
    </div>
</div>

