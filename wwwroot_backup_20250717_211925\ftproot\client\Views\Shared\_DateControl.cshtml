﻿@model AppTech.BusinessLogic.ReportHelpers.DateReportModel
@*<style>
    .ui-datepicker-calendar {
        display: none;
    }
</style>*@
<div class="widget-box">


    <div class="widget-body">
        <div class="widget-main">
            <div class="form-inline">
                @Html.EnumDropDownListFor(
                    x => x.PeriodType,
                    new { @class = "input-small", onchange = "showSecondInput(this) " })
                <div class="input-group input-group-sm">
                    @Html.EditorFor(model => model.StartDate, new { htmlAttributes = new { @class = "input-small form-control date-picker", @readonly = "@readonly" } })
                    <span class="input-group-addon">
                        <i class="ace-icon fa fa-calendar"></i>
                    </span>
                </div>
          
                    

                <div id="end_date" class="input-group input-group-sm">
                    <span class="input-group-addon">
                        &nbsp;  الى&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    </span>
                    @Html.EditorFor(model => model.EndDate, new {htmlAttributes = new {@class = " input-small form-control  date-picker", id = "EndDate", @readonly = "@readonly"}})

                </div>

                </div>
    </div>
</div>
</div>
@*<div class="row">
    <div class="col-xs-6">
        <div class="input-group input-group-sm">
            <input type="text" id="datepicker" class="form-control" />
            <span class="input-group-addon">
                <i class="ace-icon fa fa-calendar"></i>
            </span>
        </div>
    </div>
</div>*@

<!-- ./span -->
    @*<div class="row">
        <div class="col-xs-8 col-sm-11">
            <div class="input-group date form-inline">
                @Html.EnumDropDownListFor(
                    x => x.PeriodType,
                    new { @class = "input-small", onchange = "showSecondInput(this) " })


                @Html.EditorFor(model => model.StartDate, new { htmlAttributes = new { @class = "input-small date-picker", @readonly = "@readonly" } })


            </div>
        </div>
    </div>

<div id="end_date" class="row">
 
    <div class="col-xs-8 col-sm-11">

        <div class="input-group date form-inline">
            <label for="EndDate">الى تاريخ</label>
            @Html.EditorFor(model => model.EndDate, new {htmlAttributes = new {@class = " input-small date-picker", id = "EndDate", @readonly = "@readonly"}})
        </div>
    </div>
</div>*@

<div class="space-6"></div>


<script>

    try {
        $('.date-picker').datepicker({
            showOtherMonths: true,
            selectOtherMonths: true,
            isRTL:true,
            dateFormat: "dd/MM/yy",
            changeMonth: true,
            changeYear: true,
            yearRange: "-60:+0",
            autoclose: true,
            todayHighlight: true,
            showButtonPanel: true,

        }).next().on('click',
            function() {
                $(this).prev().focus();
            });
    } catch (e) {
        alert("Couldnt set date-picker: " + e);
    }

</script>