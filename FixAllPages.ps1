# Fix All AppTech Pages Script
# سكريبت إصلاح جميع صفحات النظام

Write-Host @"
╔══════════════════════════════════════════════════════════════╗
║                Fix All AppTech Pages                        ║
║              إصلاح جميع صفحات النظام                       ║
╚══════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Red

Write-Host "`nStarting comprehensive system fix..." -ForegroundColor Yellow

# الخطوة 1: إنشاء ملفات web.config صحيحة لكل تطبيق
Write-Host "`n=== Step 1: Creating Proper Web.config Files ===" -ForegroundColor Cyan

$webConfigTemplate = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  
  <connectionStrings>
    <add name="DefaultConnection" connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\AppTech.mdf;Integrated Security=True" providerName="System.Data.SqlClient" />
  </connectionStrings>
  
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="vs:EnableBrowserLink" value="false" />
    <add key="ApplicationType" value="web" />
    <add key="ConnectionStringSource" value="ConfigFile" />
    <add key="DemoMode" value="false" />
  </appSettings>
  
  <system.web>
    <authentication mode="Forms">
      <forms loginUrl="~/Account/Login" timeout="2880" />
    </authentication>
    <compilation debug="true" targetFramework="4.8" />
    <httpRuntime targetFramework="4.8" maxRequestLength="51200" executionTimeout="300" />
    <customErrors mode="Off" />
    <trust level="Full" />
    <pages>
      <namespaces>
        <add namespace="System.Web.Helpers" />
        <add namespace="System.Web.Mvc" />
        <add namespace="System.Web.Mvc.Ajax" />
        <add namespace="System.Web.Mvc.Html" />
        <add namespace="System.Web.Optimization"/>
        <add namespace="System.Web.Routing" />
      </namespaces>
    </pages>
  </system.web>
  
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <defaultDocument>
      <files>
        <clear />
        <add value="default.aspx" />
        <add value="index.html" />
        <add value="default.html" />
      </files>
    </defaultDocument>
    <directoryBrowse enabled="false" />
  </system.webServer>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
</configuration>
"@

# تطبيق web.config على جميع التطبيقات
$apps = @("portal", "api", "client", "apinewAN")
foreach ($app in $apps) {
    $appPath = "C:\inetpub\wwwroot\$app"
    $webConfigPath = "$appPath\web.config"
    
    if (Test-Path $appPath) {
        Write-Host "Fixing $app web.config..." -ForegroundColor Yellow
        
        # نسخ احتياطي من web.config الحالي
        if (Test-Path $webConfigPath) {
            Copy-Item $webConfigPath "$webConfigPath.backup" -Force -ErrorAction SilentlyContinue
        }
        
        # إنشاء web.config جديد
        $webConfigTemplate | Out-File -FilePath $webConfigPath -Encoding UTF8 -Force
        Write-Host "✅ $app web.config fixed" -ForegroundColor Green
    } else {
        Write-Host "❌ $app directory not found" -ForegroundColor Red
    }
}

# الخطوة 2: إنشاء صفحات افتراضية لكل تطبيق
Write-Host "`n=== Step 2: Creating Default Pages ===" -ForegroundColor Cyan

$defaultPageTemplate = @"
<%@ Page Language="C#" AutoEventWireup="true" %>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppTech System - {APP_NAME}</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container { 
            text-align: center; 
            background: rgba(255,255,255,0.1); 
            padding: 40px; 
            border-radius: 15px; 
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            max-width: 600px;
        }
        h1 { 
            font-size: 3em; 
            margin: 0 0 20px 0; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .status { 
            font-size: 1.2em; 
            margin: 20px 0; 
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            margin: 10px; 
            background: rgba(255,255,255,0.2); 
            color: white; 
            text-decoration: none; 
            border-radius: 8px; 
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }
        .btn:hover { 
            background: rgba(255,255,255,0.3); 
            transform: translateY(-2px);
        }
        .info { 
            margin: 20px 0; 
            font-size: 0.9em; 
            opacity: 0.8; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AppTech {APP_NAME}</h1>
        <div class="status">
            <p>✅ النظام يعمل بنجاح</p>
            <p>📊 جاهز للاستخدام</p>
            <p>🔧 تم الإصلاح والتحديث</p>
        </div>
        <div>
            <a href="/portal" class="btn">🏠 البوابة الرئيسية</a>
            <a href="/api" class="btn">🔌 API</a>
            <a href="/client" class="btn">👥 العملاء</a>
            <a href="/collections_system" class="btn">💰 التحصيلات</a>
        </div>
        <div class="info">
            <p>تم إصلاح النظام وتحديث جميع الملفات المطلوبة</p>
            <p>التاريخ: <%= DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") %></p>
        </div>
    </div>
</body>
</html>
"@

foreach ($app in $apps) {
    $appPath = "C:\inetpub\wwwroot\$app"
    $defaultPagePath = "$appPath\default.aspx"
    
    if (Test-Path $appPath) {
        Write-Host "Creating default page for $app..." -ForegroundColor Yellow
        
        $pageContent = $defaultPageTemplate -replace "{APP_NAME}", $app.ToUpper()
        $pageContent | Out-File -FilePath $defaultPagePath -Encoding UTF8 -Force
        
        Write-Host "✅ $app default page created" -ForegroundColor Green
    }
}

# الخطوة 3: إصلاح صلاحيات المجلدات
Write-Host "`n=== Step 3: Fixing Folder Permissions ===" -ForegroundColor Cyan

foreach ($app in $apps) {
    $appPath = "C:\inetpub\wwwroot\$app"
    if (Test-Path $appPath) {
        Write-Host "Fixing permissions for $app..." -ForegroundColor Yellow
        
        # إعطاء صلاحيات IIS
        icacls $appPath /grant "IIS_IUSRS:(OI)(CI)F" /T >$null 2>&1
        icacls $appPath /grant "IUSR:(OI)(CI)F" /T >$null 2>&1
        icacls $appPath /grant "Everyone:(OI)(CI)F" /T >$null 2>&1
        
        Write-Host "✅ $app permissions fixed" -ForegroundColor Green
    }
}

# الخطوة 4: إعادة تشغيل IIS
Write-Host "`n=== Step 4: Restarting IIS ===" -ForegroundColor Cyan
try {
    iisreset /restart
    Write-Host "✅ IIS restarted successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Please restart IIS manually" -ForegroundColor Yellow
}

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "SYSTEM FIX COMPLETED" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`n🎉 All pages should now be working!" -ForegroundColor Green
Write-Host "`n🌐 Test these URLs:" -ForegroundColor Yellow
foreach ($app in $apps) {
    Write-Host "  • http://localhost/$app" -ForegroundColor Cyan
}

Write-Host "`n✨ System is ready for use! ✨" -ForegroundColor Green
