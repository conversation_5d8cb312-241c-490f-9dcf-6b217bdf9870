﻿# Digital Signature Analyzer
param([string]$FilePath)

try {
    $signature = Get-AuthenticodeSignature $FilePath
    Write-Host "File: $FilePath" -ForegroundColor Yellow
    Write-Host "Status: $($signature.Status)" -ForegroundColor White
    Write-Host "Certificate: $($signature.SignerCertificate.Subject)" -ForegroundColor White
    Write-Host "Timestamp: $($signature.TimeStamperCertificate.NotAfter)" -ForegroundColor White
} catch {
    Write-Host "Error analyzing signature: $($_.Exception.Message)" -ForegroundColor Red
}
