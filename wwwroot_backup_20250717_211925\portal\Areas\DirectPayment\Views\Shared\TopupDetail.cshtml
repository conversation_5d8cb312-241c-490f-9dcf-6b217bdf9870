﻿@model AppTech.MSMS.Domain.Models.Topup

<h3 class="header blue lighter smaller">
    <i class="ace-icon fa fa-sort-desc smaller-90"></i>
    التفاصيل
</h3>

<div id="tabs">
    <ul>
        <li>
            <a href="#tabs-1">تفاصيل العملية</a>
        </li>

        <li>
            <a href="#tabs-2">تفاصيل الفاتورة</a>
        </li>

        <li>
            <a href="#tabs-3">تفاصيل التكلفة</a>
        </li>
        <li>
            <a href="#tabs-4">تفاصيل الربط</a>
        </li>
        
        <li>
            <a href="#tabs-5">أخرى</a>
        </li>
    </ul>
    
    
<div id="tabs-1">
    
<div id="user-profile-1" class="user-profile row">
    <div class="profile-user-info profile-user-info-striped">

        <div class="profile-info-row">
            <div class="profile-info-name"> رقم العملية </div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @Html.DisplayFor(model => model.Number) </span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name">الحساب </div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @Html.DisplayFor(model => model.Account.Name) </span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> رقم المشترك </div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @Html.DisplayFor(model => model.SubscriberNumber) </span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> الصنف </div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @Html.DisplayFor(model => model.ServiceInfo.Name) </span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> التفاصيل </div>

            <div class="profile-info-value">
                <span class="editable" id="about"> @Html.DisplayFor(model => model.BundleName)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> وقت العملية </div>

            <div class="profile-info-value">
                <span class="editable" id="about"> @Html.DisplayFor(model => model.Date)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> وقت التنفيذ </div>

            <div class="profile-info-value">
                <span class="editable" id="about"> @Html.DisplayFor(model => model.ResponseTime)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> مدة التنفيذ (بالثانية) </div>

            <div class="profile-info-value">
                <span class="editable" id="about"> @Html.DisplayFor(model => model.ExecutionPeroid)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> الحالة </div>

            <div class="profile-info-value">
                <span class="editable" id="signup"> @Html.DisplayFor(model => model.StateClass)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> الملاحظات </div>

            <div class="profile-info-value">
                <span class="editable" id="about"> @Html.DisplayFor(model => model.Note)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> @Html.DisplayNameFor(model => model.AdminNote) </div>

            <div class="profile-info-value">
                <span class="editable" id="about"> @Html.DisplayFor(model => model.AdminNote)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> @Html.DisplayNameFor(model => model.LineType)</div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @Html.DisplayFor(model => model.LineType) </span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> @Html.DisplayNameFor(model => model.Method)</div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @Html.DisplayFor(model => model.Method) </span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> @Html.DisplayNameFor(model => model.Debited)</div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @Html.DisplayFor(model => model.Debited) </span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> @Html.DisplayNameFor(model => model.Channel)</div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @Html.DisplayFor(model => model.Channel) </span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> المستخدم</div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @Html.DisplayFor(model => model.UserInfo.UserName) </span>
            </div>
        </div>




    </div>
</div>
</div>

    <div id="tabs-2">
        <div class="profile-user-info profile-user-info-striped">

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.BillNumber)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.BillNumber) </span>
                </div>
            </div>


            <div class="profile-info-row">
                <div class="profile-info-name"> تاريخ الفاتورة </div>

                <div class="profile-info-value">
                    <span class="editable" id="about"> @Html.DisplayFor(model => model.Date)</span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.Type)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.Type) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.Quantity)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.Quantity) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.UnitPrice)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.UnitPrice) </span>
                </div>
            </div>


            <div class="profile-info-row">
                <div class="profile-info-name">  إجمالي المبلغ</div>

                <div class="profile-info-value">
                    <span class="editable" id="signup"> @Html.DisplayFor(model => model.Amount)</span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.Discount)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.Discount) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.CommissionAmount)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.CommissionAmount) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.TotalAmount)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.TotalAmount) </span>
                </div>
            </div>


            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.QuotaionID)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.QuotaionID) </span>
                </div>
            </div>

        </div>
    </div>

    <div id="tabs-3">
        <div class="profile-user-info profile-user-info-striped">



            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.UnitCost)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.UnitCost) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.Quantity)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.Quantity) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.CostAmount) </div>

                <div class="profile-info-value">
                    <span class="editable" id="signup"> @Html.DisplayFor(model => model.CostAmount)</span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.DifferentialAmount) </div>

                <div class="profile-info-value">
                    <span class="editable" id="signup"> @Html.DisplayFor(model => model.DifferentialAmount)</span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.TotalCost)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.TotalCost) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.Profits)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.Profits) </span>
                </div>
            </div>
        </div>
    </div>

    <div id="tabs-4">
        <div class="profile-user-info profile-user-info-striped">
            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.TopupProvider.Name)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.TopupProvider.Name) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.TopupProvider.Number)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.TopupProvider.Number) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.Responded)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.Responded) </span>
                </div>
            </div>


            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.RequestInfo) </div>

                <div class="profile-info-value">
                    <span class="editable" id="signup"> @Html.DisplayFor(model => model.RequestInfo)</span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.ResponseInfo)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.ResponseInfo) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.ProviderRM)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.ProviderRM) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name"> وقت الرد </div>

                <div class="profile-info-value">
                    <span class="editable" id="about"> @Html.DisplayFor(model => model.ResponseTime)</span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">رقم عملية التنفيذ</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.TransactionID) </span>
                </div>
            </div>
            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.RefNumber)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.RefNumber) </span>
                </div>
            </div>


            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.ResponseStatus)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.ResponseStatus) </span>
                </div>
            </div>


            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.BundleCode)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.BundleCode) </span>
                </div>
            </div>



            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.FaildRequest)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.FaildRequest) </span>
                </div>
            </div>

            @if (Model.FaildRequest)
            {

                <div class="profile-info-row">
                    <div class="profile-info-name">  @Html.DisplayNameFor(model => model.FailedType)</div>
                    <div class="profile-info-value">
                        <span class="editable" id="age"> @Html.DisplayFor(model => model.FailedType) </span>
                    </div>
                </div>


                <div class="profile-info-row">
                    <div class="profile-info-name">  @Html.DisplayNameFor(model => model.FailedReason)</div>
                    <div class="profile-info-value">
                        <span class="editable" id="age"> @Html.DisplayFor(model => model.FailedReason) </span>
                    </div>
                </div>

            }

        </div>
    </div>
    
    
    <div id="tabs-5">
        <div class="profile-user-info profile-user-info-striped">


            <div class="profile-info-row">
                <div class="profile-info-name">@Html.DisplayNameFor(model => model.TransactionID)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.TransactionID) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.TransNumber)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.TransNumber) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.Description)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.Description) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.Cured)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.Cured) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.CuredBy) </div>

                <div class="profile-info-value">
                    <span class="editable" id="signup"> @Html.DisplayFor(model => model.CuredBy)</span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.CuredInfo) </div>

                <div class="profile-info-value">
                    <span class="editable" id="signup"> @Html.DisplayFor(model => model.CuredInfo)</span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.InspectInfo)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.InspectInfo) </span>
                </div>
            </div>


            <div class="profile-info-row">
                <div class="profile-info-name">  @Html.DisplayNameFor(model => model.Flag)</div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Html.DisplayFor(model => model.Flag) </span>
                </div>
            </div>


        </div>
    </div>
</div>


<script>

    $(function () {
        $( "#tabs" ).tabs();
        @*var status =@Model.Status;
     //   ar(status);
        status = Number(status);
         var $modal = $("#modal");
        if (status == 0)
            $modal.find(".modal-header").css("background", "red");
        else  if (status == 1)
            $modal.find(".modal-header").css("background", "green");
        else  if (status == 2)
             $modal.find(".modal-header").css("background", "orange");
        else
            $modal.find(".modal-header").css("background", "slateblue");*@
    })
</script>