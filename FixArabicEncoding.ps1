# Fix Arabic Encoding Issue
# إصلاح مشكلة ترميز النص العربي

Write-Host "=== Fixing Arabic Text Encoding ===" -ForegroundColor Green

$portalPath = "C:\inetpub\wwwroot\portal"

# إنشاء صفحة رئيسية بترميز صحيح للعربية
$fixedHomePage = @'
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppTech Portal - نظام إدارة الأعمال</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            direction: rtl;
        }
        
        .navbar-custom {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
            color: white !important;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            color: white !important;
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }
        
        .main-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        
        .main-header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-weight: 700;
        }
        
        .dashboard-grid {
            margin-top: -30px;
            position: relative;
            z-index: 10;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            border: none;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .card-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .card-description {
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
            color: white;
            transform: translateY(-2px);
        }
        
        .stats-section {
            background: white;
            padding: 40px 0;
            margin: 40px 0;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #3498db;
            display: block;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 1.1rem;
            margin-top: 10px;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0;
            margin-top: 60px;
        }
        
        .alert-custom {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .breadcrumb-custom {
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="/portal">
                <i class="fas fa-rocket me-2"></i>
                AppTech Portal
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/portal">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#dashboard">
                            <i class="fas fa-chart-line me-1"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#agents">
                            <i class="fas fa-users me-1"></i>الوكلاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#clients">
                            <i class="fas fa-building me-1"></i>العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#merchants">
                            <i class="fas fa-store me-1"></i>التجار
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/api">
                            <i class="fas fa-plug me-1"></i>API
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/collections_system">
                            <i class="fas fa-money-bill me-1"></i>التحصيلات
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Header -->
    <div class="main-header">
        <div class="container">
            <h1><i class="fas fa-rocket me-3"></i>مرحباً بك في AppTech Portal</h1>
            <p class="lead">نظام إدارة الأعمال المتكامل - الواجهة الأصلية</p>
            <div class="breadcrumb-custom d-inline-block">
                <i class="fas fa-info-circle me-2"></i>
                النظام يعمل في وضع العرض التوضيحي - جميع الوظائف متاحة للاستعراض
            </div>
        </div>
    </div>

    <!-- Dashboard Grid -->
    <div class="container dashboard-grid">
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card text-center">
                    <i class="fas fa-chart-line card-icon text-primary"></i>
                    <h3 class="card-title">لوحة التحكم</h3>
                    <p class="card-description">عرض شامل للنظام والإحصائيات الرئيسية والمؤشرات المهمة</p>
                    <a href="#dashboard" class="btn btn-custom">
                        <i class="fas fa-arrow-left me-2"></i>دخول لوحة التحكم
                    </a>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card text-center">
                    <i class="fas fa-users card-icon text-success"></i>
                    <h3 class="card-title">إدارة الوكلاء</h3>
                    <p class="card-description">إضافة وإدارة حسابات الوكلاء والمندوبين وتتبع أدائهم</p>
                    <a href="#agents" class="btn btn-custom">
                        <i class="fas fa-arrow-left me-2"></i>إدارة الوكلاء
                    </a>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card text-center">
                    <i class="fas fa-building card-icon text-info"></i>
                    <h3 class="card-title">إدارة العملاء</h3>
                    <p class="card-description">إدارة حسابات العملاء والخدمات المقدمة ومتابعة المعاملات</p>
                    <a href="#clients" class="btn btn-custom">
                        <i class="fas fa-arrow-left me-2"></i>إدارة العملاء
                    </a>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card text-center">
                    <i class="fas fa-store card-icon text-warning"></i>
                    <h3 class="card-title">إدارة التجار</h3>
                    <p class="card-description">إدارة حسابات التجار والمعاملات التجارية والمدفوعات</p>
                    <a href="#merchants" class="btn btn-custom">
                        <i class="fas fa-arrow-left me-2"></i>إدارة التجار
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Section -->
    <div class="container">
        <div class="stats-section">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-item">
                        <span class="stat-number">1,234</span>
                        <div class="stat-label">إجمالي الوكلاء</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <span class="stat-number">5,678</span>
                        <div class="stat-label">العملاء النشطين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <span class="stat-number">890</span>
                        <div class="stat-label">التجار المسجلين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <span class="stat-number">12,345</span>
                        <div class="stat-label">المعاملات اليومية</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Features -->
    <div class="container">
        <div class="row">
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                        التقارير والإحصائيات
                    </h3>
                    <p class="card-description">تقارير مفصلة وتحليلات شاملة لجميع العمليات والأنشطة</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>تقارير المبيعات اليومية والشهرية</li>
                        <li><i class="fas fa-check text-success me-2"></i>إحصائيات أداء الوكلاء</li>
                        <li><i class="fas fa-check text-success me-2"></i>تحليل الأرباح والخسائر</li>
                        <li><i class="fas fa-check text-success me-2"></i>تقارير رضا العملاء</li>
                    </ul>
                    <a href="#reports" class="btn btn-custom">
                        <i class="fas fa-chart-line me-2"></i>عرض التقارير
                    </a>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <h3 class="card-title">
                        <i class="fas fa-cogs me-2 text-secondary"></i>
                        إعدادات النظام
                    </h3>
                    <p class="card-description">تخصيص إعدادات النظام والتفضيلات العامة والأمان</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>إعدادات المستخدمين والصلاحيات</li>
                        <li><i class="fas fa-check text-success me-2"></i>إعدادات الأمان والحماية</li>
                        <li><i class="fas fa-check text-success me-2"></i>النسخ الاحتياطية التلقائية</li>
                        <li><i class="fas fa-check text-success me-2"></i>إعدادات الإشعارات</li>
                    </ul>
                    <a href="#settings" class="btn btn-custom">
                        <i class="fas fa-cog me-2"></i>الإعدادات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status Alert -->
    <div class="container">
        <div class="alert-custom">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5><i class="fas fa-info-circle me-2"></i>حالة النظام الحالية</h5>
                    <p class="mb-0">
                        <strong>الحالة:</strong> النظام يعمل في وضع العرض التوضيحي بدون قاعدة بيانات
                        <br>
                        <strong>الوصول:</strong> جميع الوظائف متاحة للاستعراض والاختبار
                        <br>
                        <strong>آخر تحديث:</strong> <span id="currentTime"></span>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <i class="fas fa-server fa-3x text-success"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>AppTech Portal</h5>
                    <p>نظام إدارة الأعمال المتكامل - الواجهة الأصلية</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>© 2024 AppTech System - جميع الحقوق محفوظة</p>
                    <p>تم التطوير بواسطة فريق AppTech المتخصص</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديث الوقت
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        }
        updateTime();
        setInterval(updateTime, 1000);

        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير ظهور البطاقات
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // تأثير hover على الإحصائيات
            const statItems = document.querySelectorAll('.stat-item');
            statItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
'@

# حفظ الصفحة بترميز UTF-8 صحيح
Write-Host "Creating fixed Arabic page..." -ForegroundColor Yellow

# استخدام UTF8 بدون BOM
$utf8NoBom = New-Object System.Text.UTF8Encoding $false
[System.IO.File]::WriteAllText("$portalPath\index.html", $fixedHomePage, $utf8NoBom)
[System.IO.File]::WriteAllText("$portalPath\default.html", $fixedHomePage, $utf8NoBom)

Write-Host "✅ Arabic encoding fixed!" -ForegroundColor Green

# تحديث web.config لدعم UTF-8
$webConfigWithUTF8 = @'
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.web>
    <globalization requestEncoding="utf-8" responseEncoding="utf-8" culture="ar-SA" uiCulture="ar-SA" />
  </system.web>
  <system.webServer>
    <defaultDocument>
      <files>
        <clear />
        <add value="index.html" />
        <add value="default.html" />
      </files>
    </defaultDocument>
    <directoryBrowse enabled="false" />
    <staticContent>
      <remove fileExtension=".html" />
      <mimeMap fileExtension=".html" mimeType="text/html; charset=utf-8" />
    </staticContent>
  </system.webServer>
</configuration>
'@

[System.IO.File]::WriteAllText("$portalPath\web.config", $webConfigWithUTF8, $utf8NoBom)
Write-Host "✅ Web.config updated for UTF-8" -ForegroundColor Green

# إعادة تشغيل IIS
Write-Host "Restarting IIS..." -ForegroundColor Yellow
iisreset /restart >$null 2>&1
Write-Host "✅ IIS restarted" -ForegroundColor Green

Write-Host "`n🎉 Arabic text encoding fixed!" -ForegroundColor Green
Write-Host "🌐 Access the fixed page: http://localhost/portal" -ForegroundColor Cyan
