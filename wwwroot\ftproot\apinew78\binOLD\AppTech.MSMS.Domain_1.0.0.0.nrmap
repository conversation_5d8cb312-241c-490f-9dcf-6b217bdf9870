<<type>>
<Module>
<Module>
<Module>
<Module>
<<type>>
m1mFDf9evLKEcwqM1q.kmmkcgGj9QlnVC1LJt
YpGHUWPcZR9TSDIMKU
kmmkcgGj9QlnVC1LJt
YpGHUWPcZR9TSDIMKU
<<type>>
<>f__AnonymousType0`2
<>f__AnonymousType0`2
<>f__AnonymousType0`2
<>f__AnonymousType0`2
<<type>>
<>f__AnonymousType1`2
<>f__AnonymousType1`2
<>f__AnonymousType1`2
<>f__AnonymousType1`2
<<type>>
<>f__AnonymousType2`2
<>f__AnonymousType2`2
<>f__AnonymousType2`2
<>f__AnonymousType2`2
<<type>>
<>f__AnonymousType3`2
<>f__AnonymousType3`2
<>f__AnonymousType3`2
<>f__AnonymousType3`2
<<type>>
<>f__AnonymousType4`3
<>f__AnonymousType4`3
<>f__AnonymousType4`3
<>f__AnonymousType4`3
<<type>>
<>f__AnonymousType5`2
<>f__AnonymousType5`2
<>f__AnonymousType5`2
<>f__AnonymousType5`2
<<type>>
UeQGZjwRymuEF6kgsG.rTWQmh0lHIxON1kYCX
Derhim.DerhimApi
rTWQmh0lHIxON1kYCX
DerhimApi
nMADSVKGC
get_userID
sU11KuxW4
get_password
BRv6IDcy6
get_userName
liLXwpLDo
MD5
ATJMLBxa1
GetServiceType
h2ejGTpd2
GetErrorCodeMsg
H0RuICEA6
OnResponse
GIiq3bYrj
BuildHeader
YyCWDOfNh
BuildBody
eqK44VCQi
GetQueryResponse
F0kHdcrq8
serviceClient
v7KB2GahQ
_disposed
p2rsnJVKi
userID
vmOfMjucd
userName
<<type>>
HashidsNet.Hashids
HashidsNet.Hashids
Hashids
Hashids
Jeft3D8IC
SetupSeps
DZlxCrGm2
SetupGuards
auK5UWKO9
Hash
MqWrOymUN
Unhash
zOdGOBigm
ConsistentShuffle
N8ogGhDRy
hexValidator
cMxVr0Vng
hexSplitter
woQejeM3O
alphabet
MA1nOXtgd
guards
cgc8dZ8Bi
guardsRegex
lwio8ieGS
minHashLength
u9TTLOq2A
salt
gJCPcpqli
seps
rDnRkBnKZ
sepsRegex
<<type>>
AppTech.MSMS.Domain.AppTechInfo
AppTech.MSMS.Domain.AppTechInfo
AppTechInfo
AppTechInfo
jd0AE5nKB
<Name>k__BackingField
oJcbNrWgl
<Contact>k__BackingField
C0HIh1l2u
<Website>k__BackingField
SODNZjvWu
<Email>k__BackingField
aSvkn3hOI
<DeveloperName>k__BackingField
j9R71mEs4
<ContactNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.DevSetting
AppTech.MSMS.Domain.DevSetting
DevSetting
DevSetting
YsYc5oc3O
<Debug>k__BackingField
<<type>>
AppTech.MSMS.Domain.AppContext
AppTech.MSMS.Domain.AppContext
AppContext
AppContext
<<type>>
AppTech.MSMS.Domain.DomainManager
AppTech.MSMS.Domain.DomainManager
DomainManager
DomainManager
b43i0fbJk
InitSecurity
ucGdLctG1
InitLicensedServices
mvowY11wF
CheckDb
xHDy2Knpt
IntiModules
Ervh1Dwnx
CreateImageDirectories
tCvFYZdFL
<AdminstrationBranch>k__BackingField
GhAlrDc08
<LicensedModules>k__BackingField
<<type>>
AppTech.MSMS.Domain.Res
AppTech.MSMS.Domain.Res
Res
Res
JW1YJtbM4
<LoginAttemptTable>k__BackingField
DKiJlKmS3
<UserLogin>k__BackingField
<<type>>
AppTech.MSMS.Domain.FrancyExtensions
AppTech.MSMS.Domain.FrancyExtensions
FrancyExtensions
FrancyExtensions
<<type>>
AppTech.MSMS.Domain.FrancyGateway
AppTech.MSMS.Domain.FrancyGateway
FrancyGateway
FrancyGateway
u9XKmeyO1
ExecuteGomalaAsync
aB79MpDge
ExecuteTopupAsync
ecROGcllt
ExecuteBagatAsync
k1OUw1h9c
ExecuteAdenNetAsync
YOxE3DrHb
ExecuteRiyalBagat
nKgSLdfAI
ExecuteRiyalTopup
A3wpQwvr0
<>n__0
lrBL9h9kI
_disposed
<<type>>
AppTech.MSMS.Domain.ClientLicense
AppTech.MSMS.Domain.ClientLicense
ClientLicense
ClientLicense
B2Ya42WB3
<Customer>k__BackingField
<<type>>
AppTech.MSMS.Domain.Customer
AppTech.MSMS.Domain.Customer
Customer
Customer
pyb0N2olh
<CompanyInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.CompanyInfo
AppTech.MSMS.Domain.CompanyInfo
CompanyInfo
CompanyInfo
GQeQuN3Pp
<Name>k__BackingField
jut3wrxe2
<Description>k__BackingField
uuKvrGU3u
<Address>k__BackingField
su8ZuMuYK
<Email>k__BackingField
IH7mh9EFx
<Website>k__BackingField
EFKCU0wrG
<Contacts>k__BackingField
JUyzkADDb
<ExtraContacts>k__BackingField
PXZX2gwnoX
<More>k__BackingField
<<type>>
AppTech.MSMS.Domain.MsError
AppTech.MSMS.Domain.MsError
MsError
MsError
<<type>>
AppTech.MSMS.Domain.MsLogger
AppTech.MSMS.Domain.MsLogger
MsLogger
MsLogger
wbjXXxJjLD
LockObj
<<type>>
AppTech.MSMS.Domain.ServiceFactory
AppTech.MSMS.Domain.ServiceFactory
ServiceFactory
ServiceFactory
<<type>>
xKMQlaTSDQcoE6k1iU.RsY7D4su80hYuogeWW
AppTech.MSMS.Domain.TestClass
RsY7D4su80hYuogeWW
TestClass
gwNXMjg532
ReceiveSmsMessage
yHcXjX431d
SendSms
AeCXuJD78v
GetTransactonType
<<type>>
AppTech.MSMS.Domain.TestHelper
AppTech.MSMS.Domain.TestHelper
TestHelper
TestHelper
<<type>>
K7QQ4ZeW0HGtBMU7Ik.Nt4f2L6cbmXZRjpflb
AppTech.MSMS.Domain.RemittanceHelper
Nt4f2L6cbmXZRjpflb
RemittanceHelper
ph9Xqw5uxp
IsNameLessThanThree
<<type>>
AppTech.MSMS.Domain.MsmsModule
AppTech.MSMS.Domain.MsmsModule
MsmsModule
MsmsModule
gI9XWB4vRJ
<ID>k__BackingField
yTRX4JUuIc
<Name>k__BackingField
JZLXDH4ShQ
<Title>k__BackingField
NkIXsdLUyn
<Icon>k__BackingField
<<type>>
AppTech.MSMS.Domain.MsSetting
AppTech.MSMS.Domain.MsSetting
MsSetting
MsSetting
fktX1O7XFE
<AllowOnlyPermittedUsers>k__BackingField
KkCX6ieLur
<AllowLoginAttemps>k__BackingField
ammXfAakGq
<AutoPermitDeviceOnActivateAccount>k__BackingField
GbxXHhYc61
<RegisterationFullNamedModitory>k__BackingField
VVyXB5hJro
<RegisterationCardModitory>k__BackingField
FOQXtDGl4q
<SessionTimeout>k__BackingField
pRfXxCTVW2
<ShowExpiredBalances>k__BackingField
IMVX5AZgTY
<AddSeperatorsToAmount>k__BackingField
IOnXrSi8fG
<AddTwoDecimalToAmount>k__BackingField
dA9XGI6YtM
<AlertOnNewOrders>k__BackingField
lMcXgZHyLR
<OrdersNotifyInterval>k__BackingField
TYOXVbDqfP
<DisableOrders>k__BackingField
yYcXeZotPV
<AccountFullNamedModitory>k__BackingField
j5DXnn5OB3
<Points_GrantParentPermissionsToPoint>k__BackingField
aRMX86Hrd4
<Points_GrantParentQuotationToPoint>k__BackingField
MZVXo4FlyL
<Points_GrantParentProviderPoints>k__BackingField
OqXXTmasYw
<VoucherRepeatTime>k__BackingField
RkeXPxqhgU
<CashTransferSuspended>k__BackingField
UPCXRYtw7a
<Agency_CashTransferSuspendedExceptBetweenTree>k__BackingField
bkZXAskaUc
<Agency_CashTransferSuspendedExceptFromParents>k__BackingField
dyWXbytD7E
<CashTransfer_CommissionMandotry>k__BackingField
pk6XI17YZQ
<DisableExpressRemittance>k__BackingField
DaVXNHVdJW
<Remittance_SendByServicProvider>k__BackingField
LYsXk8Vexh
<Remittance_ReceiveByServiceProvider>k__BackingField
T5rX7osxA3
<Transfers_ReceiverCardMandatory>k__BackingField
MWmXcDe7pX
<DirectCurrencyExchange>k__BackingField
QOEXi6QKAx
<DisableCurrencyExchange>k__BackingField
DQ2XdQ6SjB
<DisableCurrencyExchangeOnNoUpdatedDate>k__BackingField
YRGXw6nQL3
<CE_CheckBalViaAdmin>k__BackingField
<<type>>
AppTech.MSMS.Domain.MsUnitOfWork
AppTech.MSMS.Domain.MsUnitOfWork
MsUnitOfWork
MsUnitOfWork
<<type>>
AppTech.MSMS.Domain.FundRepo`1
AppTech.MSMS.Domain.FundRepo`1
FundRepo`1
FundRepo`1
fSHXy00oGf
business
<<type>>
AppTech.MSMS.Domain.Repo`2
AppTech.MSMS.Domain.Repo`2
Repo`2
Repo`2
C9oXhcon9K
business
<<type>>
AppTech.MSMS.Domain.Clients
AppTech.MSMS.Domain.Clients
Clients
Clients
<<type>>
AppTech.MSMS.Domain.test
AppTech.MSMS.Domain.test
test
test
SBoXFFmT2u
x
<<type>>
AppTech.MSMS.Domain.Context
AppTech.MSMS.Domain.Context
Context
Context
<<type>>
AppTech.MSMS.Domain.CuredBy
AppTech.MSMS.Domain.CuredBy
CuredBy
CuredBy
<<type>>
AppTech.MSMS.Domain.TopupClass
AppTech.MSMS.Domain.TopupClass
TopupClass
TopupClass
<<type>>
AppTech.MSMS.Domain.TopupCurer
AppTech.MSMS.Domain.TopupCurer
TopupCurer
TopupCurer
<<type>>
AppTech.MSMS.Domain.TopupType
AppTech.MSMS.Domain.TopupType
TopupType
TopupType
<<type>>
AppTech.MSMS.Domain.TopupServiceType
AppTech.MSMS.Domain.TopupServiceType
TopupServiceType
TopupServiceType
<<type>>
AppTech.MSMS.Domain.TopupMethod
AppTech.MSMS.Domain.TopupMethod
TopupMethod
TopupMethod
<<type>>
AppTech.MSMS.Domain.LineType
AppTech.MSMS.Domain.LineType
LineType
LineType
<<type>>
AppTech.MSMS.Domain.ClientStatus
AppTech.MSMS.Domain.ClientStatus
ClientStatus
ClientStatus
<<type>>
AppTech.MSMS.Domain.ProductStatus
AppTech.MSMS.Domain.ProductStatus
ProductStatus
ProductStatus
<<type>>
AppTech.MSMS.Domain.ProductType
AppTech.MSMS.Domain.ProductType
ProductType
ProductType
<<type>>
AppTech.MSMS.Domain.TransferType
AppTech.MSMS.Domain.TransferType
TransferType
TransferType
<<type>>
AppTech.MSMS.Domain.InvoiceType
AppTech.MSMS.Domain.InvoiceType
InvoiceType
InvoiceType
<<type>>
AppTech.MSMS.Domain.PaidState
AppTech.MSMS.Domain.PaidState
PaidState
PaidState
<<type>>
AppTech.MSMS.Domain.ProviderType
AppTech.MSMS.Domain.ProviderType
ProviderType
ProviderType
<<type>>
AppTech.MSMS.Domain.GroupType
AppTech.MSMS.Domain.GroupType
GroupType
GroupType
<<type>>
AppTech.MSMS.Domain.AccountState
AppTech.MSMS.Domain.AccountState
AccountState
AccountState
<<type>>
AppTech.MSMS.Domain.ItemState
AppTech.MSMS.Domain.ItemState
ItemState
ItemState
<<type>>
AppTech.MSMS.Domain.BagatType
AppTech.MSMS.Domain.BagatType
BagatType
BagatType
<<type>>
AppTech.MSMS.Domain.ClientType
AppTech.MSMS.Domain.ClientType
ClientType
ClientType
<<type>>
AppTech.MSMS.Domain.CommissionType
AppTech.MSMS.Domain.CommissionType
CommissionType
CommissionType
<<type>>
AppTech.MSMS.Domain.SimStatus
AppTech.MSMS.Domain.SimStatus
SimStatus
SimStatus
<<type>>
AppTech.MSMS.Domain.LoanStatus
AppTech.MSMS.Domain.LoanStatus
LoanStatus
LoanStatus
<<type>>
AppTech.MSMS.Domain.BillStatus
AppTech.MSMS.Domain.BillStatus
BillStatus
BillStatus
<<type>>
AppTech.MSMS.Domain.TopupStatus
AppTech.MSMS.Domain.TopupStatus
TopupStatus
TopupStatus
<<type>>
AppTech.MSMS.Domain.ExchangeType
AppTech.MSMS.Domain.ExchangeType
ExchangeType
ExchangeType
<<type>>
AppTech.MSMS.Domain.OfferType
AppTech.MSMS.Domain.OfferType
OfferType
OfferType
<<type>>
AppTech.MSMS.Domain.EntryStatus
AppTech.MSMS.Domain.EntryStatus
EntryStatus
EntryStatus
<<type>>
AppTech.MSMS.Domain.PaymentStatus
AppTech.MSMS.Domain.PaymentStatus
PaymentStatus
PaymentStatus
<<type>>
AppTech.MSMS.Domain.PaymentMode
AppTech.MSMS.Domain.PaymentMode
PaymentMode
PaymentMode
<<type>>
AppTech.MSMS.Domain.OrderStatus
AppTech.MSMS.Domain.OrderStatus
OrderStatus
OrderStatus
<<type>>
AppTech.MSMS.Domain.PartyStatus
AppTech.MSMS.Domain.PartyStatus
PartyStatus
PartyStatus
<<type>>
AppTech.MSMS.Domain.UserStatus
AppTech.MSMS.Domain.UserStatus
UserStatus
UserStatus
<<type>>
AppTech.MSMS.Domain.RemittancePointType
AppTech.MSMS.Domain.RemittancePointType
RemittancePointType
RemittancePointType
<<type>>
AppTech.MSMS.Domain.RemittanceType
AppTech.MSMS.Domain.RemittanceType
RemittanceType
RemittanceType
<<type>>
AppTech.MSMS.Domain.RemittanceStatus
AppTech.MSMS.Domain.RemittanceStatus
RemittanceStatus
RemittanceStatus
<<type>>
AppTech.MSMS.Domain.UserType
AppTech.MSMS.Domain.UserType
UserType
UserType
<<type>>
AppTech.MSMS.Domain.MasterService
AppTech.MSMS.Domain.MasterService
MasterService
MasterService
<<type>>
AppTech.MSMS.Domain.PartyPortalType
AppTech.MSMS.Domain.PartyPortalType
PartyPortalType
PartyPortalType
<<type>>
AppTech.MSMS.Domain.CustomerBusinessType
AppTech.MSMS.Domain.CustomerBusinessType
CustomerBusinessType
CustomerBusinessType
<<type>>
AppTech.MSMS.Domain.YmLoanStatus
AppTech.MSMS.Domain.YmLoanStatus
YmLoanStatus
YmLoanStatus
<<type>>
AppTech.MSMS.Domain.SimType
AppTech.MSMS.Domain.SimType
SimType
SimType
<<type>>
AppTech.MSMS.Domain.BalanceState
AppTech.MSMS.Domain.BalanceState
BalanceState
BalanceState
<<type>>
AppTech.MSMS.Domain.SatellitePayStatus
AppTech.MSMS.Domain.SatellitePayStatus
SatellitePayStatus
SatellitePayStatus
<<type>>
AppTech.MSMS.Domain.Path
AppTech.MSMS.Domain.Path
Path
Path
<<type>>
AppTech.MSMS.Domain.Helper
AppTech.MSMS.Domain.Helper
Helper
Helper
o20Xl7gYd3
GetChannel
<<type>>
AppTech.MSMS.Domain.XmlProvider
AppTech.MSMS.Domain.XmlProvider
XmlProvider
XmlProvider
<<type>>
AppTech.MSMS.Domain.Updates.DbScriptManager
AppTech.MSMS.Domain.Updates.DbScriptManager
DbScriptManager
DbScriptManager
WEkXYpemN1
SaveScriptVersion
f2wXJTB5to
Execute
UL3XKpAxSk
DbScriptTable
<<type>>
AppTech.MSMS.Domain.Updates.DbScriptManager2017
AppTech.MSMS.Domain.Updates.DbScriptManager2017
DbScriptManager2017
DbScriptManager2017
LqWX9WaClL
SaveScriptVersion
U3RXOkTIFD
Execute
xAaXUmMFdF
DbScriptTable
<<type>>
AppTech.MSMS.Domain.Updates.DbUpdater
AppTech.MSMS.Domain.Updates.DbUpdater
DbUpdater
DbUpdater
mPuXEGuf2M
updateClientDistrub
jp7XSHoqM9
updateClientExtra
Au7XpELA4n
AddPages
OHqXLClOfV
WifiPaymentUpdates
tIDXakW7Jj
TopupPermissions
yQ5X0IyHcF
OrderNewPermissions
RAWXQWUwvj
UpdateScripts
KHyX3ZtFUl
CreateVersionTable
NGEXvKBIM1
CreateLoginAttempts
OqVXZUKhnv
IsSmoExist
OfVXmdxcFf
ServiceCliam_SetAccountID_AsNullable
B6uXCICDIo
ServiceCliamNewFields
r4yXzNk4ic
TopupCommission_Add_Field
agPM2cxu0a
AddReceiveAnyTransfer
En2MXJl0NC
AddCheckLoanService
eblMMOpsKM
RemoveTopup_lineType_Check
BFTMjUYa7u
SimInvoiceUpdates_v2
DR8Mun5N6k
SimUpdates
wUoMqHXm5w
JournalUpdates
LMLMWePB04
SimInvoiceUpdates
ktMM4y4lKt
ProviderTableUpdates
UDsMDey2lq
Execute
PlQMsiaBNv
Execute
Kp0M1hWYB0
Exist
QWSM6TrWRb
ExecuteConstraint
oTYMf5sB84
ExecuteIndex
X54MHGli65
DbScriptTable
<<type>>
AppTech.MSMS.Domain.Topup.TopupFactory
AppTech.MSMS.Domain.Topup.TopupFactory
TopupFactory
TopupFactory
<<type>>
AppTech.MSMS.Domain.JournalEntries.JournalEntryModel
AppTech.MSMS.Domain.JournalEntries.JournalEntryModel
JournalEntryModel
JournalEntryModel
xi6MBtqTVX
<ID>k__BackingField
dZUMtpdPlM
<UserID>k__BackingField
UZhMxC0VGj
<RecordID>k__BackingField
MQ6M5RFjLH
<RecordNumber>k__BackingField
PHWMroJQ6N
<Date>k__BackingField
LuvMGp3dBW
<Note>k__BackingField
yMaMgxAuMK
<IsDebited>k__BackingField
hskMVBTpri
<Entries>k__BackingField
YRYMeV34Bw
<RefNumber>k__BackingField
AWnMndHxCm
<ReqType>k__BackingField
<<type>>
AppTech.MSMS.Domain.JournalEntries.Entry
AppTech.MSMS.Domain.JournalEntries.Entry
Entry
Entry
bvBM8YHkQG
<Amount>k__BackingField
A53Mo2yOII
<CurrencyID>k__BackingField
sHUMTQ10uX
<CreditorAccountID>k__BackingField
gr8MPd8vKh
<DebitorAccountID>k__BackingField
EjIMRv1PH9
<DebitorNote>k__BackingField
PA3MATyW7g
<CreditorNote>k__BackingField
<<type>>
AppTech.MSMS.Domain.JournalEntries.JournalEntryManager
AppTech.MSMS.Domain.JournalEntries.JournalEntryManager
JournalEntryManager
JournalEntryManager
gZqMbre8r2
GetVoucherId
abWMINl7rk
AddEntries
W0bMNaiXSX
EditJournalEntry
vy5Mk7um6l
_session
k1nM7ufxF5
_voucherId
<<type>>
AppTech.MSMS.Domain.Providers.AlAssedApi
AppTech.MSMS.Domain.Providers.AlAssedApi
AlAssedApi
AlAssedApi
nmiMc3OG2l
GenerateToken
YGuMi0IwTt
BuildGomlaParam
Vg3MdwCcbv
OnPaymentResponse
j6QMwBOgeG
BalanceQuery
MlaMyTfOWU
QueryYmOffers
BjeMhTQwbW
BuildQueryPara
vmIMFPnSP6
GetQueryUrl
fhUMlXpged
OnBalanceQueryResponse
eaKMYyJnDZ
OnOfferQueryRespone
RryMJxQng3
ParseAdslBalance
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi
AppTech.MSMS.Domain.Providers.AlbayanApi
AlbayanApi
AlbayanApi
mMIMK5QAAx
OnResponse
fWEM9LPg4x
serviceClient
mIdMOBNsx7
_disposed
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi_v2
AppTech.MSMS.Domain.Providers.AlbayanApi_v2
AlbayanApi_v2
AlbayanApi_v2
o5nMUhgGqQ
OnResponse
wbHMEGiest
serviceClient
eCQMSOZe22
_disposed
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi
AppTech.MSMS.Domain.Providers.AlhashediApi
AlhashediApi
AlhashediApi
eFGMpRsiak
BuildGomlaParam
HPgMLxc5Na
BuildPaymentParam
fywMaglZJS
OnPaymentResponse
fy2M0uqdpi
BalanceQuery
OPsMQ0wFJ4
BuildQueryPara
yCMM3NOTKX
GetQueryUrl
CNiMvN9c3d
OnBalanceQueryResponse
iqZMZ8K5MD
ParseAdslBalance
A5UMmV44q7
BuildBagatParam
DCdMCH1tl8
GetBagatUrl
g9yMz6qSLA
OnBagatResponse
v2hj27EAs4
OnYmBagatResponse
I5HjX2SPQm
QueryYmOffers
jFnjMtpeuw
OnOfferQueryRespone
B4MjjPVeHd
GenerateToken
rIajuK9Dly
ParseErrorMessage
<<type>>
AppTech.MSMS.Domain.Providers.EasyConnectAPI
AppTech.MSMS.Domain.Providers.EasyConnectAPI
EasyConnectAPI
EasyConnectAPI
KikjqHWa4x
OnPaymentResponse
iZkjWZ501C
OnCheckResponse
xIsj4laB2J
Timeout
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM_YRM
AppTech.MSMS.Domain.Providers.EasySIM_YRM
EasySIM_YRM
EasySIM_YRM
mUNjDf5sBM
OnPaymentResponse
FKWjshkM9M
OnCheckResponse
HbYj1HwpZI
Timeout
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi
AppTech.MSMS.Domain.Providers.GMaxApi
GMaxApi
GMaxApi
tmbj6pqTow
OnPaymentResponse
BikjfuPmqV
OnQueryResponse
PXYjHXAdbv
OnBalanceQueryResponse
sQjjBnH6qF
OnOfferQueryRespone
CYVjtW38Ip
ParseAdslBalance
M68jxWiOJt
OnBagatResponse
asVj5feoM1
Timeout
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM
AppTech.MSMS.Domain.Providers.EasySIM
EasySIM
EasySIM
aBHjrECgsl
OnPaymentResponse
OrUjGFZ1ms
OnCheckResponse
b9NjgB3XWS
Timeout
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi
AppTech.MSMS.Domain.Providers.AbuOsamaApi
AbuOsamaApi
AbuOsamaApi
ElVjkxfskD
get_mAccessToken
H1bjVeuj3Q
BuildPaymentParam
OunjeWOpLW
OnPaymentResponse
JZ6jnJQRvi
BalanceQuery
hILj8svZvL
OnBalanceQueryResponse
iqnjoGR33e
ParseAdslBalance
o8sjT5pIKO
QueryYmOffers
MdWjPAn23r
OnOfferQueryRespone
t1SjR9Kt4v
OnBagatResponse
A2tjAEbOyQ
BuildBagatPara
NTijb63S0t
ParseErrorMessage
UjejIUSrKh
GenerateToken
cSgjNAgmQq
BalanceQueryAsync
HCvj77EuZ2
mAccessToken
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi
AppTech.MSMS.Domain.Providers.AlConApi
AlConApi
AlConApi
hlbjcJo3hC
OnTopupRespone
nm1jibLL2D
OnBalanceRespons
aTSjdkVnAI
MakeMtnOffer
CTejwPqXOL
parseErrorResponse
R33jywcicw
QueryYmOffers
FG8jhiTdUR
YMOffer
nYMjFCPPDb
Balance
mBmjlef0no
isApi
<<type>>
AppTech.MSMS.Domain.Providers.RialMobileAPI
AppTech.MSMS.Domain.Providers.RialMobileAPI
RialMobileAPI
RialMobileAPI
gtPjYE6Wkp
OnResponse
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi
AppTech.MSMS.Domain.Providers.SharabiApi
SharabiApi
SharabiApi
YN6j3B7IFY
get_mAccessToken
mHCjJP0mFl
BuildPaymentParam
T6hjK12iYE
OnPaymentResponse
mToj90yZDF
BalanceQuery
M6djOesQwl
OnBalanceQueryResponse
rvijUTxe32
ParseAdslBalance
p5njEGk8ZO
QueryYmOffers
dQ3jSR1JSu
OnOfferQueryRespone
CwjjpIi20U
OnBagatResponse
raqjLxUIVN
BuildBagatPara
Ct5jasQhSb
ParseErrorMessage
Ycij0NgnRh
GenerateToken
Cb4jQNAuyb
BalanceQueryAsync
BByjvNKfbm
mAccessToken
<<type>>
AppTech.MSMS.Domain.Providers.TopupProviderApi
AppTech.MSMS.Domain.Providers.TopupProviderApi
TopupProviderApi
TopupProviderApi
kgdjZ3pNdp
<TransactionID>k__BackingField
zHrjm6kn1l
<BaseUrl>k__BackingField
rvbjCn0bO1
<ProviderTransId>k__BackingField
W8gjzG7Yg1
<MyTransId>k__BackingField
QNeu2O0s5Z
<RecordNumber>k__BackingField
AAnuXArMno
_disposed
<<type>>
AppTech.MSMS.Domain.Providers.TopupProccessor
AppTech.MSMS.Domain.Providers.TopupProccessor
TopupProccessor
TopupProccessor
F2ouMkxRIe
TestQuery
sRFujEKG04
OnResponse
Qqiuu90u3w
ParseError
frNuqMpATR
_mTopupProviderApi
U1IuWju5FY
_disposed
<<type>>
c0MjUnF9jyXPDrlDNy.ok9FISIqLj0HEoKoEK
AppTech.MSMS.Domain.Topuping.AlanwarAPI
ok9FISIqLj0HEoKoEK
AlanwarAPI
KsPussAKYu
get_UserName
yxSu1OpS4v
get_Password
sWnufGGScA
get_token
qnouBog1mE
get_baseUrl
ykgu4jTCRx
OnPaymentResponse
eZpuDfrUJ2
OnQueryResponse
D5Mu6hEYcq
Password
ESHuHHNjFn
token
nudutopQqv
baseUrl
<<type>>
AppTech.MSMS.Domain.Topuping.AlGumaiiApi
AppTech.MSMS.Domain.Topuping.AlGumaiiApi
AlGumaiiApi
AlGumaiiApi
RYbux72eph
BuildPaymentParam
rf7u5DomDF
OnPaymentResponse
TBqur2wFB1
BuildBalanceQuery
nVluGpcQ0D
BalanceQuery
Ylvug277bK
OnBalanceQueryResponse
CW5uVL6GB5
ParseAdslBalance
mfuue9Eyp4
MakeSPBagat
zIkun0srEe
MakeMtnOffer
wC6u8h15kl
OnBagatResponse
IuSuoPx5xU
QueryYmOffers
jpIuTkTCsx
OnOfferQueryRespone
OeduPEhdsT
BalanceQueryAsync
kHHuRSVAWS
GenerateToken
oNUuAyru4o
ParseErrorMessage
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetAPI
AppTech.MSMS.Domain.Topuping.AlmoheetAPI
AlmoheetAPI
AlmoheetAPI
FR1ubV9Rcy
OnQueryResponse
eZDuIVFbfV
OnPaymentResponse
ByXuNth7iG
GetTransId
h3oukx8KCu
OnReportResponse
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetReportResponse
AppTech.MSMS.Domain.Topuping.AlmoheetReportResponse
AlmoheetReportResponse
AlmoheetReportResponse
zOLu7jDell
<SERVICE_NAME>k__BackingField
gWBucE9bsZ
<TRANS_DATE>k__BackingField
kOmuiyqXcr
<MOBILE>k__BackingField
V8nudqDhJw
<AMOUNT>k__BackingField
EnkuwOIVWi
<NOTE>k__BackingField
PCYuyWDW7F
<STATUS>k__BackingField
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetReportRequest
AppTech.MSMS.Domain.Topuping.AlmoheetReportRequest
AlmoheetReportRequest
AlmoheetReportRequest
moyuhgFEOs
<serviceId>k__BackingField
gDhuFJ4KNq
<MOBILE>k__BackingField
dhfulfmTbC
<FROM_DATE>k__BackingField
qVbuYqwr4A
<TO_DATE>k__BackingField
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetTopupRequest
AppTech.MSMS.Domain.Topuping.AlmoheetTopupRequest
AlmoheetTopupRequest
AlmoheetTopupRequest
OJAuJV1Goa
<MOBILE>k__BackingField
VhFuKQdXNU
<TRANS_TYPE>k__BackingField
zlyu9GyZN7
<Amount>k__BackingField
e6kuOT5BLj
<RequestType>k__BackingField
gvEuUdb8R3
<transId>k__BackingField
QcRuEyCkCk
<ProductId>k__BackingField
stNuSGPGUL
<UserId>k__BackingField
RCnupBvp6C
<passowrd>k__BackingField
QhZuL0vSGU
<FROM_DATE>k__BackingField
xkvuaD5ESx
<TO_DATE>k__BackingField
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi
AppTech.MSMS.Domain.Topuping.AlMutarrebApi
AlMutarrebApi
AlMutarrebApi
Wu7u03QFws
OnPaymentReponse
eF5uQjl1DH
OnOfferReponse
hCnu3TxjcV
serviceClient
SgVuvUP52V
action
yQPuZEXZrL
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebServices
AppTech.MSMS.Domain.Topuping.AlmutarrebServices
AlmutarrebServices
AlmutarrebServices
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2
AlmutarrebAPI_v2
AlmutarrebAPI_v2
Q3Vum8ItIy
OnPaymentReponse
puSuCIAY2x
OnOfferReponse
aLiuzEITLU
serviceClient
yRGq2H5fZm
action
uauqXna031
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebV2Services
AppTech.MSMS.Domain.Topuping.AlmutarrebV2Services
AlmutarrebV2Services
AlmutarrebV2Services
<<type>>
AppTech.MSMS.Domain.Topuping.AlSareeaOnLineApi
AppTech.MSMS.Domain.Topuping.AlSareeaOnLineApi
AlSareeaOnLineApi
AlSareeaOnLineApi
hg7qMhOGrM
OnResponse
pHfqjmunjt
serviceClient
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi_v2
AppTech.MSMS.Domain.Topuping.AppTechApi_v2
AppTechApi_v2
AppTechApi_v2
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi_v3
AppTech.MSMS.Domain.Topuping.AppTechApi_v3
AppTechApi_v3
AppTechApi_v3
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi
AppTech.MSMS.Domain.Topuping.AtheerApi
AtheerApi
AtheerApi
hv6quXtHb1
serviceClient
sTRqqvti17
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_Riyal
AppTech.MSMS.Domain.Topuping.AtheerApi_Riyal
AtheerApi_Riyal
AtheerApi_Riyal
IDBqWW0fcG
serviceClient
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South
AppTech.MSMS.Domain.Topuping.AtheerApi_South
AtheerApi_South
AtheerApi_South
O5jq4GlmLJ
OnResponse
u5CqD6iEWC
serviceClient
eJOqsEpETR
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2
AtheerApi_South_v2
AtheerApi_South_v2
tPGq1Grxy3
OnResponse
Vk2q6QGQ1s
serviceClient
Rb0qfmaety
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v2
AppTech.MSMS.Domain.Topuping.AtheerApi_v2
AtheerApi_v2
AtheerApi_v2
BtWqHFeNGK
OnResponse
ir5qBxRLfe
serviceClient
mfaqtQ5LK7
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3
AppTech.MSMS.Domain.Topuping.AtheerApi_v3
AtheerApi_v3
AtheerApi_v3
p6CqxVjJGp
OnResponse
R37q5Ij2ow
serviceClient
KuWqr7KE9D
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi
AppTech.MSMS.Domain.Topuping.BaAmerApi
BaAmerApi
BaAmerApi
cfsqGpH43N
OnPaymentReponse
QEUqgIo2bb
OnOfferReponse
LfoqVAa0ng
serviceClient
ykJqe2Uihk
action
ybLqnA3tCs
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerServices
AppTech.MSMS.Domain.Topuping.BaAmerServices
BaAmerServices
BaAmerServices
<<type>>
AppTech.MSMS.Domain.Topuping.FrenchiApi
AppTech.MSMS.Domain.Topuping.FrenchiApi
FrenchiApi
FrenchiApi
<<type>>
AppTech.MSMS.Domain.Topuping.ForMeAPI
AppTech.MSMS.Domain.Topuping.ForMeAPI
ForMeAPI
ForMeAPI
afaq89bToK
OnResponse
h0rqoNb6HH
serviceClient
BiuqTakkPr
_disposed
<<type>>
syYWI9hEvY44C7NHCm.y2FZ7yxw4qJcqxB11H
AppTech.MSMS.Domain.Topuping.JuzaifaAPI
y2FZ7yxw4qJcqxB11H
JuzaifaAPI
FyJqbDLB4J
get_UserID
kYmqNbf7CS
get_password
dHnqk2QG81
get_vClientID
OUvqPV3rfT
BuildHeader
XcUqRLY8QR
BuildBody
EFtqAXR2Eu
GetErrorCodeMsg
cS7qcl71Tw
serviceClient
TuwqIDHV4i
UserID
cpSq7d8jMV
vClientID
<<type>>
AppTech.MSMS.Domain.Topuping.MainCenterApi
AppTech.MSMS.Domain.Topuping.MainCenterApi
MainCenterApi
MainCenterApi
TIvqidKCWK
GetPath
kLIqdcpdOV
OpResponse
waXqws2Gn2
GetBagatNumber
AjaqyO9903
GenerateToken
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi
AppTech.MSMS.Domain.Topuping.AppTechApi
AppTechApi
AppTechApi
gLMqhWV6Z0
GenerateToken
<<type>>
AppTech.MSMS.Domain.Topuping.OnsSoftApi
AppTech.MSMS.Domain.Topuping.OnsSoftApi
OnsSoftApi
OnsSoftApi
<<type>>
AppTech.MSMS.Domain.Topuping.QulaidiApi
AppTech.MSMS.Domain.Topuping.QulaidiApi
QulaidiApi
QulaidiApi
A1IqF3TLwp
serviceClient
RHhql9sRJo
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.TadawulApi
AppTech.MSMS.Domain.Topuping.TadawulApi
TadawulApi
TadawulApi
tHaqYCFPZF
OnResponse
ylkqJUVJBd
_proxy
w09qKbcLEt
<Header>k__BackingField
snFq9qKSgv
_disposed
crFqOu3joR
action
<<type>>
AppTech.MSMS.Domain.Topuping.TadawulServiceType
AppTech.MSMS.Domain.Topuping.TadawulServiceType
TadawulServiceType
TadawulServiceType
<<type>>
AppTech.MSMS.Domain.Topuping.TopupException
AppTech.MSMS.Domain.Topuping.TopupException
TopupException
TopupException
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper
AppTech.MSMS.Domain.Topuping.TopupHelper
TopupHelper
TopupHelper
gC9qUCAUU2
QueryMtnInfo
HrWqEpTM8h
TranslateOffers
qPCqSXC1BP
GetExecutionPeriod
ilVqpcSK2a
GetExceptionStatus
aksqLZ6HsV
_session
<<type>>
AppTech.MSMS.Domain.Topuping.Providers
AppTech.MSMS.Domain.Topuping.Providers
Providers
Providers
<<type>>
AppTech.MSMS.Domain.Topuping.TelNetwork
AppTech.MSMS.Domain.Topuping.TelNetwork
TelNetwork
TelNetwork
<<type>>
AppTech.MSMS.Domain.Topuping.Services
AppTech.MSMS.Domain.Topuping.Services
Services
Services
<<type>>
AppTech.MSMS.Domain.Topuping.TopupUtils
AppTech.MSMS.Domain.Topuping.TopupUtils
TopupUtils
TopupUtils
<<type>>
AppTech.MSMS.Domain.Topuping.YemenPostAPI
AppTech.MSMS.Domain.Topuping.YemenPostAPI
YemenPostAPI
YemenPostAPI
tBYqaG4AMe
OnResponse
uNHq07b71a
OnOfferResponse
KL7qQh9sRH
InitiCertificate
DO2q30DarX
SaveProviderPublicKey
oTOqvGXyTR
serviceClient
AoeqZh9eEn
keySize
abqqmaLRqB
_optimalAsymmetricEncryptionPadding
yWoqCJ4AwS
publicKey
vVfqzQUgQh
requestInfo
RGTW21Ij1p
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.YemenSaeedApi
AppTech.MSMS.Domain.Topuping.YemenSaeedApi
YemenSaeedApi
YemenSaeedApi
<<type>>
AppTech.MSMS.Domain.Topuping.YmLoanResponse
AppTech.MSMS.Domain.Topuping.YmLoanResponse
YmLoanResponse
YmLoanResponse
IXXWXIXBLO
<Message>k__BackingField
vhfWMdh0kr
<Status>k__BackingField
<<type>>
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure
TopupClosure
TopupClosure
OegWjLrRHw
SaveBalance
SZFWue7ivr
Log
<<type>>
AppTech.MSMS.Domain.Sync.EbsAccountService
AppTech.MSMS.Domain.Sync.EbsAccountService
EbsAccountService
EbsAccountService
<<type>>
AppTech.MSMS.Domain.Sync.EbsJournalManager
AppTech.MSMS.Domain.Sync.EbsJournalManager
EbsJournalManager
EbsJournalManager
<<type>>
AppTech.MSMS.Domain.Sync.RemittanceSync
AppTech.MSMS.Domain.Sync.RemittanceSync
RemittanceSync
RemittanceSync
LfHWq7ldtF
GetReceivedTargetId
AkWWWXXDaj
Throw
H6mW4UgFO0
InitPaged
toGWDTqRAS
CreatePaged
jwNWs2L1kk
GetCurrencyName
ARlW159JxF
PendingTransfer
NHYW6Abd2a
AddEntryDetails
hJmWfonWE8
_dbHelper
<<type>>
AppTech.MSMS.Domain.Sync.SyncResult
AppTech.MSMS.Domain.Sync.SyncResult
SyncResult
SyncResult
znfWHh8uSp
<ID>k__BackingField
brFWBX2ihZ
<TargetID>k__BackingField
hyYWtbNiUd
<SourceID>k__BackingField
fAEWxuIWL2
<RecordNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Sync.SyncSmsDb
AppTech.MSMS.Domain.Sync.SyncSmsDb
SyncSmsDb
SyncSmsDb
<<type>>
AppTech.MSMS.Domain.Sync.SyncBalanceSheetReport
AppTech.MSMS.Domain.Sync.SyncBalanceSheetReport
SyncBalanceSheetReport
SyncBalanceSheetReport
HArW5MiDQT
GetGrossBalanceSheet
WuDWrkAwYw
GetDetailedBalanceSheet
sDSWGPEWn0
BuildDetialedQuery
D3AWg4u9SZ
BuildDetialedQueryAllCurrencies
hn8WVnTIGx
Query
YPtWe7BSn5
_db
mcjWnsC4S1
EntriesView
<<type>>
AppTech.MSMS.Domain.Sync.SyncDb
AppTech.MSMS.Domain.Sync.SyncDb
SyncDb
SyncDb
J2QW8paPHo
CreateAccount
KTqWoMfStp
GenerateAccountNumber
gLWWTTL8tP
DeleteAccount
<<type>>
AppTech.MSMS.Domain.Sync.SyncJournalManager
AppTech.MSMS.Domain.Sync.SyncJournalManager
SyncJournalManager
SyncJournalManager
wYAWPhD724
GetVoucher
TAAWR1l8SA
AddDebitedEntry
WOqWAhZUS7
AddCreditedEntry
yMaWb6Am8a
DeleteJournal
lTGWI6Dtln
IsEntryExist
ly6WNTnujT
_session
<<type>>
AppTech.MSMS.Domain.Sync.SyncSetting
AppTech.MSMS.Domain.Sync.SyncSetting
SyncSetting
SyncSetting
GBcWk1ZiVk
_dbHelper
h7sW7S3NkE
<TopupAccountID>k__BackingField
QKGWcBXsLL
<CurrencyExchageAccountID>k__BackingField
IpLWivn9SH
<CashDepositAccountID>k__BackingField
IBlWd8qOap
<CommissionAccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.BaseSetting`1
AppTech.MSMS.Domain.Settings.BaseSetting`1
BaseSetting`1
BaseSetting`1
QOpWwwTXaA
<SettingName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.CardSetting
AppTech.MSMS.Domain.Settings.CardSetting
CardSetting
CardSetting
eRFWyFBNBK
<SettingName>k__BackingField
zf8Wh2NGU6
<PrafitAccount>k__BackingField
ftmWFG2M0t
<DisableGames>k__BackingField
L7eWl7grt7
<DisableCards>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.CashTransSetting
AppTech.MSMS.Domain.Settings.CashTransSetting
CashTransSetting
CashTransSetting
R61WYCiwXt
<CashTransferSuspended>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.MainSetting`1
AppTech.MSMS.Domain.Settings.MainSetting`1
MainSetting`1
MainSetting`1
<<type>>
AppTech.MSMS.Domain.Settings.SimSetting
AppTech.MSMS.Domain.Settings.SimSetting
SimSetting
SimSetting
eINWJJ8F7H
<SettingName>k__BackingField
E97WKUrWla
<NewSimPrice>k__BackingField
nDBW9oiAqd
<ReplaceSimPrice>k__BackingField
YfhWOGVs2k
<RefundInReplaceSim>k__BackingField
bdiWUHcYpD
<OpenSimCardNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.SmsSetting
AppTech.MSMS.Domain.Settings.SmsSetting
SmsSetting
SmsSetting
<<type>>
AppTech.MSMS.Domain.Settings.TopupSetting
AppTech.MSMS.Domain.Settings.TopupSetting
TopupSetting
TopupSetting
SO0WE3He0f
<RestrictRepeatTime>k__BackingField
llcWSsbvY4
<AllowtRepeatAmount>k__BackingField
kmSWpRLE5R
<ExchangeAccountID>k__BackingField
l1sWLg2JEa
<SeparateYmQuotaBagat>k__BackingField
grCWaS2dZ2
<NotifyAfterTopup>k__BackingField
uEgW0KOncC
<TopupAsOrder>k__BackingField
yHdWQ5xyv7
<DisableTopup>k__BackingField
CRVW3GgoNe
<ElectriciyLimitPercent>k__BackingField
TqQWv2pOSt
<Topup_DisableGomala>k__BackingField
RXCWZvyAvR
<Topup_PreventGomalaForPoints>k__BackingField
J4PWmRG5rT
<Topup_PreventGomalaForClients>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.SatelliteSetting
AppTech.MSMS.Domain.Settings.SatelliteSetting
SatelliteSetting
SatelliteSetting
m1oWCi5PW0
<SettingName>k__BackingField
awGWzsS8m2
<PrafitAccount>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.WifiSetting
AppTech.MSMS.Domain.Settings.WifiSetting
WifiSetting
WifiSetting
ty542tst67
<SettingName>k__BackingField
ABQ4XQwymx
<PrafitAccount>k__BackingField
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService
AppTech.MSMS.Domain.Sessions.LoginService
LoginService
LoginService
iUf4MTfNV7
BlockDevice
g734joluGQ
IsDevicePermitted
PbZ4uSO4Oh
_credentials
MDW4qc4LVl
_disposed
nEq4WO3Udq
_unitOfWork
<<type>>
AppTech.MSMS.Domain.Sessions.AdminSession
AppTech.MSMS.Domain.Sessions.AdminSession
AdminSession
AdminSession
<<type>>
AppTech.MSMS.Domain.Sessions.UserSession
AppTech.MSMS.Domain.Sessions.UserSession
UserSession
UserSession
tTQ44GUbI9
OnSuccess
Dxk4DJCkZd
InitBranching
aQs4sHiQgK
<IncludeBalance>k__BackingField
MAE41ddOhc
<Type>k__BackingField
LNc46Noj31
<Party>k__BackingField
j7R4fZCfGa
<CurrentBalance>k__BackingField
qDk4HPCFu7
<IsPrimary>k__BackingField
HvM4B6fwXq
<IsPOR>k__BackingField
zpO4tN56vO
<RemittancePoint>k__BackingField
Q1C4xIre71
<Claims>k__BackingField
<<type>>
AppTech.MSMS.Domain.Sessions.AgentSession
AppTech.MSMS.Domain.Sessions.AgentSession
AgentSession
AgentSession
bci4rwX5RZ
set_Agent
k2545qPqJb
ValidateAgent
Mgk4GCWOaS
<Agent>k__BackingField
<<type>>
AppTech.MSMS.Domain.Sessions.MerchantSession
AppTech.MSMS.Domain.Sessions.MerchantSession
MerchantSession
MerchantSession
HZq4gSLF8W
set_Merchant
uv54VWtw1Q
<Merchant>k__BackingField
<<type>>
AppTech.MSMS.Domain.Rest.GenericApi
AppTech.MSMS.Domain.Rest.GenericApi
GenericApi
GenericApi
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest
AppTech.MSMS.Domain.Rest.ApiRequest
ApiRequest
ApiRequest
faW4e4ahMK
InitRestClient
xqr4nWTvl4
<TransactionID>k__BackingField
SOS48eaDjv
mHeaders
o2C4oQjvfr
_url
dRo4TMt9Gj
_disposed
<<type>>
AppTech.MSMS.Domain.Reports.AccountBalanceReport
AppTech.MSMS.Domain.Reports.AccountBalanceReport
AccountBalanceReport
AccountBalanceReport
<<type>>
AppTech.MSMS.Domain.Reports.AccountReport
AppTech.MSMS.Domain.Reports.AccountReport
AccountReport
AccountReport
<<type>>
AppTech.MSMS.Domain.Reports.DataList
AppTech.MSMS.Domain.Reports.DataList
DataList
DataList
SJK4PufdjO
<Name>k__BackingField
XZT4RrjCWp
<ID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.AgentsReport
AppTech.MSMS.Domain.Reports.AgentsReport
AgentsReport
AgentsReport
QXb4AidZOJ
GetDetails
SiC4buKiUw
GetGross
<<type>>
AppTech.MSMS.Domain.Reports.BalancesReport
AppTech.MSMS.Domain.Reports.BalancesReport
BalancesReport
BalancesReport
<<type>>
AppTech.MSMS.Domain.Reports.CardReport
AppTech.MSMS.Domain.Reports.CardReport
CardReport
CardReport
u3j4IcBT2U
GetDetails
xDx4NLiCNt
GetGross
Cay4k1AHP5
GetOrderDetails
rYo478PZZ7
GetOrderGross
<<type>>
AppTech.MSMS.Domain.Reports.CurrencyExchangeReport
AppTech.MSMS.Domain.Reports.CurrencyExchangeReport
CurrencyExchangeReport
CurrencyExchangeReport
fZp4cdAEw2
GetGross
AZZ4ieb7tP
GetDetails
<<type>>
AppTech.MSMS.Domain.Reports.FundReport
AppTech.MSMS.Domain.Reports.FundReport
FundReport
FundReport
Uej4d9fuGT
GetGrossBalanceSheet
<<type>>
AppTech.MSMS.Domain.Reports.IdleAccountsReport
AppTech.MSMS.Domain.Reports.IdleAccountsReport
IdleAccountsReport
IdleAccountsReport
<<type>>
AppTech.MSMS.Domain.Reports.InvoiceReport
AppTech.MSMS.Domain.Reports.InvoiceReport
InvoiceReport
InvoiceReport
dMi4wAtLQx
GetConsumeResult
<<type>>
AppTech.MSMS.Domain.Reports.PointsBalanceReport
AppTech.MSMS.Domain.Reports.PointsBalanceReport
PointsBalanceReport
PointsBalanceReport
<<type>>
AppTech.MSMS.Domain.Reports.SimReport
AppTech.MSMS.Domain.Reports.SimReport
SimReport
SimReport
kTs4yDl5Ul
GetDetails
GQ94hKc8rB
GetGross
<<type>>
AppTech.MSMS.Domain.Reports.TopupAccountsReport
AppTech.MSMS.Domain.Reports.TopupAccountsReport
TopupAccountsReport
TopupAccountsReport
Yv44FkvrY1
get_ReportView
O694lDqFph
ReportView
<<type>>
AppTech.MSMS.Domain.Reports.TopupClosureReport
AppTech.MSMS.Domain.Reports.TopupClosureReport
TopupClosureReport
TopupClosureReport
<<type>>
AppTech.MSMS.Domain.Reports.TopupOrderReport
AppTech.MSMS.Domain.Reports.TopupOrderReport
TopupOrderReport
TopupOrderReport
sVq4YIg2l2
GetDetails
HN84JCGW3p
GetGross
<<type>>
AppTech.MSMS.Domain.Reports.VoucherReport
AppTech.MSMS.Domain.Reports.VoucherReport
VoucherReport
VoucherReport
cEx4KYgxbY
GetGross
JNM492I8jw
GetDetails
<<type>>
AppTech.MSMS.Domain.Reports.WalletReport
AppTech.MSMS.Domain.Reports.WalletReport
WalletReport
WalletReport
<<type>>
AppTech.MSMS.Domain.Reports.WifiReport
AppTech.MSMS.Domain.Reports.WifiReport
WifiReport
WifiReport
U3P4OBNRxF
GetDetails
OyS4Uwf81D
GetGross
<<type>>
AppTech.MSMS.Domain.Reports.TransferReport
AppTech.MSMS.Domain.Reports.TransferReport
TransferReport
TransferReport
<<type>>
AppTech.MSMS.Domain.Reports.OrderReport
AppTech.MSMS.Domain.Reports.OrderReport
OrderReport
OrderReport
<<type>>
AppTech.MSMS.Domain.Reports.RemittanceReport
AppTech.MSMS.Domain.Reports.RemittanceReport
RemittanceReport
RemittanceReport
pL14E5Qp1U
GetGross
X5k4SXlpdM
GetDetails
<<type>>
AppTech.MSMS.Domain.Reports.TopupReport
AppTech.MSMS.Domain.Reports.TopupReport
TopupReport
TopupReport
S3H4aRewLY
get_ReportView
rXi4QRxrQG
get_ReportView_AllAccounts
vra4paDsmg
BuildConditionByItem
UlM4Lg4AKA
BuildConditionForAgent
abj40i1Jg2
ReportView
mSx43j94I9
ReportView_AllAccounts
<<type>>
AppTech.MSMS.Domain.Reports.TransactionReport
AppTech.MSMS.Domain.Reports.TransactionReport
TransactionReport
TransactionReport
tN04veMSlP
GetDetails
N2X4ZtO0Ea
GetGross
<<type>>
AppTech.MSMS.Domain.Reports.AgentTransReport
AppTech.MSMS.Domain.Reports.AgentTransReport
AgentTransReport
AgentTransReport
<<type>>
AppTech.MSMS.Domain.Reports.AgentTransModel
AppTech.MSMS.Domain.Reports.AgentTransModel
AgentTransModel
AgentTransModel
oQF4mtPTGm
<AgentID>k__BackingField
V8n4CkqnkY
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.BalanceSheetReport
AppTech.MSMS.Domain.Reports.BalanceSheetReport
BalanceSheetReport
BalanceSheetReport
uj34zPRjd5
GetGrossBalanceSheet
UbyD2LTVvC
GetGross
Hc7DX1Q3sp
GetPreBalanceDateCondition
AWJDMmHVPU
GetDetailedBalanceSheet
cyIDjqttQp
GetDetails_AllCurrencies
djfDuMYg84
BuildDetialedQueryWithPreBalance_v2
IKEDqw2rHI
GetQueryParameters
<<type>>
AppTech.MSMS.Domain.Reports.Models.AccountBalanceModel
AppTech.MSMS.Domain.Reports.Models.AccountBalanceModel
AccountBalanceModel
AccountBalanceModel
BXwDWkHuHO
<AccountID>k__BackingField
eY3D49LV6D
<ActiveAccountID>k__BackingField
gLZDDtDLTU
<MainAccountID>k__BackingField
aaKDs7PU6r
<CurrencyID>k__BackingField
PL0D11Q9bq
<GroupID>k__BackingField
dSwD6cwa4k
<Type>k__BackingField
pKjDfyGmXq
<Status>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.BalanceStuates
AppTech.MSMS.Domain.Reports.Models.BalanceStuates
BalanceStuates
BalanceStuates
<<type>>
AppTech.MSMS.Domain.Reports.Models.AccountModel
AppTech.MSMS.Domain.Reports.Models.AccountModel
AccountModel
AccountModel
FANDHtcttZ
<Type>k__BackingField
VVIDBad6DA
<Level>k__BackingField
MsmDtHZ765
<CurrencyID>k__BackingField
r8BDxossY3
<IgnoreZeroBalnces>k__BackingField
NhZD5xUouQ
<TillNow>k__BackingField
cIwDrGWl0L
<ShowLevelAccountsOnly>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.AccountReportType
AppTech.MSMS.Domain.Reports.Models.AccountReportType
AccountReportType
AccountReportType
<<type>>
AppTech.MSMS.Domain.Reports.Models.CardModel
AppTech.MSMS.Domain.Reports.Models.CardModel
CardModel
CardModel
ra2DGsgtXb
<State>k__BackingField
We0DgDo6kA
<AccountID>k__BackingField
RmJDVaOrb8
<ReportType>k__BackingField
AU4DechmMS
<CardTypeID>k__BackingField
p76DnJ0pWX
<CardFactionID>k__BackingField
ImOD8Zb8XY
<IsOrder>k__BackingField
S9kDoCwaGJ
<GroupByAccounts>k__BackingField
HNsDT8QTR1
<GroupByTypes>k__BackingField
keODP3klZ0
<GroupByFactions>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.CurrencyExchangeModel
AppTech.MSMS.Domain.Reports.Models.CurrencyExchangeModel
CurrencyExchangeModel
CurrencyExchangeModel
ONbDRnJlZy
<ReportType>k__BackingField
SKoDALT4Lm
<Type>k__BackingField
J9YDbA04IZ
<CurrencyID>k__BackingField
n5EDIxgcXP
<AccountID>k__BackingField
jhgDNvuKna
<ExchangeAccountID>k__BackingField
b5lDk16UDP
<UserID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.FundReportModel
AppTech.MSMS.Domain.Reports.Models.FundReportModel
FundReportModel
FundReportModel
ktxD7OlJJp
<AccountID>k__BackingField
iXIDcSJs4n
<CurrencyID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.IdleAccountsModel
AppTech.MSMS.Domain.Reports.Models.IdleAccountsModel
IdleAccountsModel
IdleAccountsModel
JWvDiAeDpx
<State>k__BackingField
UcCDdeG7dV
<AccountID>k__BackingField
swFDwJGGLd
<ReportType>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.InvoiceModel
AppTech.MSMS.Domain.Reports.Models.InvoiceModel
InvoiceModel
InvoiceModel
Qp7Dyw1I20
<AccountID>k__BackingField
RbEDhglVVc
<Type>k__BackingField
dCvDFel5Nd
<InvoiceType>k__BackingField
NOwDlNWn85
<PaidState>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.SimReportModel
AppTech.MSMS.Domain.Reports.Models.SimReportModel
SimReportModel
SimReportModel
mhaDYSbmgM
<GroupByAccount>k__BackingField
nHQDJYJooP
<GroupByUser>k__BackingField
bLKDKS1OE0
<AccountID>k__BackingField
iLaD9G5fbK
<UserID>k__BackingField
QcuDO9q25L
<SimNumber>k__BackingField
RUyDUskCp8
<State>k__BackingField
UWuDEym780
<SimType>k__BackingField
EdODSpd6QK
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.SimState
AppTech.MSMS.Domain.Reports.Models.SimState
SimState
SimState
<<type>>
AppTech.MSMS.Domain.Reports.Models.SlatingReportModel
AppTech.MSMS.Domain.Reports.Models.SlatingReportModel
SlatingReportModel
SlatingReportModel
KHtDpTbXiw
<AccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.TopupOrderModel
AppTech.MSMS.Domain.Reports.Models.TopupOrderModel
TopupOrderModel
TopupOrderModel
q7mDLh9MuB
<AccountID>k__BackingField
D5XDapTCcw
<ServiceID>k__BackingField
OUaD059yl9
<ReportType>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.VoucherModel
AppTech.MSMS.Domain.Reports.Models.VoucherModel
VoucherModel
VoucherModel
TNtDQcaSjs
<Type>k__BackingField
aMwD3hYlPu
<AccountID>k__BackingField
DMHDva9TeL
<CurrencyID>k__BackingField
f5lDZcryEM
<UserID>k__BackingField
qmaDmefk3s
<ReportType>k__BackingField
iDPDC4guvF
<GroupByAccounts>k__BackingField
FQmDz6UP7L
<GroupByUsers>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.VoucherType
AppTech.MSMS.Domain.Reports.Models.VoucherType
VoucherType
VoucherType
<<type>>
AppTech.MSMS.Domain.Reports.Models.WalletReportModel
AppTech.MSMS.Domain.Reports.Models.WalletReportModel
WalletReportModel
WalletReportModel
DsJs2mwP5s
<Type>k__BackingField
oSSsXaKKnW
<CurrencyID>k__BackingField
awmsM1gAsp
<AccountID>k__BackingField
uULsjwZbLa
<AccountNumber>k__BackingField
pWgsuBaXiT
<UserID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.WalletType
AppTech.MSMS.Domain.Reports.Models.WalletType
WalletType
WalletType
<<type>>
AppTech.MSMS.Domain.Reports.Models.WifiModel
AppTech.MSMS.Domain.Reports.Models.WifiModel
WifiModel
WifiModel
CodsqRTIYW
<State>k__BackingField
RcPsWII3jC
<AccountID>k__BackingField
X2rs40NhIw
<ReportType>k__BackingField
p75sD8gge1
<ProviderID>k__BackingField
jsSssAAe5n
<FactionID>k__BackingField
cqes1IWuIV
<GroupByAccounts>k__BackingField
G2vs6I4GxS
<GroupByProviders>k__BackingField
diesfBPE3Z
<GroupByFactions>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.TransferModel
AppTech.MSMS.Domain.Reports.Models.TransferModel
TransferModel
TransferModel
t8ysHGnvlI
<External>k__BackingField
y4HsB4nAy5
<InArabic>k__BackingField
U8LstCq1uw
<InOut>k__BackingField
iMlsxV8rTm
<AccountID>k__BackingField
rv9s5RJMOv
<UserID>k__BackingField
V7MsrEYy5N
<CurrencyID>k__BackingField
bycsGkHDl7
<ExchangerID>k__BackingField
tSusgw4KMk
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.RemittReportModel
AppTech.MSMS.Domain.Reports.Models.RemittReportModel
RemittReportModel
RemittReportModel
FRKsVew4IM
<ReportType>k__BackingField
DyAseZmIql
<Type>k__BackingField
PcLsnUDQro
<TargetID>k__BackingField
ktIs8Y764t
<SourceID>k__BackingField
a2fsoeCGas
<CurrencyID>k__BackingField
wmUsTWPhf3
<AccountID>k__BackingField
yImsPbUlMp
<UserID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.AuditLogModel
AppTech.MSMS.Domain.Reports.Models.AuditLogModel
AuditLogModel
AuditLogModel
fFEsRNZige
<UserID>k__BackingField
FLPsAZqiR9
<PageName>k__BackingField
Deesbxc3Ku
<Action>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.OrderModel
AppTech.MSMS.Domain.Reports.Models.OrderModel
OrderModel
OrderModel
lMisIsrIhd
<AccountID>k__BackingField
EqcsNHaBLq
<ServiceID>k__BackingField
hpxsklhKgI
<OrderState>k__BackingField
PlQs7r2UTS
<Status>k__BackingField
FvAsc2EwMV
<IsMobile>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.TopupModel
AppTech.MSMS.Domain.Reports.Models.TopupModel
TopupModel
TopupModel
x3wsi7p34J
<AccountID>k__BackingField
mjGsd2lCIN
<ServiceID>k__BackingField
JRBswvWyG3
<ProviderID>k__BackingField
RKtsyasyl7
<Status>k__BackingField
RQDsh9qNPK
<Type>k__BackingField
d3IsF9t1yI
<AllBranchAccounts>k__BackingField
VZeslZO5BY
<WithoutPointsTopup>k__BackingField
cYwsYvNs3I
<GrossGrouping>k__BackingField
atjsJ0vsEI
<WithAgentClients>k__BackingField
UnjsKrbWe9
<GroupByAccounts>k__BackingField
MJes9cOxr5
<GroupByServices>k__BackingField
mH1sOGmbc0
<GroupByProviders>k__BackingField
GZXsUDq4rT
<GroupByStatus>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.TopupOperatorModel
AppTech.MSMS.Domain.Reports.Models.TopupOperatorModel
TopupOperatorModel
TopupOperatorModel
xJXsEdxXMG
<ProviderID>k__BackingField
T6fsSs1Qew
<AccountID>k__BackingField
Iu5spsaANh
<Operator>k__BackingField
Nl5sLj8RFQ
<ItemID>k__BackingField
foUsaqnQXE
<SNO>k__BackingField
Q1Js0g7a9X
<LineType>k__BackingField
IEMsQR0xNH
<Status>k__BackingField
vX8s3BrZdH
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.TransactionModel
AppTech.MSMS.Domain.Reports.Models.TransactionModel
TransactionModel
TransactionModel
LnGsvDGPb7
<Type>k__BackingField
AJDsZlrLci
<AccountID>k__BackingField
sxxsmd6Os3
<ServiceID>k__BackingField
QvcsCPZIRP
<UserID>k__BackingField
bjfszjxt46
<IsAgent>k__BackingField
kUw12KW0VI
<GroupByPoints>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.TransferReportModel
AppTech.MSMS.Domain.Reports.Models.TransferReportModel
TransferReportModel
TransferReportModel
bUl1XCVEBO
<SenderClientID>k__BackingField
CCB1MXMtQy
<ReceiverClientID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Offline.OffLineHelper
AppTech.MSMS.Domain.Offline.OffLineHelper
OffLineHelper
OffLineHelper
JMs1j2RYmM
ExtractDate
<<type>>
AppTech.MSMS.Domain.Offline.SmsRequest
AppTech.MSMS.Domain.Offline.SmsRequest
SmsRequest
SmsRequest
qEl1uUOhUh
<UN>k__BackingField
dh71qGLXoB
<PSS>k__BackingField
eZl1Wle9jK
<MSG>k__BackingField
rX514NKETk
<Target>k__BackingField
gkx1DiqiTv
<SNO>k__BackingField
OeD1s4xF1M
<ID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Offline.SmsResponse
AppTech.MSMS.Domain.Offline.SmsResponse
SmsResponse
SmsResponse
tfL11ZdQYU
<Success>k__BackingField
zPq16sR1pP
<MSG>k__BackingField
FQ81fYrYjp
<SNO>k__BackingField
<<type>>
AppTech.MSMS.Domain.Offline.SmsRequestHandler
AppTech.MSMS.Domain.Offline.SmsRequestHandler
SmsRequestHandler
SmsRequestHandler
<<type>>
AppTech.MSMS.Domain.Offline.SmsType
AppTech.MSMS.Domain.Offline.SmsType
SmsType
SmsType
<<type>>
AppTech.MSMS.Domain.Hubs.SignalRProvider
AppTech.MSMS.Domain.Hubs.SignalRProvider
SignalRProvider
SignalRProvider
XRI1HgMiwN
OnMessageArrived
fFD1BDXJ0x
hubConnection
CSm1tYNqOD
hubProxy
<<type>>
AppTech.MSMS.Domain.Express.RemittanceException
AppTech.MSMS.Domain.Express.RemittanceException
RemittanceException
RemittanceException
<<type>>
AppTech.MSMS.Domain.Express.RemittanceResponse
AppTech.MSMS.Domain.Express.RemittanceResponse
RemittanceResponse
RemittanceResponse
p9c1xwhxq9
<Status>k__BackingField
dPU15jOFTM
<Success>k__BackingField
vsg1rxMxpU
<Result>k__BackingField
FFJ1GX2K3C
<Message>k__BackingField
i3a1gniNJp
<ProviderResponse>k__BackingField
R2n1VfcQTw
<Number>k__BackingField
F2V1ejJ8sq
<RefNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Express.RemittanceRequest
AppTech.MSMS.Domain.Express.RemittanceRequest
RemittanceRequest
RemittanceRequest
cA51naRviy
<ID>k__BackingField
nZj18piY6M
<Number>k__BackingField
l5l1o1UeUe
<Amount>k__BackingField
vMA1TJQseo
<CurrencyID>k__BackingField
Nox1Phnfwe
<BeneficiaryName>k__BackingField
E0l1RAGUJR
<BeneficiaryPhone>k__BackingField
LFI1ATBpaj
<SenderName>k__BackingField
BXH1bn48N5
<SenderPhone>k__BackingField
RCD1Iy7Kms
<TargetNumber>k__BackingField
iAP1NJuqLw
<Note>k__BackingField
eGy1kDhuUu
<Purpose>k__BackingField
XB717q7q9X
<Status>k__BackingField
FxQ1cF5kv2
<CommissionAmount>k__BackingField
pyB1iD6KYM
<CommissionCurrencyID>k__BackingField
itC1dGqQGu
<ExchangeAccountID>k__BackingField
gy01wgSSk0
<RefNumber>k__BackingField
djj1yGqJST
<Channel>k__BackingField
<<type>>
AppTech.MSMS.Domain.Remittances.SyncRemittanceOutService
AppTech.MSMS.Domain.Remittances.SyncRemittanceOutService
SyncRemittanceOutService
SyncRemittanceOutService
V4u1hEDuVX
AddRecordInSyncSystem
U4d1FdEWPZ
Notify
gXL1lUDIwg
_RemittanceIn
ctd1Y3thO5
<AutoNumbering>k__BackingField
NYG1J6GVQp
_remittanceCommission
R2q1Kb3fDX
syncDeliveredTransfer
Owi19llwDJ
ServiceId
l2n1OinDUg
incomingTransfer
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.AccountBindService
AppTech.MSMS.Domain.Remittances.Intilization.AccountBindService
AccountBindService
AccountBindService
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService
ExchangerTargetService
ExchangerTargetService
<<type>>
AppTech.MSMS.Domain.Notifications.Firebase
AppTech.MSMS.Domain.Notifications.Firebase
Firebase
Firebase
Fx61UDyc3q
Fire
zGl1E5W7ZL
Log
<<type>>
AppTech.MSMS.Domain.Notifications.FirebaseCredential
AppTech.MSMS.Domain.Notifications.FirebaseCredential
FirebaseCredential
FirebaseCredential
viB1SsDbGJ
<type>k__BackingField
vFo1pEJG0n
<project_id>k__BackingField
wlB1LRnvXU
<private_key_id>k__BackingField
JTX1aKKq4E
<private_key>k__BackingField
drc10OKGVV
<client_email>k__BackingField
G7o1QgAyGJ
<client_id>k__BackingField
oAC13txB3v
<auth_uri>k__BackingField
LM91vDM8gS
<token_uri>k__BackingField
v4B1ZfLOmy
<auth_provider_x509_cert_url>k__BackingField
SQQ1mQXjID
<client_x509_cert_url>k__BackingField
<<type>>
AppTech.MSMS.Domain.Notifications.FmcProvider
AppTech.MSMS.Domain.Notifications.FmcProvider
FmcProvider
FmcProvider
qfq1Cl5I1h
_listener
<<type>>
AppTech.MSMS.Domain.Notifications.NotificationBuilder
AppTech.MSMS.Domain.Notifications.NotificationBuilder
NotificationBuilder
NotificationBuilder
<<type>>
AppTech.MSMS.Domain.Notifications.NotificationListener
AppTech.MSMS.Domain.Notifications.NotificationListener
NotificationListener
NotificationListener
<<type>>
AppTech.MSMS.Domain.Notifications.Notifier
AppTech.MSMS.Domain.Notifications.Notifier
Notifier
Notifier
<<type>>
AppTech.MSMS.Domain.Notifications.NotificationService
AppTech.MSMS.Domain.Notifications.NotificationService
NotificationService
NotificationService
<<type>>
AppTech.MSMS.Domain.Services.ReceiptCreditorService
AppTech.MSMS.Domain.Services.ReceiptCreditorService
ReceiptCreditorService
ReceiptCreditorService
<<type>>
AppTech.MSMS.Domain.Services.ReceiptDebitorService
AppTech.MSMS.Domain.Services.ReceiptDebitorService
ReceiptDebitorService
ReceiptDebitorService
<<type>>
AppTech.MSMS.Domain.Services.AccountDocumentService
AppTech.MSMS.Domain.Services.AccountDocumentService
AccountDocumentService
AccountDocumentService
<<type>>
AppTech.MSMS.Domain.Services.AccountEntryService`1
AppTech.MSMS.Domain.Services.AccountEntryService`1
AccountEntryService`1
AccountEntryService`1
<<type>>
AppTech.MSMS.Domain.Services.AccountRegionService
AppTech.MSMS.Domain.Services.AccountRegionService
AccountRegionService
AccountRegionService
<<type>>
AppTech.MSMS.Domain.Services.AdminNotificationService
AppTech.MSMS.Domain.Services.AdminNotificationService
AdminNotificationService
AdminNotificationService
<<type>>
AppTech.MSMS.Domain.Services.DistributorService
AppTech.MSMS.Domain.Services.DistributorService
DistributorService
DistributorService
JhE1zHsve9
CreateNewUser
TmO62hvVeD
<AutoNumbering>k__BackingField
HZw6XgDY9L
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CoverageOrderService
AppTech.MSMS.Domain.Services.CoverageOrderService
CoverageOrderService
CoverageOrderService
NJ76MBO8jH
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CurrencyRateAccountService
AppTech.MSMS.Domain.Services.CurrencyRateAccountService
CurrencyRateAccountService
CurrencyRateAccountService
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoSyncService
AppTech.MSMS.Domain.Services.OrderInfoSyncService
OrderInfoSyncService
OrderInfoSyncService
<<type>>
AppTech.MSMS.Domain.Services.AsyncBagatService
AppTech.MSMS.Domain.Services.AsyncBagatService
AsyncBagatService
AsyncBagatService
bhp6jHBMx5
_disposed
<<type>>
AppTech.MSMS.Domain.Services.AsyncTopupService
AppTech.MSMS.Domain.Services.AsyncTopupService
AsyncTopupService
AsyncTopupService
uBY6uWE9OJ
ExcuteAsOrder
HaS6qP58Hj
GenerateTransactionId
<<type>>
AppTech.MSMS.Domain.Services.BankDepositService
AppTech.MSMS.Domain.Services.BankDepositService
BankDepositService
BankDepositService
<<type>>
AppTech.MSMS.Domain.Services.BankService
AppTech.MSMS.Domain.Services.BankService
BankService
BankService
Wke6WkDXr8
currentUnitOfWork
<<type>>
AppTech.MSMS.Domain.Services.BranchTargetService
AppTech.MSMS.Domain.Services.BranchTargetService
BranchTargetService
BranchTargetService
fMP641d2nB
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ClaimGroupService
AppTech.MSMS.Domain.Services.ClaimGroupService
ClaimGroupService
ClaimGroupService
B266D5eUAG
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ConsumeInvoiceService
AppTech.MSMS.Domain.Services.ConsumeInvoiceService
ConsumeInvoiceService
ConsumeInvoiceService
<<type>>
AppTech.MSMS.Domain.Services.DbBackupService
AppTech.MSMS.Domain.Services.DbBackupService
DbBackupService
DbBackupService
OJG6s0Dg52
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.DeviceService
AppTech.MSMS.Domain.Services.DeviceService
DeviceService
DeviceService
LTl61eZnL9
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.EntryService`1
AppTech.MSMS.Domain.Services.EntryService`1
EntryService`1
EntryService`1
<<type>>
AppTech.MSMS.Domain.Services.ExchangerCommissionService
AppTech.MSMS.Domain.Services.ExchangerCommissionService
ExchangerCommissionService
ExchangerCommissionService
vkV66RjJAB
CalcCommissionAmount
cRC6fspf7P
<EditableFields>k__BackingField
fGC6HFiHrX
<ViewName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.FeedbackService
AppTech.MSMS.Domain.Services.FeedbackService
FeedbackService
FeedbackService
<<type>>
AppTech.MSMS.Domain.Services.TargetGroupService
AppTech.MSMS.Domain.Services.TargetGroupService
TargetGroupService
TargetGroupService
pe56BShF8C
<SelectedItems>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.GroupService
AppTech.MSMS.Domain.Services.GroupService
GroupService
GroupService
sDu6tPANcW
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.PartyService
AppTech.MSMS.Domain.Services.PartyService
PartyService
PartyService
<<type>>
AppTech.MSMS.Domain.Services.ProviderCommissionService
AppTech.MSMS.Domain.Services.ProviderCommissionService
ProviderCommissionService
ProviderCommissionService
CQ66xKN4wD
<OnValidate>b__7_1
Ges652DVk4
<OnValidate>b__7_2
ogF6rP1fhC
_provider
P6E6GCy0c6
<ViewName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.RegionService
AppTech.MSMS.Domain.Services.RegionService
RegionService
RegionService
<<type>>
AppTech.MSMS.Domain.Services.RegisterService
AppTech.MSMS.Domain.Services.RegisterService
RegisterService
RegisterService
kUH6gM8EL5
<Registeration>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CommissionReceiptService
AppTech.MSMS.Domain.Services.CommissionReceiptService
CommissionReceiptService
CommissionReceiptService
Umo6VKFoST
<OnValidate>b__3_1
rcr6e4w4uP
<OnValidate>b__3_2
<<type>>
AppTech.MSMS.Domain.Services.SubscribService
AppTech.MSMS.Domain.Services.SubscribService
SubscribService
SubscribService
LEu6n36OM7
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.GsmService
AppTech.MSMS.Domain.Services.GsmService
GsmService
GsmService
ycP68WYOwR
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.YMaxFactionService
AppTech.MSMS.Domain.Services.YMaxFactionService
YMaxFactionService
YMaxFactionService
<<type>>
AppTech.MSMS.Domain.Services.TransferCommissionService
AppTech.MSMS.Domain.Services.TransferCommissionService
TransferCommissionService
TransferCommissionService
SJV6okNknc
CalcCommissionAmount
djg6TiQQfj
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.TransferInService
AppTech.MSMS.Domain.Services.TransferInService
TransferInService
TransferInService
W4a6P6SQTV
Notify
ck16RmO6uZ
_disposed
VXg6AbPf1Q
_exchangerCommission
TKv6bV2Ry6
_exchangerService
wUU6IdktFO
_service
CwI6Nlp2BD
_serviceCommission
EPd6ktlqB5
exchangerCommission
<<type>>
AppTech.MSMS.Domain.Services.TransferOutOrderService
AppTech.MSMS.Domain.Services.TransferOutOrderService
TransferOutOrderService
TransferOutOrderService
<<type>>
AppTech.MSMS.Domain.Services.TransferOutService
AppTech.MSMS.Domain.Services.TransferOutService
TransferOutService
TransferOutService
B4T675m0eh
Notify
Jx86c8btTN
_disposed
Mrs6ichX2a
_exchangerCommission
TcM6dFj01n
_exchangerCommissionService
UdM6wtNFPM
_exchangerService
f4a6yVqyow
_service
<<type>>
AppTech.MSMS.Domain.Services.TransportOrderService
AppTech.MSMS.Domain.Services.TransportOrderService
TransportOrderService
TransportOrderService
<<type>>
AppTech.MSMS.Domain.Services.UserDeviceService
AppTech.MSMS.Domain.Services.UserDeviceService
UserDeviceService
UserDeviceService
<<type>>
AppTech.MSMS.Domain.Services.TransporterService
AppTech.MSMS.Domain.Services.TransporterService
TransporterService
TransporterService
UIj6hu6gZW
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.AccountCoverageService
AppTech.MSMS.Domain.Services.AccountCoverageService
AccountCoverageService
AccountCoverageService
<<type>>
AppTech.MSMS.Domain.Services.NetworkRemittanceInService
AppTech.MSMS.Domain.Services.NetworkRemittanceInService
NetworkRemittanceInService
NetworkRemittanceInService
<<type>>
AppTech.MSMS.Domain.Services.WifiCardService
AppTech.MSMS.Domain.Services.WifiCardService
WifiCardService
WifiCardService
iHf6FsfQZr
GetCard
<<type>>
AppTech.MSMS.Domain.Services.WifiFactionService
AppTech.MSMS.Domain.Services.WifiFactionService
WifiFactionService
WifiFactionService
cYq6lYBTMO
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.WifiProviderService
AppTech.MSMS.Domain.Services.WifiProviderService
WifiProviderService
WifiProviderService
ejR6YgnidT
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.AccountSetting
AppTech.MSMS.Domain.Services.AccountSetting
AccountSetting
AccountSetting
hbs6Jok3Jj
<Subscribers>k__BackingField
hNU6KiZ2Ol
<Agents>k__BackingField
FuD69RvIlQ
<Clients>k__BackingField
RA36Oss2gF
<Distributors>k__BackingField
nQy6UEbonG
<Providers>k__BackingField
WgD6EYhdv1
<Funds>k__BackingField
lLL6S3ZQkp
<Banks>k__BackingField
Fjf6pMx8Ir
<Merchants>k__BackingField
ywo6LPjHfy
<ExternalBranches>k__BackingField
KXH6aQ5e0o
<Exchangers>k__BackingField
wyX60tZbdP
<SimInvoice>k__BackingField
DiN6QPbAnK
<SimPurchases>k__BackingField
Nl363ugn51
<BuyCurrencyDiffer>k__BackingField
Urj6voEk1g
<SalsCurrencyDiffer>k__BackingField
r8y6ZcuAEE
<ServicesCommission>k__BackingField
CCy6ma4RJJ
<RemittanceCommission>k__BackingField
zVv6CX6BlG
<TopupDifferials>k__BackingField
tSg6z5H9Tf
<TopupCommissionMadeen>k__BackingField
k16f2OJIts
<Remittance>k__BackingField
xlFfXyRDbd
<CashLoan>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.SyncAccountSetting
AppTech.MSMS.Domain.Services.SyncAccountSetting
SyncAccountSetting
SyncAccountSetting
<<type>>
AppTech.MSMS.Domain.Services.BuyCurrencyService
AppTech.MSMS.Domain.Services.BuyCurrencyService
BuyCurrencyService
BuyCurrencyService
dfdfM9aIKt
<VoucherName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.LoanOrderService
AppTech.MSMS.Domain.Services.LoanOrderService
LoanOrderService
LoanOrderService
<<type>>
AppTech.MSMS.Domain.Services.MsExtensions
AppTech.MSMS.Domain.Services.MsExtensions
MsExtensions
MsExtensions
<<type>>
AppTech.MSMS.Domain.Services.AccountService
AppTech.MSMS.Domain.Services.AccountService
AccountService
AccountService
ebAfjmJVnp
ParentAccountID
iwBfua0F7q
GetDirectParentAccountID
by1fqAY405
GetParentAccountIdOrZero
jXjfW9QlIM
CanTransferBetweenPointsTree
scyf4yJjnd
GetDirectParentAccountID2
XWEfDJMkfL
GetTargetAccountID
Oa8fsM73er
SuspendedOrderAmount
Dcof16HsGo
<OnUpdated>b__14_1
Q9Of6Z8YqL
<OnDeleteValidate>b__24_0
<<type>>
AppTech.MSMS.Domain.Services.AccountSlatingService
AppTech.MSMS.Domain.Services.AccountSlatingService
AccountSlatingService
AccountSlatingService
VkQffPMXPm
Notify
tRVfHPS9tx
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ActionService
AppTech.MSMS.Domain.Services.ActionService
ActionService
ActionService
<<type>>
AppTech.MSMS.Domain.Services.AuditLogService
AppTech.MSMS.Domain.Services.AuditLogService
AuditLogService
AuditLogService
<<type>>
AppTech.MSMS.Domain.Services.AgentService
AppTech.MSMS.Domain.Services.AgentService
AgentService
AgentService
IgGfB5HJj7
CreateNewUser
nUbftxhVKS
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.AgentPointService
AppTech.MSMS.Domain.Services.AgentPointService
AgentPointService
AgentPointService
egRfxdWnRf
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.AgentPointUserService
AppTech.MSMS.Domain.Services.AgentPointUserService
AgentPointUserService
AgentPointUserService
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService
AppTech.MSMS.Domain.Services.ExternalBranchService
ExternalBranchService
ExternalBranchService
DYtf5fUMYm
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.FundUserService
AppTech.MSMS.Domain.Services.FundUserService
FundUserService
FundUserService
cCJfrk0yJg
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.MsCrudBusiness`1
AppTech.MSMS.Domain.Services.MsCrudBusiness`1
MsCrudBusiness`1
MsCrudBusiness`1
pO1fG5Xvfg
BuildRecord
O8ffgsIsL8
_setting
MgGfVRbILo
<LogUserAction>k__BackingField
QGZfelWF6A
<Problem>k__BackingField
kKlfnwchel
<MainUnitOfWork>k__BackingField
f5lf8i8XoF
<FilterByBranch>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.BrochureService
AppTech.MSMS.Domain.Services.BrochureService
BrochureService
BrochureService
<<type>>
AppTech.MSMS.Domain.Services.CashDepositService
AppTech.MSMS.Domain.Services.CashDepositService
CashDepositService
CashDepositService
uRifoC3PB1
Notify
OuSfTX9wCr
mServiceId
LYJfP8i1Yf
<LogUserAction>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CashWithdrawService
AppTech.MSMS.Domain.Services.CashWithdrawService
CashWithdrawService
CashWithdrawService
qoefRjtQwT
Notify
An4fAuLiPA
CheckIfByAgent
jrWfbMNGuX
<LogUserAction>k__BackingField
OEqfITMpdr
mServiceId
<<type>>
AppTech.MSMS.Domain.Services.ClientService
AppTech.MSMS.Domain.Services.ClientService
ClientService
ClientService
TcXfN7hnYh
GetParent
F5Sfkh7L5u
CreateNewUser
Xulf75J32K
Test
LIdfcCF2tD
<CreateUserWithClient>k__BackingField
YuJfi3dOdO
<Registeration>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Notification
AppTech.MSMS.Domain.Services.Notification
Notification
Notification
CRhfdwNguf
NotifyAndForget
VMXfwNvu21
SendSmsIfAllowed
dsefy0A3M0
<EditableFields>k__BackingField
wxyfhww3o0
<Auditable>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.OfferOrderService
AppTech.MSMS.Domain.Services.OfferOrderService
OfferOrderService
OfferOrderService
<<type>>
AppTech.MSMS.Domain.Services.RemittanceNumberService
AppTech.MSMS.Domain.Services.RemittanceNumberService
RemittanceNumberService
RemittanceNumberService
NwwfFEUsSX
SaveNumber
<<type>>
AppTech.MSMS.Domain.Services.SaleCurrencyService
AppTech.MSMS.Domain.Services.SaleCurrencyService
SaleCurrencyService
SaleCurrencyService
Yvmfl3tXmC
<VoucherName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService
AppTech.MSMS.Domain.Services.ServiceClaimService
ServiceClaimService
ServiceClaimService
GDXfY9KpIA
CheckServicePermission_v0
T1pfJrh2Ij
CheckServicePermission_v2
PcufKBT4WB
CheckServicePermission
<<type>>
AppTech.MSMS.Domain.Services.ClientAction
AppTech.MSMS.Domain.Services.ClientAction
ClientAction
ClientAction
Skcf9WQva4
<Value>k__BackingField
euqfOlSQMv
<Text>k__BackingField
DyxfU1bf2O
<IsAllowed>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ClientSmsService
AppTech.MSMS.Domain.Services.ClientSmsService
ClientSmsService
ClientSmsService
v8UfERddv6
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CurrencyService
AppTech.MSMS.Domain.Services.CurrencyService
CurrencyService
CurrencyService
poffSmkC1o
GetIdForOrderCallback
woqfpRGAqV
<DefaultCurrecnyID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CurrencyExchangeService
AppTech.MSMS.Domain.Services.CurrencyExchangeService
CurrencyExchangeService
CurrencyExchangeService
z4Nfae6Mrv
get_IsBuy
fmffQ6YrI5
get_DirectCurrencyExchange
O3xfLAZtav
MakeDirectExchangeCurrency
PrAfvgt2uA
_serviceId
PTAfZeJUSx
<LogUserAction>k__BackingField
UcOf0bB3Bg
IsBuy
z0Yf3xU4NY
DirectCurrencyExchange
<<type>>
AppTech.MSMS.Domain.Services.CurrencyRateService
AppTech.MSMS.Domain.Services.CurrencyRateService
CurrencyRateService
CurrencyRateService
<<type>>
AppTech.MSMS.Domain.Services.DepositOrderService
AppTech.MSMS.Domain.Services.DepositOrderService
DepositOrderService
DepositOrderService
<<type>>
AppTech.MSMS.Domain.Services.JournalService
AppTech.MSMS.Domain.Services.JournalService
JournalService
JournalService
EHDfmidv4G
Entries
<<type>>
AppTech.MSMS.Domain.Services.JournalEntryService
AppTech.MSMS.Domain.Services.JournalEntryService
JournalEntryService
JournalEntryService
<<type>>
AppTech.MSMS.Domain.Services.ErrorService
AppTech.MSMS.Domain.Services.ErrorService
ErrorService
ErrorService
<<type>>
AppTech.MSMS.Domain.Services.ExchangerService
AppTech.MSMS.Domain.Services.ExchangerService
ExchangerService
ExchangerService
<<type>>
AppTech.MSMS.Domain.Services.YmFactionService
AppTech.MSMS.Domain.Services.YmFactionService
YmFactionService
YmFactionService
<<type>>
AppTech.MSMS.Domain.Services.BasicFactionService
AppTech.MSMS.Domain.Services.BasicFactionService
BasicFactionService
BasicFactionService
<<type>>
AppTech.MSMS.Domain.Services.FactionService
AppTech.MSMS.Domain.Services.FactionService
FactionService
FactionService
<<type>>
AppTech.MSMS.Domain.Services.FsmsReport`1
AppTech.MSMS.Domain.Services.FsmsReport`1
FsmsReport`1
FsmsReport`1
<<type>>
AppTech.MSMS.Domain.Services.FundService
AppTech.MSMS.Domain.Services.FundService
FundService
FundService
<<type>>
AppTech.MSMS.Domain.Services.GeneralInfoService
AppTech.MSMS.Domain.Services.GeneralInfoService
GeneralInfoService
GeneralInfoService
k8OfC1ErOu
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.InstructionService
AppTech.MSMS.Domain.Services.InstructionService
InstructionService
InstructionService
FXUfzkb36H
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.DoubleEntryService`1
AppTech.MSMS.Domain.Services.DoubleEntryService`1
DoubleEntryService`1
DoubleEntryService`1
FXvH2XnLX0
entryManager
VeAHXBOc37
_disposed
<<type>>
AppTech.MSMS.Domain.Services.MerchantService
AppTech.MSMS.Domain.Services.MerchantService
MerchantService
MerchantService
CCAHMlRm16
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.MerchantCategoryService
AppTech.MSMS.Domain.Services.MerchantCategoryService
MerchantCategoryService
MerchantCategoryService
PfkHjOhSiI
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.MerchantPaymentService
AppTech.MSMS.Domain.Services.MerchantPaymentService
MerchantPaymentService
MerchantPaymentService
<<type>>
AppTech.MSMS.Domain.Services.TopupNetworkService
AppTech.MSMS.Domain.Services.TopupNetworkService
TopupNetworkService
TopupNetworkService
<<type>>
AppTech.MSMS.Domain.Services.OpeningBalanceService
AppTech.MSMS.Domain.Services.OpeningBalanceService
OpeningBalanceService
OpeningBalanceService
QHHHuFFJGf
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService
AppTech.MSMS.Domain.Services.OrderInfoService
OrderInfoService
OrderInfoService
Tc1Hqe3UKk
MakeRayalMobile
cLbHWTS0Rw
MakeTopupPayment
I8VH4pB0IH
MakeAccountSlating
f1hHDxJlXP
MakeCurrencyExchange
flbHstuGLR
x
kJOH1md6h8
DeleteChild
<<type>>
AppTech.MSMS.Domain.Services.IOrder
AppTech.MSMS.Domain.Services.IOrder
IOrder
IOrder
<<type>>
AppTech.MSMS.Domain.Services.OrderDetailService`1
AppTech.MSMS.Domain.Services.OrderDetailService`1
OrderDetailService`1
OrderDetailService`1
cVUH6I2Zs4
<LogUserAction>k__BackingField
v3vHf2Bxvv
<_provider>k__BackingField
BfiHHIlSrX
_lock
<<type>>
AppTech.MSMS.Domain.Services.PageService
AppTech.MSMS.Domain.Services.PageService
PageService
PageService
<<type>>
AppTech.MSMS.Domain.Services.PaymentService`1
AppTech.MSMS.Domain.Services.PaymentService`1
PaymentService`1
PaymentService`1
KBiHBWMKMj
<LogUserAction>k__BackingField
tyaHt4owg4
<ServiceKeyName>k__BackingField
T3tHxOqMTM
<AccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.PersonalInfoService
AppTech.MSMS.Domain.Services.PersonalInfoService
PersonalInfoService
PersonalInfoService
<<type>>
AppTech.MSMS.Domain.Services.SimInvoiceService
AppTech.MSMS.Domain.Services.SimInvoiceService
SimInvoiceService
SimInvoiceService
<<type>>
AppTech.MSMS.Domain.Services.SimpleEntryService
AppTech.MSMS.Domain.Services.SimpleEntryService
SimpleEntryService
SimpleEntryService
<<type>>
AppTech.MSMS.Domain.Services.TopupCommissionService
AppTech.MSMS.Domain.Services.TopupCommissionService
TopupCommissionService
TopupCommissionService
B3RH5Trybj
Notify
y47HrWCgWH
<OnValidate>b__4_1
A2DHGSjhTK
<OnValidate>b__4_2
liyHgQtVX7
serviceName
<<type>>
AppTech.MSMS.Domain.Services.TopupService
AppTech.MSMS.Domain.Services.TopupService
TopupService
TopupService
L22HV24VgA
LogSuspended
peYHeG98Rf
ProcessOnSyncSystem
AvUHnTPx6I
OnProcessed
J4FH8oRTED
GetStatusFromProvider
LJWHoaLG2g
MakeAduitLog
kZPHTrquH6
_disposed
OQ7HPv1Meh
_topupSetting
LGOHRlrxyX
<LogUserAction>k__BackingField
fCFHAlevAG
<VoucherName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.TopupOrderService
AppTech.MSMS.Domain.Services.TopupOrderService
TopupOrderService
TopupOrderService
ueZHbRKv6W
IsDirect
<<type>>
AppTech.MSMS.Domain.Services.CountryService
AppTech.MSMS.Domain.Services.CountryService
CountryService
CountryService
vgdHIlWXtG
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ProvinceService
AppTech.MSMS.Domain.Services.ProvinceService
ProvinceService
ProvinceService
faOHNd2p6r
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CashInService
AppTech.MSMS.Domain.Services.CashInService
CashInService
CashInService
QgtHkiZ76s
Notify
<<type>>
AppTech.MSMS.Domain.Services.Registeration
AppTech.MSMS.Domain.Services.Registeration
Registeration
Registeration
QCXH7NBudL
DecrptPassword
<<type>>
AppTech.MSMS.Domain.Services.RemittanceCommissionService
AppTech.MSMS.Domain.Services.RemittanceCommissionService
RemittanceCommissionService
RemittanceCommissionService
EOMHcU3nEB
CalcCommissionAmount
tcxHihmOJf
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.RemittanceInService
AppTech.MSMS.Domain.Services.RemittanceInService
RemittanceInService
RemittanceInService
RPPHd2hApB
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.RemittanceOutService
AppTech.MSMS.Domain.Services.RemittanceOutService
RemittanceOutService
RemittanceOutService
Ds0HwMeX9K
Notify
mZnHyEYkWQ
_remittanceCommission
Cl5HhqWD94
_RemittanceIn
k8yHF7R5Dx
syncDeliveredTransfer
MFnHlQfteP
<AutoNumbering>k__BackingField
E3THYXhsNj
Services
gOuHJLJWlk
ServiceId
<<type>>
AppTech.MSMS.Domain.Services.RemittancePointService
AppTech.MSMS.Domain.Services.RemittancePointService
RemittancePointService
RemittancePointService
JPqHK7bD9y
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.RemittanceRegionService
AppTech.MSMS.Domain.Services.RemittanceRegionService
RemittanceRegionService
RemittanceRegionService
KKtH9RSPG6
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.RssService
AppTech.MSMS.Domain.Services.RssService
RssService
RssService
ys0HOpaRYM
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.PaymentCommissionService
AppTech.MSMS.Domain.Services.PaymentCommissionService
PaymentCommissionService
PaymentCommissionService
kvxHUk2C93
CalcCommissionAmount
Q0wHEfcJ5h
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.PaymentEntryService
AppTech.MSMS.Domain.Services.PaymentEntryService
PaymentEntryService
PaymentEntryService
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService
AppTech.MSMS.Domain.Services.TopupProviderService
TopupProviderService
TopupProviderService
<<type>>
AppTech.MSMS.Domain.Services.LiveTopupService
AppTech.MSMS.Domain.Services.LiveTopupService
LiveTopupService
LiveTopupService
lMNHSpZNdl
AsOrderByAccount
<<type>>
AppTech.MSMS.Domain.Services.YMobileBagatService
AppTech.MSMS.Domain.Services.YMobileBagatService
YMobileBagatService
YMobileBagatService
jbeHp0mENN
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.BagatService
AppTech.MSMS.Domain.Services.BagatService
BagatService
BagatService
oIvHLUbbHT
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.MoneyMasterService
AppTech.MSMS.Domain.Services.MoneyMasterService
MoneyMasterService
MoneyMasterService
<<type>>
AppTech.MSMS.Domain.Services.WERegionService
AppTech.MSMS.Domain.Services.WERegionService
WERegionService
WERegionService
HOSHa436o3
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ServiceInfoService
AppTech.MSMS.Domain.Services.ServiceInfoService
ServiceInfoService
ServiceInfoService
<<type>>
AppTech.MSMS.Domain.Services.SimCardOrderService
AppTech.MSMS.Domain.Services.SimCardOrderService
SimCardOrderService
SimCardOrderService
IDEH0y6QwL
InitSpecialSim
d9sHQqXksL
_simSetting
ipjH3VNco9
_refundAmount
<<type>>
AppTech.MSMS.Domain.Services.SmsService
AppTech.MSMS.Domain.Services.SmsService
SmsService
SmsService
iROHv3Axw8
SendToDispatcher
<<type>>
AppTech.MSMS.Domain.Services.CashOutService
AppTech.MSMS.Domain.Services.CashOutService
CashOutService
CashOutService
aOWHZ6cCyd
Notify
<<type>>
AppTech.MSMS.Domain.Services.TrailToupOrderService
AppTech.MSMS.Domain.Services.TrailToupOrderService
TrailToupOrderService
TrailToupOrderService
WHTHmHYRHd
<>n__0
<<type>>
AppTech.MSMS.Domain.Services.CashTransferService
AppTech.MSMS.Domain.Services.CashTransferService
CashTransferService
CashTransferService
NaXHCR4QZ2
Notify
uEQHzaYDJi
commission
gRUB2KBI99
paymentCommission
l69BXkO1O0
obj
<<type>>
AppTech.MSMS.Domain.Services.TransferOrderService
AppTech.MSMS.Domain.Services.TransferOrderService
TransferOrderService
TransferOrderService
gA8BM7MXVu
GetExchangeAccountId
NycBjgQHP5
GetCurrencyID
<<type>>
AppTech.MSMS.Domain.Services.UserRoleService
AppTech.MSMS.Domain.Services.UserRoleService
UserRoleService
UserRoleService
<<type>>
AppTech.MSMS.Domain.Services.UserService
AppTech.MSMS.Domain.Services.UserService
UserService
UserService
iG5BuPr6hH
SetPassword
qCoBq5Q8Na
ResetAccountApiToken
T9nBWT0ZqY
CheckUserPassword
GPiB4otg9E
<AdminID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.UserPageService
AppTech.MSMS.Domain.Services.UserPageService
UserPageService
UserPageService
<<type>>
AppTech.MSMS.Domain.Services.UserPermission
AppTech.MSMS.Domain.Services.UserPermission
UserPermission
UserPermission
GKPBDIg7wi
set_Page
y8pBsRCAjG
set_PageActions
bk5B13LvF6
<Page>k__BackingField
C3BB6Ac9Dv
<PageActions>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Page
AppTech.MSMS.Domain.Services.Page
Page
Page
<<type>>
AppTech.MSMS.Domain.Services.Action
AppTech.MSMS.Domain.Services.Action
Action
Action
RomBfeBX2X
<Value>k__BackingField
FxsBHjiGWn
<Text>k__BackingField
aNWBBOByoc
<Name>k__BackingField
uSDBtqi0Wp
<IsAllow>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.PermissionManager
AppTech.MSMS.Domain.Services.PermissionManager
PermissionManager
PermissionManager
TQXBxM5p2Z
set_UserPermissions
X7kB59lGpm
mIncludeAllPages
n62BrIuuga
mUserId
zFMBGNFLaR
<UserPermissions>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.UserPermissionService
AppTech.MSMS.Domain.Services.UserPermissionService
UserPermissionService
UserPermissionService
<<type>>
AppTech.MSMS.Domain.Services.AccountUserService
AppTech.MSMS.Domain.Services.AccountUserService
AccountUserService
AccountUserService
<<type>>
AppTech.MSMS.Domain.Services.VoucherService
AppTech.MSMS.Domain.Services.VoucherService
VoucherService
VoucherService
<<type>>
AppTech.MSMS.Domain.Services.WithdrawOrderService
AppTech.MSMS.Domain.Services.WithdrawOrderService
WithdrawOrderService
WithdrawOrderService
<<type>>
AppTech.MSMS.Domain.Services.BagatPaymentService
AppTech.MSMS.Domain.Services.BagatPaymentService
BagatPaymentService
BagatPaymentService
Bh5BRHDB4d
get_NoteMsg
FLeBgnkeZH
CheckLastPaid
YACBVoj51i
MakeJournalEntries
J6hBeawCsk
Topup
ofJBnP3arr
ProccessViaProvider
cqFB8IO4oP
SendNotification
VKvBoqo4w6
OnProcessed
RCCBTwcoTs
GetStatusFromProvider
v7rBP9PwuL
MakeAduitLog
zrhBbIZXqv
_accountService
rBTBIvNSPu
_bagat
LsxBNagVTF
_bagatActivated
M0jBkNv6qj
_bagatService
IShB7CQOHu
_liveTopupService
qNrBcJaEMx
_service
St5Bi8J2Wo
_topupService
UWVBdu4tAN
failureMessgae
PBuBwq3Hlh
SyncJournal
hlLBy9tWGs
<AutoNumbering>k__BackingField
dARBhFLxWK
currentProvider
aEtBFICJE6
_disposed
wYJBA8HReq
NoteMsg
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService
WifiInventoryService
WifiInventoryService
M3nBlGHK9n
ExtractAndSaveCards
pdoBYdOhWt
wifiCardService
jbVBJFZRZW
wifiProviderService
CfqBKriKvl
InsertedCards
F08B9XF4UZ
RepeatedCards
gWyBOGMedE
_disposed
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiMultiPaymentService
AppTech.MSMS.Domain.Services.WifiCards.WifiMultiPaymentService
WifiMultiPaymentService
WifiMultiPaymentService
UaCBUoBLKn
faction
rffBEFDvv5
provider
w65BS7xmD1
wifiCards
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiPaymentService
AppTech.MSMS.Domain.Services.WifiCards.WifiPaymentService
WifiPaymentService
WifiPaymentService
M78BpcJBM3
wifiCard
bW4BLXC93R
faction
YnqBas8OQY
provider
eINB026NI9
_disposed
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.Models.WifiInventory
AppTech.MSMS.Domain.Services.WifiCards.Models.WifiInventory
WifiInventory
WifiInventory
IN6BQ2gs67
<ProviderID>k__BackingField
gmDB32iP8H
<FactionID>k__BackingField
tZtBvr3v9r
<Cards>k__BackingField
NLgBZOdMav
<ID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.Models.WifiCardModel
AppTech.MSMS.Domain.Services.WifiCards.Models.WifiCardModel
WifiCardModel
WifiCardModel
BuOBmpoYgT
<Username>k__BackingField
HdIBCQu8hO
<Password>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.BundleManager
AppTech.MSMS.Domain.Services.TopupPayments.BundleManager
BundleManager
BundleManager
ab1BzHREbd
SetGomalaAmount
YVUt2ioSat
SetQuantityAmount
sSotXwLcic
KhedmatiCalc
iPptMCCusm
GetSubServiceID
pSgtjVQuKR
_currentUnitOfWork
pdRtuG8Q42
_currentSesssion
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.BundleService
AppTech.MSMS.Domain.Services.TopupPayments.BundleService
BundleService
BundleService
FRYtqsTwSm
GetBundle
gmwtW5nUV2
GetBundle
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.Operator
AppTech.MSMS.Domain.Services.TopupPayments.Operator
Operator
Operator
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.GomalaTopupService
AppTech.MSMS.Domain.Services.TopupPayments.GomalaTopupService
GomalaTopupService
GomalaTopupService
iKXt4sRcZm
ExcuteAsOrder
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.IBundleService`1
AppTech.MSMS.Domain.Services.TopupPayments.IBundleService`1
IBundleService`1
IBundleService`1
ak4tDTr3od
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.ItemCostService
AppTech.MSMS.Domain.Services.TopupPayments.ItemCostService
ItemCostService
ItemCostService
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.ItemService
AppTech.MSMS.Domain.Services.TopupPayments.ItemService
ItemService
ItemService
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.OperatorService
AppTech.MSMS.Domain.Services.TopupPayments.OperatorService
OperatorService
OperatorService
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.QuotationService
AppTech.MSMS.Domain.Services.TopupPayments.QuotationService
QuotationService
QuotationService
f5ttskx3SB
GetFullQuoation
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileBagatService
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileBagatService
RiyalMobileBagatService
RiyalMobileBagatService
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileOrderService
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileOrderService
RiyalMobileOrderService
RiyalMobileOrderService
R1Dt1rXuCm
get_ServiceID
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileService
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileService
RiyalMobileService
RiyalMobileService
ixqt6ILacF
_disposed
fEAtfrJ0fs
<LogUserAction>k__BackingField
PqKtHOCApo
<VoucherName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileTopupService
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileTopupService
RiyalMobileTopupService
RiyalMobileTopupService
s1YtBd8KFY
serviceId
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.SuspendTopupService
AppTech.MSMS.Domain.Services.TopupPayments.SuspendTopupService
SuspendTopupService
SuspendTopupService
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.TopupCallback
AppTech.MSMS.Domain.Services.TopupPayments.TopupCallback
TopupCallback
TopupCallback
mcQtt5nYhV
ShowError
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.TopupPaymentService
AppTech.MSMS.Domain.Services.TopupPayments.TopupPaymentService
TopupPaymentService
TopupPaymentService
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.Models.IBundle
AppTech.MSMS.Domain.Services.TopupPayments.Models.IBundle
IBundle
IBundle
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.Models.TopupItem
AppTech.MSMS.Domain.Services.TopupPayments.Models.TopupItem
TopupItem
TopupItem
TFBtxBOJv5
<ID>k__BackingField
xpnt5aGaT1
<Name>k__BackingField
dpvtruKmBV
<Operator>k__BackingField
LCJtGK91QX
<Service>k__BackingField
dbAtgx8yZM
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimPurchaseService
AppTech.MSMS.Domain.Services.Sims.SimPurchaseService
SimPurchaseService
SimPurchaseService
LO0tVcMMVQ
get_SimByExcel
FG2teaB0Rn
SimByExcel
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimSaleService
AppTech.MSMS.Domain.Services.Sims.SimSaleService
SimSaleService
SimSaleService
REFtnl38RU
simService
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimTransferService
AppTech.MSMS.Domain.Services.Sims.SimTransferService
SimTransferService
SimTransferService
VFjt8QOfV0
simService
<<type>>
AppTech.MSMS.Domain.Services.Sims.SpecialSimService
AppTech.MSMS.Domain.Services.Sims.SpecialSimService
SpecialSimService
SpecialSimService
<<type>>
AppTech.MSMS.Domain.Services.Security.AccountApiService
AppTech.MSMS.Domain.Services.Security.AccountApiService
AccountApiService
AccountApiService
lJ9to02fbU
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Payment.IBagatService
AppTech.MSMS.Domain.Services.Payment.IBagatService
IBagatService
IBagatService
<<type>>
AppTech.MSMS.Domain.Services.Payment.SimService
AppTech.MSMS.Domain.Services.Payment.SimService
SimService
SimService
<<type>>
AppTech.MSMS.Domain.Services.Payment.TopupReceiptService
AppTech.MSMS.Domain.Services.Payment.TopupReceiptService
TopupReceiptService
TopupReceiptService
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmBagatService
AppTech.MSMS.Domain.Services.Payment.YmBagatService
YmBagatService
YmBagatService
nrhtTYlVsB
ExecuteBagat
JCOtPU7MQq
InitLineType
vkZtRKxF2Z
SendNotification
XHItAoJa3R
_disposed
v1mtbVak5Z
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService
YmTopupBagatService
YmTopupBagatService
uIqtI1X5aP
<>n__0
<<type>>
AppTech.MSMS.Domain.Services.Parties.PointBalanceService
AppTech.MSMS.Domain.Services.Parties.PointBalanceService
PointBalanceService
PointBalanceService
Ac7tNheDVr
<ViewName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.GeneralLedger.AccountFrozenService
AppTech.MSMS.Domain.Services.GeneralLedger.AccountFrozenService
AccountFrozenService
AccountFrozenService
<<type>>
AppTech.MSMS.Domain.Services.GeneralLedger.DoubleEntryBondService
AppTech.MSMS.Domain.Services.GeneralLedger.DoubleEntryBondService
DoubleEntryBondService
DoubleEntryBondService
<<type>>
AppTech.MSMS.Domain.Services.GeneralLedger.WalletInService
AppTech.MSMS.Domain.Services.GeneralLedger.WalletInService
WalletInService
WalletInService
<<type>>
AppTech.MSMS.Domain.Services.Clients.BranchClientService
AppTech.MSMS.Domain.Services.Clients.BranchClientService
BranchClientService
BranchClientService
<<type>>
AppTech.MSMS.Domain.Services.Cards.CardFactionService
AppTech.MSMS.Domain.Services.Cards.CardFactionService
CardFactionService
CardFactionService
<<type>>
AppTech.MSMS.Domain.Services.Cards.CardOrderService
AppTech.MSMS.Domain.Services.Cards.CardOrderService
CardOrderService
CardOrderService
smutkE9Kvg
exchangeRate
VpZt79tM5D
cardFaction
W3TtcBxPQ3
cardType
K0etiXpgaZ
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Cards.CardPaymentService
AppTech.MSMS.Domain.Services.Cards.CardPaymentService
CardPaymentService
CardPaymentService
Yo0td95v70
card
T73twSRaBU
exchangeRate
utvtyrGo1A
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Cards.CardService
AppTech.MSMS.Domain.Services.Cards.CardService
CardService
CardService
SCPthZChfJ
GetCard
L02tFEeqK3
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Cards.CardTypeService
AppTech.MSMS.Domain.Services.Cards.CardTypeService
CardTypeService
CardTypeService
<<type>>
AppTech.MSMS.Domain.Services.Branching.BranchOrderService
AppTech.MSMS.Domain.Services.Branching.BranchOrderService
BranchOrderService
BranchOrderService
<<type>>
AppTech.MSMS.Domain.Services.Branching.BranchService
AppTech.MSMS.Domain.Services.Branching.BranchService
BranchService
BranchService
E6ptlSRj26
GetAccountBranch
N3GtYaCcO0
GetAccountID
qY3tJNJ0Ae
IsBranchUser
jcHtKjBcv6
IsMainBranch
kFpt9x5QIF
<Adminstration>k__BackingField
<<type>>
Le1htHJpfmp0s9lgId.k0XkRtAobe9Y793KXf
AppTech.MSMS.Domain.Services.Accounting.AccountProfileService
k0XkRtAobe9Y793KXf
AccountProfileService
<<type>>
AppTech.MSMS.Domain.Services.Accounting.PersonService`1
AppTech.MSMS.Domain.Services.Accounting.PersonService`1
PersonService`1
PersonService`1
oW5tOAXREr
ValidateNumbers
Du7tUT5nFC
_partiesService
<<type>>
AppTech.MSMS.Domain.Services.Accounting.PartiesService
AppTech.MSMS.Domain.Services.Accounting.PartiesService
PartiesService
PartiesService
P0ntEukBCD
currentUnitOfWork
<<type>>
AppTech.MSMS.Domain.Services.Accounting.SubscriberService
AppTech.MSMS.Domain.Services.Accounting.SubscriberService
SubscriberService
SubscriberService
<<type>>
AppTech.MSMS.Domain.Services.Remittances.BaseRemittanceInService
AppTech.MSMS.Domain.Services.Remittances.BaseRemittanceInService
BaseRemittanceInService
BaseRemittanceInService
U74tSNZPJV
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Remittances.DirectRemittanceInService
AppTech.MSMS.Domain.Services.Remittances.DirectRemittanceInService
DirectRemittanceInService
DirectRemittanceInService
QF7tp5J7gF
CheckSenderAndReceiverNames
J5VtLTfS4b
PushRemittance
tpAta4nkp1
OnExpressRespons
C1It0xoRKR
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Satellite.OrderSatelliteQuotaService
AppTech.MSMS.Domain.Services.Satellite.OrderSatelliteQuotaService
OrderSatelliteQuotaService
OrderSatelliteQuotaService
DL5tQMrLFA
<EditableFields>k__BackingField
qB8t35tAE2
<ExtraCondition>k__BackingField
Gm8tvuGqKb
<ViewName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Satellite.SatelliteFactionService
AppTech.MSMS.Domain.Services.Satellite.SatelliteFactionService
SatelliteFactionService
SatelliteFactionService
zdltZyfM3k
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Satellite.SatelliteProviderService
AppTech.MSMS.Domain.Services.Satellite.SatelliteProviderService
SatelliteProviderService
SatelliteProviderService
Cq6tmQI2rZ
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Satellite.SatellitePaymentService
AppTech.MSMS.Domain.Services.Satellite.SatellitePaymentService
SatellitePaymentService
SatellitePaymentService
VWJtCMuEEw
Notify
QMHtzv7g1R
faction
RQWx2fDkOG
provider
COfxXZpFlm
_disposed
<<type>>
AppTech.MSMS.Domain.Models.Account
AppTech.MSMS.Domain.Models.Account
Account
Account
csFxMJkCxw
<ID>k__BackingField
JR3xjdqujd
<ParentNumber>k__BackingField
W0jxuvSWRh
<Name>k__BackingField
nOBxq8iq39
<Number>k__BackingField
tPZxWp5Zv7
<Type>k__BackingField
WS3x4uRVyh
<CreatedBy>k__BackingField
KFVxD9oTph
<CreatedTime>k__BackingField
rqExs6klYC
<RowVersion>k__BackingField
ldxx1DWYAy
<BranchID>k__BackingField
rW9x6MhUA1
<Status>k__BackingField
jl3xfBEe0I
<AccountLedgerID>k__BackingField
N7WxHJAD1M
<Description>k__BackingField
bUixBNeT5f
<IsCash>k__BackingField
xN4xtMnAol
<IsContraAccount>k__BackingField
daOxxL0wiE
<IsParty>k__BackingField
UmVx56QGQ7
<Branch>k__BackingField
wvXxreePpt
<UserInfo>k__BackingField
UMSxGMNNUF
<AccountApis>k__BackingField
xWUxgWjpeH
<AccountDocuments>k__BackingField
hZjxVPVtoO
<AccountRegions>k__BackingField
UAsxeyGuC8
<AccountSlatings>k__BackingField
EMpxnnEOWj
<AccountUsers>k__BackingField
guDx8CCTNN
<Agents>k__BackingField
n4OxoNcKAg
<BagatPayments>k__BackingField
BaQxT3nHwL
<Banks>k__BackingField
Pj1xP7nB0C
<BankDeposits>k__BackingField
oxnxRZkouL
<BuyCurrencies>k__BackingField
yOixAyA9ke
<BuyCurrencies1>k__BackingField
KJ6xbHMIGe
<CardOrders>k__BackingField
UpRxIQNZu3
<CardPayments>k__BackingField
W7ixNF59Qf
<CardTypes>k__BackingField
sI2xkVu82j
<CashDeposits>k__BackingField
y69x7c6NB4
<CashIns>k__BackingField
sXTxcsOiZ4
<CashOuts>k__BackingField
eusxi5oVBM
<CashWithdraws>k__BackingField
rdOxdoq6sv
<Cheques>k__BackingField
Y07xwdF8Iu
<Clients>k__BackingField
JMLxya9r7K
<AccountNotifications>k__BackingField
VBkxh7ocER
<ServiceClaims>k__BackingField
SZMxF6bMom
<CashTransfers>k__BackingField
JMCxlpBldP
<CommissionReceipts>k__BackingField
SuwxYGGklc
<CommissionReceiptLines>k__BackingField
SxCxJiYAXX
<ConsumeInvoices>k__BackingField
qxbxKPaVvE
<ConsumeInvoices1>k__BackingField
mPZx90re42
<CoverageOrders>k__BackingField
UaixOmKvdv
<CoverageOrders1>k__BackingField
rLyxUfR5dm
<CurrencyExchanges>k__BackingField
VvgxEdGP7a
<DepositOrders>k__BackingField
l1yxS6xToE
<Devices>k__BackingField
ywNxpL7k1e
<Distributors>k__BackingField
VeOxLnismx
<Exchangers>k__BackingField
jBjxavVtMC
<ExternalBranches>k__BackingField
JZSx0MBKbA
<Feedbacks>k__BackingField
CKMxQEQwCw
<Funds>k__BackingField
QjWx31Tg6S
<JournalEntries>k__BackingField
GjdxvYvXE0
<LiveTopups>k__BackingField
TF7xZfxZy7
<LoanOrders>k__BackingField
W6dxmWlWOF
<Merchants>k__BackingField
kO6xCoh3pY
<MerchantPayments>k__BackingField
gtuxzZ0aCE
<OfferOrders>k__BackingField
bXy5215BFW
<OpeningBalances>k__BackingField
Wun5XJm3oH
<OrderInfoes>k__BackingField
dsN5MGat4D
<Parties>k__BackingField
MrJ5jR1JQ4
<Payments>k__BackingField
zbe5uTDGcq
<PurchaseInvoices>k__BackingField
tFh5qIqqtw
<PurchaseInvoices1>k__BackingField
gC55WGEF4N
<Quotations>k__BackingField
gf154kuiPg
<ReceiptCreditors>k__BackingField
QHr5DeHuBa
<ReceiptDebitors>k__BackingField
zpA5souDas
<RemittanceIns>k__BackingField
NXj519hTKg
<RemittanceOuts>k__BackingField
rID56DZamq
<RiyalMobiles>k__BackingField
kr85f9LqP5
<RiyalMobiles1>k__BackingField
TIa5HonPdf
<SaleCurrencies>k__BackingField
xeB5BXY6gF
<SaleCurrencies1>k__BackingField
krc5ts4ZKu
<SaleInvoices>k__BackingField
DiE5xoWO64
<SaleInvoices1>k__BackingField
PVX55xZ6CZ
<SatellitePayments>k__BackingField
dG15r00ips
<SatelliteProviders>k__BackingField
oGn5Gs88bW
<Sims>k__BackingField
TJa5gkVGx7
<SimCardOrders>k__BackingField
bce5VJTC5F
<SimInvoices>k__BackingField
MZM5eecMIs
<SimInvoices1>k__BackingField
PKg5nhOCmj
<SimpleEntries>k__BackingField
iug58SV0Ns
<SimPurchases>k__BackingField
YGt5oBVrDH
<SimPurchases1>k__BackingField
rQl5Tqdp0F
<Subscribers>k__BackingField
IfV5PKrytB
<Suppliers>k__BackingField
XhW5RDE93n
<CashDeposits1>k__BackingField
UL35As1E6P
<CashWithdraws1>k__BackingField
nq25bDov8X
<Cheques1>k__BackingField
MfU5IV4wfS
<MerchantPayments1>k__BackingField
mRr5NkrN5B
<OrderSatelliteQuotas>k__BackingField
Jbc5kbpIwM
<OrderSatelliteQuotas1>k__BackingField
p6057HqvLk
<ReceiptCreditors1>k__BackingField
ih05c0UcSf
<ReceiptDebitors1>k__BackingField
Jo95i6EpGB
<CashIns1>k__BackingField
vJL5d9Alvx
<RemittanceIns1>k__BackingField
NQK5wxONLO
<RemittanceOuts1>k__BackingField
TLQ5ySQHM4
<SatellitePayments1>k__BackingField
WVH5hcbikn
<SimpleEntries1>k__BackingField
HRf5FXsgVB
<CashOuts1>k__BackingField
yN65lSIc1W
<CashTransfers1>k__BackingField
V8X5YfGX0h
<WifiPayments>k__BackingField
qvv5JkaCdO
<Topups>k__BackingField
Hvn5KP7VXF
<Topups1>k__BackingField
KHr595mpaF
<TopupCommissions>k__BackingField
kdc5OCSvuP
<TopupCommissions1>k__BackingField
UwY5UCDPG7
<TopupOrders>k__BackingField
NJq5EFd28r
<TopupProviders>k__BackingField
taX5SvRe1n
<TrailToupOrders>k__BackingField
HMV5p6i1LC
<TransferIns>k__BackingField
HPa5LUk0V5
<TransferOrders>k__BackingField
B265aZ3rBK
<TransferOuts>k__BackingField
CYT50mgGt1
<Transporters>k__BackingField
B1M5QbSW1S
<TransportOrders>k__BackingField
T6e53jaRuy
<WifiPayments1>k__BackingField
Gp25vTsq3Z
<WifiProviders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountApi
AppTech.MSMS.Domain.Models.AccountApi
AccountApi
AccountApi
yvu5Z6guKI
<ID>k__BackingField
Dsu5mEc9aq
<RowVersion>k__BackingField
Nkd5Cj70Ib
<Number>k__BackingField
egA5zDWvMd
<AccountId>k__BackingField
iQBr2sH8Fo
<UserId>k__BackingField
Tx0rXyRinA
<ApiKey>k__BackingField
PahrM1vUFk
<Token>k__BackingField
raPrjv80Ef
<GuidNumber>k__BackingField
Wk9ru7Py8A
<PublicKey>k__BackingField
WcCrqDyfjh
<PrivateKey>k__BackingField
DgerWwXVGM
<IpAddress>k__BackingField
iuOr4IVll7
<Protocol>k__BackingField
HAbrDUYenV
<Note>k__BackingField
CyErsBOd2N
<ExtraID>k__BackingField
pwYr1EuhyE
<BindID>k__BackingField
Abbr6wkMjU
<SyncID>k__BackingField
fOqrfVhAM7
<ParentID>k__BackingField
L9UrHYlVJM
<IsAllowed>k__BackingField
JxDrB23JEo
<CheckIp>k__BackingField
xGxrtGawXR
<Channel>k__BackingField
fNSrxaogQW
<Port>k__BackingField
tfDr5vZrQF
<Permitted>k__BackingField
Cnsrr63KrL
<Primary>k__BackingField
K5BrG6sadq
<Binded>k__BackingField
QvvrgJgowA
<Active>k__BackingField
yU9rVnRE0s
<Synced>k__BackingField
A3drejmG5j
<Status>k__BackingField
w0orn0HZiD
<Type>k__BackingField
Vwfr8f3BCh
<Binding>k__BackingField
mGeroqNwse
<ExtraInfo>k__BackingField
FYRrTSq66P
<BranchID>k__BackingField
Xw1rPHPp52
<CreatedBy>k__BackingField
uedrRlf5QP
<CreatedTime>k__BackingField
uMQrAt9QNs
<Account>k__BackingField
yW4rbImSGF
<Branch>k__BackingField
rJJrI7Q7PA
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountDocument
AppTech.MSMS.Domain.Models.AccountDocument
AccountDocument
AccountDocument
EHKrNBGooa
<ID>k__BackingField
lM2rkcH2EV
<RowVersion>k__BackingField
cqvr7bvNL7
<AccountID>k__BackingField
ymLrclRXe0
<ImageName>k__BackingField
XMBriIOxf9
<Type>k__BackingField
f44rdivLsm
<Note>k__BackingField
lrdrwtFQO8
<BranchID>k__BackingField
QBBry3QAdR
<CreatedBy>k__BackingField
JHJrhTZTul
<CreatedTime>k__BackingField
dYTrF0mKWK
<Account>k__BackingField
xA8rlqQMsI
<Branch>k__BackingField
KaPrYomOir
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountLedger
AppTech.MSMS.Domain.Models.AccountLedger
AccountLedger
AccountLedger
VIYrJjCFH3
<ID>k__BackingField
xtqrKhuDqY
<Name>k__BackingField
p1Or97jZdd
<CrOrDr>k__BackingField
SsvrOS1ngr
<Refernce>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountNotification
AppTech.MSMS.Domain.Models.AccountNotification
AccountNotification
AccountNotification
zf7rUfTtFl
<ID>k__BackingField
q6trEUpe9G
<RowVersion>k__BackingField
LnrrSqLJy6
<Message>k__BackingField
OGVrpXilq1
<CreatedTime>k__BackingField
r40rLbxWj8
<AccountID>k__BackingField
X8Mra2M6iy
<Sent>k__BackingField
Gtrr03uQkO
<RealTime>k__BackingField
SFTrQjjrBf
<Title>k__BackingField
qROr3nf0gv
<Topic>k__BackingField
USWrvKpJka
<AccountState>k__BackingField
Kl2rZeRoTi
<GroupID>k__BackingField
Fctrm8eYol
<PriorityLevel>k__BackingField
e7PrCoJPkP
<Status>k__BackingField
ac0rzKOI07
<ResonseState>k__BackingField
ChPG2ownvG
<ResonseInfo>k__BackingField
wciGXauGas
<Channels>k__BackingField
Q7dGM00J0a
<DispatchedBy>k__BackingField
Gi5GjOGP0u
<SentTime>k__BackingField
bRYGuSnIrk
<Channel>k__BackingField
JE5GqNpiJb
<ServiceID>k__BackingField
TTbGWTrRje
<Dispatcher>k__BackingField
iGNG4EP9CR
<IsSynce>k__BackingField
wLbGDwqe0X
<Direct>k__BackingField
KeGGsF55Pe
<Deleted>k__BackingField
yZaG1A1ePH
<Seen>k__BackingField
tytG6w0rRc
<IsScheduled>k__BackingField
WkTGfTbfbW
<IncludeSms>k__BackingField
gICGHPpHp8
<ScheduleTime>k__BackingField
cn5GB49orS
<Note>k__BackingField
t2YGtUpnhy
<ExtraInfo>k__BackingField
zg6Gx8yv5e
<ExtraID>k__BackingField
fy3G5pyqqS
<CreatedBy>k__BackingField
soiGr80Ykp
<BranchID>k__BackingField
ggtGGTynFY
<Account>k__BackingField
NHhGg5PQOi
<AccountGroupID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountRegion
AppTech.MSMS.Domain.Models.AccountRegion
AccountRegion
AccountRegion
b1OGV1KHBt
<ID>k__BackingField
W4bGesVUsn
<RowVersion>k__BackingField
QCiGnYNQFy
<Number>k__BackingField
dlrG8yrNpI
<AccountID>k__BackingField
sFIGo85Err
<RegionID>k__BackingField
eBdGTtuxZ8
<BranchID>k__BackingField
D41GPMegJR
<CreatedBy>k__BackingField
BiWGRP7XJ4
<CreatedTime>k__BackingField
G68GAwD0xs
<Account>k__BackingField
ImvGbi49lm
<Branch>k__BackingField
Bv4GIdNXRs
<Region>k__BackingField
WgUGNeyK2o
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountSlating
AppTech.MSMS.Domain.Models.AccountSlating
AccountSlating
AccountSlating
BAgGkEjXkl
<ID>k__BackingField
UOnG7DfScf
<RowVersion>k__BackingField
qIuGccKj2F
<AccountID>k__BackingField
RijGiS0Fwj
<Amount>k__BackingField
YsnGdF7p8J
<Note>k__BackingField
DxVGwIlXIn
<CreatedBy>k__BackingField
krLGy8LR3O
<BranchID>k__BackingField
vSwGh3r49D
<CreatedTime>k__BackingField
nQqGFa2GCh
<CurrencyID>k__BackingField
kRBGlcRmQG
<Account>k__BackingField
fIgGYVCpFb
<Branch>k__BackingField
d5ZGJ7BhGj
<Currency>k__BackingField
oKDGKFbcpi
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountUser
AppTech.MSMS.Domain.Models.AccountUser
AccountUser
AccountUser
xYoG99humY
<ID>k__BackingField
CL3GOFWeDI
<RowVersion>k__BackingField
tvTGUUDvJB
<RoleID>k__BackingField
P8IGEXrHdp
<ParentID>k__BackingField
YXTGScBjXJ
<UserID>k__BackingField
WuYGpttYAK
<PrimaryUser>k__BackingField
g3aGLrg4CB
<DeviceID>k__BackingField
u9sGa4VXbf
<CreatedBy>k__BackingField
SCPG03kF5i
<BranchID>k__BackingField
OwSGQ5L2Vr
<CreatedTime>k__BackingField
OU4G3t41Q2
<AccountID>k__BackingField
pqBGv6Aypn
<Account>k__BackingField
iXWGZg5r8j
<UserInfo>k__BackingField
urwGm38uBZ
<Branch>k__BackingField
eYYGCIyuff
<RoleInfo>k__BackingField
kaRGz57aCA
<UserInfo1>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Address
AppTech.MSMS.Domain.Models.Address
Address
Address
W0Hg2q9HUm
<ID>k__BackingField
rjHgXmGowB
<Name>k__BackingField
yAEgMIoIug
<Street>k__BackingField
SwVgjQSYnD
<City>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AdminNotification
AppTech.MSMS.Domain.Models.AdminNotification
AdminNotification
AdminNotification
TbEgunRlmY
<ID>k__BackingField
OfKgqBuLgt
<RowVersion>k__BackingField
uhagW4vjBN
<Message>k__BackingField
ommg4mdDyI
<Type>k__BackingField
DJwgD0kRcT
<Seen>k__BackingField
jG7gsXAKhG
<BranchID>k__BackingField
dHNg1FyJon
<CreatedBy>k__BackingField
Ol0g6wY9sc
<CreatedTime>k__BackingField
MCAgffK9mZ
<Branch>k__BackingField
SqsgHCIdJH
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Agent
AppTech.MSMS.Domain.Models.Agent
Agent
Agent
fC6gBcuZUY
<ID>k__BackingField
LRNgtZx49q
<RowVersion>k__BackingField
dHcgxVRPsD
<Number>k__BackingField
xwig56BeUN
<Name>k__BackingField
x0bgrScNN6
<AccountID>k__BackingField
dhqgGiW8JF
<PhoneNumber>k__BackingField
bsQggRoaGN
<ContactNumber>k__BackingField
etBgVysr0J
<Address>k__BackingField
WJhge31YU3
<Note>k__BackingField
wlsgnbyfqC
<Email>k__BackingField
fjjg8fZHVl
<CardType>k__BackingField
RvjgoOhphi
<CardNumber>k__BackingField
oaogTqHlGt
<CardIssuePlace>k__BackingField
fh5gPxPvJS
<CardIssueDate>k__BackingField
olRgR4Z0jy
<ImageName>k__BackingField
SO2gAOwNTj
<CreatedBy>k__BackingField
GGUgbQRoiF
<BranchID>k__BackingField
QIWgI841Eo
<CreatedTime>k__BackingField
SuIgNJGIxt
<Type>k__BackingField
GJSgkpZh3g
<Status>k__BackingField
TdOg7gTwM1
<SyncAccountID>k__BackingField
atMgcnqVqk
<RefNumber>k__BackingField
C5wgiWg153
<Extra>k__BackingField
yRvgdC6LaW
<Account>k__BackingField
E7Sgw6Y3Zm
<Branch>k__BackingField
U3hgy8Fb1r
<UserInfo>k__BackingField
dW1ghKXCqd
<AgentPoints>k__BackingField
dwVgFZqCuM
<CashDeposits>k__BackingField
xlvgluvVvJ
<CashWithdraws>k__BackingField
dxogY3pUR0
<Clients>k__BackingField
SGggJJqZDX
<Distributors>k__BackingField
PlYgKp9esa
<RemittanceIns>k__BackingField
xN4g9dnkQ9
<RemittanceOuts>k__BackingField
SykgOGUq4n
<RiyalMobiles>k__BackingField
Vv0gUq8PRm
<Topups>k__BackingField
YySgEUZ93m
<Username>k__BackingField
rZYgSgm8EY
<Password>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AgentPoint
AppTech.MSMS.Domain.Models.AgentPoint
AgentPoint
AgentPoint
RBxgpo3Yie
<ID>k__BackingField
rVWgLmuTN5
<RowVersion>k__BackingField
uXngaGEisu
<AgentID>k__BackingField
ojlg0mDm12
<RemittancePointID>k__BackingField
YUegQjGxdf
<Primary>k__BackingField
CrWg3aiEkl
<CreatedBy>k__BackingField
qGGgvgCGLQ
<BranchID>k__BackingField
F47gZAjmE8
<CreatedTime>k__BackingField
a0igmyISjP
<Agent>k__BackingField
nCFgCFas5p
<Branch>k__BackingField
xC9gzODJvH
<RemittancePoint>k__BackingField
nWCV24VyF3
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AgentPointUser
AppTech.MSMS.Domain.Models.AgentPointUser
AgentPointUser
AgentPointUser
MHJVX6lexe
<ID>k__BackingField
QTZVMjvCw0
<RowVersion>k__BackingField
hIhVjIV1aR
<UserID>k__BackingField
otjVuwQhAL
<RemittancePointID>k__BackingField
qbDVqDdCUC
<CreatedBy>k__BackingField
VK9VW8mgXv
<BranchID>k__BackingField
DyjV44In37
<CreatedTime>k__BackingField
nFuVDuafWs
<Branch>k__BackingField
REZVsCwhuW
<RemittancePoint>k__BackingField
nrsV1CfZFb
<UserInfo>k__BackingField
z8kV6I3vko
<UserInfo1>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AuditLog
AppTech.MSMS.Domain.Models.AuditLog
AuditLog
AuditLog
OSpVfgkQCB
<ID>k__BackingField
TyeVHuyvpj
<UserID>k__BackingField
SLvVBS7AMI
<PageName>k__BackingField
OvfVtt4QNv
<Action>k__BackingField
A2LVx7vLv8
<AduitDate>k__BackingField
QFqV5b4ZNj
<TableName>k__BackingField
yQCVrsV8Hg
<RecordId>k__BackingField
uyiVGnQqLo
<OriginalData>k__BackingField
usNVgaCFrK
<NewData>k__BackingField
TiUVVj07AO
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Bagat
AppTech.MSMS.Domain.Models.Bagat
Bagat
Bagat
IGQVe9lxum
<ID>k__BackingField
GPUVnAcnE0
<RowVersion>k__BackingField
HLWV81W8hO
<Name>k__BackingField
eExVoPnQFH
<Code>k__BackingField
KG1VTYvIpJ
<LineType>k__BackingField
GIZVPJEmq5
<Mode>k__BackingField
zQmVRwFYEG
<Price>k__BackingField
AEYVAfsqDS
<CreatedBy>k__BackingField
tLhVbfoQW9
<BranchID>k__BackingField
M3UVI3iZ2s
<CreatedTime>k__BackingField
UECVNJUbvV
<SimType>k__BackingField
pbmVkcBcWs
<ServiceID>k__BackingField
at8V7PKWGa
<TelPrice>k__BackingField
mybVcuZQEe
<ProviderPrice>k__BackingField
jWNViCVqo4
<PersonnalPrice>k__BackingField
YtmVdYd6JK
<OrderNo>k__BackingField
KTtVwqDBDd
<Number>k__BackingField
XxDVyTMhC7
<Quantity>k__BackingField
yI2Vhl6ABs
<Units>k__BackingField
KjjVFcc3SI
<Active>k__BackingField
YbeVlWMPuH
<Balance>k__BackingField
hFWVY1lxNV
<Command>k__BackingField
slkVJHv95c
<Description>k__BackingField
e0VVK8cyfJ
<CategoryID>k__BackingField
kMAV9UE2ni
<ProviderID>k__BackingField
tS3VOLsSrC
<ByProvider>k__BackingField
whRVUDCwAn
<Branch>k__BackingField
nO4VEyUXMo
<ServiceInfo>k__BackingField
vjoVSWHup9
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BagatPayment
AppTech.MSMS.Domain.Models.BagatPayment
BagatPayment
BagatPayment
c4EVpBJWrC
<ID>k__BackingField
OGUVLcTqlZ
<RowVersion>k__BackingField
kYRVaCXqb5
<SubscriberNumber>k__BackingField
Lr1V0FJiYd
<LineType>k__BackingField
VYhVQIeZVi
<SimType>k__BackingField
eT0V32eZaE
<Date>k__BackingField
GjaVvt8DhZ
<Note>k__BackingField
imQVZP2PVL
<Channel>k__BackingField
hEBVmUFiV4
<ServiceEntryID>k__BackingField
kSPVCBwxpJ
<CreatedBy>k__BackingField
Vo1VzLkjaY
<BranchID>k__BackingField
D4pe2U7HdB
<CreatedTime>k__BackingField
JVseXhXgL1
<IncludePay>k__BackingField
rSJeM0n9Fd
<ActionType>k__BackingField
eOgejy36vo
<OfferID>k__BackingField
DGjeu2s9cY
<ServiceID>k__BackingField
TcSeqipX3b
<ProviderID>k__BackingField
qyDeWhcVTC
<Amount>k__BackingField
oyDe4bOhGi
<HasLoan>k__BackingField
tlheDoUOwq
<OfferCode>k__BackingField
CNfesV7UbI
<OfferName>k__BackingField
S7Fe1GVmlb
<OfferAction>k__BackingField
DXGe6ZgxSm
<AccountID>k__BackingField
dUSefNmwhs
<Number>k__BackingField
LEZeHHCI3r
<RefNumber>k__BackingField
yBreBE396I
<TransactionID>k__BackingField
J9aetGH9fs
<ProviderRM>k__BackingField
UjKex9ENZi
<ProviderPrice>k__BackingField
snPe5k6Esm
<SubNote>k__BackingField
sLYerdd0K4
<Datestamb>k__BackingField
Ms5eGiMl7E
<Status>k__BackingField
yaxegt9Qkr
<EntryID>k__BackingField
cDaeVO8XNc
<UniqueNo>k__BackingField
VwYee290js
<Quantity>k__BackingField
f5henPEj1j
<UnitPrice>k__BackingField
saGe8Do67q
<UnitCost>k__BackingField
HTpeoDvBYP
<CostAmount>k__BackingField
siIeTU6j67
<DifferentialAmount>k__BackingField
YN0ePFFKF9
<CommissionAmount>k__BackingField
e5NeRTWiMp
<Discount>k__BackingField
EXleAelb6o
<TotalCost>k__BackingField
kvfebIUJW9
<TotalAmount>k__BackingField
LTNeIZgDkF
<Profits>k__BackingField
UPWeN0yaaC
<ExCode>k__BackingField
PI8ekuDxub
<RequestInfo>k__BackingField
G1Ne7eZCCx
<ResponseTime>k__BackingField
FiPecVbBlQ
<ResponseStatus>k__BackingField
lPeeinSnkE
<ExecutionPeroid>k__BackingField
hU2edo2VvX
<Debited>k__BackingField
DPEewpSN6o
<StateClass>k__BackingField
PdeeyREJpJ
<BillState>k__BackingField
wL1ehSd6ay
<State>k__BackingField
MU1eF0HmSV
<ByChild>k__BackingField
GvBelHlH8v
<ParentAccountID>k__BackingField
p2PeYBYZqi
<TransNumber>k__BackingField
vc2eJVLgMV
<Identifier>k__BackingField
t5keKVbhrn
<AdminNote>k__BackingField
QCDe9CF4el
<Cured>k__BackingField
GOPeO8muXt
<CuredBy>k__BackingField
pUneUfmD0U
<CuredInfo>k__BackingField
oZFeE3sgOa
<InspectInfo>k__BackingField
Iw4eSBSWO8
<Flag>k__BackingField
Xpreps1Vom
<Channeling>k__BackingField
QnkeLJ8YKM
<Account>k__BackingField
kVceajQCQl
<Branch>k__BackingField
liee0mtwbJ
<UserInfo>k__BackingField
W74eQZMKiw
<Offers>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Bank
AppTech.MSMS.Domain.Models.Bank
Bank
Bank
qoXe3wZNkq
<ID>k__BackingField
fB1evfsucx
<Name>k__BackingField
Oc8eZFYT1p
<AccountID>k__BackingField
ne8emb14Tn
<Note>k__BackingField
k8CeCq4gxY
<CreatedBy>k__BackingField
PI4ezyUqHM
<CreatedTime>k__BackingField
n64n292vUI
<RowVersion>k__BackingField
i9ynXZVLrT
<BranchID>k__BackingField
ci1nMf90Tj
<Account>k__BackingField
uKCnjrWYd1
<Branch>k__BackingField
UT1nuxJGnF
<UserInfo>k__BackingField
NUZnqAihic
<Cheques>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BankDeposit
AppTech.MSMS.Domain.Models.BankDeposit
BankDeposit
BankDeposit
WlYnWEwYbJ
<ID>k__BackingField
nnWn4nqQIP
<RowVersion>k__BackingField
PJwnDAVucp
<ServiceID>k__BackingField
Uh9nscjo9N
<AccountID>k__BackingField
VNXn1kxkCd
<Amount>k__BackingField
IIKn6rBXtG
<CurrencyID>k__BackingField
HX8nffD4Nl
<CountryID>k__BackingField
QqcnHaGaub
<ProvinceID>k__BackingField
EnsnB6RHJs
<BankName>k__BackingField
gUKntXj2VS
<ExchangeCurrencyID>k__BackingField
vOenxlWCm6
<SourceName>k__BackingField
I1Qn5Mvh5T
<TargetName>k__BackingField
BUenr5l42i
<InvoiceNo>k__BackingField
DYHnGIKcGN
<Note>k__BackingField
HhingQRQl7
<Channel>k__BackingField
mEpnVPOYK1
<ParentID>k__BackingField
EcGneunLb6
<Status>k__BackingField
CrlnnOi1Cg
<CreatedBy>k__BackingField
kSIn8nplbS
<BranchID>k__BackingField
MArno8QPqt
<CreatedTime>k__BackingField
lgvnTBAhBB
<RefNumber>k__BackingField
jTmnPpP2fd
<Extra1>k__BackingField
QQqnRtuhoR
<Extra2>k__BackingField
KtunAWvKP1
<Extra3>k__BackingField
g00nbvxhjL
<Extra4>k__BackingField
qLfnIwEhdt
<Account>k__BackingField
W4anNVNBPo
<Branch>k__BackingField
sldnkk0buZ
<Country>k__BackingField
Jnon7oJPdN
<Currency>k__BackingField
vwJncU2m0R
<Currency1>k__BackingField
QWHnieJskN
<OrderInfo>k__BackingField
PSRndZ9ALI
<Province>k__BackingField
JQunwprmRn
<ServiceInfo>k__BackingField
kAhnyuN7nD
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Branch
AppTech.MSMS.Domain.Models.Branch
Branch
Branch
PtZnhHaBN2
<ID>k__BackingField
prCnFjLCcP
<RowVersion>k__BackingField
Nlinl9as8T
<Number>k__BackingField
Vf0nYFqgba
<Name>k__BackingField
Ti6nJNPZ6Q
<AccountID>k__BackingField
ccOnKyjgyr
<Note>k__BackingField
ylBn9gaMGc
<EnglishName>k__BackingField
kRVnOGQePj
<Phone>k__BackingField
srGnUC3KVS
<Fax>k__BackingField
rlcnE25G2D
<Address>k__BackingField
xZcnSbX3g6
<CreatedBy>k__BackingField
xS7npNlTVm
<BranchID>k__BackingField
Wx4nLjNWoK
<CreatedTime>k__BackingField
keAnaG3mfG
<Accounts>k__BackingField
Ghbn01Y4Il
<AccountApis>k__BackingField
bSynQmO9VE
<AccountDocuments>k__BackingField
JfKn3TdXGy
<AccountRegions>k__BackingField
HoFnv4f4Gk
<AccountSlatings>k__BackingField
ITGnZA0XU9
<AccountUsers>k__BackingField
VxRnmic8ne
<AdminNotifications>k__BackingField
wAcnCotNcD
<Agents>k__BackingField
T9ynze8qBL
<AgentPoints>k__BackingField
dD582ExKTt
<AgentPointUsers>k__BackingField
Imk8XO9n5V
<Bagats>k__BackingField
FXP8MGsgd7
<BagatPayments>k__BackingField
p9t8joJo8U
<Banks>k__BackingField
u928uF0GPC
<BankDeposits>k__BackingField
BqG8qXBXdU
<Branch1>k__BackingField
h9d8WN96P6
<Branch2>k__BackingField
SCc84r4jgR
<BranchTargets>k__BackingField
oLh8DHMuIB
<Brochures>k__BackingField
Dlq8sour87
<BuyCurrencies>k__BackingField
InA81IRbhD
<Cards>k__BackingField
I0u86Nu90r
<CardFactions>k__BackingField
LeJ8fdmYsv
<CardOrders>k__BackingField
sJf8HXaEaZ
<CardPayments>k__BackingField
d9Q8Bc1QXq
<CardTypes>k__BackingField
J5x8tmqiyg
<CashDeposits>k__BackingField
Xi88xZvL8N
<CashIns>k__BackingField
T4R85RpAyi
<CashOuts>k__BackingField
vHG8ryI6VI
<CashWithdraws>k__BackingField
dFB8G7PvdM
<Cheques>k__BackingField
oBl8gsQpqV
<ClaimGroups>k__BackingField
qnJ8VE3cQC
<Clients>k__BackingField
qb38eD0OdW
<ServiceClaims>k__BackingField
jds8n8NYQy
<SMSDispatches>k__BackingField
WF788yEQ8A
<CashTransfers>k__BackingField
bBP8o6JJW3
<CommissionReceipts>k__BackingField
yco8T1Ht8d
<ConsumeInvoices>k__BackingField
UFY8PaPbXu
<Countries>k__BackingField
nOF8RbgdaI
<CoverageOrders>k__BackingField
PcL8AqcPno
<Currencies>k__BackingField
Hou8btsWUj
<CurrencyExchanges>k__BackingField
Ag08I0fYKi
<CurrencyRates>k__BackingField
CZk8Nae9Zm
<CurrencyRateAccounts>k__BackingField
AcM8kXuO8a
<DbBackups>k__BackingField
Brg87UDIM5
<DepositOrders>k__BackingField
y9G8ceKikm
<Devices>k__BackingField
zSr8ilbskr
<Distributors>k__BackingField
TYs8dB3wo2
<Exchangers>k__BackingField
zBs8w6YvBO
<ExchangerTargets>k__BackingField
m0t8yKFhxL
<ExternalBranches>k__BackingField
FOf8hWgUy5
<Factions>k__BackingField
AJj8FGEVA1
<Feedbacks>k__BackingField
CMA8lmvUGf
<Funds>k__BackingField
WKW8YsfOOE
<FundUsers>k__BackingField
tXY8JDnDOo
<GeneralInfoes>k__BackingField
zXG8Kdau8s
<GroupItems>k__BackingField
HDn89V3CpG
<Gsms>k__BackingField
laU8OfvkQG
<Instructions>k__BackingField
ze88UdbeAI
<Items>k__BackingField
iZK8EaORvu
<ItemCosts>k__BackingField
U858S3U75Y
<Journals>k__BackingField
IAZ8p9YKN1
<LiveTopups>k__BackingField
LJo8LmPZhM
<LoanOrders>k__BackingField
HB18aJt9dg
<Merchants>k__BackingField
yld802QULc
<MerchantCategories>k__BackingField
Ywu8Q0ivXo
<MerchantPayments>k__BackingField
vF783VrZ8N
<MoneyMasters>k__BackingField
TV58v1nEas
<OfferOrders>k__BackingField
RrQ8ZGgq4Y
<OpeningBalances>k__BackingField
FvH8mYbSa7
<OrderInfoes>k__BackingField
dpF8CCsf79
<OrderSatelliteQuotas>k__BackingField
BrI8zGOVLb
<Parties>k__BackingField
kDYo2p1IxL
<PartyGroups>k__BackingField
hyloXJyNJJ
<PaymentCommissions>k__BackingField
JB8oMZGQtG
<Payments>k__BackingField
y6Zoj8inKf
<People>k__BackingField
zPZouJ1ByZ
<Products>k__BackingField
IV6oqpNZiG
<ProductCategories>k__BackingField
MJQoWef2Sy
<ProductImages>k__BackingField
q9wo45LGh5
<Provinces>k__BackingField
MPBoD3Au5o
<PurchaseInvoices>k__BackingField
LEeos4ZemB
<PurchaseInvoiceLines>k__BackingField
A0Wo1QskHh
<Quotations>k__BackingField
bh2o6EpppR
<ReceiptCreditors>k__BackingField
wroofooqG3
<ReceiptDebitors>k__BackingField
cHuoHKtVEF
<Regions>k__BackingField
RoRoBuxlEq
<RemittanceCommissions>k__BackingField
uN0otTEks7
<RemittanceIns>k__BackingField
oEcoxjQJtS
<RemittanceOuts>k__BackingField
sYTo50jexA
<RemittancePoints>k__BackingField
w3Jor82jDy
<RemittanceRegions>k__BackingField
raboGnZJdR
<RiyalMobiles>k__BackingField
akhogiGEpF
<RSSes>k__BackingField
JqcoVMqPwS
<SaleCurrencies>k__BackingField
zoIoeQMbJT
<SaleInvoices>k__BackingField
PaaonpdXwH
<SaleInvoiceLines>k__BackingField
puQo8aEtyv
<SatelliteFactions>k__BackingField
ra6oowgSuw
<SatellitePayments>k__BackingField
C87oTEpPYc
<SatelliteProviders>k__BackingField
kFWoPSlBDQ
<ServiceInfoes>k__BackingField
gCUoR5jQrE
<Sims>k__BackingField
jGjoAIcUEE
<SimCardOrders>k__BackingField
HbjobS9RUF
<SimInvoices>k__BackingField
DJNoITiZG8
<SimpleEntries>k__BackingField
nmCoNIoF9C
<SimPurchases>k__BackingField
s8fokbkexJ
<SMSMessages>k__BackingField
Boco78Rimq
<SpecialSims>k__BackingField
RGtocZVxVG
<Subscribers>k__BackingField
cCdoioQ94W
<Suppliers>k__BackingField
U0UodcC62e
<Topups>k__BackingField
KWqowuwnpT
<TopupCommissions>k__BackingField
te4oyxL0er
<TopupOrders>k__BackingField
LNoohLcN8C
<TopupProviders>k__BackingField
EFVoFFEAZg
<TrailToupCommissions>k__BackingField
um2olwQCqx
<TrailToupOrders>k__BackingField
Q3FoYNBgh2
<TransferIns>k__BackingField
mP6oJD4iPt
<TransferOrders>k__BackingField
qfToKPbWuM
<TransferOuts>k__BackingField
yOvo9CyIJH
<Transporters>k__BackingField
fv0oO1OCNV
<TransportOrders>k__BackingField
IBvoU3ZMDZ
<UserDevices>k__BackingField
fIToEm9OKp
<UserInfoes>k__BackingField
TE4oSxy3EK
<Vouchers>k__BackingField
DtvopoVPIp
<WERegions>k__BackingField
eJKoLgkPSy
<WifiCards>k__BackingField
h4Zoat1OYL
<WifiFactions>k__BackingField
R7Oo0Ts8Qj
<WifiPayments>k__BackingField
hrsoQuECK3
<WifiProviders>k__BackingField
JTao3eihpu
<WithdrawOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BranchTarget
AppTech.MSMS.Domain.Models.BranchTarget
BranchTarget
BranchTarget
w5DovT0Z53
<ID>k__BackingField
EpAoZQrfq2
<RowVersion>k__BackingField
wXqomiP7Ua
<BranchPintID>k__BackingField
a5roCHgUN5
<RemittancePointID>k__BackingField
bbRozcTLHp
<Primary>k__BackingField
eY7T2sbvqn
<CreatedBy>k__BackingField
bxUTXhF0QO
<BranchID>k__BackingField
SAETMSHGyi
<CreatedTime>k__BackingField
HfVTjJynpl
<Branch>k__BackingField
qbLTuFoB4U
<RemittancePoint>k__BackingField
HMTTqwIny8
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Brochure
AppTech.MSMS.Domain.Models.Brochure
Brochure
Brochure
T1OTWOsHSJ
<ID>k__BackingField
lSET4LYGMP
<RowVersion>k__BackingField
hhdTDAx0wm
<ImageUrl>k__BackingField
JbmTsEwh1R
<Description>k__BackingField
wf0T10NPcy
<Channel>k__BackingField
oUxT6HFeqv
<BranchID>k__BackingField
fPQTfBF0tn
<CreatedBy>k__BackingField
OCiTHCTSBF
<CreatedTime>k__BackingField
GT4TB1sFvp
<Branch>k__BackingField
Yn6TtAeywe
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Bundle
AppTech.MSMS.Domain.Models.Bundle
Bundle
Bundle
g6fTxJpbaO
<ID>k__BackingField
ycWT5nsbjY
<ProviderNumber>k__BackingField
NypTrvUYhq
<FactionID>k__BackingField
h6ZTGifbrC
<FactionNumber>k__BackingField
W2lTg1jsDu
<ServiceID>k__BackingField
FAuTVZoDf6
<Code>k__BackingField
tfwTeR7xU1
<ExtraCode>k__BackingField
x95TnEpt27
<CostPrice>k__BackingField
mlET8nVlU1
<Type>k__BackingField
dqBTojABIf
<BindID>k__BackingField
IK8TTfeYTm
<Active>k__BackingField
L8jTPRwwWp
<Flag>k__BackingField
cfdTRaXnMT
<ProviderID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BuyCurrency
AppTech.MSMS.Domain.Models.BuyCurrency
BuyCurrency
BuyCurrency
UyETAlWH7P
<ID>k__BackingField
cPnTbp423q
<RowVersion>k__BackingField
pm3TIsvFjS
<Number>k__BackingField
klgTNCueFV
<Amount>k__BackingField
hCbTkwdUNH
<CurrencyID>k__BackingField
uRLT7elnTH
<ExchangePrice>k__BackingField
Or0TcFpB17
<ExchangeAmount>k__BackingField
J03TiPTcMp
<ExchangeCurrencyID>k__BackingField
i00TdnjtFU
<CreditorAccountID>k__BackingField
cVYTwStHKq
<DebitorAccountID>k__BackingField
MpBTyvte7o
<Date>k__BackingField
QpEThfN8Ct
<Note>k__BackingField
lQsTFVglBE
<EntryID>k__BackingField
nBwTludpdR
<Year>k__BackingField
SBYTYNBe0L
<Status>k__BackingField
ziETJJQa4C
<Channel>k__BackingField
kZqTKwmOn0
<CreatedTime>k__BackingField
RTTT9yOOMr
<CreatedBy>k__BackingField
lxuTOBXrpq
<BranchID>k__BackingField
XDLTUFFUKV
<RefNumber>k__BackingField
I5sTEwohtb
<CreatedDate>k__BackingField
HnUTS1qDTx
<HourTime>k__BackingField
q5qTpoKge0
<MinuteTime>k__BackingField
jM1TL9lxTc
<TransNumber>k__BackingField
S5yTac7be4
<Attachments>k__BackingField
hebT03AHKU
<ExtraInfo>k__BackingField
lAdTQB2bqX
<ExtraID>k__BackingField
EquT3DjtUA
<SyncID>k__BackingField
g6BTvLT5No
<RefID>k__BackingField
HyTTZh8A9Y
<BindID>k__BackingField
GLwTmHqMie
<Binded>k__BackingField
BpKTC7Wg07
<OrderID>k__BackingField
mlXTz716rk
<ByOrder>k__BackingField
tY4P2MIBZ1
<Account>k__BackingField
Ik3PXgg98R
<Account1>k__BackingField
BTvPM0lRog
<Branch>k__BackingField
o96Pjr97MI
<Currency>k__BackingField
n0nPuOFRdZ
<Currency1>k__BackingField
K6iPqliTDL
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Card
AppTech.MSMS.Domain.Models.Card
Card
Card
QSEPWQPx2m
<ID>k__BackingField
xiPP4wPR2h
<RowVersion>k__BackingField
WAoPDm5ZNU
<Number>k__BackingField
VqPPsGc17u
<Name>k__BackingField
AA4P1XOP6Q
<Password>k__BackingField
vrdP6XC280
<Description>k__BackingField
ysLPf3HAXE
<SerialNo>k__BackingField
c5LPHkykv3
<CardTypeID>k__BackingField
R9fPBjDQCF
<CardFactionID>k__BackingField
zLIPtryeFF
<Status>k__BackingField
RFbPx19lXc
<Note>k__BackingField
GUeP5fssXm
<Quantity>k__BackingField
JvGPrfrYbq
<Active>k__BackingField
ploPG8uwHG
<BranchID>k__BackingField
wb3PgAN4qH
<CreatedBy>k__BackingField
bqwPVwUvoM
<CreatedTime>k__BackingField
mRuPeLMgBX
<Branch>k__BackingField
RqXPnbDm13
<CardFaction>k__BackingField
WkJP83mXXK
<CardType>k__BackingField
nD6PoIuGJG
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardFaction
AppTech.MSMS.Domain.Models.CardFaction
CardFaction
CardFaction
jMGPTV864W
<ID>k__BackingField
KnkPPWhpWs
<RowVersion>k__BackingField
GsFPRdPN08
<Number>k__BackingField
flsPASu0NL
<Name>k__BackingField
RY1PbPKjMD
<CostPrice>k__BackingField
q8kPIbJAy9
<SelePrice>k__BackingField
RIyPNCsB9Y
<CardTypeID>k__BackingField
ortPkv06jv
<Active>k__BackingField
kbJP78feC2
<Note>k__BackingField
wg1PcAKBeH
<OrderNo>k__BackingField
PXvPipYXgm
<Description>k__BackingField
PLLPdlD9v3
<ProviderID>k__BackingField
CEwPw9VkFI
<Status>k__BackingField
msZPygfcUp
<BranchID>k__BackingField
NT5PhFNqBa
<CreatedBy>k__BackingField
GJ3PFt5Ok6
<CreatedTime>k__BackingField
PaiPlH2vr8
<Branch>k__BackingField
HHBPYF5Mx8
<Cards>k__BackingField
kBlPJ9SjnU
<CardType>k__BackingField
PEIPKELNcS
<UserInfo>k__BackingField
AP0P9XJWYt
<CardOrders>k__BackingField
uEmPO6cL4A
<CardPayments>k__BackingField
iOkPUdcC0y
<cardAll>k__BackingField
HmqPEfWHMu
<cardNotPayed>k__BackingField
yO7PS5OjfH
<cardPayed>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardOrder
AppTech.MSMS.Domain.Models.CardOrder
CardOrder
CardOrder
I7DPpOEEcr
<ID>k__BackingField
LTGPLbNhIk
<RowVersion>k__BackingField
s9gPapucAE
<Number>k__BackingField
IT0P0sO46O
<CardFactionID>k__BackingField
s2LPQosOYS
<CardTypeID>k__BackingField
gLWP3Kg5Aq
<Amount>k__BackingField
wxXPvwoUfS
<CurrencyID>k__BackingField
w0cPZuLoy5
<Date>k__BackingField
gQKPmfpiia
<Note>k__BackingField
DNsPCZZdhH
<UserID>k__BackingField
uOSPzVMe5A
<Username>k__BackingField
SVPR2xbVGm
<Password>k__BackingField
vf4RXecAnP
<Email>k__BackingField
V3YRMMbWPl
<Phone>k__BackingField
pXURjgOXSf
<Channel>k__BackingField
bRHRuTbuI2
<ParentID>k__BackingField
CpBRqsmYYy
<ServiceID>k__BackingField
A3KRWrHYP9
<AccountID>k__BackingField
CfGR4V9nxr
<Status>k__BackingField
wIXRDypPkX
<BranchID>k__BackingField
amtRsyMkZG
<CreatedBy>k__BackingField
rQhR1J1auW
<CreatedTime>k__BackingField
sKFR6GKeb9
<Account>k__BackingField
gbPRfKutkM
<Branch>k__BackingField
NtRRHCPwxd
<CardFaction>k__BackingField
uRQRBOrMX0
<CardType>k__BackingField
kv4RtF08qn
<Currency>k__BackingField
fxJRx1AOT2
<OrderInfo>k__BackingField
X3WR5jshi8
<ServiceInfo>k__BackingField
JkJRrxFwSD
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardPayment
AppTech.MSMS.Domain.Models.CardPayment
CardPayment
CardPayment
c2YRGDH5Lm
<ID>k__BackingField
tZFRgVbRq8
<RowVersion>k__BackingField
mvSRVvH6Z1
<Number>k__BackingField
jI4ReZt4tt
<CardFactionID>k__BackingField
PKRRnSBDmC
<CardTypeID>k__BackingField
D3uR8FOhiX
<Amount>k__BackingField
ryYRoKr38N
<CurrencyID>k__BackingField
lGiRTK165u
<Date>k__BackingField
QOfRPFi1WZ
<Note>k__BackingField
S84RR0b6xe
<Username>k__BackingField
MDgRAULt1P
<Password>k__BackingField
iv9RbHHKqH
<Email>k__BackingField
XoVRI6a85M
<Phone>k__BackingField
Hr4RNGuJbH
<Channel>k__BackingField
XKgRkGxquE
<EntryID>k__BackingField
kIdR7NJvox
<CreditorAccountID>k__BackingField
XiiRcC6ZI2
<DebitorAccountID>k__BackingField
vWoRicBCtR
<Year>k__BackingField
ItnRd8PQH8
<ParentID>k__BackingField
fvDRwUgJ7b
<ServiceID>k__BackingField
xtoRysHwlD
<AccountID>k__BackingField
O8JRh25ImC
<Status>k__BackingField
VX6RFHaJig
<BranchID>k__BackingField
cBqRlqbJ3V
<CreatedBy>k__BackingField
nJDRYeoseP
<CreatedTime>k__BackingField
AT1RJc9enL
<Account>k__BackingField
bbYRK4lFDG
<Branch>k__BackingField
iUnR9fS6jr
<CardFaction>k__BackingField
GbfROteUWi
<CardType>k__BackingField
kZLRUx0ZPe
<Currency>k__BackingField
z5vREnVEbF
<OrderInfo>k__BackingField
DVaRSumQu0
<ServiceInfo>k__BackingField
Xl2RpAOtky
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardType
AppTech.MSMS.Domain.Models.CardType
CardType
CardType
wK1RLFLvq6
<ID>k__BackingField
M3FRajiF3S
<RowVersion>k__BackingField
LK7R0AH2WU
<Number>k__BackingField
RHaRQFiit6
<Name>k__BackingField
EOGR3p9jFA
<Image>k__BackingField
XjiRvIIp1c
<Active>k__BackingField
z0RRZMfYo0
<Note>k__BackingField
YBxRmrwB6A
<Description>k__BackingField
lqRRCgis3E
<Type>k__BackingField
Ra8RzAXtVP
<AccountID>k__BackingField
rBjA21VaRT
<BranchID>k__BackingField
MXkAXSbrLk
<CreatedBy>k__BackingField
gqfAMyeYTh
<CreatedTime>k__BackingField
UeSAjNac0G
<Account>k__BackingField
KILAuhkYNq
<Branch>k__BackingField
e83AqLbOYb
<Cards>k__BackingField
vnDAW0RtbC
<CardFactions>k__BackingField
XgsA4WqHPx
<CardOrders>k__BackingField
vVgAD9CQiF
<CardPayments>k__BackingField
mUtAsSWg1P
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashDeposit
AppTech.MSMS.Domain.Models.CashDeposit
CashDeposit
CashDeposit
aaGA1k2M6x
<ID>k__BackingField
RDSA6dwZGJ
<RowVersion>k__BackingField
GnpAfSW7uD
<Number>k__BackingField
txgAHrtSm1
<Date>k__BackingField
iJiABNwS3g
<Amount>k__BackingField
FhmAtUWqVm
<CurrencyID>k__BackingField
hIyAxdnROX
<CreditorAccountID>k__BackingField
vxUA5GFVlt
<DebitorAccountID>k__BackingField
ysEArH6uh1
<AgentID>k__BackingField
KEoAGvwZyg
<Note>k__BackingField
JpWAghaIVa
<Prints>k__BackingField
RmiAVeG1ZX
<EntryID>k__BackingField
He7AeUNG0T
<IsDebited>k__BackingField
VKeAneeLdM
<RefNumber>k__BackingField
F3VA8Jc9wG
<AttachmentNumbers>k__BackingField
CdpAojHNxD
<CreatedBy>k__BackingField
a5TATAEpmo
<BranchID>k__BackingField
YbGAPmrTGy
<CreatedTime>k__BackingField
TRSARSpw6Q
<Depositor>k__BackingField
zoaAAUVUjy
<Year>k__BackingField
wZbAbtJdwf
<Status>k__BackingField
t43AIIxWo5
<CreatedDate>k__BackingField
r29ANDdZaA
<HourTime>k__BackingField
vUTAkWm2wc
<MinuteTime>k__BackingField
HADA7ColjR
<TransNumber>k__BackingField
QVTAcKFJXC
<Channel>k__BackingField
NglAiC8eVv
<Attachments>k__BackingField
laRAda8MVW
<ExtraInfo>k__BackingField
RaCAwIAAvK
<ExtraID>k__BackingField
VgQAywmE86
<SyncID>k__BackingField
A5aAhuQf4C
<SyncEntryID>k__BackingField
MMFAFNuFD8
<RefID>k__BackingField
wv8AlkkubI
<BindID>k__BackingField
ItJAYskS3O
<Binded>k__BackingField
tXvAJEkRI0
<OrderID>k__BackingField
yNQAK8Z9Tv
<ByOrder>k__BackingField
z4SA94SFF1
<IsSync>k__BackingField
fiFAO2pvQN
<Type>k__BackingField
W5ZAUrgBbL
<Account>k__BackingField
r6UAEqO43M
<Account1>k__BackingField
h1UASx5NPm
<Agent>k__BackingField
EaTApiZ6A7
<Branch>k__BackingField
XunALmo3yP
<Currency>k__BackingField
m1QAa6Q4D4
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashIn
AppTech.MSMS.Domain.Models.CashIn
CashIn
CashIn
mrOA0u3tH1
<ID>k__BackingField
trHAQOYPFc
<RowVersion>k__BackingField
bBwA3aWuCu
<Number>k__BackingField
UvNAvgLySK
<Date>k__BackingField
A5pAZG0ufQ
<Method>k__BackingField
GLlAmhhoaw
<Amount>k__BackingField
vLFACNF104
<CurrencyID>k__BackingField
QO5AzJX7NH
<CreditorAccountID>k__BackingField
WxCb24vsv4
<DCAmount>k__BackingField
TTibXA7PRb
<DebitorAccountID>k__BackingField
WoGbM9gHr1
<Note>k__BackingField
RqcbjCVCnl
<Delivery>k__BackingField
Ta8bu0T7Sv
<ChequeID>k__BackingField
mrybqKdVML
<EntryID>k__BackingField
fSNbWeijSh
<RefNumber>k__BackingField
cFob4XTtm7
<AttachmentNumbers>k__BackingField
yB1bD1Eomx
<CreatedTime>k__BackingField
HwdbsY0AnQ
<CreatedBy>k__BackingField
IPHb1Kjrdm
<BranchID>k__BackingField
EA4b6ispG5
<Year>k__BackingField
mocbfFh84I
<Status>k__BackingField
lI3bHvBu3U
<CreatedDate>k__BackingField
VGJbBQV3GV
<HourTime>k__BackingField
Ef9btDxYrc
<MinuteTime>k__BackingField
LDSbxIjhIq
<TransNumber>k__BackingField
WZTb5XIEqN
<Channel>k__BackingField
saXbrTQ9F8
<Attachments>k__BackingField
NdmbGLyw28
<ExtraInfo>k__BackingField
whWbglG54U
<ExtraID>k__BackingField
X5kbVfjppC
<SyncID>k__BackingField
utxbeeUEyZ
<RefID>k__BackingField
d3ibnwcTZP
<BindID>k__BackingField
YWXb8sQawc
<Account>k__BackingField
f80boSMbbD
<Account1>k__BackingField
TjobTh04vm
<Branch>k__BackingField
TuAbPRIRYd
<Cheque>k__BackingField
WZ7bRiUYcs
<Currency>k__BackingField
kGNbAEp80D
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashOut
AppTech.MSMS.Domain.Models.CashOut
CashOut
CashOut
n0ubbRMy55
<ID>k__BackingField
SJ5bIGqTEk
<RowVersion>k__BackingField
DJbbNdJaKx
<Number>k__BackingField
mSibk0WG4A
<Date>k__BackingField
cDxb7QFo5D
<Method>k__BackingField
QybbcsYCBT
<Amount>k__BackingField
ekobiCD959
<CurrencyID>k__BackingField
R9bbdYlpFL
<CreditorAccountID>k__BackingField
lkSbwCfBig
<DCAmount>k__BackingField
bfibyeABDR
<DebitorAccountID>k__BackingField
x5RbhkmlIY
<Note>k__BackingField
jmobF3cGXb
<Delivery>k__BackingField
B9kblO9v2r
<ChequeID>k__BackingField
BwobYqwEI7
<EntryID>k__BackingField
S9rbJBiHsc
<RefNumber>k__BackingField
gK0bK8bfiY
<AttachmentNumbers>k__BackingField
JBbb9LNOEe
<CreatedBy>k__BackingField
xURbOCorEw
<BranchID>k__BackingField
zI3bUO23Sc
<CreatedTime>k__BackingField
QoAbE5Vnyh
<Year>k__BackingField
lOtbSIGAU9
<Status>k__BackingField
EIYbptXBdN
<CreatedDate>k__BackingField
KGtbLynEH4
<HourTime>k__BackingField
LuFbaNQe8a
<MinuteTime>k__BackingField
wwNb06D2t8
<TransNumber>k__BackingField
TLCbQfXH0T
<Channel>k__BackingField
b0hb3BwQiQ
<Attachments>k__BackingField
hOnbvXUJyt
<ExtraInfo>k__BackingField
fjDbZigFVB
<ExtraID>k__BackingField
GhFbmS7UlK
<SyncID>k__BackingField
bxYbCXiJAj
<RefID>k__BackingField
bYibzVTRd2
<BindID>k__BackingField
AU1I2TBoqU
<Binded>k__BackingField
z2LIXiGv6g
<Account>k__BackingField
FQgIMDxypK
<Account1>k__BackingField
JPeIjJqGbH
<Branch>k__BackingField
V2cIucXMqB
<Cheque>k__BackingField
dEvIqxvKcO
<Currency>k__BackingField
bBwIWWaTn5
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashTransfer
AppTech.MSMS.Domain.Models.CashTransfer
CashTransfer
CashTransfer
MFrI411RiU
<ID>k__BackingField
zqgIDn8IYQ
<RowVersion>k__BackingField
FesIsZkXGJ
<Number>k__BackingField
GN1I1c1xMw
<Amount>k__BackingField
ehtI6JZYQy
<CurrencyID>k__BackingField
F9fIfjyXhc
<CreditorAccountID>k__BackingField
WRkIHq1tTM
<DebitorAccountID>k__BackingField
aZEIBUVjZS
<Date>k__BackingField
L2ZItlRs7b
<Note>k__BackingField
wmDIxK5rsJ
<EntryID>k__BackingField
d8VI5yTYGZ
<RefNumber>k__BackingField
h7yIr8L4Ct
<CreatedTime>k__BackingField
AsnIGfiMOm
<CreatedBy>k__BackingField
W3QIgNXtTi
<BranchID>k__BackingField
s9dIVQLBcZ
<Year>k__BackingField
EyYIeu5ysE
<SenderID>k__BackingField
mpKIn8FjiR
<BeneficiaryID>k__BackingField
fVlI8jJFcy
<Status>k__BackingField
p1GIouH5AC
<Channel>k__BackingField
IcOITDKS6U
<CreatedDate>k__BackingField
MNGIPhlLx1
<HourTime>k__BackingField
vxkIRFOM1F
<MinuteTime>k__BackingField
TKJIAwebfR
<TransNumber>k__BackingField
DnNIb0eZS8
<Attachments>k__BackingField
gloIIbe4ao
<ExtraInfo>k__BackingField
QqhINKkKLs
<ExtraID>k__BackingField
V1fIk8Pb8S
<SyncID>k__BackingField
HU5I7ylDvh
<RefID>k__BackingField
RofIc0DdjB
<BindID>k__BackingField
jbwIiYtasw
<Binded>k__BackingField
piQIdENuTA
<Account>k__BackingField
Na9IwBUJ8q
<Account1>k__BackingField
ytbIyTMgH6
<Branch>k__BackingField
ChCIhulOJ6
<Currency>k__BackingField
daaIFDaquh
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashWithdraw
AppTech.MSMS.Domain.Models.CashWithdraw
CashWithdraw
CashWithdraw
Sw1Ilke3LJ
<ID>k__BackingField
QnuIYAD3d7
<RowVersion>k__BackingField
bguIJjRhpR
<Number>k__BackingField
yYQIKsjjrS
<Date>k__BackingField
JJOI9r1MAw
<Amount>k__BackingField
pWkIO1HJRv
<CurrencyID>k__BackingField
UUiIUVwr4h
<CreditorAccountID>k__BackingField
n5HIEULRI9
<DebitorAccountID>k__BackingField
SxKISuUjgl
<AgentID>k__BackingField
zidIpMs7fH
<Note>k__BackingField
N9lILc5jm7
<Delivery>k__BackingField
XOiIa3ahDX
<Prints>k__BackingField
YkpI01hb7G
<EntryID>k__BackingField
Iu4IQ8vwtm
<IsDebited>k__BackingField
LgfI35VVyu
<RefNumber>k__BackingField
OihIva6d9A
<AttachmentNumbers>k__BackingField
xJEIZ9IxhW
<CreatedBy>k__BackingField
jv5ImV0Ho3
<BranchID>k__BackingField
D2BICvZW3Y
<CreatedTime>k__BackingField
OAvIzd24Zw
<Year>k__BackingField
nDjN2G2xWO
<Status>k__BackingField
b3eNXADKMw
<CreatedDate>k__BackingField
IEmNM48BCu
<HourTime>k__BackingField
Qf5NjeJhQ5
<MinuteTime>k__BackingField
Ku2NuNIn0i
<TransNumber>k__BackingField
lU3NqWBFN8
<Channel>k__BackingField
cx0NWSuMP5
<Attachments>k__BackingField
VVIN4Yd5Pw
<ExtraInfo>k__BackingField
HyjNDkT6B2
<ExtraID>k__BackingField
nkvNsG3l7I
<SyncID>k__BackingField
LxhN1BWxOl
<RefID>k__BackingField
sjeN6CK26Z
<BindID>k__BackingField
vLcNffMpcS
<Binded>k__BackingField
wJKNHHwqhJ
<Account>k__BackingField
YtTNBbayja
<Account1>k__BackingField
TB9NtwUdI8
<Agent>k__BackingField
PTSNxfksO8
<Branch>k__BackingField
uwZN5EsUYD
<Currency>k__BackingField
EeVNrUtrjq
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Cheque
AppTech.MSMS.Domain.Models.Cheque
Cheque
Cheque
yYNNGqYFbD
<ID>k__BackingField
JLONgcgGom
<ChequeNumber>k__BackingField
SU1NVL0jx8
<ChequeDate>k__BackingField
BBANe2fv74
<BankID>k__BackingField
hdrNnQGafv
<AccountID>k__BackingField
dBbN8hKb7s
<ExchangeAccountID>k__BackingField
SIFNo74Zem
<Amount>k__BackingField
zrHNTaLrKt
<CurrencyID>k__BackingField
uC0NPBEPRk
<Date>k__BackingField
btBNR8Jxra
<VoucherID>k__BackingField
TgGNAl2fts
<ChequeType>k__BackingField
aa6NbHMUeE
<CreatedBy>k__BackingField
DbpNIvce7F
<BranchID>k__BackingField
XRbNNvMXFk
<CreatedTime>k__BackingField
QoANkCyhZh
<IsDebited>k__BackingField
UHUN7vHcHL
<Year>k__BackingField
i9NNc1j3l6
<Account>k__BackingField
am4NiggtMK
<Account1>k__BackingField
txgNdMUVOk
<Bank>k__BackingField
RmXNwTZBdg
<Branch>k__BackingField
LB2NyoE9V6
<CashIns>k__BackingField
kyqNhX8Vl7
<CashOuts>k__BackingField
XXaNFkVQgV
<Currency>k__BackingField
UagNlnLBNH
<UserInfo>k__BackingField
o62NYEha9u
<Voucher>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ClaimGroup
AppTech.MSMS.Domain.Models.ClaimGroup
ClaimGroup
ClaimGroup
VUFNJ864Gj
<ID>k__BackingField
svcNKgZbu9
<RowVersion>k__BackingField
zetN94jPbS
<Name>k__BackingField
Pg9NOmJVnG
<Note>k__BackingField
vuXNUlVGYR
<BranchID>k__BackingField
bayNElp49f
<CreatedBy>k__BackingField
rvoNSTO599
<CreatedTime>k__BackingField
yLiNp9YJuM
<Branch>k__BackingField
xN8NL4xTMB
<UserInfo>k__BackingField
ULnNatAHGL
<UserInfoes>k__BackingField
heVN0GCyJJ
<UserPermissions>k__BackingField
YOcNQwrQ05
<SelectedServices>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Client
AppTech.MSMS.Domain.Models.Client
Client
Client
bGfN3oVSCv
<ID>k__BackingField
F7cNvos2L5
<RowVersion>k__BackingField
bFSNZef4yL
<Number>k__BackingField
Ws4NmOiprB
<Name>k__BackingField
STSNCJmM7M
<AccountID>k__BackingField
tmNNzJGDgb
<Username>k__BackingField
hoik2QPWHk
<Password>k__BackingField
WlukXf8iaS
<ShopName>k__BackingField
aiikM9p02t
<PhoneNumber>k__BackingField
YTKkjunk1Q
<Address>k__BackingField
tUxkuExjrr
<IsActive>k__BackingField
f3TkqjgEWT
<CreatedBy>k__BackingField
ntBkWuXHpm
<BranchID>k__BackingField
SJik4wxlYB
<CreatedTime>k__BackingField
hvekDrNaEc
<BirthDate>k__BackingField
hYpksdYuPC
<ContactNumber>k__BackingField
BErk1cI2iS
<Email>k__BackingField
wmEk6rGJBw
<CardType>k__BackingField
k3bkf2QxLC
<CardNumber>k__BackingField
WE4kHcWfUH
<CardIssuePlace>k__BackingField
v8lkBhEmfR
<CardIssueDate>k__BackingField
Wsokt92fSm
<ImageName>k__BackingField
lwMkxISdK4
<ActivateBy>k__BackingField
UMCk5WtVp8
<Channel>k__BackingField
GpCkrmcpd2
<DeviceID>k__BackingField
N1dkGvZmms
<Type>k__BackingField
jBYkgNuhZu
<Note>k__BackingField
G4CkVNmEsH
<Status>k__BackingField
N3qkeRp0av
<PhoneNumberConfirmed>k__BackingField
Hitkn7CBQa
<TwoFactorEnabled>k__BackingField
vnsk8mU8HK
<AgentID>k__BackingField
mdjko2YQGs
<SyncAccountID>k__BackingField
dwJkT7v3Mh
<LastSyncedTime>k__BackingField
yhMkPEQDfi
<DistributorID>k__BackingField
l7EkRUkAKn
<ParentAccountID>k__BackingField
qyskAKaySF
<ParentType>k__BackingField
ruBkblKIVI
<GroupID>k__BackingField
whykItlxWP
<Token>k__BackingField
IdakNrj9g2
<Account>k__BackingField
A6pkkOdEjp
<Agent>k__BackingField
wODk74bybX
<Branch>k__BackingField
PPZkcvgdSw
<UserInfo>k__BackingField
L99kiLJQNH
<SMSDispatches>k__BackingField
I2XkdNAATf
<WithdrawOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CommissionReceipt
AppTech.MSMS.Domain.Models.CommissionReceipt
CommissionReceipt
CommissionReceipt
A1XkwfGYxA
<ID>k__BackingField
KZKky4ELqn
<RowVersion>k__BackingField
gqrkhQr3IN
<Number>k__BackingField
ICfkFqQkm8
<StartDate>k__BackingField
qcGkluv2pK
<EndDate>k__BackingField
XX0kYeBK2y
<OpsCount>k__BackingField
xHYkJEbbOD
<Percentage>k__BackingField
HgKkKJFFig
<Amount>k__BackingField
tqXk9KqcaT
<CurrencyID>k__BackingField
tbvkOX4RNA
<AccountID>k__BackingField
AMHkU2rwk1
<AccountGroupID>k__BackingField
YnKkEqR6ej
<ServiceState>k__BackingField
p4TkSdGKMY
<ServiceGroupID>k__BackingField
ahrkpHchNw
<ServiceID>k__BackingField
yyfkL0AfuT
<EntryID>k__BackingField
N2nkaB5VJ4
<Note>k__BackingField
Yw2k0i43rA
<RefNumber>k__BackingField
EYtkQ9uChG
<Date>k__BackingField
GGpk3pf0t5
<Channel>k__BackingField
X1nkvTnHx9
<ExtraInfo>k__BackingField
kg3kZX5rBE
<Status>k__BackingField
Ob6kmFiU7e
<Year>k__BackingField
vN9kCv2VU8
<BranchID>k__BackingField
VeukzIIYSM
<CreatedBy>k__BackingField
vuJ72SY28R
<CreatedTime>k__BackingField
IaI7XSgECd
<Account>k__BackingField
Quf7MIeaAQ
<Branch>k__BackingField
SJZ7jZ9x7P
<Currency>k__BackingField
zAg7uoKrgY
<Journal>k__BackingField
b3S7q5Opgm
<ServiceInfo>k__BackingField
GOQ7WwsNgy
<UserInfo>k__BackingField
uSc7491Bsl
<CommissionReceiptLines>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CommissionReceiptLine
AppTech.MSMS.Domain.Models.CommissionReceiptLine
CommissionReceiptLine
CommissionReceiptLine
B1Y7D9wZU0
<ID>k__BackingField
y8K7s9Mj32
<ParentID>k__BackingField
Rcy71RpUHQ
<AccountID>k__BackingField
qFx76RFyN9
<TotalTopup>k__BackingField
UXI7fqC5J6
<Amount>k__BackingField
MPi7HNOK7f
<Note>k__BackingField
oD87BkGfbV
<Account>k__BackingField
vQu7tlOLB9
<CommissionReceipt>k__BackingField
SWe7xdPh2W
<AccountName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Company
AppTech.MSMS.Domain.Models.Company
Company
Company
lbL75MfuVE
<ID>k__BackingField
kBS7rH4mP0
<Code>k__BackingField
b297GmRbv4
<Name>k__BackingField
pM37gEgOih
<ShortName>k__BackingField
LV97VjtD8L
<Address>k__BackingField
CCS7e4ACic
<Logo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ConsumeInvoice
AppTech.MSMS.Domain.Models.ConsumeInvoice
ConsumeInvoice
ConsumeInvoice
ems7nbDThW
<ID>k__BackingField
W4078YVktg
<RowVersion>k__BackingField
sqZ7obmE8X
<Number>k__BackingField
lds7TAUUiY
<CreditorAccountID>k__BackingField
TOw7PFXUxt
<DebitorAccountID>k__BackingField
W7P7RrQlQF
<SubscriberID>k__BackingField
Fm77A0vePG
<InvoiceNo>k__BackingField
qhZ7beffPo
<Date>k__BackingField
C467Ie6EgA
<StartDate>k__BackingField
WeJ7N4dZLL
<EndDate>k__BackingField
MrK7kNBdyi
<CurrencyID>k__BackingField
hNs77dZdoK
<UnitPrice>k__BackingField
qVp7cehQXI
<Quantity>k__BackingField
YIi7iAoYeb
<Amount>k__BackingField
gVH7dDDqpj
<LateAmount>k__BackingField
Fc87wDBg0R
<ExtraAmount>k__BackingField
XDs7yoSfLc
<Discount>k__BackingField
f4c7hHk9vT
<TotalAmount>k__BackingField
UFo7FjRpiZ
<Note>k__BackingField
Xpi7lvrry7
<EntryID>k__BackingField
nmU7YbyWc1
<RefNumber>k__BackingField
dC27JjkgoE
<Extra>k__BackingField
SGK7KS2Bmp
<AttachmentNumbers>k__BackingField
SWU79Umh59
<Footer>k__BackingField
Bga7OVCjXk
<Year>k__BackingField
X5q7UWlSv8
<Status>k__BackingField
jZy7EK3jsj
<CreatedTime>k__BackingField
Weu7SMMftW
<CreatedBy>k__BackingField
g7i7pEDEhY
<BranchID>k__BackingField
Exc7LYINdK
<FineAmount>k__BackingField
TNa7ap9bwT
<FineNote>k__BackingField
S8d70Fdg6p
<SubscriptionFee>k__BackingField
xVB7QXwfNk
<Account>k__BackingField
kOM73cU2sA
<Account1>k__BackingField
HFX7vsjBDA
<Branch>k__BackingField
p7k7ZHMYoY
<Currency>k__BackingField
hJ27mmmNFv
<Journal>k__BackingField
zam7CRDcWn
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Country
AppTech.MSMS.Domain.Models.Country
Country
Country
Erd7zDJjmV
<ID>k__BackingField
LCVc2ov6vE
<RowVersion>k__BackingField
aZccXy9W9Y
<Name>k__BackingField
AIjcMWPTrO
<CreatedBy>k__BackingField
hb6cjXVcDf
<BranchID>k__BackingField
reIcuUKH3r
<CreatedTime>k__BackingField
Qo6cq2IBlS
<BankDeposits>k__BackingField
HNtcWYmZEP
<Branch>k__BackingField
tq0c4wO6Av
<UserInfo>k__BackingField
uIscDTTnfy
<Provinces>k__BackingField
unYcsKUAHH
<TransferOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CoverageOrder
AppTech.MSMS.Domain.Models.CoverageOrder
CoverageOrder
CoverageOrder
qGac1lDLsF
<ID>k__BackingField
jHXc6Rg3Xm
<RowVersion>k__BackingField
YbAcfokDhR
<Number>k__BackingField
PBRcHKsgOK
<ParentID>k__BackingField
CpFcBlMRRT
<ServiceID>k__BackingField
SX9ct1YeHc
<AccountID>k__BackingField
dZbcxFONPK
<ExchangeAccountID>k__BackingField
IU4c576yCA
<Amount>k__BackingField
k33crTClR7
<CurrencyID>k__BackingField
AsrcGORoYC
<AccountNumber>k__BackingField
xdLcg1l9b4
<EntryID>k__BackingField
IdKcVY4Hft
<Date>k__BackingField
hoBcebebtj
<Type>k__BackingField
HjgcnWDcrd
<Status>k__BackingField
FfZc8oySq6
<IsDirect>k__BackingField
aqlcoRdl5A
<Purpose>k__BackingField
YxNcT1SPkj
<ResponseMessage>k__BackingField
tskcPa08RD
<ResponseInfo>k__BackingField
stucRWdbmj
<RefNumber>k__BackingField
lhhcATMSub
<Note>k__BackingField
RXAcbguvog
<Channel>k__BackingField
IhbcIkQaDT
<CreatedBy>k__BackingField
zL9cNRoYaj
<BranchID>k__BackingField
eXTckR2B2F
<CreatedTime>k__BackingField
NTcc79lx46
<Account>k__BackingField
bkOccmRyCe
<Account1>k__BackingField
GXOciPOFR4
<Branch>k__BackingField
nNXcdGdvZv
<Currency>k__BackingField
WGVcwbLMNm
<OrderInfo>k__BackingField
dKLcymTRV0
<ServiceInfo>k__BackingField
tFechIuDJy
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Currency
AppTech.MSMS.Domain.Models.Currency
Currency
Currency
YapcFnyxZP
<ID>k__BackingField
SYwcly8Wkg
<Name>k__BackingField
wHecYAkpHJ
<Symbol>k__BackingField
KDUcJQ5j0D
<Note>k__BackingField
bAUcKoTF2B
<CreatedBy>k__BackingField
Akrc9CkSRK
<CreatedTime>k__BackingField
Iy1cO4jQmC
<RowVersion>k__BackingField
pOPcU3OpHs
<BranchID>k__BackingField
zjVcEcVnOa
<IsDefault>k__BackingField
xs5cS1Z6TN
<SyncCurrencyID>k__BackingField
DvXcp0M1S3
<AccountSlatings>k__BackingField
FdicLasxYO
<BankDeposits>k__BackingField
PrMca5dyd3
<BankDeposits1>k__BackingField
RK9c0lUGuL
<Branch>k__BackingField
ehwcQLoKFo
<BuyCurrencies>k__BackingField
P45c3gSvI2
<BuyCurrencies1>k__BackingField
FMYcvA5nbU
<CardOrders>k__BackingField
ehpcZ3ywSP
<CardPayments>k__BackingField
E1ccmrFa7y
<CashDeposits>k__BackingField
BxccCls0kC
<CashIns>k__BackingField
VH0cz4q1Fa
<CashOuts>k__BackingField
y3ci2nl3lH
<CashTransfers>k__BackingField
WeRiXISPyn
<CashWithdraws>k__BackingField
D11iMTPqpe
<Cheques>k__BackingField
ionijR0WMK
<CommissionReceipts>k__BackingField
iJyiuZ04EL
<ConsumeInvoices>k__BackingField
vi8iqycZ2p
<CoverageOrders>k__BackingField
gwliWBsf56
<UserInfo>k__BackingField
U5qi4kVRTS
<CurrencyExchanges>k__BackingField
KPFiDZ0unW
<CurrencyRates>k__BackingField
bEGisxNf9T
<CurrencyRateAccounts>k__BackingField
aJWi1LTG5x
<CurrencyRateAccounts1>k__BackingField
aZEi6sZVMT
<DepositOrders>k__BackingField
o4eif5wycq
<JournalEntries>k__BackingField
BmSiHec1mZ
<LoanOrders>k__BackingField
KMiiBe0FUK
<MerchantPayments>k__BackingField
eSeitoHJKF
<OpeningBalances>k__BackingField
ToFixvuFq9
<OrderSatelliteQuotas>k__BackingField
TPoi5i48aE
<Payments>k__BackingField
q7TireQKcV
<PaymentCommissions>k__BackingField
JwmiGwavRo
<Products>k__BackingField
B75igoQtK2
<PurchaseInvoices>k__BackingField
O79iVyTTpv
<ReceiptCreditors>k__BackingField
yebieTn3mE
<ReceiptDebitors>k__BackingField
W7YinrBV6a
<RemittanceCommissions>k__BackingField
eqQi8bnSAf
<RemittanceIns>k__BackingField
oJ6ioCyGIL
<RemittanceOuts>k__BackingField
hJdiTsXBb1
<RiyalMobiles>k__BackingField
X6EiPRe8G2
<SaleCurrencies>k__BackingField
J4WiRT9wPr
<SaleCurrencies1>k__BackingField
kIyiAmO6J1
<SaleInvoices>k__BackingField
rHJibTxnXO
<SatellitePayments>k__BackingField
MBciIhQDlI
<SimInvoices>k__BackingField
C0EiNo5HrW
<SimpleEntries>k__BackingField
q43ikB23in
<SimPurchases>k__BackingField
ulEi7eU7lZ
<CurrencyExchanges1>k__BackingField
o1AiciIZhf
<CurrencyRates1>k__BackingField
QE1iirjMNa
<UserInfo1>k__BackingField
agsidPde5I
<RemittanceIns1>k__BackingField
yoSiwrlVK0
<Topups>k__BackingField
YiXiyKcYq4
<TopupCommissions>k__BackingField
hL0ih1lB8Y
<TransferIns>k__BackingField
h3oiFoXWMo
<TransferIns1>k__BackingField
xSVil7Bmux
<TransferOrders>k__BackingField
UFNiYVCbHR
<TransferOrders1>k__BackingField
v7viJqUKr1
<TransferOuts>k__BackingField
spsiKCrh9f
<TransferOuts1>k__BackingField
DD7i9VL1iT
<TransportOrders>k__BackingField
eGjiOGvwB9
<WifiPayments>k__BackingField
KWCiU6UJ5u
<WithdrawOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CurrencyExchange
AppTech.MSMS.Domain.Models.CurrencyExchange
CurrencyExchange
CurrencyExchange
LqKiE69Xwq
<ID>k__BackingField
GZ5iSc3RYI
<RowVersion>k__BackingField
wDZipbG0gI
<SourceCurrencyID>k__BackingField
tDviLOiYug
<TargetCurrencyID>k__BackingField
eI2iahuheL
<Amount>k__BackingField
RJqi0oBd15
<ExchangePrice>k__BackingField
ylqiQwYIqb
<Note>k__BackingField
iw5i3IULiO
<CreatedBy>k__BackingField
skFiv2BQaE
<BranchID>k__BackingField
T5kiZT4tlk
<CreatedTime>k__BackingField
q62imyrKmV
<ExchangeAmount>k__BackingField
S0hiCuvBFp
<Status>k__BackingField
DRjizUVXq2
<Number>k__BackingField
MB9d2tchGc
<Type>k__BackingField
UdcdXUvhBp
<EntryID>k__BackingField
q3SdMGu2o4
<Channel>k__BackingField
DAYdj0WC0s
<AccountID>k__BackingField
ccJdu5pTUM
<ParentID>k__BackingField
MAmdqxvjju
<ServiceID>k__BackingField
aJadWJCDSP
<Account>k__BackingField
viBd4ymEZK
<Branch>k__BackingField
V8jdDZPmVm
<Currency>k__BackingField
Tj8dsY2A3v
<Currency1>k__BackingField
oqDd1hctJ8
<OrderInfo>k__BackingField
Qd9d6T0naJ
<ServiceInfo>k__BackingField
xjhdf32Psw
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CurrencyRate
AppTech.MSMS.Domain.Models.CurrencyRate
CurrencyRate
CurrencyRate
jLcdHMwWdf
<ID>k__BackingField
CgZdBNboke
<SourceCurrencyID>k__BackingField
iW4dtL40WI
<TargetCurrencyID>k__BackingField
QaedxCy6wu
<ExchangeRate>k__BackingField
Qd7d58B9rH
<BuyPrice>k__BackingField
nmcdrsiCTG
<SellPrice>k__BackingField
rRqdGoaYgd
<Note>k__BackingField
a1pdgYsJhX
<CreatedBy>k__BackingField
g8hdVZDq5v
<CreatedTime>k__BackingField
LZMdevruVV
<RowVersion>k__BackingField
jPOdnvWX1e
<BranchID>k__BackingField
G0Wd8YZDpe
<UpDown>k__BackingField
xx7doW4Epc
<ModifiedDate>k__BackingField
XWWdTLVn4q
<ExBuyPrice>k__BackingField
XMmdPyg2WT
<ExSalePrice>k__BackingField
ojgdR9DMIm
<Branch>k__BackingField
FOFdAVrAQW
<Currency>k__BackingField
FqidbsSvwO
<Currency1>k__BackingField
yJYdI87e1Z
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CurrencyRateAccount
AppTech.MSMS.Domain.Models.CurrencyRateAccount
CurrencyRateAccount
CurrencyRateAccount
H8YdNAE8OB
<ID>k__BackingField
oiHdkkIZZC
<RowVersion>k__BackingField
HYid7xS6Wo
<SourceCurrencyID>k__BackingField
jmjdceMGSt
<TargetCurrencyID>k__BackingField
kepdihhrA0
<AccountState>k__BackingField
anSddSmYgG
<AccountID>k__BackingField
yOndwNfxbj
<AccountGroupID>k__BackingField
Y4qdypBmCZ
<ExchangeRate>k__BackingField
ymKdhUTUZp
<BuyPrice>k__BackingField
VM6dFwJc4f
<SellPrice>k__BackingField
DVHdlB5ep7
<Note>k__BackingField
muMdYuAn6q
<ModifiedDate>k__BackingField
aHMdJnvnOT
<BranchID>k__BackingField
EBTdKWbwee
<CreatedBy>k__BackingField
dSFd93iv9d
<CreatedTime>k__BackingField
A6bdOnkp1d
<Branch>k__BackingField
Jt6dU8KEny
<Currency>k__BackingField
uApdEkvXQJ
<Currency1>k__BackingField
NKWdSUKIRp
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DbBackup
AppTech.MSMS.Domain.Models.DbBackup
DbBackup
DbBackup
rQ8dpDBgcm
<ID>k__BackingField
wQndLWrXhL
<RowVersion>k__BackingField
yv0daI4j14
<Number>k__BackingField
DGqd0KNnsu
<Name>k__BackingField
pRedQEmLLc
<Path>k__BackingField
DdDd3FeeHn
<Keyname>k__BackingField
ziNdvZGE32
<Note>k__BackingField
M5ZdZVH637
<BranchID>k__BackingField
y7jdmUcAaW
<CreatedBy>k__BackingField
t07dCNgl9d
<CreatedTime>k__BackingField
LTrdzcCStZ
<Branch>k__BackingField
Ly4w26XDwN
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DbScript
AppTech.MSMS.Domain.Models.DbScript
DbScript
DbScript
dsNwXRbEZj
<ID>k__BackingField
Jl6wMlEWpl
<Number>k__BackingField
ekywjWy3oE
<Name>k__BackingField
lKUwuRPOQN
<ServerVersion>k__BackingField
heJwqb8QBv
<Description>k__BackingField
p43wWrLqJy
<Note>k__BackingField
Srow4XD4sX
<Status>k__BackingField
mC7wD7891L
<CreatedTime>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DepositOrder
AppTech.MSMS.Domain.Models.DepositOrder
DepositOrder
DepositOrder
oZnwshZ4bM
<ID>k__BackingField
Jxjw1fIIHG
<RowVersion>k__BackingField
KgDw6NH44r
<CreatedBy>k__BackingField
IMuwfPVjeR
<BranchID>k__BackingField
go2wH9EH8Z
<CreatedTime>k__BackingField
evGwBpwngb
<Amount>k__BackingField
mGgwtXSrpA
<CurrencyID>k__BackingField
mNswxbof00
<MCAmount>k__BackingField
Kyjw5smSDw
<Note>k__BackingField
rKpwrAtEGr
<Channel>k__BackingField
xeAwGg1CTa
<ImageName>k__BackingField
bDwwgXXdAp
<ParentID>k__BackingField
cnKwVf5dXL
<ServiceID>k__BackingField
bMBwexmaEc
<AccountID>k__BackingField
HDrwnV84DZ
<Account>k__BackingField
NNUw86oaV3
<Branch>k__BackingField
m0WwoT9Oja
<Currency>k__BackingField
npAwT6is6u
<OrderInfo>k__BackingField
kChwP3yFnR
<ServiceInfo>k__BackingField
y27wRyfV38
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Device
AppTech.MSMS.Domain.Models.Device
Device
Device
AUKwAxwXHD
<ID>k__BackingField
e1kwbmft6o
<RowVersion>k__BackingField
LGMwIyfmAX
<Number>k__BackingField
yFpwNQgjeC
<AccountID>k__BackingField
meiwkYIaXB
<Active>k__BackingField
PSPw7MpbDF
<Note>k__BackingField
W1AwcUWp5Y
<BranchID>k__BackingField
nb9wi7Y5oi
<CreatedBy>k__BackingField
wnGwd0XpSv
<CreatedTime>k__BackingField
qRswwGvPkI
<Account>k__BackingField
hBSwyAZtAe
<Branch>k__BackingField
exSwhZNjpS
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Distributor
AppTech.MSMS.Domain.Models.Distributor
Distributor
Distributor
HCWwFgOcDV
<ID>k__BackingField
rxlwlLGr8Y
<RowVersion>k__BackingField
CeDwYeKf0S
<Number>k__BackingField
e9mwJl1o19
<Name>k__BackingField
aOjwKMblEN
<AccountID>k__BackingField
Ah7w9nLCxm
<Major>k__BackingField
i5YwOoM40O
<PhoneNumber>k__BackingField
UucwUJb0De
<Address>k__BackingField
eAXwEE9Jbb
<IsActive>k__BackingField
RJLwSkS80q
<BirthDate>k__BackingField
YjMwpxe5bq
<ContactNumber>k__BackingField
easwL279rN
<Email>k__BackingField
Pqmwa7cQgC
<CardType>k__BackingField
oHlw0LdXSN
<CardNumber>k__BackingField
QQLwQ5LFNm
<CardIssuePlace>k__BackingField
eCqw3v6Dvt
<CardIssueDate>k__BackingField
DwYwvpv39t
<ImageName>k__BackingField
FqowZlUp72
<ActivateBy>k__BackingField
ejdwm0sYeH
<Channel>k__BackingField
udlwCT6XNk
<DeviceID>k__BackingField
F8JwzgA1HL
<Type>k__BackingField
ACwy2foA1g
<Note>k__BackingField
ESiyXJbtOT
<Status>k__BackingField
kOlyMpLHD5
<PhoneNumberConfirmed>k__BackingField
BWPyjRYD6K
<TwoFactorEnabled>k__BackingField
BGMyu3DfrP
<AgentID>k__BackingField
rpryqHoUPv
<SyncAccountID>k__BackingField
SiKyWChRMi
<LastSyncedTime>k__BackingField
wYxy48T9D8
<CreatedBy>k__BackingField
q9HyD5y6Ag
<BranchID>k__BackingField
es3ysj46iJ
<CreatedTime>k__BackingField
I5Ny1kg77M
<Account>k__BackingField
jbYy6yDBAq
<Agent>k__BackingField
D63yfb2PuU
<Branch>k__BackingField
CwiyHshHYp
<UserInfo>k__BackingField
DYnyBJ8f59
<Username>k__BackingField
eSvytedoBa
<Password>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ELMAH_Error
AppTech.MSMS.Domain.Models.ELMAH_Error
ELMAH_Error
ELMAH_Error
wOMyxuTdXA
<ErrorId>k__BackingField
lX7y5CcOPU
<Application>k__BackingField
LLkyrP32V2
<Host>k__BackingField
yPwyGyQ6XU
<Type>k__BackingField
opnyggiC8k
<Source>k__BackingField
GSiyV9yPkK
<Message>k__BackingField
PrCyeBxiB9
<User>k__BackingField
DncynBx7PD
<StatusCode>k__BackingField
HYOy8dF7cw
<TimeUtc>k__BackingField
jwkyoBaNvl
<Sequence>k__BackingField
WXUyTB3KgD
<AllXml>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Error
AppTech.MSMS.Domain.Models.Error
Error
Error
h4ByPUDEgP
<ID>k__BackingField
Jr3yRiSgS3
<Channel>k__BackingField
oJjyACEuNA
<UserID>k__BackingField
lehyb9fCCX
<Date>k__BackingField
qOlyI6tvQF
<Source>k__BackingField
Yr8yNr0JI9
<Type>k__BackingField
RJSykHx9Os
<IpAddress>k__BackingField
uMQy7kg0yr
<UserAgent>k__BackingField
Aarycy0BUl
<Message>k__BackingField
wqpyi7PpyF
<Exception>k__BackingField
pYOydes2Op
<InnerException>k__BackingField
QVQywbA74S
<Extra>k__BackingField
BJGyy3sEYH
<Stacktrace>k__BackingField
juiyhmhdhg
<HttpReferer>k__BackingField
bOryFrTBrN
<PathAndQuery>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Exchanger
AppTech.MSMS.Domain.Models.Exchanger
Exchanger
Exchanger
CXoylflmFi
<ID>k__BackingField
Pe2yYgWVxm
<RowVersion>k__BackingField
IIEyJ4Ss7N
<Number>k__BackingField
GceyKUhmEE
<Name>k__BackingField
hUuy96wu8u
<CreatedBy>k__BackingField
TOQyOZojoy
<BranchID>k__BackingField
pLlyUOiAuR
<CreatedTime>k__BackingField
qTyyESi0UO
<AccountID>k__BackingField
xULySkqpVd
<IsExternal>k__BackingField
PDcypIk4yK
<Account>k__BackingField
SbFyL0rCqV
<Branch>k__BackingField
wT7ya1S0BR
<UserInfo>k__BackingField
ScVy0y2Sqp
<ExchangerTargets>k__BackingField
O9wyQpKvcK
<TransferIns>k__BackingField
mlKy33ZcW2
<TransferOrders>k__BackingField
MPeyvn7S1L
<TransferOuts>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ExchangerTarget
AppTech.MSMS.Domain.Models.ExchangerTarget
ExchangerTarget
ExchangerTarget
mxjyZAnQc3
<ID>k__BackingField
d9ZymxuqM1
<RowVersion>k__BackingField
d1uyCD30XJ
<ExchangerID>k__BackingField
KjuyzRN029
<RemittancePointID>k__BackingField
Xmoh2ttA1n
<ProviderID>k__BackingField
H9LhX8b8nu
<ExtraID>k__BackingField
WBVhMlcaq3
<BindID>k__BackingField
DmChjGdKCY
<SyncID>k__BackingField
IcOhuPEke4
<ParentID>k__BackingField
yfGhqwndHI
<Primary>k__BackingField
lCYhWajjn1
<Binded>k__BackingField
n0fh4NOFlQ
<Active>k__BackingField
OaUhD9jEgF
<Synced>k__BackingField
C6whshtdtM
<Status>k__BackingField
Wo7h1O2vEL
<Type>k__BackingField
x5Nh66uOdD
<Binding>k__BackingField
f09hf6Gk9h
<Note>k__BackingField
GqhhHc7YAx
<ExtraInfo>k__BackingField
r9nhBubsLR
<Token>k__BackingField
dethtqSwm4
<CreatedBy>k__BackingField
bfLhxfh7MC
<BranchID>k__BackingField
NG2h5KXAeR
<CreatedTime>k__BackingField
sbyhrskTyL
<Branch>k__BackingField
TsIhGy0uRj
<Exchanger>k__BackingField
cnOhgSxbyY
<RemittancePoint>k__BackingField
oUShVJWBWq
<TopupProvider>k__BackingField
cGlheuDXy8
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ExternalBranch
AppTech.MSMS.Domain.Models.ExternalBranch
ExternalBranch
ExternalBranch
iVNhnZHU3m
<ID>k__BackingField
vLwh81X3rh
<RowVersion>k__BackingField
fv7hojgJse
<Number>k__BackingField
STWhTqL03D
<Name>k__BackingField
owLhPdvlYE
<AccountID>k__BackingField
OuyhReL3Ub
<PhoneNumber>k__BackingField
uvmhAPGbDg
<Address>k__BackingField
tnjhbofEYA
<Fax>k__BackingField
mXihIBwy7Q
<Note>k__BackingField
JAahNlXdxS
<Key>k__BackingField
bUdhkTpDee
<CreatedBy>k__BackingField
o69h7quNjE
<BranchID>k__BackingField
M1mhcBkxxK
<CreatedTime>k__BackingField
G8xhi6v6Mo
<Status>k__BackingField
XVxhdq5Gb7
<ImageName>k__BackingField
CPvhwv5wuR
<IsExternal>k__BackingField
mT2hygTAmp
<IsSpecial>k__BackingField
hk4hhrXyq4
<BaseUrl>k__BackingField
TVFhFvPdJ2
<Type>k__BackingField
IHQhl6IqVs
<Token>k__BackingField
b71hYaCj8H
<AppName>k__BackingField
tTchJ5xM5x
<ContactNumbers>k__BackingField
RfthK4CcCp
<ClaimID>k__BackingField
OdHh9x2qxS
<MainBranchID>k__BackingField
kuKhOtZLgA
<Keyname>k__BackingField
EVChUwyiSb
<ExtraInfo>k__BackingField
cqVhEmAn7F
<ExtraID>k__BackingField
ApPhSVs5r6
<SyncID>k__BackingField
lsVhp7qCdp
<SyncAccountID>k__BackingField
LnrhL7Q8hC
<Account>k__BackingField
cAChammyWY
<Branch>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Faction
AppTech.MSMS.Domain.Models.Faction
Faction
Faction
feoh0aCEX5
<ID>k__BackingField
M0fhQEX9tt
<RowVersion>k__BackingField
G9Bh3OZ4L7
<Name>k__BackingField
mMfhvCJNhS
<Price>k__BackingField
GtohZG4NKA
<Type>k__BackingField
vnahmeK9HG
<CreatedBy>k__BackingField
ByShC5Ie3U
<BranchID>k__BackingField
FWghz1iCE2
<CreatedTime>k__BackingField
kvgF2ACh1G
<ServiceID>k__BackingField
PEHFXESFpu
<ProviderPrice>k__BackingField
hiOFMBn5ho
<PersonnalPrice>k__BackingField
DJjFjrGIUj
<OrderNo>k__BackingField
XHQFuXQ626
<ProviderCode>k__BackingField
uYQFqGDHE2
<RefID>k__BackingField
mmmFWvZNlu
<Note>k__BackingField
E82F4CsMj1
<Number>k__BackingField
bmJFDoP5HB
<CurrencyID>k__BackingField
G2eFsRFcZh
<CategoryID>k__BackingField
FMiF1GaqQS
<ProviderID>k__BackingField
KbFF6rMXP7
<Description>k__BackingField
pkXFfTaGqd
<Status>k__BackingField
IWMFH8vnEV
<ClassType>k__BackingField
LghFBGCRdM
<RefNumber>k__BackingField
UIhFtgj4cQ
<LineType>k__BackingField
KWrFxOjX1e
<Quantity>k__BackingField
p2IF5d4qP5
<Units>k__BackingField
YcCFriBxfc
<Active>k__BackingField
RaNFGQQgkT
<Balance>k__BackingField
Q9LFgMENpS
<Command>k__BackingField
TagFVlb4iP
<Code>k__BackingField
LCVFeBLA48
<Branch>k__BackingField
J7cFnJTTmK
<UserInfo>k__BackingField
icQF8wtxHa
<PurchaseInvoiceLines>k__BackingField
pIDFo5exRx
<SaleInvoiceLines>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Feedback
AppTech.MSMS.Domain.Models.Feedback
Feedback
Feedback
oFcFTvm3As
<ID>k__BackingField
hv3FP7vqx0
<RowVersion>k__BackingField
wHBFRGGaQJ
<AccountID>k__BackingField
tl6FAXUhBQ
<ServiceName>k__BackingField
TpJFbnd9XV
<Feed>k__BackingField
K3nFIGLT6m
<Type>k__BackingField
edJFNghtxf
<Note>k__BackingField
cHFFkWfoB0
<BranchID>k__BackingField
qgmF71GDmH
<CreatedBy>k__BackingField
OtiFcEULYi
<CreatedTime>k__BackingField
JAqFi9Hkgp
<Account>k__BackingField
RajFdObrRo
<Branch>k__BackingField
fyEFwlIhvM
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Fund
AppTech.MSMS.Domain.Models.Fund
Fund
Fund
BseFygWgbt
<ID>k__BackingField
QMJFhNoWt3
<RowVersion>k__BackingField
M2aFFnQWY4
<Number>k__BackingField
mILFl69t3u
<Name>k__BackingField
yhiFYW8aLr
<AccountID>k__BackingField
esrFJelXOE
<UserID>k__BackingField
ABaFK5IbHt
<Note>k__BackingField
apMF9KOiLU
<CreatedBy>k__BackingField
kdwFOPp1s4
<BranchID>k__BackingField
KinFUwkh9b
<CreatedTime>k__BackingField
wUsFE2qqUr
<Account>k__BackingField
w56FS0AbmT
<Branch>k__BackingField
BOoFpfueJA
<UserInfo>k__BackingField
cCWFLtcXfL
<FundUsers>k__BackingField
xc3FaFDIwR
<UserInfo1>k__BackingField
doHF0sYgxy
<Extra>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.FundUser
AppTech.MSMS.Domain.Models.FundUser
FundUser
FundUser
X9qFQl7hiH
<ID>k__BackingField
XSTF3NuQhC
<RowVersion>k__BackingField
JwlFvKAOmH
<FundID>k__BackingField
RJeFZVhDDA
<UserID>k__BackingField
zjqFmFSUS2
<Note>k__BackingField
nuhFCL5oH8
<CreatedBy>k__BackingField
QKYFzIRcfy
<BranchID>k__BackingField
lHsl2N85EU
<CreatedTime>k__BackingField
mNGlXR59GN
<Branch>k__BackingField
uOXlMpYxbc
<Fund>k__BackingField
n8gljYxHSJ
<UserInfo>k__BackingField
bR0luiLt9Q
<UserInfo1>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.GeneralInfo
AppTech.MSMS.Domain.Models.GeneralInfo
GeneralInfo
GeneralInfo
bkJlqv4vWm
<ID>k__BackingField
GNalWLNH0t
<RowVersion>k__BackingField
TmOl4JYJci
<Title>k__BackingField
fcZlDkL5mL
<Description>k__BackingField
qfklsM43iC
<CreatedBy>k__BackingField
DdIl108fWQ
<BranchID>k__BackingField
pc3l6dRlqu
<CreatedTime>k__BackingField
HDJlfY0eYC
<Branch>k__BackingField
kuylHX8KWg
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.GroupItem
AppTech.MSMS.Domain.Models.GroupItem
GroupItem
GroupItem
EJ4lBeFC8n
<ID>k__BackingField
DSHltqT3g6
<GroupID>k__BackingField
AZrlx2K0tG
<ItemID>k__BackingField
TPPl5e489o
<Type>k__BackingField
PyvlrUCjJ8
<BranchID>k__BackingField
AiOlGSN9Sx
<CreatedBy>k__BackingField
zpdlgF4Mqv
<CreatedTime>k__BackingField
svTlVmPxLM
<Branch>k__BackingField
QpMleNJs8j
<PartyGroup>k__BackingField
F08lnir65a
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Gsm
AppTech.MSMS.Domain.Models.Gsm
Gsm
Gsm
efrl85fGPR
<ID>k__BackingField
wC4loFNmHw
<RowVersion>k__BackingField
dd8lT43Q9P
<Number>k__BackingField
x9GlPdVyGy
<Status>k__BackingField
u5ylRZeboP
<Status_chng_date>k__BackingField
UuClANpfFc
<Account_code>k__BackingField
CJ9lbGtC5M
<Name>k__BackingField
dsqlIJ75AX
<Bills>k__BackingField
VO7lNu72Ix
<Limit>k__BackingField
kDKlkErsy7
<Deposit>k__BackingField
yeHl7qhAoV
<Invoice_date>k__BackingField
W71lceKQpa
<On>k__BackingField
q4mliFaYTO
<BN>k__BackingField
W42ld9Ks39
<GroupID>k__BackingField
VG1lwPna0d
<GroupName>k__BackingField
Qsllym46Ur
<BranchID>k__BackingField
qcqlheerBX
<CreatedBy>k__BackingField
fMqlFQAQQe
<CreatedTime>k__BackingField
VCHllHJUpe
<Branch>k__BackingField
QqClYflKcP
<UserInfo>k__BackingField
cdWlJ4gSBc
<ViaExcel>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Instruction
AppTech.MSMS.Domain.Models.Instruction
Instruction
Instruction
kbvlKvFf14
<ID>k__BackingField
dt8l9fPQX8
<RowVersion>k__BackingField
BrYlOF575K
<Number>k__BackingField
pK4lUffTZT
<Text>k__BackingField
cadlErCrvm
<CreatedBy>k__BackingField
nUblSQjj40
<BranchID>k__BackingField
g9jlp5fOpu
<CreatedTime>k__BackingField
g2KlLO1bvF
<Branch>k__BackingField
tkolaw4pdT
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Item
AppTech.MSMS.Domain.Models.Item
Item
Item
QJyl0jnigm
<ID>k__BackingField
yDilQo7ltw
<RowVersion>k__BackingField
GFAl3swpsh
<Number>k__BackingField
e9ZlvsMBJ7
<Name>k__BackingField
cg6lZCoEM4
<ServiceID>k__BackingField
k1Glm2T3Rb
<ItemID>k__BackingField
OnslCyO8sU
<Type>k__BackingField
gLElzFxn7A
<ChildID>k__BackingField
sdbY2nv5q8
<ChildType>k__BackingField
bRBYXT258v
<CostPrice>k__BackingField
sPOYMtJqPN
<SalePrice>k__BackingField
OU6Yj6UmkQ
<PersonnalPrice>k__BackingField
BLWYu4rn8J
<AgentPrice>k__BackingField
hKUYqrk9gx
<BranchPrice>k__BackingField
JBCYW8ux5L
<OriginalPrice>k__BackingField
J9DY4s7vaK
<StartAmount>k__BackingField
pr4YDXyBd6
<EndAmount>k__BackingField
MuPYsQplpQ
<InRange>k__BackingField
sjdY1q6cm3
<ByPercentage>k__BackingField
E8tY6aiNZe
<RayalMobile>k__BackingField
Gu7YfjfSla
<ByItemID>k__BackingField
bJoYH6MimG
<Flag>k__BackingField
HMXYBsBJTm
<OrderNo>k__BackingField
Ar6YtPhybF
<Code>k__BackingField
AeTYxSQnoa
<RefID>k__BackingField
ItZY5PZh5w
<Note>k__BackingField
tCNYrIw5vP
<CategoryID>k__BackingField
J5ZYGkqsMH
<ProviderID>k__BackingField
nP9Yg9o8O4
<CreatedBy>k__BackingField
CyJYVjgk47
<BranchID>k__BackingField
EAwYeplmi3
<CreatedTime>k__BackingField
DqIYn1Q3EM
<Description>k__BackingField
J0yY8rYsE4
<Status>k__BackingField
p4xYo88gUy
<ClassType>k__BackingField
VaHYTGm7MT
<RefNumber>k__BackingField
xEOYPiUD6X
<Branch>k__BackingField
YlCYR9xM7M
<ServiceInfo>k__BackingField
k0wYA90Z4X
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ItemCost
AppTech.MSMS.Domain.Models.ItemCost
ItemCost
ItemCost
CFqYb4BYgk
<ID>k__BackingField
GymYIcCiO5
<RowVersion>k__BackingField
xDyYNZnndh
<ServiceID>k__BackingField
qvBYkAUgWf
<ProviderID>k__BackingField
og6Y7MYdkR
<Price>k__BackingField
JVCYc08RZf
<Type>k__BackingField
FZlYibCqN0
<Note>k__BackingField
UKOYdqt25Z
<CreatedBy>k__BackingField
XqOYwBJwSt
<BranchID>k__BackingField
msmYyKmXcq
<CreatedTime>k__BackingField
fLwYhUAi4C
<Branch>k__BackingField
YjnYFSyL8n
<TopupProvider>k__BackingField
sLBYl9C5XV
<ServiceInfo>k__BackingField
nS3YYrZQ2j
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Journal
AppTech.MSMS.Domain.Models.Journal
Journal
Journal
EAaYJ63aKq
<ID>k__BackingField
tIGYKMh7KI
<VoucherID>k__BackingField
U4LY9M4xmY
<Number>k__BackingField
Y53YO2I7xg
<EntryID>k__BackingField
HEQYU2JK5w
<Date>k__BackingField
U5YYE464kb
<CreatedBy>k__BackingField
An8YSnqckh
<Debited>k__BackingField
snyYppRjUU
<CreatedTime>k__BackingField
gkTYL6YJcd
<BranchID>k__BackingField
sYWYa7f3pc
<Year>k__BackingField
glGY0ECBbk
<Status>k__BackingField
G8vYQAiZWX
<TotalAmount>k__BackingField
knEY3HUUr5
<SyncJournalID>k__BackingField
cLrYvuo6YO
<datestamb>k__BackingField
Xm2YZVNe2Y
<EntrySource>k__BackingField
r0GYmywvAn
<Depended>k__BackingField
WluYCiq6J5
<RefNumber>k__BackingField
u6oYzfX7IV
<CurrencyID>k__BackingField
XePJ2Lg0gj
<Branch>k__BackingField
KmQJXmDG7Z
<CommissionReceipts>k__BackingField
wiyJMljelm
<ConsumeInvoices>k__BackingField
u87Jj906n9
<JournalEntries>k__BackingField
khdJueqAm2
<UserInfo>k__BackingField
EFTJq1y0OQ
<Voucher>k__BackingField
m1eJW4pRft
<OrderInfoes>k__BackingField
cq3J4ecn3B
<PurchaseInvoices>k__BackingField
YOxJDv9TQH
<RemittanceIns>k__BackingField
vH8JsPrR7O
<RemittanceOuts>k__BackingField
KfaJ13Hj9g
<RiyalMobiles>k__BackingField
RcuJ6YH8gO
<SaleInvoices>k__BackingField
bjmJfILc7r
<Topups>k__BackingField
qrQJH91vyr
<TopupCommissions>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.JournalEntry
AppTech.MSMS.Domain.Models.JournalEntry
JournalEntry
JournalEntry
Bk5JBayXOZ
<ID>k__BackingField
BK2Jt8Lrh8
<ParentID>k__BackingField
uAsJxjUl2F
<Amount>k__BackingField
JORJ5Asxxf
<CurrencyID>k__BackingField
u5PJroE4aH
<DCAmount>k__BackingField
zEoJGKDVcN
<AccountID>k__BackingField
JY0JgFFwHc
<CostCenterID>k__BackingField
duCJVUrTgl
<Note>k__BackingField
zovJeCwlVT
<Datestamp>k__BackingField
vRyJnbdy0J
<ExchangeRate>k__BackingField
URsJ8O3VwP
<SyncEntryID>k__BackingField
OEqJoF5WN3
<Account>k__BackingField
q17JTVjmIj
<Currency>k__BackingField
eQKJPF4Bx9
<Journal>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.LiveTopup
AppTech.MSMS.Domain.Models.LiveTopup
LiveTopup
LiveTopup
MMZJRn0hhj
<ID>k__BackingField
YBKJAenYGZ
<RowVersion>k__BackingField
Yb5JbitUjS
<ProviderID>k__BackingField
chQJIUDbtB
<ServiceID>k__BackingField
SHkJNqduMy
<ServiceCode>k__BackingField
YcCJkRVuPA
<Active>k__BackingField
q4HJ7CQTLM
<CreatedBy>k__BackingField
jNkJcxdEY5
<BranchID>k__BackingField
PtHJiM8htY
<CreatedTime>k__BackingField
euOJdNh3RS
<ProviderCommission>k__BackingField
cyQJwCZgoO
<BranchCommission>k__BackingField
QtpJycVOV2
<AccountID>k__BackingField
eVbJhOYJ8f
<ActiveForAccount>k__BackingField
ATkJF9JLhZ
<Type>k__BackingField
MBmJloZ3XC
<Status>k__BackingField
XuHJYHO2Zm
<Flag>k__BackingField
IW9JJnBOMQ
<Bundling>k__BackingField
emAJKEs4Av
<ProcessMethod>k__BackingField
NBEJ9KcDgr
<ConnectMethod>k__BackingField
aRQJObKaRW
<AccountState>k__BackingField
ufwJUhbmSj
<AccountGroupID>k__BackingField
wIdJE82sMc
<Account>k__BackingField
pOmJSEYokl
<Branch>k__BackingField
OCZJpXdMTW
<ServiceInfo>k__BackingField
albJLbOfBq
<TopupProvider>k__BackingField
YhvJaQpS5D
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.LoanOrder
AppTech.MSMS.Domain.Models.LoanOrder
LoanOrder
LoanOrder
COaJ0LxIp1
<ID>k__BackingField
gf7JQPr9MI
<RowVersion>k__BackingField
i1uJ37HQ6D
<ServiceID>k__BackingField
l8gJv9SmJ8
<AccountID>k__BackingField
B41JZHxvCj
<Amount>k__BackingField
oe6JmGDS8q
<CurrencyID>k__BackingField
pulJCWVBXL
<Note>k__BackingField
X9CJzTho8X
<Channel>k__BackingField
y8cK2McyhP
<ParentID>k__BackingField
IEqKXCsIgi
<Status>k__BackingField
hD2KMo79hc
<CreatedBy>k__BackingField
P5UKjS0qnJ
<BranchID>k__BackingField
E6YKubHYXP
<CreatedTime>k__BackingField
KwPKqcl3U5
<Account>k__BackingField
z7SKWX7bCc
<Branch>k__BackingField
KN0K4mjRw9
<Currency>k__BackingField
xteKDdlant
<OrderInfo>k__BackingField
ga1KsReWlX
<ServiceInfo>k__BackingField
CFnK1By9g3
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Log
AppTech.MSMS.Domain.Models.Log
Log
Log
tx9K65Cfkr
<ID>k__BackingField
LbSKf2aCpr
<TimeStamp>k__BackingField
kFLKHGZ5uN
<Level>k__BackingField
yB5KBY2PPo
<Type>k__BackingField
LTiKtM7NZp
<Logger>k__BackingField
VorKxFbewX
<Message>k__BackingField
Ar2K5GF3X4
<Username>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Membership
AppTech.MSMS.Domain.Models.Membership
Membership
Membership
GUrKr786SN
<ID>k__BackingField
lD4KGacrkI
<UserID>k__BackingField
YeMKgulX8x
<CreateDate>k__BackingField
gyeKVRxD1R
<ConfirmationToken>k__BackingField
SvWKe4OueZ
<IsConfirmed>k__BackingField
oU0KncjHdJ
<LastPasswordFailureDate>k__BackingField
HcfK8fGeZi
<PasswordFailuresSinceLastSuccess>k__BackingField
s95KoEDDTI
<Password>k__BackingField
j6fKTQUwsi
<PasswordChangedDate>k__BackingField
YOyKPVktWG
<PasswordSalt>k__BackingField
CVtKRNf0hk
<PasswordVerificationToken>k__BackingField
cbeKAuvM2U
<PasswordVerificationTokenExpirationDate>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Merchant
AppTech.MSMS.Domain.Models.Merchant
Merchant
Merchant
qxgKbLIn7f
<ID>k__BackingField
uYFKIts3If
<RowVersion>k__BackingField
mqQKNDsGQ8
<Number>k__BackingField
eJeKkQd8Oq
<Name>k__BackingField
zEyK78Qg2r
<CategoryID>k__BackingField
k0oKcOIn4s
<Description>k__BackingField
vyNKiwAAcH
<OwnerName>k__BackingField
tAPKdQbKrq
<AccountID>k__BackingField
sreKw0j1jt
<PhoneNumber>k__BackingField
LA4Ky801mQ
<ContactNumber>k__BackingField
CcaKhl8kE2
<Address>k__BackingField
mowKFN6bsP
<Note>k__BackingField
kRGKlVfPoV
<Email>k__BackingField
p1GKYG249H
<CreatedBy>k__BackingField
GjdKJ3lN1J
<BranchID>k__BackingField
tFYKKlHfmY
<CreatedTime>k__BackingField
LNhK9jnail
<ImageName>k__BackingField
tATKOFfELS
<Status>k__BackingField
Bd1KU2chao
<InvoiceImage>k__BackingField
xyhKE9LgUg
<SyncAccountID>k__BackingField
lgDKSf59ii
<RefNumber>k__BackingField
ua7KpIN18A
<Extra>k__BackingField
EPXKL0DGkq
<Account>k__BackingField
qCNKa8fFYh
<Branch>k__BackingField
AaQK0Qy8WM
<UserInfo>k__BackingField
CmsKQgncBH
<MerchantPayments>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.MerchantCategory
AppTech.MSMS.Domain.Models.MerchantCategory
MerchantCategory
MerchantCategory
bPPK3eSWLB
<ID>k__BackingField
J5rKvgcLtL
<RowVersion>k__BackingField
qUkKZdbT8o
<Name>k__BackingField
LcaKmFFTnK
<CreatedBy>k__BackingField
ugwKCPEjg3
<BranchID>k__BackingField
Sy8KzeQBvA
<CreatedTime>k__BackingField
mCk92mxUtY
<ImageName>k__BackingField
i1I9Xriase
<Branch>k__BackingField
Io39Mkuqpp
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.MerchantPayment
AppTech.MSMS.Domain.Models.MerchantPayment
MerchantPayment
MerchantPayment
dqa9jDw3ZB
<ID>k__BackingField
gwp9uBfrv7
<RowVersion>k__BackingField
WUm9qNFGG7
<Number>k__BackingField
HVE9WRjGdx
<Amount>k__BackingField
qsw94xSdmE
<CurrencyID>k__BackingField
fXT9DpjenF
<CreditorAccountID>k__BackingField
Ypv9s67p7d
<DebitorAccountID>k__BackingField
Cia919jBqB
<MerchantID>k__BackingField
A3u965i2ow
<Date>k__BackingField
fZC9fXdLhg
<Note>k__BackingField
C2E9HfGSQk
<EntryID>k__BackingField
muu9BOqSas
<IsDebited>k__BackingField
E2L9t5SUTr
<RefNumber>k__BackingField
Vuc9xX8HSI
<CreatedBy>k__BackingField
c8F95iUIO9
<BranchID>k__BackingField
M4p9r4oqKy
<CreatedTime>k__BackingField
S9Y9GUBUPK
<InvoiceNumber>k__BackingField
ShA9gSyGAx
<Year>k__BackingField
lOd9V5a0yv
<Status>k__BackingField
zo39eBFgKi
<InvoiceImage>k__BackingField
zPK9n80IqK
<Extra>k__BackingField
UA598y8tJ2
<Channel>k__BackingField
fc69ojkay2
<CreatedDate>k__BackingField
YXU9TNTLNw
<HourTime>k__BackingField
fpI9Psywbl
<MinuteTime>k__BackingField
sQY9RyTOHk
<TransNumber>k__BackingField
BJt9AyLnQF
<Attachments>k__BackingField
jIS9bi9URS
<ExtraInfo>k__BackingField
acO9IcBtEy
<ExtraID>k__BackingField
zSt9Nhe3HA
<SyncID>k__BackingField
Ajc9k8LeqK
<RefID>k__BackingField
ykd97YQA4o
<BindID>k__BackingField
t2n9cw2ykP
<Binded>k__BackingField
wE99iruhg3
<Account>k__BackingField
SLe9dDEAPD
<Account1>k__BackingField
qCY9wijYlJ
<Branch>k__BackingField
tSB9y3s5a9
<Currency>k__BackingField
LFE9hHn9tt
<Merchant>k__BackingField
MoF9FI6tax
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Entities
AppTech.MSMS.Domain.Models.Entities
Entities
Entities
bX59lJt5Kh
<Accounts>k__BackingField
Yvc9YGCOZi
<AccountApis>k__BackingField
Ucf9JM57Qw
<AccountDocuments>k__BackingField
jjD9KGNsh3
<AccountLedgers>k__BackingField
ihm99X6lGQ
<AccountNotifications>k__BackingField
pQN9OhS8uu
<AccountRegions>k__BackingField
y0s9UFpBAj
<AccountSlatings>k__BackingField
NND9Epy132
<AccountUsers>k__BackingField
uke9SF57w3
<Addresses>k__BackingField
GGP9pladvi
<AdminNotifications>k__BackingField
t2J9LvYLNh
<Agents>k__BackingField
TIn9ayj0oK
<AgentPoints>k__BackingField
SpG90IYl0P
<AgentPointUsers>k__BackingField
WSe9Qe7RjF
<AuditLogs>k__BackingField
WIg93MUOwS
<Bagats>k__BackingField
O6d9vf2tSr
<BagatPayments>k__BackingField
nY39ZSchAx
<Banks>k__BackingField
WnT9mLyhhg
<BankDeposits>k__BackingField
vLm9CN8kq4
<Branches>k__BackingField
qX89zm1pHs
<BranchTargets>k__BackingField
ubAO26uDtd
<Brochures>k__BackingField
D3bOXuR2Du
<Bundles>k__BackingField
n5gOM2qg12
<BuyCurrencies>k__BackingField
v0POjD5ZwG
<Cards>k__BackingField
VDDOu3M37p
<CardFactions>k__BackingField
g1qOq1ii2Y
<CardOrders>k__BackingField
DpyOWVZ7Zx
<CardPayments>k__BackingField
oVdO4t1rCt
<CardTypes>k__BackingField
T44OD57LQG
<CashDeposits>k__BackingField
xRFOsycQg5
<CashIns>k__BackingField
zDRO1k5OL0
<CashOuts>k__BackingField
JV2O6bxTse
<CashTransfers>k__BackingField
YZCOfwrwlD
<CashWithdraws>k__BackingField
ShaOHXkDv7
<Cheques>k__BackingField
GXZOBQZVTY
<ClaimGroups>k__BackingField
YM5Ot3ZFuQ
<Clients>k__BackingField
AQhOxscxdL
<CommissionReceipts>k__BackingField
NUIO5ZrAnm
<CommissionReceiptLines>k__BackingField
XehOrrJplO
<Companies>k__BackingField
yOrOG5iGk0
<ConsumeInvoices>k__BackingField
E62OggmLS7
<Countries>k__BackingField
JGdOVLc9ph
<CoverageOrders>k__BackingField
Py3OeM24ZP
<Currencies>k__BackingField
PaPOnmVTNy
<CurrencyExchanges>k__BackingField
ssJO8ujH7C
<CurrencyRates>k__BackingField
oOJOoW1W4T
<CurrencyRateAccounts>k__BackingField
WuaOTRWGgt
<DbBackups>k__BackingField
BgBOPorHh7
<DepositOrders>k__BackingField
ztDORYruGg
<Devices>k__BackingField
RjLOAdrrCb
<Distributors>k__BackingField
lqCObaChxV
<ELMAH_Error>k__BackingField
IEpOInZSxT
<Errors>k__BackingField
qcqONjJye4
<Exchangers>k__BackingField
TucOkSi03y
<ExchangerTargets>k__BackingField
U0FO78J3kr
<ExternalBranches>k__BackingField
RVrOcvuCJo
<Factions>k__BackingField
DCPOihkorS
<Feedbacks>k__BackingField
EJkOdSYjl4
<Funds>k__BackingField
hauOwdRjRs
<FundUsers>k__BackingField
IHkOyQNWCa
<GeneralInfoes>k__BackingField
j4XOh5aZod
<GroupItems>k__BackingField
sBEOFUH5Pk
<Gsms>k__BackingField
LuiOlvJWwy
<Instructions>k__BackingField
EfhOYJAp1L
<Items>k__BackingField
diOOJhgZTK
<ItemCosts>k__BackingField
uoqOKTfRjh
<Journals>k__BackingField
CxhO9emQPv
<JournalEntries>k__BackingField
c6sOOA96wU
<LiveTopups>k__BackingField
mjVOUwERPr
<LoanOrders>k__BackingField
eNCOEVMSw7
<Logs>k__BackingField
LLSOSi2Ngw
<Memberships>k__BackingField
BX4Opa2ThY
<Merchants>k__BackingField
RxPOLpHOFc
<MerchantCategories>k__BackingField
CNJOaoxP9X
<MerchantPayments>k__BackingField
yDsO09mLQ9
<MoneyMasters>k__BackingField
AgPOQ6fA07
<Numberings>k__BackingField
DSkO3Aqps3
<OfferOrders>k__BackingField
VydOvjGuRS
<OpeningBalances>k__BackingField
L31OZQ01P0
<OrderInfoes>k__BackingField
BxeOmaywgP
<OrderSatelliteQuotas>k__BackingField
hNSOCP8qvs
<PageActions>k__BackingField
UjKOzIDMHg
<PageInfoes>k__BackingField
eQpU2cufn6
<Parties>k__BackingField
FuSUXrPjrk
<PartyGroups>k__BackingField
oicUMmlIuG
<Payments>k__BackingField
qd9UjfbHD5
<PaymentCommissions>k__BackingField
vR8UuPOxcN
<People>k__BackingField
cSCUqRAVHs
<Products>k__BackingField
J4WUWt2gyZ
<ProductCategories>k__BackingField
HGcU4T3wyb
<ProductImages>k__BackingField
nSIUDBk71p
<Provinces>k__BackingField
wKtUsVr7uq
<PurchaseInvoices>k__BackingField
vUGU1yA2qL
<PurchaseInvoiceLines>k__BackingField
AF4U6kxAGF
<Quotations>k__BackingField
uv2UfyYIpo
<ReceiptCreditors>k__BackingField
G6WUHVfKRu
<ReceiptDebitors>k__BackingField
AhhUBPABlq
<Regions>k__BackingField
zV2UtK7p0j
<RemittanceCommissions>k__BackingField
baqUxydgKm
<RemittanceIns>k__BackingField
DEpU54DfJm
<RemittanceNumbers>k__BackingField
bLDUrB1NuL
<RemittanceOuts>k__BackingField
chrUG2x7J3
<RemittancePoints>k__BackingField
ywlUgRCNDa
<RemittanceRegions>k__BackingField
s2HUVli3X7
<RiyalMobiles>k__BackingField
WCtUeBZhhc
<RoleClaims>k__BackingField
vQiUniMEdT
<RoleInfoes>k__BackingField
RXmU8LV7oi
<RSSes>k__BackingField
VD8Uo0fuXM
<SaleCurrencies>k__BackingField
fxWUTTFWkD
<SaleInvoices>k__BackingField
BYDUP3Ebhx
<SaleInvoiceLines>k__BackingField
yHRURV45E8
<SatelliteFactions>k__BackingField
prKUAUgiYX
<SatellitePayments>k__BackingField
SLAUb6DSSb
<SatelliteProviders>k__BackingField
j23UIccHZw
<ServiceClaims>k__BackingField
yQ3UNHBwjQ
<ServiceInfoes>k__BackingField
HfXUkKL2ut
<Settings>k__BackingField
lNHU77BjYV
<Sims>k__BackingField
OdsUctgwgO
<SimCardOrders>k__BackingField
IFlUiygeWA
<SimInvoices>k__BackingField
OmfUd69h6J
<SimpleEntries>k__BackingField
lOqUwa1QQd
<SimPurchases>k__BackingField
N9TUyFdWG2
<SMSDispatches>k__BackingField
b5NUhvYwpY
<SMSLogs>k__BackingField
zQxUFeW5DK
<SMSMessages>k__BackingField
uYDUlFLtRw
<SpecialSims>k__BackingField
Xs4UYQwRdr
<Subscribers>k__BackingField
sLMUJuub81
<Suppliers>k__BackingField
wW5UKDCPZp
<SuspendTopups>k__BackingField
TQTU9xnQhP
<Topups>k__BackingField
cZeUOhVGqN
<TopupClosures>k__BackingField
n3xUUumg8y
<TopupCommissions>k__BackingField
rSDUEvbfl1
<TopupNetworks>k__BackingField
cTnUSUknH8
<TopupOrders>k__BackingField
Be5Up6fjAC
<TopupProviders>k__BackingField
h9vULsEESj
<TrailToupCommissions>k__BackingField
aFOUaAqCWb
<TrailToupOrders>k__BackingField
wn0U0VmReZ
<TransferIns>k__BackingField
S9VUQiSmdt
<TransferOrders>k__BackingField
NMwU3Ln7S2
<TransferOuts>k__BackingField
aHnUvbQir5
<Transporters>k__BackingField
wKUUZiwRYI
<TransportOrders>k__BackingField
RNNUm3OEOf
<UserClaims>k__BackingField
Wp5UCuTUWY
<UserDevices>k__BackingField
hllUz73WY6
<UserInfoes>k__BackingField
R7OE2r4kut
<UserPages>k__BackingField
IpgEXfKJXg
<UserPermissions>k__BackingField
oOLEMQ3xn3
<UserRoles>k__BackingField
GROEjJHlEh
<UserTokens>k__BackingField
zSeEulrV7a
<Vouchers>k__BackingField
aoMEq6f3X9
<WERegions>k__BackingField
gPuEWC7FcJ
<WifiCards>k__BackingField
ahBE4m7PnK
<WifiFactions>k__BackingField
e1WEDDoPD9
<WifiPayments>k__BackingField
xAqEsSeAAQ
<WifiProviders>k__BackingField
PxhE1gdydV
<WithdrawOrders>k__BackingField
YvpE6yhcaR
<DbScripts>k__BackingField
uvMEfaNL7B
<PaymentStatus>k__BackingField
Gl7EH4bey6
<Parties1>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.MoneyMaster
AppTech.MSMS.Domain.Models.MoneyMaster
MoneyMaster
MoneyMaster
l3AEB40qrv
<ID>k__BackingField
u21EtxqpQZ
<RowVersion>k__BackingField
tmmExFl9mG
<Name>k__BackingField
Te9E5VMhsZ
<CreatedBy>k__BackingField
J8dEr9HnIM
<BranchID>k__BackingField
U70EGgIXsN
<CreatedTime>k__BackingField
R6tEgt2fqs
<ObjectName>k__BackingField
qEdEVbnjWn
<Branch>k__BackingField
yKYEevLg6i
<UserInfo>k__BackingField
T4MEnSG67l
<OrderInfoes>k__BackingField
nPvE89N7gb
<ServiceInfoes>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Numbering
AppTech.MSMS.Domain.Models.Numbering
Numbering
Numbering
MrNEoYUYdW
<ID>k__BackingField
ftlETJEpWJ
<Number>k__BackingField
nCwEPyS7m8
<Name>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OfferOrder
AppTech.MSMS.Domain.Models.OfferOrder
OfferOrder
OfferOrder
KOYERE68bl
<ID>k__BackingField
oM5EA0JlF4
<RowVersion>k__BackingField
RiNEbH8Ndb
<ParentID>k__BackingField
TB7EIhAdRO
<ServiceID>k__BackingField
dwNENJqSda
<AccountID>k__BackingField
Kv7EkVqIRt
<SubscriberNo>k__BackingField
amPE79E3yB
<Amount>k__BackingField
jM8Ecr0ul6
<Description>k__BackingField
AqUEiiZ8dy
<NetworkID>k__BackingField
lZHEdCHLqS
<OfferType>k__BackingField
qqJEwdYNxb
<SimType>k__BackingField
wRNEyFjxB6
<Note>k__BackingField
SKEEhd7Fix
<Channel>k__BackingField
sypEFor1tD
<ImageName>k__BackingField
TNlElAFo1R
<CreatedBy>k__BackingField
efFEYq68kn
<BranchID>k__BackingField
EB0EJvlVSb
<CreatedTime>k__BackingField
fb5EKmAu60
<Account>k__BackingField
MUNE9v424i
<Branch>k__BackingField
W4yEOh19ju
<OrderInfo>k__BackingField
VcqEUtbGSH
<ServiceInfo>k__BackingField
t2bEEDfq1P
<TopupNetwork>k__BackingField
BYXESTejHR
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OpeningBalance
AppTech.MSMS.Domain.Models.OpeningBalance
OpeningBalance
OpeningBalance
rLaEp1gfOl
<ID>k__BackingField
VhmELWgKsx
<RowVersion>k__BackingField
iHtEaXooNq
<Number>k__BackingField
BrME0n6T2d
<AccountID>k__BackingField
hOwEQIgkn6
<Amount>k__BackingField
w3WE3K4awe
<CurrencyID>k__BackingField
MLTEv5d71s
<EntryID>k__BackingField
HZjEZZy41N
<Date>k__BackingField
qndEmtc7nu
<Note>k__BackingField
JwiECU5GLu
<CreatedBy>k__BackingField
kJxEzPWYFW
<BranchID>k__BackingField
YP4S2QH4ni
<CreatedTime>k__BackingField
W7bSXojlrE
<Account>k__BackingField
PqZSMfB0ox
<Branch>k__BackingField
aQ8SjHRZhv
<Currency>k__BackingField
itXSuf6U1g
<UserInfo>k__BackingField
VcgSqrHXmv
<ExchangeAccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OrderInfo
AppTech.MSMS.Domain.Models.OrderInfo
OrderInfo
OrderInfo
FjpSWaey4M
<ID>k__BackingField
zxwS4cPSRn
<RowVersion>k__BackingField
wmnSDuGa9w
<Number>k__BackingField
XfqSslDglS
<ServiceCategoryID>k__BackingField
nqeS1RkVHd
<ServiceID>k__BackingField
DGDS67CMTu
<OrderType>k__BackingField
FwDSfGKxFa
<Amount>k__BackingField
ua0SH6kCfW
<CurrencyID>k__BackingField
XIMSBIf4xH
<CreditorAccountID>k__BackingField
UTMSt9Ai8x
<MCAmount>k__BackingField
fdkSxq1wX7
<DebitorAccountID>k__BackingField
yUfS5sN26u
<Description>k__BackingField
vXNSrCu57Y
<Date>k__BackingField
zu0SG4M50F
<Channel>k__BackingField
yTdSgCO0PO
<Note>k__BackingField
KRWSVrlfHW
<ExtraAmount>k__BackingField
ESaSejmbWp
<RefNumber>k__BackingField
V1TSnDivuy
<IsAmountModified>k__BackingField
v69S8inHGc
<AmountModifiedBy>k__BackingField
AQ0SoLjg2A
<State>k__BackingField
L0AST0p09B
<InUse>k__BackingField
eFqSPGJdlo
<UsageTime>k__BackingField
OJ8SRyfnmh
<BindBy>k__BackingField
PrWSAVvGd8
<IsDebited>k__BackingField
iJKSbnNjwF
<DebitedBy>k__BackingField
lf4SIqM1tJ
<IsRejected>k__BackingField
LcGSNrbTDl
<RejectReason>k__BackingField
NxfSkOdX4k
<RejectedBy>k__BackingField
Y5oS7JnOfa
<EntryID>k__BackingField
vswSc3RYoq
<ServiceEntryID>k__BackingField
FONSiYe3Yb
<CreatedBy>k__BackingField
qvwSdyOtEI
<BranchID>k__BackingField
nDDSw6rGWS
<CreatedTime>k__BackingField
e9tSypfbv8
<Year>k__BackingField
JXcSh8ePj7
<Status>k__BackingField
TpZSFTpcrU
<AccountID>k__BackingField
ADeSlM92gN
<SubNote>k__BackingField
EpASYS6GMZ
<CommissionAmount>k__BackingField
XCWSJJEbNG
<CommissionCurrencyID>k__BackingField
N3gSK70hyc
<Account>k__BackingField
u8LS9Fner8
<BankDeposits>k__BackingField
UAcSOqqIc6
<Branch>k__BackingField
dxeSUulaYt
<CardOrders>k__BackingField
QYqSEyDg52
<CardPayments>k__BackingField
YQPSSJSHSC
<CoverageOrders>k__BackingField
dgtSpIgjsj
<CurrencyExchanges>k__BackingField
M3kSLWR29g
<DepositOrders>k__BackingField
oEeSa2nJgT
<Journal>k__BackingField
nJuS0wP98c
<LoanOrders>k__BackingField
MZASQWUZYy
<MoneyMaster>k__BackingField
TqaS3XYYwf
<OfferOrders>k__BackingField
ApGSvxrltA
<OrderInfo1>k__BackingField
GUOSZbEeQR
<OrderInfo2>k__BackingField
lUESmQdvqm
<Payment>k__BackingField
rwvSCAPyn0
<ServiceInfo>k__BackingField
a9fSzVcorE
<UserInfo>k__BackingField
DRQp2MFRxl
<SimCardOrders>k__BackingField
buMpXlOP4w
<OrderInfo11>k__BackingField
nLZpM2lQW0
<OrderInfo3>k__BackingField
MjVpjG8nMP
<UserInfo1>k__BackingField
vnQpucpn56
<UserInfo2>k__BackingField
oWMpqj88wY
<TopupOrders>k__BackingField
KyIpW0jZQn
<TrailToupOrders>k__BackingField
i4Up4IKPst
<TransferOrders>k__BackingField
vgCpDx49wl
<TransportOrders>k__BackingField
k6ZpslV7i5
<ExchangerAccountID>k__BackingField
kB6p1QyRuF
<TransactionID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PageAction
AppTech.MSMS.Domain.Models.PageAction
PageAction
PageAction
PDip6IRkMA
<ID>k__BackingField
qGppfs3MmK
<ControlName>k__BackingField
KUMpHXlu4Y
<Title>k__BackingField
R1OpBhD02T
<PageType>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PageInfo
AppTech.MSMS.Domain.Models.PageInfo
PageInfo
PageInfo
SNZptaeokK
<ID>k__BackingField
FAjpxipDpN
<PageName>k__BackingField
k6Lp5MllC4
<Title>k__BackingField
Jn5prRb1k4
<Assembly>k__BackingField
TVRpGQsP2I
<PageType>k__BackingField
FwppgfUmNl
<Visible>k__BackingField
D3ipVnRtJN
<Description>k__BackingField
aXCpeHXFNv
<Prefix>k__BackingField
BHEpnajvCH
<Class>k__BackingField
N4Cp85wEwt
<OrderNumber>k__BackingField
p1Ypo9pfpq
<Role>k__BackingField
FtrpTv5c4f
<Module>k__BackingField
MhmpPue55t
<ModuleTitle>k__BackingField
tifpRWYfI8
<Version>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Party
AppTech.MSMS.Domain.Models.Party
Party
Party
qxLpAydCS2
<ID>k__BackingField
cZFpbdXi51
<RowVersion>k__BackingField
VjdpIs5Z4P
<AccountID>k__BackingField
FwEpNqZpcj
<PhoneNumber>k__BackingField
nxopkObFNI
<ClaimGroupID>k__BackingField
rsQp7M0MRi
<Status>k__BackingField
LA1pc0Ftxe
<Token>k__BackingField
k02pixV2OM
<Note>k__BackingField
k5ypdPUnML
<SyncAccountID>k__BackingField
oxspwBH412
<CreatedBy>k__BackingField
nSSpyXG9Ev
<BranchID>k__BackingField
CHbphxVipW
<CreatedTime>k__BackingField
aNEpFvbxOj
<Account>k__BackingField
o7jplbdUT8
<Branch>k__BackingField
E4apYUsffT
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Party1
AppTech.MSMS.Domain.Models.Party1
Party1
Party1
QXApJCX50l
<ID>k__BackingField
hjxpKomDRh
<Number>k__BackingField
O1Sp9HIOj0
<Name>k__BackingField
pQGpOBTeQl
<PhoneNumber>k__BackingField
clSpUK1YGx
<AccountID>k__BackingField
g4bpEyUGYx
<Address>k__BackingField
aPcpSIMhYy
<Type>k__BackingField
meFppTVNye
<UserType>k__BackingField
KKIpLpVLlJ
<BranchID>k__BackingField
FWopaXXOAq
<SyncAccountID>k__BackingField
LiUp0Y4juW
<Status>k__BackingField
rdqpQgGBbI
<AgentID>k__BackingField
DXlp3RrEkk
<DistributorID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PartyGroup
AppTech.MSMS.Domain.Models.PartyGroup
PartyGroup
PartyGroup
iWJpvqx6EA
<ID>k__BackingField
diypZnnQqs
<RowVersion>k__BackingField
qvfpmW0LnR
<Name>k__BackingField
I2SpCdFBq9
<Type>k__BackingField
T3mpz3pyIe
<KeyName>k__BackingField
i2AL2uS7NA
<Description>k__BackingField
aVRLX6X5P2
<BranchID>k__BackingField
wbvLMAkxJV
<CreatedBy>k__BackingField
Nf0LjWKOgC
<CreatedTime>k__BackingField
iUbLuWEoAh
<Branch>k__BackingField
X7nLqL5vOv
<GroupItems>k__BackingField
bu6LWDdxxC
<UserInfo>k__BackingField
YTvL4U7DTC
<SelectedItems>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Payment
AppTech.MSMS.Domain.Models.Payment
Payment
Payment
zHmLDWvsdB
<ID>k__BackingField
uZgLssS0qx
<RowVersion>k__BackingField
hA2L1yahyP
<Number>k__BackingField
qV2L6DXpEQ
<RecordID>k__BackingField
jFMLfXcnet
<ServiceID>k__BackingField
Lp3LHdf3d9
<Amount>k__BackingField
UD6LBkZRax
<Debited>k__BackingField
KRwLt5knyg
<Date>k__BackingField
WwULxKmege
<CreatedBy>k__BackingField
U6FL5eUvXB
<BranchID>k__BackingField
QVeLrHxEHg
<CreatedTime>k__BackingField
bJCLGr2jxO
<Note>k__BackingField
cZKLgPHdNs
<Year>k__BackingField
ugSLVghHTU
<Status>k__BackingField
p9ILeJSdCv
<Method>k__BackingField
hT5Ln2SWZJ
<Datestamp>k__BackingField
i8yL8WyD2u
<Channel>k__BackingField
VFoLoIgIaA
<CurrencyID>k__BackingField
qJULTHgOOc
<IsSynced>k__BackingField
y5GLPiU17x
<UserID>k__BackingField
NxJLRIdZrg
<CrOrDr>k__BackingField
suBLArGTks
<AccountID>k__BackingField
FBTLb3qEej
<Account>k__BackingField
Vu7LII6H3p
<Branch>k__BackingField
xoBLNYLoeS
<Currency>k__BackingField
oAcLkY8Wqc
<OrderInfoes>k__BackingField
w09L715NyY
<UserInfo>k__BackingField
RbvLc92jBe
<ServiceInfo>k__BackingField
EGjLiP3nlc
<UserInfo1>k__BackingField
SEALdqvCme
<RiyalMobiles>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PaymentCommission
AppTech.MSMS.Domain.Models.PaymentCommission
PaymentCommission
PaymentCommission
bvNLwZAraX
<ID>k__BackingField
UiJLybuSFF
<RowVersion>k__BackingField
ARpLhas2vh
<ServiceID>k__BackingField
KotLFg4V2n
<FromAmount>k__BackingField
iDLLlULNWK
<ToAmount>k__BackingField
OSMLY51qb0
<TraderAmount>k__BackingField
IYkLJBnYBL
<Note>k__BackingField
E41LK3ME7n
<CreatedBy>k__BackingField
YjbL9UGKSK
<BranchID>k__BackingField
Bg4LO3Vaia
<CreatedTime>k__BackingField
wxQLUxN9y7
<PersonalAmount>k__BackingField
a72LEwunsj
<CurrencyID>k__BackingField
FmoLSCRdhI
<CommissionCurrencyID>k__BackingField
bGvLpn1upo
<AccountState>k__BackingField
SdsLLe2F7E
<AccountID>k__BackingField
aQJLae8FRJ
<AccountGroupID>k__BackingField
xn7L010BQp
<CommissionType>k__BackingField
aFaLQMQFg5
<CurrencyState>k__BackingField
d4XL3ClP7Y
<IsAgainst>k__BackingField
NlKLvOFnoC
<Branch>k__BackingField
aSLLZLvgvm
<Currency>k__BackingField
Xp7LmJGKGy
<ServiceInfo>k__BackingField
sX7LCHOOSD
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PaymentStatu
AppTech.MSMS.Domain.Models.PaymentStatu
PaymentStatu
PaymentStatu
dJyLznOi6r
<ID>k__BackingField
m0Ua2I0bPa
<Name>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Person
AppTech.MSMS.Domain.Models.Person
Person
Person
gCyaXwSLCD
<ID>k__BackingField
hRhaM8mmex
<RowVersion>k__BackingField
D6majdjdec
<Name>k__BackingField
P7AaurLVyD
<PhoneNumber>k__BackingField
cQFaq00o9A
<Address>k__BackingField
PGiaW8X1bD
<Email>k__BackingField
vkBa450QUQ
<Nationality>k__BackingField
cdoaDEdO7D
<CardType>k__BackingField
LqjasnpP3h
<CardNumber>k__BackingField
vWUa170mkb
<CardIssuePlace>k__BackingField
FnLa6vsbNW
<CardIssueDate>k__BackingField
MUkafbqnyW
<CardExpireDate>k__BackingField
qF2aHKiRwD
<FrontImage>k__BackingField
uOSaBqEeZt
<BackImage>k__BackingField
F2uatF77Yw
<CreatedBy>k__BackingField
ELQaxiXaRL
<BranchID>k__BackingField
nrBa5Lvxb9
<CreatedTime>k__BackingField
qZTartxg92
<Type>k__BackingField
H23aG4otFD
<Branch>k__BackingField
hEIagJxNkA
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Product
AppTech.MSMS.Domain.Models.Product
Product
Product
klKaVqNr5t
<ID>k__BackingField
p29aemv47f
<RowVersion>k__BackingField
gcOanY5C4G
<Number>k__BackingField
wNpa8OxniC
<Name>k__BackingField
K87ao5bd8H
<Title>k__BackingField
JQ4aTtoIYd
<Description>k__BackingField
eTfaPDUt8G
<Type>k__BackingField
PU3aR2sVeM
<ClassType>k__BackingField
s5EaAH4OXF
<CategoryID>k__BackingField
phlabUqdEU
<Status>k__BackingField
P99aI7UABk
<Active>k__BackingField
jhZaNwNw5M
<CostPrice>k__BackingField
UVHakkjUAU
<Price>k__BackingField
mnQa7QwbZU
<PersonnalPrice>k__BackingField
PKPac4Hvo5
<CurrencyID>k__BackingField
uUrai95vBr
<UintID>k__BackingField
BMhad5LDHV
<ImageName>k__BackingField
BgrawGiFBI
<Flag>k__BackingField
PA6ay705KE
<SoldOut>k__BackingField
iESahrQ8ss
<IsNew>k__BackingField
P2iaF7MrR0
<Limited>k__BackingField
nTsalha78I
<IsSpecail>k__BackingField
JuAaY0eUoy
<HasDiscount>k__BackingField
jdiaJ8mrAR
<Discount>k__BackingField
E0taKwwhEn
<OrderNo>k__BackingField
v1Ua9uLDFT
<Code>k__BackingField
TQkaOjsLF0
<Tag>k__BackingField
O8PaUHdx6s
<RefID>k__BackingField
EmdaE8CPN5
<Note>k__BackingField
UdPaS9eWnt
<ExtraInfo>k__BackingField
bXrapfPlce
<ExtraID>k__BackingField
PuKaLkevRV
<Dispatcher>k__BackingField
etkaaluDdD
<IsSynce>k__BackingField
Kh9a0ZV0j3
<Direct>k__BackingField
GL5aQnpEDN
<Deleted>k__BackingField
U1Sa3k7CXv
<Seen>k__BackingField
rn3avsjsHR
<Channel>k__BackingField
gapaZYv7oA
<SyncID>k__BackingField
KKyameWLcY
<BindID>k__BackingField
WHwaC394oD
<CreatedBy>k__BackingField
zmsazpqYfI
<BranchID>k__BackingField
Hdx027RXrN
<CreatedTime>k__BackingField
xbn0XQtVAt
<Branch>k__BackingField
QhB0MNDTBQ
<Currency>k__BackingField
Wot0jA65EB
<ProductCategory>k__BackingField
mxs0u7aYqe
<UserInfo>k__BackingField
vH80qQ94JC
<ProductImages>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ProductCategory
AppTech.MSMS.Domain.Models.ProductCategory
ProductCategory
ProductCategory
fnt0WsOkEG
<ID>k__BackingField
giC04bZxhs
<RowVersion>k__BackingField
w6H0D88WY2
<Number>k__BackingField
UJu0sElaYq
<Name>k__BackingField
K0S01SfQoi
<Note>k__BackingField
xUJ06LeDP4
<BranchID>k__BackingField
wkW0fBwL69
<CreatedBy>k__BackingField
KIW0HlYymE
<CreatedTime>k__BackingField
Lp10BF74eb
<Branch>k__BackingField
s630txsUi8
<Products>k__BackingField
Kce0xKmA6I
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ProductImage
AppTech.MSMS.Domain.Models.ProductImage
ProductImage
ProductImage
qXf05m8jAU
<ID>k__BackingField
fSL0r0dZ8D
<RowVersion>k__BackingField
bhe0GbqNgG
<ProductID>k__BackingField
NWi0gx6MrG
<ImageName>k__BackingField
Aqf0Vvqalu
<Title>k__BackingField
XTB0eXssqj
<Description>k__BackingField
BWA0ny5WpD
<Extainfo>k__BackingField
JD708K8V1q
<BranchID>k__BackingField
hoZ0ovtUmY
<CreatedBy>k__BackingField
QdF0T5I8LZ
<CreatedTime>k__BackingField
aDI0PLLANe
<Branch>k__BackingField
pV80RqUuMu
<Product>k__BackingField
TUf0AHQBFQ
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Province
AppTech.MSMS.Domain.Models.Province
Province
Province
R3O0bjvJFI
<ID>k__BackingField
RgR0I6uWEi
<RowVersion>k__BackingField
n7m0NSnrUU
<Name>k__BackingField
xyV0kUl4Km
<CountryID>k__BackingField
OAA0713T5N
<CreatedBy>k__BackingField
jD30ccmpRN
<BranchID>k__BackingField
DSX0inWmZr
<CreatedTime>k__BackingField
Ixm0dP4nhb
<BankDeposits>k__BackingField
LeV0wmIVWN
<Branch>k__BackingField
aCV0yZjrVn
<Country>k__BackingField
aZ30hsRU9j
<UserInfo>k__BackingField
qSn0Ftet97
<Regions>k__BackingField
jjs0lEghjg
<RemittanceRegions>k__BackingField
FpE0YEUhKc
<TransferOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PurchaseInvoice
AppTech.MSMS.Domain.Models.PurchaseInvoice
PurchaseInvoice
PurchaseInvoice
G900JEv5m6
<ID>k__BackingField
v7N0K1gbXb
<RowVersion>k__BackingField
UJr09pau6X
<Number>k__BackingField
RFe0OTptMR
<Amount>k__BackingField
qUq0UT79aF
<CurrencyID>k__BackingField
Xgn0EI0770
<CreditorAccountID>k__BackingField
fe20S6c1cq
<DebitorAccountID>k__BackingField
bAS0p2b1k8
<SupplierID>k__BackingField
isK0LYiVxV
<InvoiceSupplierNo>k__BackingField
qD30aDh7Et
<Discount>k__BackingField
N4T00ghKVX
<Subtotal>k__BackingField
IZm0QiRohd
<Date>k__BackingField
cRb03ZGxcl
<DueDate>k__BackingField
kkC0vYWAEU
<Note>k__BackingField
LiQ0Z0PFOw
<EntryID>k__BackingField
qtV0myjO4y
<InventoryID>k__BackingField
wNs0ChRrO0
<RefNumber>k__BackingField
sJR0zSqGpU
<Extra>k__BackingField
ysdQ2jvAro
<AttachmentNumbers>k__BackingField
GhDQXNeZyg
<Footer>k__BackingField
Yq5QMI9dav
<Year>k__BackingField
TPRQjFG16K
<Status>k__BackingField
RxiQuN5jnb
<CreatedTime>k__BackingField
qjHQq7DjbM
<CreatedBy>k__BackingField
niiQWHJTek
<BranchID>k__BackingField
NJeQ4A84ib
<Account>k__BackingField
xjYQD1Htrt
<Account1>k__BackingField
MU9Qsq6Ivo
<Branch>k__BackingField
M49Q1jI68X
<Currency>k__BackingField
uV5Q6D1bvn
<Journal>k__BackingField
VUkQfhnRny
<UserInfo>k__BackingField
JRLQH9uq7C
<PurchaseInvoiceLines>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PurchaseInvoiceLine
AppTech.MSMS.Domain.Models.PurchaseInvoiceLine
PurchaseInvoiceLine
PurchaseInvoiceLine
ddVQB2vwtZ
<ID>k__BackingField
lypQtfWYGX
<RowVersion>k__BackingField
v3gQxm9G4o
<ParentID>k__BackingField
TcvQ5DQReb
<SerialNumber>k__BackingField
VyHQruUGYF
<ProductID>k__BackingField
L6TQGg0d0J
<Quantity>k__BackingField
KigQgdkvGw
<UnitPrice>k__BackingField
nRSQV2HEj0
<Discount>k__BackingField
JlWQen3kPZ
<SubTotal>k__BackingField
VehQnh35dD
<TotalAmount>k__BackingField
hrIQ8icrQy
<InventoryLineID>k__BackingField
g77Qo7iNyV
<Note>k__BackingField
vqoQTDo2U2
<CreatedTime>k__BackingField
uUTQPK2pD0
<CreatedBy>k__BackingField
MgGQRA6laP
<BranchID>k__BackingField
UWfQA4Bbwf
<Branch>k__BackingField
FdeQbIA35L
<Faction>k__BackingField
HdVQIye3b9
<PurchaseInvoice>k__BackingField
lBCQNaIiMn
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Quotation
AppTech.MSMS.Domain.Models.Quotation
Quotation
Quotation
GfDQkcmkkb
<ID>k__BackingField
qo4Q7WDwZB
<RowVersion>k__BackingField
CLqQcRRFru
<ServiceID>k__BackingField
m31QiOOdPY
<AccountState>k__BackingField
CUHQdmRGBK
<AccountID>k__BackingField
U57QwE4OUD
<AccountGroupID>k__BackingField
JVvQygqbLC
<Price>k__BackingField
W6wQhIoHat
<Price2>k__BackingField
HwOQF8HaGa
<Type>k__BackingField
qEhQlQ3Kit
<Note>k__BackingField
c0JQYiBQ3N
<CreatedBy>k__BackingField
nfwQJ3EfuS
<BranchID>k__BackingField
jRPQKR9fSq
<CreatedTime>k__BackingField
o7vQ9LI54v
<Account>k__BackingField
sEXQOS1DA1
<Branch>k__BackingField
r95QUWIbOZ
<ServiceInfo>k__BackingField
Pf4QEeG0Fy
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ReceiptCreditor
AppTech.MSMS.Domain.Models.ReceiptCreditor
ReceiptCreditor
ReceiptCreditor
EU8QSTb9ZL
<ID>k__BackingField
Fl9QpO5ueT
<RowVersion>k__BackingField
PAqQL2mwbf
<Number>k__BackingField
Fo6QauI4Us
<Amount>k__BackingField
x2PQ0HWRni
<CurrencyID>k__BackingField
kvZQQgSgNn
<CreditorAccountID>k__BackingField
fueQ3Hgugb
<DebitorAccountID>k__BackingField
zUSQvnusW7
<Date>k__BackingField
ttiQZh2tWa
<Note>k__BackingField
AJPQmeQ775
<EntryID>k__BackingField
enIQCVjxTD
<RefNumber>k__BackingField
XLWQz2bUnr
<AttachmentNumbers>k__BackingField
pkP32CiT2G
<IsDebited>k__BackingField
kD53XiGHJp
<CreatedBy>k__BackingField
nt23MFB0m1
<Prints>k__BackingField
Jwc3jpwc6P
<CreatedTime>k__BackingField
jpi3u9qSrJ
<BranchID>k__BackingField
A1p3qIkrZU
<Year>k__BackingField
v1H3WOVgdh
<Status>k__BackingField
PAD34ODrWk
<CreatedDate>k__BackingField
oC93DQUXHs
<HourTime>k__BackingField
REb3sKMP7I
<MinuteTime>k__BackingField
RFk31VqnpP
<TransNumber>k__BackingField
X4b36WV1k5
<Channel>k__BackingField
nN83flnINY
<Attachments>k__BackingField
TWM3HWZIpj
<ExtraInfo>k__BackingField
VPj3BZO7fw
<ExtraID>k__BackingField
VXo3t8g0Fx
<SyncID>k__BackingField
FBH3xmKYMl
<RefID>k__BackingField
P0a35J08DA
<BindID>k__BackingField
OKX3raYLwM
<Binded>k__BackingField
jX43GXwAU5
<Account>k__BackingField
VUl3gwhRcg
<Account1>k__BackingField
TIq3VYWvyP
<Branch>k__BackingField
IdE3e41iFc
<Currency>k__BackingField
qDC3nKSkD8
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ReceiptDebitor
AppTech.MSMS.Domain.Models.ReceiptDebitor
ReceiptDebitor
ReceiptDebitor
TNH38iOSuF
<ID>k__BackingField
dB83oMZgMv
<RowVersion>k__BackingField
t7d3TKrSB1
<Number>k__BackingField
o2L3PmEKOG
<Amount>k__BackingField
r3T3RrBFf9
<CurrencyID>k__BackingField
fFg3AsMYhN
<CreditorAccountID>k__BackingField
axa3bfFsMn
<DebitorAccountID>k__BackingField
atW3IyBCBA
<Date>k__BackingField
T1R3NAZS6a
<Note>k__BackingField
nrl3kKRZPl
<EntryID>k__BackingField
sTa37c4Gfj
<RefNumber>k__BackingField
A0T3cyX8j3
<AttachmentNumbers>k__BackingField
HT93iH5mA0
<IsDebited>k__BackingField
DN83dV1wD6
<CreatedBy>k__BackingField
xcB3w6oonk
<Prints>k__BackingField
p0h3ymJNPP
<CreatedTime>k__BackingField
L7r3hIke4r
<BranchID>k__BackingField
PPA3F8MKT2
<Year>k__BackingField
v463lsDd2b
<Status>k__BackingField
Lkh3Y3NCYO
<CreatedDate>k__BackingField
ola3JPZhRY
<HourTime>k__BackingField
B2M3KWPREn
<MinuteTime>k__BackingField
w9j39rh7Fj
<TransNumber>k__BackingField
Hyl3OLTDWI
<Channel>k__BackingField
UPd3UGwncZ
<Attachments>k__BackingField
CfE3ENu2jZ
<ExtraInfo>k__BackingField
HrS3ShPPa5
<ExtraID>k__BackingField
M9C3pCUqmg
<SyncID>k__BackingField
La83LLaSRd
<RefID>k__BackingField
ymt3a2HUQs
<BindID>k__BackingField
D9k30l6VOQ
<Binded>k__BackingField
yPk3QmFoxm
<Account>k__BackingField
jee33Ev375
<Account1>k__BackingField
c703vPWXeD
<Branch>k__BackingField
uI93ZVWXTX
<Currency>k__BackingField
RXo3mw4Tnq
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Region
AppTech.MSMS.Domain.Models.Region
Region
Region
YAf3ClR7QG
<ID>k__BackingField
kWG3zEhUh3
<RowVersion>k__BackingField
vy5v25Yu25
<Number>k__BackingField
B5HvXFaWqv
<Name>k__BackingField
jn7vMbiAZm
<Note>k__BackingField
eGkvj50WFh
<ProvinceID>k__BackingField
QhQvuV79Ov
<BranchID>k__BackingField
AocvqAlbPx
<CreatedBy>k__BackingField
kUpvWCZb4y
<CreatedTime>k__BackingField
OYtv4OjGRI
<AccountRegions>k__BackingField
gymvDGTJoH
<Branch>k__BackingField
DpkvsmfSHK
<Province>k__BackingField
CkLv1jxSsC
<UserInfo>k__BackingField
MRIv6n07pa
<SatelliteProviders>k__BackingField
wnXvf8XZlo
<WifiProviders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceCommission
AppTech.MSMS.Domain.Models.RemittanceCommission
RemittanceCommission
RemittanceCommission
SPbvHVeqWn
<ID>k__BackingField
ONgvB8R98R
<RowVersion>k__BackingField
nskvt7jAq1
<RemittanceType>k__BackingField
vUvvxNlKjx
<CurrencyID>k__BackingField
nZIv5Fla01
<StartAmount>k__BackingField
dU1vrvNwRU
<EndAmount>k__BackingField
RlOvG4M2qF
<CommmissionType>k__BackingField
bcCvgn6781
<CenterCommission>k__BackingField
Cc1vVnhMt2
<PointCommission>k__BackingField
twEve5ibXD
<CreatedBy>k__BackingField
VSAvnQi5D9
<BranchID>k__BackingField
cCGv8iaWpC
<CreatedTime>k__BackingField
RluvowNfmT
<CommissionCurrencyID>k__BackingField
tg5vToIECO
<IsExpress>k__BackingField
X1cvP3Z1a9
<TargetState>k__BackingField
JOMvRnOp9n
<TargetID>k__BackingField
KJ1vAblZtY
<TargetGroupID>k__BackingField
Qf6vbIf9Wg
<AccountState>k__BackingField
WgRvIbYMI1
<AccountID>k__BackingField
H69vN7kUbE
<AccountGroupID>k__BackingField
UbivkRHftu
<IsAgainst>k__BackingField
kqlv726sWp
<VoucherType>k__BackingField
Bj7vcei5Kh
<CurrencyState>k__BackingField
Yu7vitiwIV
<AmountState>k__BackingField
mTbvddxdX1
<EntryType>k__BackingField
lQtvwoPujF
<SourceState>k__BackingField
AKAvyCREoA
<SourceID>k__BackingField
h7jvhmeN2i
<SourceGroupID>k__BackingField
ClcvFJ8YUh
<Branch>k__BackingField
GIWvlsfp5H
<Currency>k__BackingField
tOvvYpas0o
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceIn
AppTech.MSMS.Domain.Models.RemittanceIn
RemittanceIn
RemittanceIn
HcJvJ0cN8s
<ID>k__BackingField
MrvvK1Xfqt
<RowVersion>k__BackingField
JvKv9Dp7VY
<Number>k__BackingField
USHvOb6Iqv
<RemittanceNumber>k__BackingField
D6BvU72E4K
<Amount>k__BackingField
rK4vESoiG4
<CurrencyID>k__BackingField
WaWvS4Uph3
<CreditorAccountID>k__BackingField
cn9vp7aWrg
<DebitorAccountID>k__BackingField
UjavLQvIvO
<BeneficiaryName>k__BackingField
FyxvambGjD
<BeneficiaryPhone>k__BackingField
fGqv0X3N2o
<SenderName>k__BackingField
vIOvQaeMF2
<SenderPhone>k__BackingField
z3Tv3NUAf3
<SourcePointID>k__BackingField
aaivvqKlHa
<TargetPointID>k__BackingField
v4lvZRfJqn
<AgentID>k__BackingField
fExvmxH6rE
<BranchID>k__BackingField
DOsvCLB4yy
<SourceRegionID>k__BackingField
Nw4vzjEC3G
<TargetRegionID>k__BackingField
Yy5Z2LCBCZ
<Date>k__BackingField
VO6ZXjNtHP
<Note>k__BackingField
OjbZMaplml
<Purpose>k__BackingField
zKSZjCNrFC
<Prints>k__BackingField
hKMZubVfmV
<Status>k__BackingField
VykZqHlrGN
<EntryID>k__BackingField
tnjZWn7vt7
<CommissionAmount>k__BackingField
lfXZ4ZHUSN
<CommissionCurrencyID>k__BackingField
RvVZDmZc49
<RefNumber>k__BackingField
AseZs1o9sN
<SenderCardID>k__BackingField
nUkZ1BvVfw
<BeneficiaryCardID>k__BackingField
dIvZ6qRCxX
<SearchOnlyByNumber>k__BackingField
bfBZfryURH
<CreatedBy>k__BackingField
fOOZH4JFvQ
<CreatedTime>k__BackingField
ilNZBa4soI
<Year>k__BackingField
o14ZtfN3Om
<CenterCommission>k__BackingField
SyFZxnoPt2
<PointCommission>k__BackingField
NrAZ5bWO3o
<CommissionType>k__BackingField
G1NZrBJEgP
<QueriedBy>k__BackingField
JhqZGcdcdW
<IsSync>k__BackingField
SqeZgUi1Sh
<SyncRemittanceID>k__BackingField
B3sZV4G0JR
<SyncNumber>k__BackingField
ffFZecUpMi
<Imported>k__BackingField
IrhZnEcENE
<NetworkTarget>k__BackingField
hojZ8aiXKN
<ExchangeAmount>k__BackingField
SeDZornNKr
<ExchangeCurrencyID>k__BackingField
pBHZTIl9kr
<Channel>k__BackingField
HMKZPnIust
<Delivered>k__BackingField
zQjZRC9K6l
<CreatedDate>k__BackingField
AusZAmY3qm
<HourTime>k__BackingField
USmZbva3ut
<MinuteTime>k__BackingField
EYxZImks8x
<TransNumber>k__BackingField
uZtZNPV9Ls
<Attachments>k__BackingField
Xe0Zkng9Vk
<ExtraNote>k__BackingField
ntlZ7aaq6Z
<RequetInfo>k__BackingField
VfCZc0TUFR
<ResponseInfo>k__BackingField
cGbZivteFg
<RequestNote>k__BackingField
VSFZdV8Dr6
<ExtraInfo>k__BackingField
uT0ZwhIpNB
<ProviderRef>k__BackingField
U9wZymOfSf
<TransactionId>k__BackingField
SlhZhCMF4f
<UniqueNo>k__BackingField
jfUZFIVb5Y
<ExtraID>k__BackingField
oAbZlSIENJ
<SyncID>k__BackingField
TpxZY6f2E7
<RefID>k__BackingField
g93ZJnMU1U
<BindID>k__BackingField
eobZKvZMl0
<AutoExport>k__BackingField
k35Z9D129t
<SyncEntyID>k__BackingField
AwjZOSDtGg
<EntryNumber>k__BackingField
Yv0ZUkEsHZ
<Binded>k__BackingField
IJYZEpsmfp
<TransferNumber>k__BackingField
qSrZSit1QC
<BillNumber>k__BackingField
aoVZpwfDt9
<Method>k__BackingField
VjRZLet2ql
<State>k__BackingField
u85ZaPvunF
<Suspended>k__BackingField
QQ2Z0jogPA
<Depended>k__BackingField
hMnZQ6Wdek
<ExpressSync>k__BackingField
d9sZ3OaA6D
<ExpressReference>k__BackingField
BTaZvr1ZCA
<SyncTime>k__BackingField
xDLZZb9q6Z
<SyncMethod>k__BackingField
g3BZmgHsBy
<AdminNote>k__BackingField
NHtZCpe8tM
<AccountNote>k__BackingField
L9hZzLKcjp
<ByOrder>k__BackingField
s7gm20Hu8q
<OrderID>k__BackingField
yBRmXahu1j
<Account>k__BackingField
m7GmMEe9b2
<Account1>k__BackingField
t75mjptbLO
<Agent>k__BackingField
uXVmu73OKL
<Branch>k__BackingField
tfCmqbHvxb
<Currency>k__BackingField
MdGmWQb4b6
<Currency1>k__BackingField
nlmm42wARs
<Journal>k__BackingField
Ua1mDAVoIW
<RemittancePoint>k__BackingField
XlnmsRTdyA
<UserInfo>k__BackingField
qpZm1rqbb0
<RemittanceOuts>k__BackingField
HDXm6s1q9h
<RemittancePoint1>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceNumber
AppTech.MSMS.Domain.Models.RemittanceNumber
RemittanceNumber
RemittanceNumber
EE0mfA7HpL
<ID>k__BackingField
LAsmHaTqll
<Number>k__BackingField
HZ1mBL8t7p
<Key>k__BackingField
c2imtK6Q7I
<CreatedTime>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceOut
AppTech.MSMS.Domain.Models.RemittanceOut
RemittanceOut
RemittanceOut
qUcmxp2028
<ID>k__BackingField
WL4m5gprWn
<RowVersion>k__BackingField
URymrCaaPS
<Number>k__BackingField
LVWmGh2QDa
<RemittanceID>k__BackingField
YOrmgAX9xM
<RemittanceNumber>k__BackingField
VoCmVJlvaC
<Amount>k__BackingField
X21me9d37x
<CurrencyID>k__BackingField
c2umnyFhGc
<CreditorAccountID>k__BackingField
MhJm8R6coW
<DebitorAccountID>k__BackingField
yxEmoSZRSV
<RemittancePointID>k__BackingField
SwtmTrRSDw
<AgentID>k__BackingField
EBDmPdRXq1
<BranchID>k__BackingField
RmpmRwSP9k
<RegionID>k__BackingField
GyOmAsF8Oc
<Date>k__BackingField
dRxmb7HOSJ
<Note>k__BackingField
cnLmIwPWN6
<Prints>k__BackingField
URCmNX85Ii
<CommissionAmount>k__BackingField
RXsmkHca1h
<CommissionCurrencyID>k__BackingField
Yogm7WvNZl
<Delivered>k__BackingField
dm5mcRNCb1
<EntryID>k__BackingField
cMEmiGBMpm
<BeneficiaryCardID>k__BackingField
RC7mdSwTs8
<CreatedBy>k__BackingField
tK8mwjblIE
<CreatedTime>k__BackingField
rKnmyAflev
<RefNumber>k__BackingField
Kv7mh9jUmp
<Year>k__BackingField
jXOmFsv5GQ
<CenterCommission>k__BackingField
TaUmlpxWf5
<PointCommission>k__BackingField
aHwmYDds79
<CommissionType>k__BackingField
qLNmJ6m2sL
<BenficiaryID>k__BackingField
An8mK3vsEu
<BenficiaryCard>k__BackingField
WaPm9C0AHM
<Channel>k__BackingField
MMsmO0Pb7S
<Status>k__BackingField
geWmUOTpO5
<CreatedDate>k__BackingField
CNPmEjZQMv
<HourTime>k__BackingField
YufmSSftl7
<MinuteTime>k__BackingField
k7nmpK2pNt
<TransNumber>k__BackingField
DJlmLNsS3V
<Attachments>k__BackingField
QO9maPZlOH
<ExtraInfo>k__BackingField
I36m0wYeYs
<ExtraID>k__BackingField
KcAmQvDJBs
<SyncID>k__BackingField
iGjm3NJ96M
<RefID>k__BackingField
MDkmvjmZQu
<BindID>k__BackingField
cm0mZxMZ5I
<Binded>k__BackingField
rHNmmhhDQ1
<Method>k__BackingField
c94mC2ZKt9
<TransferNumber>k__BackingField
wYNmzckqgS
<BillNumber>k__BackingField
IRIC2OtKUN
<State>k__BackingField
gmRCXDUJPF
<Exported>k__BackingField
rmeCM8WA0R
<Depended>k__BackingField
LRKCjvggOC
<ByOrder>k__BackingField
PuYCu65wVb
<OrderID>k__BackingField
punCqqs2Fr
<Account>k__BackingField
OZrCWxKxRP
<Account1>k__BackingField
FdRC45bpnh
<Agent>k__BackingField
i1wCDmb6lV
<Branch>k__BackingField
f3QCsMGd0R
<Currency>k__BackingField
mytC1jcW6i
<Journal>k__BackingField
djsC6fPvdw
<RemittanceIn>k__BackingField
b8OCfyKdOM
<RemittancePoint>k__BackingField
epWCHsZdCF
<RemittanceRegion>k__BackingField
zS5CBu365e
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittancePoint
AppTech.MSMS.Domain.Models.RemittancePoint
RemittancePoint
RemittancePoint
bDFCtxpyIS
<ID>k__BackingField
HT3Cx1w5vG
<RowVersion>k__BackingField
UD6C5fjFLP
<Number>k__BackingField
D99Crcmctb
<Name>k__BackingField
G2jCGyp4sD
<RegionID>k__BackingField
LTrCgaBLhP
<Description>k__BackingField
hWeCV0TD6y
<Type>k__BackingField
T3JCeTYn4f
<Phone>k__BackingField
sgLCnMefEa
<Address>k__BackingField
ra4C8sqsx8
<Fax>k__BackingField
Cs9Co8TGKa
<Note>k__BackingField
aDLCTulasq
<Active>k__BackingField
yVMCPfkYXu
<CreatedBy>k__BackingField
HCMCRTaJTS
<BranchID>k__BackingField
yX3CAlUbvV
<CreatedTime>k__BackingField
WqXCbWiglb
<AccountID>k__BackingField
ewICIfukOx
<AutoExport>k__BackingField
YqGCNbRMhR
<Class>k__BackingField
ll8CkOyC8y
<SyncID>k__BackingField
fZHC7mgPfW
<Status>k__BackingField
HuiCcoYaiG
<IsDirect>k__BackingField
bdyCiu35Ri
<AgentPoints>k__BackingField
PwRCde3gUm
<AgentPointUsers>k__BackingField
zBpCwjhO3f
<Branch>k__BackingField
pBDCyR5QXa
<BranchTargets>k__BackingField
zmcChBexvm
<ExchangerTargets>k__BackingField
SBWCF2erwO
<RemittanceIns>k__BackingField
NDxClhl56P
<RemittanceIns1>k__BackingField
BAVCYoCwcT
<RemittanceOuts>k__BackingField
OSGCJ4uvvf
<RemittanceRegion>k__BackingField
xPwCKUiMpa
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceRegion
AppTech.MSMS.Domain.Models.RemittanceRegion
RemittanceRegion
RemittanceRegion
bB6C9lHfa9
<ID>k__BackingField
jUDCOiuBsU
<RowVersion>k__BackingField
WZ1CUZuM5u
<Name>k__BackingField
aOXCEkrGCN
<ProvinceID>k__BackingField
AXGCSN5kdR
<CreatedBy>k__BackingField
Rq6CpyQjAq
<BranchID>k__BackingField
tpICLjIhiy
<CreatedTime>k__BackingField
NJmCarKS6G
<Branch>k__BackingField
V0tC00UnGo
<Province>k__BackingField
i9MCQIaOyS
<RemittanceOuts>k__BackingField
mPiC3TCkb5
<RemittancePoints>k__BackingField
e1kCvOe2PR
<UserInfo>k__BackingField
TvLCZZeEVR
<TransferOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RiyalMobile
AppTech.MSMS.Domain.Models.RiyalMobile
RiyalMobile
RiyalMobile
uHkCm6WpKI
<ID>k__BackingField
LAMCC3NBO8
<Number>k__BackingField
UY2CzWijmi
<ServiceID>k__BackingField
BMoz2EI9q3
<NetworkID>k__BackingField
mqpzXachAr
<SubscriberNumber>k__BackingField
kwOzMtilGl
<Amount>k__BackingField
e3yzjOs7S4
<FactionID>k__BackingField
OpRzuUR8Mn
<RegionID>k__BackingField
HrOzq2g1m6
<LineType>k__BackingField
b6dzW1dCAg
<Date>k__BackingField
Sk0z4CHdK0
<Year>k__BackingField
cuozDULVYM
<Status>k__BackingField
Lt1zsjq6pG
<Note>k__BackingField
YWmz1pCrmY
<CreditorAccountID>k__BackingField
nC1z67oPLq
<CurrencyID>k__BackingField
hl7zfhCMi2
<DebitorAccountID>k__BackingField
n00zHEA6kr
<AgentID>k__BackingField
V5XzBjx5pR
<RefNumber>k__BackingField
VjtztIe7Ka
<TransactionID>k__BackingField
DPrzxt2Yal
<ProviderID>k__BackingField
n3rz5adptu
<EntryID>k__BackingField
RKjzrHnsRe
<PaymentEntryID>k__BackingField
XrqzG9sQb1
<Channel>k__BackingField
BRVzgaOIjV
<CreatedBy>k__BackingField
XTUzV4N30A
<BranchBy>k__BackingField
EVMze1qlmI
<CreatedTime>k__BackingField
n4Qzn11Twf
<RowVersion>k__BackingField
pMUz8wIdZ8
<BranchID>k__BackingField
W2gzoq2V56
<ProviderRM>k__BackingField
RDtzTd8OQx
<ProviderPrice>k__BackingField
sDXzPF0wv6
<SubNote>k__BackingField
venzRlAwVe
<Datestamb>k__BackingField
DCLzAYIaD7
<UniqueNo>k__BackingField
NDuzbtOl50
<OrderID>k__BackingField
Fv8zIS4WhW
<ExtraID>k__BackingField
FrBzNpiFDJ
<SyncID>k__BackingField
yGyzkd29GN
<SyncEntryID>k__BackingField
zsQz7qgreK
<SyncRecordID>k__BackingField
awRzcEI9fU
<ProviderRecordID>k__BackingField
mfGziTHZlu
<Commission>k__BackingField
Y4UzdrmRfM
<Credit>k__BackingField
ECMzwBcMT0
<Percentage>k__BackingField
yZEzyjTAOd
<ByOrder>k__BackingField
V0ezhdC8SI
<IsDirect>k__BackingField
iVCzFJYcIF
<Flag>k__BackingField
GX1zlbK17P
<Verified>k__BackingField
vDfzYLdGsp
<StatusChecked>k__BackingField
hy2zJskS4E
<StatusUpdated>k__BackingField
ghGzKfFROS
<Type>k__BackingField
hdsz96WHQK
<State>k__BackingField
SDizOrYGbV
<ByApi>k__BackingField
I5lzU8uRHL
<ExternalApi>k__BackingField
SZAzE5TWWE
<Callback>k__BackingField
qMkzSNeaNM
<ApiUpdated>k__BackingField
YvAzpbI3kP
<IsDebited>k__BackingField
GKGzLh9WRN
<UpdatedByProvider>k__BackingField
FmjzaaU8FH
<Tag>k__BackingField
lY8z0kjC4l
<Token>k__BackingField
qglzQLkD6p
<FactionName>k__BackingField
xSJz35tBEf
<Description>k__BackingField
PmEzv3yZvv
<DeviceID>k__BackingField
YYszZK5TTb
<CallbackNote>k__BackingField
WlwzmNxelr
<CallbackResponse>k__BackingField
fjCzC6jAVx
<ApiMessage>k__BackingField
jR3zzNJnb3
<ApiInfo>k__BackingField
UH4X22fsUqS
<ApiNote>k__BackingField
FNRX2XUmlmk
<Reason>k__BackingField
AE5X2Msfk6T
<StateNote>k__BackingField
WUBX2jFVYbL
<RequestInfo>k__BackingField
x0QX2u61yCq
<ExtraInfo>k__BackingField
zljX2q7goO9
<ProcessNote>k__BackingField
z60X2WeA4Cl
<AccountNote>k__BackingField
xP2X248NZh2
<Account>k__BackingField
L0IX2D2uRD5
<Account1>k__BackingField
CgwX2sZFBIi
<Agent>k__BackingField
Uq8X21TTFSx
<Branch>k__BackingField
Y8FX26pMarm
<Currency>k__BackingField
q7hX2f66KcK
<Journal>k__BackingField
FnTX2Hvd32k
<Payment>k__BackingField
NpHX2BwoG6p
<TopupNetwork>k__BackingField
CgZX2t8QsbH
<RiyalMobile1>k__BackingField
m90X2xcH7oG
<RiyalMobile2>k__BackingField
SSQX25baNad
<ServiceInfo>k__BackingField
VMbX2rsKWMt
<TopupProvider>k__BackingField
rIbX2GcxZpe
<UserInfo>k__BackingField
rtEX2gNIAwr
<WERegion>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RoleClaim
AppTech.MSMS.Domain.Models.RoleClaim
RoleClaim
RoleClaim
pnOX2V0vxKJ
<ID>k__BackingField
HfDX2eeWqNE
<ClaimType>k__BackingField
fgSX2ni1xYS
<ClaimValue>k__BackingField
AKtX28ILnmE
<RoleID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RoleInfo
AppTech.MSMS.Domain.Models.RoleInfo
RoleInfo
RoleInfo
kM8X2ovba3Q
<ID>k__BackingField
I6SX2TxocpE
<Name>k__BackingField
lqgX2PS90fS
<AccountUsers>k__BackingField
jGRX2RW4S45
<UserRoles>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RSS
AppTech.MSMS.Domain.Models.RSS
RSS
RSS
SZbX2AIf35x
<ID>k__BackingField
lNoX2bXQa0r
<RowVersion>k__BackingField
jYWX2I9KNVN
<Feed>k__BackingField
ET4X2N1DI9V
<Active>k__BackingField
uqbX2k4nLKD
<CreatedBy>k__BackingField
nsQX27gMOHX
<BranchID>k__BackingField
xl9X2cWgdq1
<CreatedTime>k__BackingField
t6iX2iwKhtZ
<Branch>k__BackingField
AtpX2dv5FCg
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SaleCurrency
AppTech.MSMS.Domain.Models.SaleCurrency
SaleCurrency
SaleCurrency
AESX2wrdnxV
<ID>k__BackingField
wUuX2yXunT1
<RowVersion>k__BackingField
BiiX2hccuSy
<Number>k__BackingField
XVuX2FsRPoA
<Amount>k__BackingField
yGyX2lJpQ8C
<CurrencyID>k__BackingField
zGbX2YdNtH4
<ExchangePrice>k__BackingField
XruX2JfOLll
<ExchangeAmount>k__BackingField
Mc4X2KecONJ
<ExchangeCurrencyID>k__BackingField
CSYX29RysKc
<CreditorAccountID>k__BackingField
bQ5X2OAxvwm
<DebitorAccountID>k__BackingField
r4nX2UKTwkQ
<Date>k__BackingField
WtsX2EgQek2
<Note>k__BackingField
UYnX2S5JcVh
<EntryID>k__BackingField
TGhX2pqEZhB
<Year>k__BackingField
Q1sX2L9Mxdx
<Status>k__BackingField
FY5X2a74Vb2
<Channel>k__BackingField
TnuX20wYd5D
<CreatedTime>k__BackingField
WK3X2QJFThs
<CreatedBy>k__BackingField
mnCX23OrSUn
<BranchID>k__BackingField
JSPX2vpghAe
<RefNumber>k__BackingField
lGqX2ZQ1yKy
<CreatedDate>k__BackingField
Dq0X2mLgLP7
<HourTime>k__BackingField
TCAX2CqsSSr
<MinuteTime>k__BackingField
iFXX2zjyNIl
<TransNumber>k__BackingField
kKOXX2ANOP3
<Attachments>k__BackingField
zDkXXXMdTHs
<ExtraInfo>k__BackingField
qYcXXMip5pX
<ExtraID>k__BackingField
mXbXXjmWZ4C
<SyncID>k__BackingField
RSlXXu93HDW
<RefID>k__BackingField
mLEXXqeJxot
<BindID>k__BackingField
mvAXXWQPXY8
<Binded>k__BackingField
kwUXX47uvMn
<OrderID>k__BackingField
CoNXXDN6jdW
<ByOrder>k__BackingField
y10XXsaViBO
<Account>k__BackingField
ua5XX1b4jST
<Account1>k__BackingField
pPGXX6V5fXO
<Branch>k__BackingField
uSFXXfdh1as
<Currency>k__BackingField
B8UXXHDpIB6
<Currency1>k__BackingField
uXKXXB46Z6v
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SaleInvoice
AppTech.MSMS.Domain.Models.SaleInvoice
SaleInvoice
SaleInvoice
najXXt6Tyik
<ID>k__BackingField
es3XXxEiVov
<RowVersion>k__BackingField
h5TXX5w8ToX
<Number>k__BackingField
TxTXXrtXHWX
<Amount>k__BackingField
adJXXGNGaah
<CurrencyID>k__BackingField
qxTXXghZ9gQ
<CreditorAccountID>k__BackingField
LZiXXVLweVg
<DebitorAccountID>k__BackingField
dDdXXe27JY1
<CustomerID>k__BackingField
TpwXXn5RdSC
<PurchaseOrderNo>k__BackingField
YKXXX82TGxC
<Discount>k__BackingField
DiuXXoilKnu
<Subtotal>k__BackingField
AcyXXTlJFhn
<Date>k__BackingField
JTyXXPUIfvk
<DueDate>k__BackingField
HCOXXR4nb9c
<Note>k__BackingField
VoaXXAXKHMh
<EntryID>k__BackingField
wOGXXbHbWKF
<InventoryID>k__BackingField
AvDXXIHRo2D
<RefNumber>k__BackingField
eodXXNAbDfH
<Extra>k__BackingField
dljXXk7JIlG
<AttachmentNumbers>k__BackingField
BTMXX723Xo9
<Footer>k__BackingField
A6NXXcVsvFI
<Year>k__BackingField
oKOXXiy5SfH
<Status>k__BackingField
XK3XXdokiVi
<CreatedTime>k__BackingField
VNTXXwESHlD
<CreatedBy>k__BackingField
VyGXXyx3lOg
<BranchID>k__BackingField
kI5XXhqAic7
<Account>k__BackingField
t5GXXF0J3b1
<Account1>k__BackingField
JnfXXl33GWl
<Branch>k__BackingField
wjJXXYSApt0
<Currency>k__BackingField
vBJXXJ89Tcp
<Journal>k__BackingField
TyEXXKvnYPu
<UserInfo>k__BackingField
cmoXX94kdEj
<SaleInvoiceLines>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SaleInvoiceLine
AppTech.MSMS.Domain.Models.SaleInvoiceLine
SaleInvoiceLine
SaleInvoiceLine
tdLXXOR4tf6
<ID>k__BackingField
nX2XXUMeCOw
<RowVersion>k__BackingField
yy7XXEqG79K
<ParentID>k__BackingField
rhdXXSt60de
<SerialNumber>k__BackingField
jKdXXpV60TF
<ProductID>k__BackingField
AqbXXLe7M8w
<Quantity>k__BackingField
H4fXXarSUSa
<UnitPrice>k__BackingField
uC5XX0wxAOU
<Discount>k__BackingField
qsqXXQqhkJh
<SubTotal>k__BackingField
S5EXX3kWjFK
<TotalAmount>k__BackingField
t4IXXvepbjp
<InventoryLineID>k__BackingField
OwfXXZwQF8g
<Note>k__BackingField
ScWXXmhLouE
<CreatedTime>k__BackingField
cYyXXCsa64C
<CreatedBy>k__BackingField
dw4XXzW0Nsh
<BranchID>k__BackingField
MN0XM2hRYRF
<Branch>k__BackingField
eNOXMXA86g5
<Faction>k__BackingField
uYZXMM1ruxT
<SaleInvoice>k__BackingField
kZIXMjsJZJe
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SatelliteFaction
AppTech.MSMS.Domain.Models.SatelliteFaction
SatelliteFaction
SatelliteFaction
RcLXMu8URD8
<ID>k__BackingField
scGXMqAjqlt
<RowVersion>k__BackingField
aNlXMWWCpe0
<Number>k__BackingField
QGfXM4VjR4C
<Name>k__BackingField
GriXMDwAdvK
<Description>k__BackingField
llmXMsXLhYk
<Price>k__BackingField
SEjXM15dFbJ
<Note>k__BackingField
b8kXM6ceMl8
<SatelliteProviderID>k__BackingField
oiAXMfxhL4R
<BranchID>k__BackingField
IB9XMHYfhds
<CreatedBy>k__BackingField
pLgXMBWEDi6
<CreatedTime>k__BackingField
NBxXMtLKpvp
<Branch>k__BackingField
WjqXMxyifv6
<SatelliteProvider>k__BackingField
q93XM5v9ReJ
<UserInfo>k__BackingField
mMKXMrZbH2U
<SatellitePayments>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SatellitePayment
AppTech.MSMS.Domain.Models.SatellitePayment
SatellitePayment
SatellitePayment
m06XMGEyeGs
<ID>k__BackingField
EYGXMgsL0Ln
<RowVersion>k__BackingField
euMXMVZ4Ti1
<Number>k__BackingField
BprXMeEHDqZ
<ProviderID>k__BackingField
RjoXMnhJ1KQ
<FactionID>k__BackingField
pSmXM8AcEB1
<SubscriptionTerm>k__BackingField
a0sXMoLGJm4
<SubscriptionNumber>k__BackingField
jVOXMTPdxS5
<ProfitAmount>k__BackingField
YlVXMPCRBnq
<ProviderAmount>k__BackingField
eN1XMREb2pM
<Amount>k__BackingField
SnCXMAr6Nm2
<CurrencyID>k__BackingField
WXJXMbPeJL1
<CreditorAccountID>k__BackingField
DxYXMI6PgMV
<DebitorAccountID>k__BackingField
Mq2XMNRwk7N
<Date>k__BackingField
UIOXMk6yjTX
<Note>k__BackingField
SDhXM7LIeSN
<EntryID>k__BackingField
Km8XMcMMxfl
<RefNumber>k__BackingField
HEyXMiLS2vs
<IsDebited>k__BackingField
RetXMdJtKCu
<Year>k__BackingField
JWgXMwxqWjr
<CreatedBy>k__BackingField
qbbXMyKZqv9
<CreatedTime>k__BackingField
M8SXMhSkJ2x
<ProccessTime>k__BackingField
JRLXMFBlwde
<BranchID>k__BackingField
xwIXMl5rETZ
<Status>k__BackingField
j4AXMY8Vw3y
<ServiceID>k__BackingField
MmUXMJYsdtT
<Account>k__BackingField
iFiXMKgbiUf
<Account1>k__BackingField
YlnXM95fj5d
<Branch>k__BackingField
hNQXMOWaJKj
<Currency>k__BackingField
vqeXMULPg4F
<SatelliteFaction>k__BackingField
BUTXMENpaaM
<SatelliteProvider>k__BackingField
kjbXMS4gtGQ
<ServiceInfo>k__BackingField
dfbXMpPrlx5
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SatelliteProvider
AppTech.MSMS.Domain.Models.SatelliteProvider
SatelliteProvider
SatelliteProvider
g7pXML0lu7V
<ID>k__BackingField
punXMa1ZU8i
<RowVersion>k__BackingField
FnEXM0CGhLY
<Number>k__BackingField
I09XMQ6rc64
<Name>k__BackingField
HXGXM3KdkUt
<Phone>k__BackingField
HQgXMvcEY08
<Note>k__BackingField
SrVXMZ4b8xk
<PrafitStatus>k__BackingField
OITXMmLhBEl
<PrafitAmount>k__BackingField
xOvXMCjcXTg
<AccountID>k__BackingField
n43XMzWRwn0
<RegionID>k__BackingField
a8uXj258Re3
<MinSubscribe>k__BackingField
otNXjX47KLl
<BranchID>k__BackingField
K0xXjMiuurU
<CreatedBy>k__BackingField
J1aXjjNXZFV
<CreatedTime>k__BackingField
a3ZXju4BOkK
<Account>k__BackingField
aYBXjqctsMi
<Branch>k__BackingField
P05XjWn7m3A
<OrderSatelliteQuotas>k__BackingField
JBAXj4jDCDU
<Region>k__BackingField
ssHXjDZNkfG
<SatelliteFactions>k__BackingField
wk5XjsMRi7X
<SatellitePayments>k__BackingField
YwrXj1cCFOp
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OrderSatelliteQuota
AppTech.MSMS.Domain.Models.OrderSatelliteQuota
OrderSatelliteQuota
OrderSatelliteQuota
PXPXj63mpoA
<ID>k__BackingField
mwOXjfeVY7T
<RowVersion>k__BackingField
nB0XjHRMGT8
<Number>k__BackingField
JxQXjBv6MCp
<Amount>k__BackingField
qGwXjtrIXi4
<CurrencyID>k__BackingField
Yt0XjxydjTt
<ProviderID>k__BackingField
iXaXj5vaiCV
<CreditorAccountID>k__BackingField
UTcXjrbfUbY
<DebitorAccountID>k__BackingField
oZ5XjGF2MNX
<Description>k__BackingField
JkdXjgk31m5
<ProviderNote>k__BackingField
mb2XjVHLWeu
<QuotaAmount>k__BackingField
uOVXjeLWnrp
<QuotatedTime>k__BackingField
o8lXjnDixPP
<Commission>k__BackingField
AbZXj8YgRVJ
<Status>k__BackingField
OQEXjo8mx5u
<ParentID>k__BackingField
JHqXjTtwq6E
<Date>k__BackingField
CJIXjPpU7qo
<Note>k__BackingField
RcpXjRsI404
<EntryID>k__BackingField
hhHXjAkeHEZ
<IsDebited>k__BackingField
C0NXjblqkI1
<Channel>k__BackingField
ihxXjIOL0Gm
<Year>k__BackingField
y2QXjNmx0O4
<ExtraInfo>k__BackingField
LRNXjkgEs8B
<CreatedBy>k__BackingField
K1YXj7FKtZD
<BranchID>k__BackingField
XMHXjcNmvfm
<CreatedTime>k__BackingField
eWXXji1EClv
<Account>k__BackingField
lrmXjdcbTVN
<Account1>k__BackingField
lqeXjwCFBFg
<Branch>k__BackingField
ysJXjy3YbmN
<Currency>k__BackingField
BVGXjhefrHb
<SatelliteProvider>k__BackingField
CmNXjFdAmRi
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ServiceClaim
AppTech.MSMS.Domain.Models.ServiceClaim
ServiceClaim
ServiceClaim
G1EXjlqyN0w
<ID>k__BackingField
lCMXjYHoSMb
<RowVersion>k__BackingField
ROVXjJD7vIq
<ServiceID>k__BackingField
OMbXjKYxCtN
<CreatedBy>k__BackingField
TaCXj9N3CCg
<BranchID>k__BackingField
DYaXjOJ29Pm
<CreatedTime>k__BackingField
tkUXjU6rvgw
<AccountID>k__BackingField
JtuXjE4y6Vv
<AccountGroupID>k__BackingField
gNMXjSjm9qL
<AccountState>k__BackingField
Bn2Xjp18pTw
<Account>k__BackingField
MHDXjL06Up2
<Branch>k__BackingField
WwmXja3OgJU
<ServiceInfo>k__BackingField
RrFXj0rNpBI
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ServiceInfo
AppTech.MSMS.Domain.Models.ServiceInfo
ServiceInfo
ServiceInfo
DqUXjQvu4Od
<ID>k__BackingField
mSQXj33yGO4
<RowVersion>k__BackingField
IhlXjvuEjfn
<Number>k__BackingField
oMNXjZmMwIV
<Name>k__BackingField
xJqXjmaCfF2
<Note>k__BackingField
tEhXjCqdNxH
<CreatedBy>k__BackingField
Gt9XjzZhd7J
<BranchID>k__BackingField
jR4Xu2O53gE
<CreatedTime>k__BackingField
gXgXuX9thEB
<FixedPrice>k__BackingField
qaPXuM04tu0
<Prices>k__BackingField
lRDXujcvdKo
<Description>k__BackingField
ar6XuuAvAag
<CategoryID>k__BackingField
YaAXuqbsWPP
<IsActive>k__BackingField
zYNXuWyyJGd
<Type>k__BackingField
KbXXu47C53E
<KeyName>k__BackingField
lEVXuDAa7HP
<VoucherID>k__BackingField
JENXusFDARa
<Module>k__BackingField
uQvXu1GlwIi
<HadProduct>k__BackingField
fggXu6dad2i
<PartyTarget>k__BackingField
xM0Xufi6W2F
<RefNumber>k__BackingField
EjFXuHyK74P
<Extra>k__BackingField
FE3XuBJobna
<Bagats>k__BackingField
yE5XutCRIMS
<BankDeposits>k__BackingField
p4AXuxvBKUP
<Branch>k__BackingField
RBxXu5ZgPaw
<CardOrders>k__BackingField
UDcXurODAxn
<CardPayments>k__BackingField
axpXuGXKKNL
<CommissionReceipts>k__BackingField
VQnXug37DZE
<CoverageOrders>k__BackingField
HPHXuV7BJ95
<CurrencyExchanges>k__BackingField
cmWXueGXLvR
<DepositOrders>k__BackingField
aFaXunyv27I
<Items>k__BackingField
iW2Xu86g6JD
<ItemCosts>k__BackingField
SjTXuon1KNn
<LiveTopups>k__BackingField
a0FXuTFZZhS
<LoanOrders>k__BackingField
I18XuP6O6Gn
<MoneyMaster>k__BackingField
lVrXuRVQHpJ
<OfferOrders>k__BackingField
DoYXuAcb1Pr
<OrderInfoes>k__BackingField
ncYXubeZviw
<Payments>k__BackingField
zRrXuI4OQJR
<PaymentCommissions>k__BackingField
pROXuNrFWEM
<Quotations>k__BackingField
yfeXuk72YqN
<RiyalMobiles>k__BackingField
oEaXu7KGHXd
<SatellitePayments>k__BackingField
JuPXucBhgbo
<ServiceClaims>k__BackingField
fWuXuiGfMl0
<SMSDispatches>k__BackingField
a7BXudoh1nj
<UserInfo>k__BackingField
yeZXuwGlUbq
<SimCardOrders>k__BackingField
OFcXuykhqhh
<Subscribers>k__BackingField
fW2XuhTXHgQ
<Topups>k__BackingField
QnXXuFfrv8k
<TopupCommissions>k__BackingField
DfsXulBtlwQ
<TopupOrders>k__BackingField
tUZXuYfc1oE
<TrailToupOrders>k__BackingField
IsFXuJNHi5i
<TransferOrders>k__BackingField
UIqXuKby145
<TransportOrders>k__BackingField
hflXu9bwJEG
<WERegions>k__BackingField
zlPXuOSp3Yk
<WifiPayments>k__BackingField
yixXuUveRff
<WithdrawOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Setting
AppTech.MSMS.Domain.Models.Setting
Setting
Setting
hjDXuEbwNIk
<ID>k__BackingField
gxSXuSynFNR
<KeyName>k__BackingField
DmdXupsa9mS
<Value>k__BackingField
KNOXuL8P5nI
<Level>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Sim
AppTech.MSMS.Domain.Models.Sim
Sim
Sim
oE9Xuaflq3t
<ID>k__BackingField
SQiXu0Iwr8f
<RowVersion>k__BackingField
U3eXuQyMNgo
<InvoiceID>k__BackingField
FfRXu3SmKD0
<OperatorID>k__BackingField
aPeXuvy0RT1
<Number>k__BackingField
vLOXuZhVTAW
<AccountID>k__BackingField
wHHXumuo8u0
<Price>k__BackingField
wv0XuCeqEsc
<ExtraPrice>k__BackingField
H7EXuzcvu1C
<Total>k__BackingField
W70Xq2VcSx4
<DateIn>k__BackingField
whZXqX9FsPu
<DateOut>k__BackingField
us1XqMo9JDZ
<Type>k__BackingField
no7Xqj6ytPD
<Status>k__BackingField
iBhXqusDgbd
<OrderID>k__BackingField
rrSXqqqlE6M
<OrderNumber>k__BackingField
UGVXqWllSll
<ActionType>k__BackingField
FvnXq4UdbP5
<ActionID>k__BackingField
grJXqDS18bl
<SubscriberName>k__BackingField
n1NXqsmXfJw
<SubscriberNumber>k__BackingField
FYiXq1HVQhe
<Active>k__BackingField
mMBXq6ut7JX
<ActivatedBy>k__BackingField
gwLXqfLWDI5
<RefNumber>k__BackingField
unfXqHdxOWG
<Note>k__BackingField
jH7XqBpdCU7
<BranchID>k__BackingField
nNKXqttwxd4
<CreatedBy>k__BackingField
Q2OXqxgg6mH
<CreatedTime>k__BackingField
A8BXq5jFES7
<InvoiceType>k__BackingField
p2oXqrEcSDk
<Sold>k__BackingField
aZtXqGijl8B
<CostPrice>k__BackingField
iSpXqgX3Kq2
<SupplierAccountID>k__BackingField
R9IXqVaEBHG
<ExchangeAccountID>k__BackingField
xP3XqexED2l
<Account>k__BackingField
AiUXqn9xJ68
<Branch>k__BackingField
IXRXq81S80E
<SimInvoice>k__BackingField
RDXXqo2T5my
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SimCardOrder
AppTech.MSMS.Domain.Models.SimCardOrder
SimCardOrder
SimCardOrder
mAfXqTEHKyD
<ID>k__BackingField
biUXqP9Qr3V
<RowVersion>k__BackingField
Y24XqRUUyFl
<SimNumber>k__BackingField
KkUXqAp7Rla
<SimType>k__BackingField
PIBXqbLBS5U
<ContractNumber>k__BackingField
a3LXqIUW2RM
<ESDN>k__BackingField
Yq6XqNAIYHP
<MSISDN>k__BackingField
TY4XqkxhQyk
<PersonalCardType>k__BackingField
sRUXq7YmjxG
<IssueDate>k__BackingField
WCWXqcXbYJW
<BirthDate>k__BackingField
mMAXqiqFphg
<CustomerName>k__BackingField
urIXqd6Gjpk
<CustomerAddress>k__BackingField
s1FXqwIJYjc
<CreatedBy>k__BackingField
Ce5XqyD8q2b
<BranchID>k__BackingField
KpNXqh2ZI5l
<CreatedTime>k__BackingField
RMLXqFZ5SVi
<Amount>k__BackingField
SKnXqlSNafG
<SubscriberNumber>k__BackingField
g4QXqYyJSXB
<Note>k__BackingField
iTUXqJmtx8Z
<ActionType>k__BackingField
fh2XqKsZMA3
<Channel>k__BackingField
ecuXq987q8q
<FrontCardImage>k__BackingField
UC8XqO8cRlj
<BackCardImage>k__BackingField
HOGXqUq1vBI
<ExpireDate>k__BackingField
uR0XqE11lU8
<CardNumber>k__BackingField
AlnXqSVTD6q
<ServiceID>k__BackingField
Pt9XqpTcSu9
<ParentID>k__BackingField
iFIXqL7aEwp
<CardIssuePlace>k__BackingField
dNZXqa6tDx0
<LineType>k__BackingField
Q8iXq0DHvUI
<NetworkID>k__BackingField
YGCXqQPXaWf
<SimStatus>k__BackingField
TQUXq3tWpci
<SimAction>k__BackingField
fBoXqvqNQwu
<AccountID>k__BackingField
iGTXqZAPbE2
<Account>k__BackingField
y5LXqm5duJZ
<Branch>k__BackingField
yOwXqCTrvAl
<OrderInfo>k__BackingField
x5xXqznuPgt
<ServiceInfo>k__BackingField
gkiXW2Ov8pC
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SimInvoice
AppTech.MSMS.Domain.Models.SimInvoice
SimInvoice
SimInvoice
kWtXWXV3Zb5
<ID>k__BackingField
dfLXWMJNvAT
<RowVersion>k__BackingField
LWWXWjj4Nbn
<Number>k__BackingField
hbFXWuRoDjW
<NetworkID>k__BackingField
YcHXWq2GQ19
<StartNumber>k__BackingField
JX9XWWMuhUR
<EndNumber>k__BackingField
avcXW41tg2Q
<UnitPrice>k__BackingField
f2BXWDCDDOR
<TotalUnits>k__BackingField
bVJXWsKoIuC
<Amount>k__BackingField
lIKXW1ZvbLU
<CurrencyID>k__BackingField
aWLXW6h4Zaj
<CreditorAccountID>k__BackingField
EAXXWfcQmKn
<DebitorAccountID>k__BackingField
XLaXWH4jhWm
<EntryID>k__BackingField
RwHXWBd5t0J
<Note>k__BackingField
HPlXWtJFLHc
<RefNumber>k__BackingField
rEFXWxE9EtO
<Date>k__BackingField
C44XW5S9h2o
<Status>k__BackingField
EdfXWror8KC
<Channel>k__BackingField
U9iXWGAKcpo
<Year>k__BackingField
hE8XWgp2yv9
<BranchID>k__BackingField
lMSXWVpjE2Z
<CreatedBy>k__BackingField
SQcXWeidVsn
<CreatedTime>k__BackingField
MOEXWn7ZarO
<CreatedDate>k__BackingField
F5CXW8jnHdr
<HourTime>k__BackingField
tg6XWoJtnnt
<MinuteTime>k__BackingField
e3nXWTsfdZo
<TransNumber>k__BackingField
Ul1XWP56Pag
<SimType>k__BackingField
qHXXWRJce0n
<ViaExcel>k__BackingField
OU7XWAWrAg7
<Attachments>k__BackingField
QDaXWbvrVfe
<ExtraInfo>k__BackingField
wrZXWII6mwv
<ExtraID>k__BackingField
ArtXWNacYk9
<SyncID>k__BackingField
JUWXWkkwbW4
<RefID>k__BackingField
dRlXW7wIGt5
<BindID>k__BackingField
TEoXWcAyN7s
<Binded>k__BackingField
HqjXWi0Y6u2
<OrderID>k__BackingField
EY8XWdc07US
<ByOrder>k__BackingField
BJ9XWwKaWZb
<LineType>k__BackingField
eJyXWyGPSkI
<Type>k__BackingField
kF6XWhrsZLw
<Method>k__BackingField
uixXWFUF4rn
<SourceType>k__BackingField
H59XWl977ZA
<Account>k__BackingField
zAtXWYdAdIt
<Account1>k__BackingField
cNWXWJGsWi9
<Branch>k__BackingField
eHfXWKAv2Gp
<Currency>k__BackingField
C5RXW9xsorg
<Sims>k__BackingField
HTjXWO8euGK
<TopupNetwork>k__BackingField
eEEXWUkpHgH
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SimpleEntry
AppTech.MSMS.Domain.Models.SimpleEntry
SimpleEntry
SimpleEntry
M2VXWE1R1YU
<ID>k__BackingField
RFXXWSO0qXt
<RowVersion>k__BackingField
MK7XWpsV5t5
<Number>k__BackingField
cWgXWLfC15Q
<Amount>k__BackingField
mfiXWaY10AG
<CurrencyID>k__BackingField
Cq0XW03TcNW
<CreditorAccountID>k__BackingField
zf5XWQmVZxY
<DebitorAccountID>k__BackingField
zSvXW3BWSWw
<Date>k__BackingField
JjfXWvhjsCU
<Note>k__BackingField
UThXWZZu0hB
<EntryID>k__BackingField
AWuXWmQIKpP
<RefNumber>k__BackingField
CtaXWCOHNBe
<AttachmentNumbers>k__BackingField
UUsXWztMcKp
<IsDebited>k__BackingField
vCyX42v35CP
<CreatedBy>k__BackingField
GXfX4X0JTiu
<Prints>k__BackingField
z3YX4MnRyRe
<CreatedTime>k__BackingField
b7FX4jeAw3Z
<BranchID>k__BackingField
YkhX4ufm5a6
<Year>k__BackingField
BrxX4q7slJN
<Status>k__BackingField
MOeX4W8aG7g
<CreatedDate>k__BackingField
AYDX44WSn1k
<HourTime>k__BackingField
xwTX4DDHoHy
<MinuteTime>k__BackingField
NZHX4svI4U6
<TransNumber>k__BackingField
Uj3X41ZJiGd
<Channel>k__BackingField
gUoX46aqTyP
<Attachments>k__BackingField
KsOX4fbhiMR
<ExtraInfo>k__BackingField
uNmX4HWPaQC
<ExtraID>k__BackingField
W0HX4BwB5ES
<SyncID>k__BackingField
iXYX4t1D0JU
<RefID>k__BackingField
y8RX4xRsdyW
<BindID>k__BackingField
hAHX45tdvLg
<Binded>k__BackingField
sBbX4rfcPlZ
<Account>k__BackingField
nLeX4Gs97un
<Account1>k__BackingField
rxCX4gHtiDa
<Branch>k__BackingField
VaYX4V34cRS
<Currency>k__BackingField
FQOX4eJJHUn
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SimPurchase
AppTech.MSMS.Domain.Models.SimPurchase
SimPurchase
SimPurchase
SngX4nZ4dPh
<ID>k__BackingField
isaX48rKTJ4
<RowVersion>k__BackingField
OpTX4o33eRM
<Number>k__BackingField
kJ7X4T88Imd
<NetworkID>k__BackingField
UqgX4PvygEN
<StartNumber>k__BackingField
BhDX4RaCBeJ
<EndNumber>k__BackingField
TGCX4ApQN8i
<UnitPrice>k__BackingField
zCCX4bchiyR
<TotalUnits>k__BackingField
aj0X4ImhPwj
<Amount>k__BackingField
CmuX4Nfep9m
<CurrencyID>k__BackingField
Vl9X4ki1qZ8
<CreditorAccountID>k__BackingField
ryjX47DrY5n
<DebitorAccountID>k__BackingField
VEPX4cURvcD
<EntryID>k__BackingField
Uu5X4i0d9XN
<Note>k__BackingField
gaAX4dEoSsm
<RefNumber>k__BackingField
tLMX4wf8xut
<Date>k__BackingField
yxJX4ytRY81
<Status>k__BackingField
pu6X4h30csV
<Channel>k__BackingField
DbfX4FBEKk1
<Year>k__BackingField
hgpX4lCgbAA
<BranchID>k__BackingField
elfX4YlAArX
<CreatedBy>k__BackingField
Pd4X4J33mJI
<CreatedTime>k__BackingField
Dn9X4KTTFqA
<CreatedDate>k__BackingField
ppKX49qcttG
<HourTime>k__BackingField
YvsX4OYS9ku
<MinuteTime>k__BackingField
cEfX4UuRNol
<TransNumber>k__BackingField
QEjX4EGLRsf
<SimType>k__BackingField
Pe6X4SkepGL
<ViaExcel>k__BackingField
DaRX4pENHW2
<Attachments>k__BackingField
F04X4L0ZW1A
<ExtraInfo>k__BackingField
SlVX4aIEAAL
<ExtraID>k__BackingField
PYRX40DL04I
<SyncID>k__BackingField
oYNX4QDD9ZX
<RefID>k__BackingField
j7LX43kwXw1
<BindID>k__BackingField
XNCX4v7d5uX
<Binded>k__BackingField
mPqX4ZUGI3w
<OrderID>k__BackingField
EwDX4mY6L9D
<ByOrder>k__BackingField
RN7X4CaS1Lq
<LineType>k__BackingField
cxVX4z21630
<Account>k__BackingField
ODBXD2PaoLh
<Account1>k__BackingField
JjXXDXAdyXL
<Branch>k__BackingField
o6cXDM67d2a
<Currency>k__BackingField
jS1XDjiRuC1
<SimPurchase1>k__BackingField
XAtXDuOp0xJ
<SimPurchase2>k__BackingField
vfaXDqCAG30
<TopupNetwork>k__BackingField
vlXXDWxEDig
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SMSDispatch
AppTech.MSMS.Domain.Models.SMSDispatch
SMSDispatch
SMSDispatch
namXD48xhL5
<ID>k__BackingField
R3PXDDkGxIN
<RowVersion>k__BackingField
yreXDs0xgRp
<ServiceID>k__BackingField
WirXD1rJvmx
<ClientID>k__BackingField
apnXD6ihEH0
<Active>k__BackingField
t3sXDflws5x
<BranchID>k__BackingField
zBtXDHH7w36
<CreatedBy>k__BackingField
tpQXDBnw8SV
<CreatedTime>k__BackingField
QgiXDtsd2Vu
<Branch>k__BackingField
iwMXDxVocQF
<Client>k__BackingField
udhXD5QwUrR
<ServiceInfo>k__BackingField
VsxXDrsIZgB
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SMSLog
AppTech.MSMS.Domain.Models.SMSLog
SMSLog
SMSLog
yOlXDGeOrjy
<ID>k__BackingField
RyGXDgBWwPs
<UserID>k__BackingField
NrBXDV7kVaC
<AccountID>k__BackingField
wmQXDeMbDZq
<Stamp>k__BackingField
wKHXDnyBDIw
<PhoneNumber>k__BackingField
nM8XD8nQUiM
<Message>k__BackingField
oY9XDoKfjLf
<Note>k__BackingField
vfNXDTrLbQk
<Extra>k__BackingField
CTnXDPViQVH
<RequestMsg>k__BackingField
E9dXDRtiM8d
<ResponseMsg>k__BackingField
M9QXDAYYJEP
<Status>k__BackingField
mrZXDbbHRwX
<CreatedTime>k__BackingField
uD0XDIwiMsm
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SMSMessage
AppTech.MSMS.Domain.Models.SMSMessage
SMSMessage
SMSMessage
PZhXDNoxm5I
<ID>k__BackingField
pXyXDk52FKW
<RowVersion>k__BackingField
wLFXD7OEMXp
<PhoneNumber>k__BackingField
odVXDcZfLSb
<Message>k__BackingField
xXNXDiffpmx
<Status>k__BackingField
zDZXDd90FRv
<CreatedBy>k__BackingField
DlrXDwWq4M7
<BranchID>k__BackingField
eewXDyV27iM
<CreatedTime>k__BackingField
LVkXDhwt9ET
<Branch>k__BackingField
q0ZXDFJESpg
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SpecialSim
AppTech.MSMS.Domain.Models.SpecialSim
SpecialSim
SpecialSim
fHVXDlYLiQR
<ID>k__BackingField
xsXXDYVRyxN
<RowVersion>k__BackingField
tUNXDJysALs
<Number>k__BackingField
RwEXDKsn78n
<Type>k__BackingField
fbqXD9HhcKq
<Price>k__BackingField
wQMXDO6FLKv
<CostPrice>k__BackingField
C2EXDU9qFta
<OperatorID>k__BackingField
T8sXDEfv84M
<Status>k__BackingField
xknXDS7clBg
<Active>k__BackingField
BR2XDpLKs2r
<Note>k__BackingField
bG6XDL1ND7D
<BranchID>k__BackingField
mICXDa9DdYr
<CreatedBy>k__BackingField
AKlXD0v0EAP
<CreatedTime>k__BackingField
REiXDQWvDbi
<Branch>k__BackingField
zchXD3Cj4jC
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Subscriber
AppTech.MSMS.Domain.Models.Subscriber
Subscriber
Subscriber
EnbXDvE8c9S
<ID>k__BackingField
OttXDZITa9T
<RowVersion>k__BackingField
bp6XDm2xnjs
<Number>k__BackingField
EfZXDCyypl1
<Name>k__BackingField
EFSXDzHqkOl
<SubscriberNo>k__BackingField
IR3Xs2uqVDa
<AccountID>k__BackingField
MwDXsXMlKaL
<ProviderID>k__BackingField
akYXsMwHfPP
<ServiceID>k__BackingField
NG1Xsj3CnPB
<Code>k__BackingField
asGXsuyqAiQ
<Ref>k__BackingField
o6dXsqvHWM3
<Extra>k__BackingField
FlPXsWyyxP5
<Address>k__BackingField
tDdXs4BYvQc
<Note>k__BackingField
FLPXsDqJ4Fa
<BranchID>k__BackingField
gtpXssO0vPF
<CreatedBy>k__BackingField
eTIXs1fnrAX
<CreatedTime>k__BackingField
hdQXs6aaNrR
<PhoneNumber>k__BackingField
ROkXsfE7DjT
<OpeningBalance>k__BackingField
jpCXsHrYsfp
<Account>k__BackingField
EOiXsBo7IpV
<Branch>k__BackingField
IqHXstiXngP
<ServiceInfo>k__BackingField
PILXsxbnnVd
<TopupProvider>k__BackingField
GhQXs5ntqcB
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Supplier
AppTech.MSMS.Domain.Models.Supplier
Supplier
Supplier
JoJXsrsLFrD
<ID>k__BackingField
c0UXsG68Fxr
<RowVersion>k__BackingField
igJXsgIIHLF
<Number>k__BackingField
JV7XsVEguBo
<Name>k__BackingField
xPpXseb57Cl
<AccountID>k__BackingField
XtEXsnEbhPt
<PhoneNumber>k__BackingField
H77Xs86vwjJ
<ContactNumber>k__BackingField
YyQXsoLRvuS
<Address>k__BackingField
MwHXsTWU9Sl
<Note>k__BackingField
PeSXsPgoic7
<Email>k__BackingField
FAsXsRikaMx
<CardType>k__BackingField
uL5XsAvjjWt
<CardNumber>k__BackingField
c3EXsbrEbsh
<CardIssuePlace>k__BackingField
XoSXsIvlTcL
<CardIssueDate>k__BackingField
LNnXsNA0UPY
<ImageName>k__BackingField
MMyXskbQuKM
<CreatedBy>k__BackingField
rQTXs7Jg4jA
<BranchID>k__BackingField
NMbXscc5STV
<CreatedTime>k__BackingField
dK9Xsi3Q0pD
<Type>k__BackingField
p1aXsd6sWjP
<Status>k__BackingField
u7oXswNj6KL
<SyncAccountID>k__BackingField
AdsXsy1AU64
<RefNumber>k__BackingField
jcwXshTF6VS
<Extra>k__BackingField
LptXsFiLSTD
<Account>k__BackingField
FDpXslVCXPB
<Branch>k__BackingField
g0SXsY3tg6b
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SuspendTopup
AppTech.MSMS.Domain.Models.SuspendTopup
SuspendTopup
SuspendTopup
vEBXsJZ1IC3
<ID>k__BackingField
aJtXsKhslK0
<RecordID>k__BackingField
eNGXs9D1NZ0
<ProviderID>k__BackingField
oiSXsOkoCMy
<Type>k__BackingField
JEvXsUDS0uA
<Status>k__BackingField
TbpXsETDDcq
<Date>k__BackingField
oQvXsSfDugY
<CuredTime>k__BackingField
sdfXspDuDMb
<CuredBy>k__BackingField
rqkXsLi5Z9j
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Topup
AppTech.MSMS.Domain.Models.Topup
Topup
Topup
sZWXsaK9VCb
<ID>k__BackingField
EvfXs0sUkch
<Number>k__BackingField
kSMXsQYmQB6
<ServiceID>k__BackingField
RI1Xs36Kl8F
<NetworkID>k__BackingField
EI0Xsv70bT1
<SubscriberNumber>k__BackingField
diLXsZA7KBB
<Amount>k__BackingField
IRwXsm43DW6
<FactionID>k__BackingField
tQ8XsCBdrpB
<RegionID>k__BackingField
TIDXsznakNq
<LineType>k__BackingField
zH7X12ggQFt
<Date>k__BackingField
SvAX1XgFh9k
<Year>k__BackingField
H3YX1MswDkm
<Status>k__BackingField
dmGX1jFjg6u
<Note>k__BackingField
ThaX1ueVXgE
<CreditorAccountID>k__BackingField
l1PX1qPZU4J
<CurrencyID>k__BackingField
VV0X1WANt1T
<DebitorAccountID>k__BackingField
Ot1X14ejftA
<AgentID>k__BackingField
O2QX1DsYKWy
<RefNumber>k__BackingField
j7mX1sB3hfG
<TransactionID>k__BackingField
hwtX11TpT2W
<ProviderID>k__BackingField
acYX160OXia
<EntryID>k__BackingField
DwjX1fgFKAf
<PaymentEntryID>k__BackingField
wZAX1HMQD5E
<Channel>k__BackingField
rpYX1BwojjB
<CreatedBy>k__BackingField
IeQX1tgocvM
<BranchBy>k__BackingField
tZNX1xG8OxP
<CreatedTime>k__BackingField
MCbX157AsSf
<RowVersion>k__BackingField
fi9X1rbLGqP
<BranchID>k__BackingField
ajGX1GopQ5o
<ProviderRM>k__BackingField
WLjX1g9v4CC
<ProviderPrice>k__BackingField
jTgX1V4XRmf
<SubNote>k__BackingField
FGhX1e1cUhw
<Datestamb>k__BackingField
QE0X1nOv6iH
<UniqueNo>k__BackingField
k59X180OjoI
<Quantity>k__BackingField
xvHX1oYaomk
<UnitPrice>k__BackingField
wY6X1TG4lyN
<UnitCost>k__BackingField
wYIX1PaBmAP
<CostAmount>k__BackingField
e91X1RhHQsL
<DifferentialAmount>k__BackingField
xfsX1Aqj5MK
<CommissionAmount>k__BackingField
ch4X1bXRqcO
<Discount>k__BackingField
qYPX1ITDtJ6
<TotalCost>k__BackingField
J33X1NdSfKx
<TotalAmount>k__BackingField
HfsX1k1D4f2
<Profits>k__BackingField
i3mX17cu2b8
<Method>k__BackingField
o4yX1cxo15Q
<Type>k__BackingField
wb4X1ijgNoB
<Class>k__BackingField
GObX1d3Q7du
<LType>k__BackingField
qX9X1wuSdJ7
<OperatorID>k__BackingField
O9PX1yu9A2y
<AppTechApi>k__BackingField
fKuX1hkZEXV
<BillNumber>k__BackingField
ARwX1F3beK0
<BillState>k__BackingField
U5gX1lxhOKr
<Debited>k__BackingField
zxDX1YpjkTI
<ByChild>k__BackingField
UewX1JguIbY
<IsDirect>k__BackingField
iunX1Kkh90a
<BundleName>k__BackingField
jhuX19EWAV3
<BundleCode>k__BackingField
vbOX1OCEUqg
<ExCode>k__BackingField
S6GX1UCbje5
<TransNumber>k__BackingField
ca8X1E9wTww
<OperationID>k__BackingField
tKeX1StlvcE
<AccountID>k__BackingField
v0jX1pT8gpB
<State>k__BackingField
loWX1LX1y4e
<StateClass>k__BackingField
uvaX1a5aadK
<Identifier>k__BackingField
RixX10TkXAD
<AdminNote>k__BackingField
NY1X1QWjfUs
<AccountNote>k__BackingField
oMNX13Niccx
<Description>k__BackingField
nKOX1vOkvQu
<Responded>k__BackingField
FL4X1ZQiE8l
<RequestInfo>k__BackingField
bMpX1m2V103
<ResponseInfo>k__BackingField
GjBX1CJ0DAB
<ResponseTime>k__BackingField
fb8X1zrPSZ3
<ResponseStatus>k__BackingField
dWHX62jx6sh
<ResponseReference>k__BackingField
ttSX6XhMywT
<ExecutionPeroid>k__BackingField
gHVX6MctW61
<FaildRequest>k__BackingField
M5HX6jts6Sf
<FailedReason>k__BackingField
JmiX6u5qdIk
<FailedType>k__BackingField
o9vX6qDCXZ8
<Cured>k__BackingField
cVfX6Wevpyh
<CuredBy>k__BackingField
AhCX64Id9qe
<CuredInfo>k__BackingField
XOEX6Dd82Zr
<InspectInfo>k__BackingField
yfoX6sUolyj
<Flag>k__BackingField
sJkX61Y7Yjf
<Action>k__BackingField
LDGX66qosWr
<QuotaionID>k__BackingField
AhCX6frt7Mo
<SyncID>k__BackingField
HXyX6HoIhHF
<Account>k__BackingField
PjNX6B2sTlV
<Account1>k__BackingField
loUX6ta2DCb
<Agent>k__BackingField
QoVX6xXvll9
<Branch>k__BackingField
P8GX65FlTPu
<Currency>k__BackingField
HKFX6rW98Vl
<Journal>k__BackingField
A5TX6GNecst
<ServiceInfo>k__BackingField
mdIX6gExxGj
<TopupProvider>k__BackingField
I0iX6VIupAN
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupClosure
AppTech.MSMS.Domain.Models.TopupClosure
TopupClosure
TopupClosure
gKBX6emdC9O
<ID>k__BackingField
F6bX6nt0BLx
<RowVersion>k__BackingField
xsgX6874fUS
<Number>k__BackingField
PetX6oNClfA
<ProviderID>k__BackingField
mCRX6TLWu6n
<Date>k__BackingField
LoAX6PNrdlf
<TopupBalance>k__BackingField
GkwX6RUtmth
<CurrentBalance>k__BackingField
EDxX6Aw55nk
<RemoteBalance>k__BackingField
ycyX6bWGigM
<DifferBalance>k__BackingField
FX8X6I2ee15
<IsMatched>k__BackingField
N5eX6N8ykuM
<DebitedTotal>k__BackingField
wQjX6k87nUo
<FailedTotal>k__BackingField
v9FX67CUi1U
<Type>k__BackingField
VIsX6cCukG7
<Status>k__BackingField
T3EX6iFT4dt
<Flag>k__BackingField
RxqX6d0IlLm
<BalStatus>k__BackingField
z9gX6w1JIDJ
<Approved>k__BackingField
oP4X6yZoX2s
<Info>k__BackingField
rcRX6hxgdq7
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupCommission
AppTech.MSMS.Domain.Models.TopupCommission
TopupCommission
TopupCommission
E3pX6F8w5nn
<ID>k__BackingField
naoX6lkl69L
<RowVersion>k__BackingField
vt9X6Y7WAhw
<Number>k__BackingField
L0lX6JY4s5i
<ServiceID>k__BackingField
Lp6X6KRrfeY
<StartDate>k__BackingField
cq6X69TKxF5
<EndDate>k__BackingField
EuVX6OjOW7i
<OpsCount>k__BackingField
vrGX6UFEKyB
<Percentage>k__BackingField
nEUX6Eh4ffV
<Amount>k__BackingField
tZNX6SNU5Qn
<CurrencyID>k__BackingField
NSBX6p4otLe
<CreditorAccountID>k__BackingField
dItX6LBGEm0
<DebitorAccountID>k__BackingField
jTnX6ai5MiM
<Credited>k__BackingField
HKYX60dThQB
<EntryID>k__BackingField
r1rX6Q2RvMU
<Note>k__BackingField
yrAX63iQfoJ
<RefNumber>k__BackingField
fFXX6voIBMw
<LineType>k__BackingField
UhNX6ZDjwD1
<ProviderID>k__BackingField
Q92X6mjTAKa
<NetworkID>k__BackingField
O6yX6C93CvC
<Date>k__BackingField
H4iX6zHVcuv
<Status>k__BackingField
W5RXf2T8gMT
<Year>k__BackingField
gvDXfX1wiB9
<BranchID>k__BackingField
TVyXfM9eQIn
<CreatedBy>k__BackingField
cn6XfjjMTSR
<CreatedTime>k__BackingField
gTTXfuk41e5
<CreatedDate>k__BackingField
PtDXfqAljy5
<HourTime>k__BackingField
QltXfWxLJp2
<MinuteTime>k__BackingField
emhXf44fn0w
<TransNumber>k__BackingField
Uh2XfDKlDhQ
<Channel>k__BackingField
stoXfsWTO3g
<Attachments>k__BackingField
EkBXf1GBkl2
<ExtraInfo>k__BackingField
plVXf6RYJ8c
<ExtraID>k__BackingField
fHnXffM4HIe
<SyncID>k__BackingField
QCaXfHEu3kZ
<RefID>k__BackingField
fBWXfBRKsMZ
<BindID>k__BackingField
VwPXftjiAJW
<Binded>k__BackingField
KxKXfxRlrWY
<TotalTopup>k__BackingField
dfkXf5iQa7G
<Account>k__BackingField
XI8XfrhUaLQ
<Account1>k__BackingField
zamXfGsckZn
<Branch>k__BackingField
G5JXfgdmM89
<Currency>k__BackingField
tPxXfVQPEIH
<Journal>k__BackingField
EalXfew4mXe
<ServiceInfo>k__BackingField
kn6Xfn5EbE0
<TopupNetwork>k__BackingField
LkdXf8l1F8B
<TopupProvider>k__BackingField
B3nXfojefBK
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupNetwork
AppTech.MSMS.Domain.Models.TopupNetwork
TopupNetwork
TopupNetwork
ePZXfTXIAlx
<ID>k__BackingField
wKvXfPOUn2U
<Name>k__BackingField
eZIXfR5gU8v
<Type>k__BackingField
qQpXfAu3nxj
<OfferOrders>k__BackingField
wUSXfbUTVCU
<RiyalMobiles>k__BackingField
wG7XfI9KFfw
<SimInvoices>k__BackingField
oeGXfNGxeTw
<SimPurchases>k__BackingField
n7ZXfkZTWxC
<TopupCommissions>k__BackingField
uscXf7iM4YQ
<TrailToupCommissions>k__BackingField
HdMXfc9xHri
<TrailToupOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupOrder
AppTech.MSMS.Domain.Models.TopupOrder
TopupOrder
TopupOrder
uxCXfiqKVrJ
<ID>k__BackingField
g7bXfdL1DP8
<RowVersion>k__BackingField
bLrXfwFA41o
<ParentID>k__BackingField
UbwXfyGb9GO
<ServiceID>k__BackingField
qZjXfhsbvKy
<SubscriberNumber>k__BackingField
Q7CXfFqSC1T
<Amount>k__BackingField
IT0XfldSxqM
<Description>k__BackingField
nnuXfYJtoTA
<Note>k__BackingField
R2RXfJAsfsw
<Channel>k__BackingField
uIUXfK8YWsU
<ImageName>k__BackingField
mHvXf9bcUnV
<CreatedBy>k__BackingField
KxQXfOEp6X9
<BranchID>k__BackingField
w9RXfUAqYWg
<CreatedTime>k__BackingField
W6FXfEceWx5
<FactionID>k__BackingField
YhKXfSOvsVQ
<AccountID>k__BackingField
wEUXfppeley
<Account>k__BackingField
G2vXfLxw3V7
<Branch>k__BackingField
iHfXfaAmfIY
<OrderInfo>k__BackingField
QwVXf0ucCYE
<ServiceInfo>k__BackingField
Sa9XfQYNpAi
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupProvider
AppTech.MSMS.Domain.Models.TopupProvider
TopupProvider
TopupProvider
bdNXf3UBlHa
<ID>k__BackingField
AZ5Xfv9rA1u
<RowVersion>k__BackingField
z9fXfZk9U02
<Number>k__BackingField
dTKXfm1AVZp
<Name>k__BackingField
AYDXfCsTO6G
<Note>k__BackingField
YnTXfzo6dB5
<CreatedBy>k__BackingField
iT4XH2qCanD
<BranchID>k__BackingField
vtdXHXyCNNt
<CreatedTime>k__BackingField
BHnXHMK8p9V
<AccountID>k__BackingField
wsMXHjoy2BX
<ApiName>k__BackingField
Un2XHuToZO1
<Username>k__BackingField
ujwXHq28Tfq
<Password>k__BackingField
ROrXHWEgbGj
<Token>k__BackingField
tB6XH4F8tlx
<UserId>k__BackingField
Q7KXHDRE7at
<BaseUrl>k__BackingField
cIHXHsYvuFq
<Type>k__BackingField
GitXH17Y9lr
<SyncAccountID>k__BackingField
wuiXH6Um43s
<Balance>k__BackingField
KgZXHfaHCY6
<BalanceText>k__BackingField
TI0XHHFgQ0Z
<IsAppTechApi>k__BackingField
HPtXHB7hbSR
<ApiVC>k__BackingField
vMtXHtp8pd2
<IsDirect>k__BackingField
RP2XHxncb6I
<BalanceUpdatedTime>k__BackingField
iIFXH5psPjy
<AutoBalance>k__BackingField
uHGXHr0X7kF
<NotifyBalOut>k__BackingField
pslXHGGuD8c
<IsSoap>k__BackingField
ADDXHg10nMF
<Port>k__BackingField
qsLXHVcAoQ6
<Mode>k__BackingField
yWCXHefcVXo
<Bundling>k__BackingField
J3UXHnHf2H6
<Status>k__BackingField
gguXH8pTA4I
<ApiType>k__BackingField
ICwXHoVfAUe
<Flag>k__BackingField
z7vXHT3Sl09
<Warning>k__BackingField
YOFXHPLUCnG
<WarningNote>k__BackingField
zntXHR6sZwa
<ExtraNo>k__BackingField
N9kXHAO8NIF
<Token2>k__BackingField
KbCXHbQvmrN
<ExtraInfo>k__BackingField
z9uXHIX9R5m
<MiniBalance>k__BackingField
zhmXHNcXPqb
<AutoInspect>k__BackingField
R65XHkAgNYt
<Account>k__BackingField
kYFXH76vFPU
<Branch>k__BackingField
JMnXHcgoZGL
<ExchangerTargets>k__BackingField
pvtXHida8FJ
<ItemCosts>k__BackingField
vDbXHdkTNsJ
<LiveTopups>k__BackingField
uFgXHwxBZOJ
<RiyalMobiles>k__BackingField
EqkXHydR7A2
<Subscribers>k__BackingField
BatXHh4K5eo
<Topups>k__BackingField
Yj9XHFiGuhS
<TopupCommissions>k__BackingField
QrsXHlHyPU4
<UserInfo>k__BackingField
KdtXHYw2wOU
<TransportOrders>k__BackingField
WSFXHJ1Sxsk
<External>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TrailToupCommission
AppTech.MSMS.Domain.Models.TrailToupCommission
TrailToupCommission
TrailToupCommission
YtnXHKZ8ddC
<ID>k__BackingField
KLCXH9qssUp
<RowVersion>k__BackingField
uW9XHOEnAKG
<MobileNetworkID>k__BackingField
QtPXHUihrvC
<FromAmount>k__BackingField
G6gXHEkj9YU
<ToAmount>k__BackingField
SUNXHSFjmgR
<Percentage>k__BackingField
j3oXHpZ92UR
<Note>k__BackingField
AWrXHL7UPw0
<CreatedBy>k__BackingField
EFCXHaGyKCE
<CreatedTime>k__BackingField
X9OXH0kIpBo
<BranchID>k__BackingField
kdWXHQ5JR4F
<PersonnalPrice>k__BackingField
yVDXH3fyMG4
<Branch>k__BackingField
TYnXHvS85gQ
<TopupNetwork>k__BackingField
hSXXHZeAp7Z
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TrailToupOrder
AppTech.MSMS.Domain.Models.TrailToupOrder
TrailToupOrder
TrailToupOrder
wPiXHmUBeYH
<ID>k__BackingField
KOaXHCIxRyP
<RowVersion>k__BackingField
zIvXHzFrEkA
<MobileNetworkID>k__BackingField
cqwXB2tG090
<SubscriberNumber>k__BackingField
kppXBXRm64X
<Amount>k__BackingField
QrXXBM059YY
<Percentage>k__BackingField
WaIXBjXkP3t
<ExchangeAmount>k__BackingField
cfZXBuYUpqD
<Note>k__BackingField
ow7XBqsUvJs
<Channel>k__BackingField
JJSXBWMJv2I
<ParentID>k__BackingField
tLhXB4jTPmI
<ServiceID>k__BackingField
GE9XBDP1Itw
<CreatedBy>k__BackingField
rqLXBsn3rit
<BranchID>k__BackingField
OU3XB1QvKEo
<CreatedTime>k__BackingField
AjDXB6tMhpF
<AccountID>k__BackingField
VebXBfwrk1t
<Account>k__BackingField
Ht4XBHaSJWi
<Branch>k__BackingField
gWrXBB0MbMu
<OrderInfo>k__BackingField
mPXXBt8Nw6s
<ServiceInfo>k__BackingField
g5EXBxZ0iAP
<TopupNetwork>k__BackingField
hA3XB5Amivr
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransferIn
AppTech.MSMS.Domain.Models.TransferIn
TransferIn
TransferIn
qUvXBrVT7DS
<ID>k__BackingField
OI1XBGtth72
<RowVersion>k__BackingField
KgDXBg2ZUZl
<Number>k__BackingField
D2GXBVt1e2a
<TransferNumber>k__BackingField
ywVXBeJKmvi
<Amount>k__BackingField
yF0XBnV8En7
<CurrencyID>k__BackingField
lRwXB8MQqNW
<ExchangerID>k__BackingField
jA8XBo0N5Vn
<AccountID>k__BackingField
nUTXBT7cjx2
<BeneficiaryName>k__BackingField
bKpXBPD8ZPF
<BeneficiaryPhone>k__BackingField
rGyXBRR12Sx
<SenderName>k__BackingField
BfnXBAIPFXL
<SenderPhone>k__BackingField
HiiXBbRiXZb
<CommissionCurrencyID>k__BackingField
zwmXBIfYFxs
<CommissionAmount>k__BackingField
bHpXBNiVv50
<EntryID>k__BackingField
zWIXBkypUmB
<ExtraInfo>k__BackingField
re7XB7jERXD
<ExtraID>k__BackingField
kU8XBccl0MF
<RefNumber>k__BackingField
JEMXBiY1KUD
<Status>k__BackingField
l28XBdSjaJD
<Delivered>k__BackingField
dvqXBw9GFy7
<Date>k__BackingField
DVKXByFFSs3
<Year>k__BackingField
yxpXBh5GSad
<Note>k__BackingField
JhsXBFvAY59
<Channel>k__BackingField
uLkXBlMI9xq
<CreatedBy>k__BackingField
b9lXBYoI3aT
<BranchID>k__BackingField
Sf0XBJFl7TG
<CreatedTime>k__BackingField
qEbXBKMBm1g
<CreatedDate>k__BackingField
uKqXB9xgHlj
<HourTime>k__BackingField
RONXBOumHFB
<MinuteTime>k__BackingField
awVXBUFy7TL
<TransNumber>k__BackingField
aGcXBEWItki
<Attachments>k__BackingField
bP0XBS5qASu
<SyncID>k__BackingField
T9xXBpASXFo
<RefID>k__BackingField
ajlXBLf6vAw
<BindID>k__BackingField
mlkXBa3TB1T
<Binded>k__BackingField
PK3XB071U2x
<OrderID>k__BackingField
yjRXBQ1dHto
<ByOrder>k__BackingField
wZ9XB377vA8
<IsSync>k__BackingField
g55XBvwpHjS
<Type>k__BackingField
d01XBZ4BSCh
<Account>k__BackingField
fI8XBmdqAoX
<Branch>k__BackingField
AmTXBCPFPLx
<Currency>k__BackingField
bvyXBzo8Rph
<Currency1>k__BackingField
TahXt2gpxb8
<Exchanger>k__BackingField
C4wXtXnm8PG
<UserInfo>k__BackingField
wdZXtMY8tOQ
<ExchangerAccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransferOrder
AppTech.MSMS.Domain.Models.TransferOrder
TransferOrder
TransferOrder
QWhXtjmaTH0
<ID>k__BackingField
sQ4XtucObRF
<RowVersion>k__BackingField
A7MXtqsMTFk
<TransferNumber>k__BackingField
zokXtWotvPG
<ServiceID>k__BackingField
Ef2Xt41c2kM
<IsIncoming>k__BackingField
RjHXtDyUM9m
<Amount>k__BackingField
tUjXtspRIGn
<CurrencyID>k__BackingField
WsLXt1kbgJA
<ReceiverName>k__BackingField
wYgXt6LlgG2
<ReceiverMobile>k__BackingField
z4EXtfkEuEg
<ReceiverCardNo>k__BackingField
EiCXtHIDEGH
<ReceiverCardIssuerPlace>k__BackingField
El9XtBK9CU7
<ReceiverCardIssuerDate>k__BackingField
MIdXttk8BdE
<SenderName>k__BackingField
XQFXtxXeFMU
<SenderMobile>k__BackingField
pMYXt5E0NNS
<Note>k__BackingField
D72Xtr0ZeHB
<Channel>k__BackingField
acHXtGtIC0W
<ImageName>k__BackingField
oPDXtg85vWu
<CreatedBy>k__BackingField
uqjXtVTBaL1
<BranchID>k__BackingField
B5VXteQ2GCJ
<CreatedTime>k__BackingField
UoaXtnMCxPQ
<ExchangerID>k__BackingField
Vn5Xt8dGfT1
<ParentID>k__BackingField
O7XXtoohaXh
<ReceiverCardType>k__BackingField
kwnXtTTSCTf
<AccountID>k__BackingField
dDJXtPxuvqD
<CommissionCurrencyID>k__BackingField
OsIXtRWmro8
<Commission>k__BackingField
FwlXtA4Hv3D
<Status>k__BackingField
GNfXtb4VQHw
<ReceiverImage>k__BackingField
Q4lXtIdcSno
<SenderImage>k__BackingField
mM6XtNRRUmQ
<Date>k__BackingField
K6pXtkdfBuI
<CountryID>k__BackingField
pqNXt7rowlD
<ProvinceID>k__BackingField
c5LXtcPFO0h
<RegionID>k__BackingField
RZEXtiCUROW
<RefNumber>k__BackingField
ReZXtdRCYLO
<Extra>k__BackingField
bSEXtwQy4dA
<Account>k__BackingField
Dq0XtywlCF6
<Branch>k__BackingField
SmmXthLmAaW
<Country>k__BackingField
R9bXtFVlWE3
<Currency>k__BackingField
VQ1Xtlu9HEJ
<Currency1>k__BackingField
Sa0XtYoAXGa
<Exchanger>k__BackingField
D3GXtJiJr4Y
<OrderInfo>k__BackingField
iFlXtKlAGlP
<Province>k__BackingField
gekXt9I2Elr
<RemittanceRegion>k__BackingField
TJHXtOEWUUi
<ServiceInfo>k__BackingField
QHOXtUYGw3W
<TransferOrder1>k__BackingField
it6XtEb0VDd
<TransferOrder2>k__BackingField
ip9XtSQyKOB
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransferOut
AppTech.MSMS.Domain.Models.TransferOut
TransferOut
TransferOut
aFSXtpq3Cgo
<ID>k__BackingField
ei1XtLAgNmZ
<RowVersion>k__BackingField
qlLXtagybB5
<Number>k__BackingField
oVCXt0WeVoX
<TransferNumber>k__BackingField
bqkXtQu8Vy8
<Amount>k__BackingField
CtQXt32JDGG
<CurrencyID>k__BackingField
hWKXtvXg2yF
<ExchangerID>k__BackingField
oWJXtZh6T5t
<AccountID>k__BackingField
pSNXtmtNYF4
<BeneficiaryName>k__BackingField
mcEXtCNiJwP
<BeneficiaryPhone>k__BackingField
FuxXtz4tEwE
<SenderName>k__BackingField
UsfXx2TqJXO
<SenderPhone>k__BackingField
w8qXxXkhlvA
<CommissionCurrencyID>k__BackingField
YWXXxMItHvW
<CommissionAmount>k__BackingField
V9VXxjYaxSH
<ExchangerCommission>k__BackingField
wpTXxu3FdY2
<EntryID>k__BackingField
o8tXxqV4N2M
<ExtraInfo>k__BackingField
NEfXxWhGiBL
<ExtraID>k__BackingField
k5KXx4UUeXk
<RefNumber>k__BackingField
VQtXxDvCfbH
<Status>k__BackingField
n85XxsYYtRT
<Date>k__BackingField
iqHXx1HZsU4
<Year>k__BackingField
qLqXx62Tiy2
<Note>k__BackingField
VgkXxfuWI5N
<Channel>k__BackingField
H8HXxH6wJyp
<CreatedBy>k__BackingField
sAGXxBstlTg
<BranchID>k__BackingField
UQsXxtc10h6
<CreatedTime>k__BackingField
fCxXxxMdXCs
<CreatedDate>k__BackingField
gOCXx5VACgR
<HourTime>k__BackingField
TX6XxrTh7F8
<MinuteTime>k__BackingField
XlLXxGIrPNj
<TransNumber>k__BackingField
qbmXxgxI9aV
<Attachments>k__BackingField
MFXXxV4qMYI
<SyncID>k__BackingField
un9XxeArAun
<RefID>k__BackingField
O2HXxnG8Rey
<BindID>k__BackingField
oKhXx8MMBmM
<Binded>k__BackingField
BLOXxo0Zws4
<OrderID>k__BackingField
v9wXxTDY7qJ
<ByOrder>k__BackingField
QijXxPxM38P
<IsSync>k__BackingField
vl7XxRL4PuR
<Type>k__BackingField
bsVXxAZVgit
<Account>k__BackingField
BSHXxbrR0n6
<Branch>k__BackingField
tTlXxIbBtci
<Currency>k__BackingField
Sb0XxNROGY6
<Currency1>k__BackingField
pE6XxkSTSbi
<Exchanger>k__BackingField
BOuXx7w24sI
<UserInfo>k__BackingField
gyUXxcp7lyK
<ExchangerAccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Transporter
AppTech.MSMS.Domain.Models.Transporter
Transporter
Transporter
GMMXxij7Rhx
<ID>k__BackingField
lUeXxdRPB0h
<RowVersion>k__BackingField
OYiXxwrjpv2
<Number>k__BackingField
WsDXxyNo1JJ
<Name>k__BackingField
FYtXxhn113I
<AccountID>k__BackingField
FkeXxF8LZYO
<Description>k__BackingField
zl6XxlFs7Xl
<Note>k__BackingField
WwFXxYZAAWR
<Status>k__BackingField
t4lXxJQTD11
<BranchID>k__BackingField
c9QXxKKvTQU
<CreatedBy>k__BackingField
WBmXx9R32Kl
<CreatedTime>k__BackingField
LyoXxOXKrei
<ImageName>k__BackingField
kuiXxUBLZ29
<Account>k__BackingField
YWaXxEX4tmf
<Branch>k__BackingField
OpOXxSwggOx
<UserInfo>k__BackingField
E2TXxpGkkZX
<TransportOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransportOrder
AppTech.MSMS.Domain.Models.TransportOrder
TransportOrder
TransportOrder
GAQXxLB5uZS
<ID>k__BackingField
ErhXxa8aQYk
<RowVersion>k__BackingField
f0ZXx01feWh
<ServiceID>k__BackingField
mJ2XxQGOJCU
<AccountID>k__BackingField
s4BXx3QWvE1
<SourceRegionID>k__BackingField
DFCXxvM0Fsj
<TargetRegionID>k__BackingField
MFVXxZjn4OG
<Date>k__BackingField
BCFXxmP62PJ
<Time>k__BackingField
HkFXxCOSpWr
<FullName>k__BackingField
vblXxzNpJfI
<TicketNumber>k__BackingField
gmeX52UKT5U
<IsChild>k__BackingField
nZQX5XmMskU
<ProviderID>k__BackingField
oiqX5MT0s26
<ImageName>k__BackingField
EIIX5jFYBbZ
<ContactNumber>k__BackingField
dGiX5uE0ceF
<RefNumber>k__BackingField
zNoX5qL19Mu
<Amount>k__BackingField
UGrX5WVV58e
<CurrencyID>k__BackingField
NjpX54sJUtv
<Note>k__BackingField
rPpX5DQpUts
<Channel>k__BackingField
QkZX5sOwfsb
<ParentID>k__BackingField
CG2X51kYwqV
<TransType>k__BackingField
hlGX56iFZnv
<CreatedBy>k__BackingField
xxNX5fXSElm
<BranchID>k__BackingField
PEAX5HhRLGj
<CreatedTime>k__BackingField
THUX5B7nAA3
<TransporterID>k__BackingField
CpfX5tQtH6D
<Account>k__BackingField
ssyX5xkC2ko
<Branch>k__BackingField
JNcX557ZkhK
<Currency>k__BackingField
mnAX5raLJVt
<OrderInfo>k__BackingField
McZX5Ggc5Pb
<ServiceInfo>k__BackingField
RC8X5gIXgNG
<TopupProvider>k__BackingField
TtLX5VybCBt
<Transporter>k__BackingField
bbXX5egngAc
<WERegion>k__BackingField
cF4X5na1Ri2
<WERegion1>k__BackingField
dL1X58uAsip
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserClaim
AppTech.MSMS.Domain.Models.UserClaim
UserClaim
UserClaim
uUuX5oK3Biq
<ID>k__BackingField
gByX5TqTo4A
<ClaimType>k__BackingField
FfoX5PVQyew
<ClaimValue>k__BackingField
S1cX5RRiUlN
<UserID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserDevice
AppTech.MSMS.Domain.Models.UserDevice
UserDevice
UserDevice
s6IX5AVlbP2
<ID>k__BackingField
abZX5bDuBse
<RowVersion>k__BackingField
PxBX5I18brr
<UserID>k__BackingField
m8aX5Nb50xl
<Identifier>k__BackingField
DBpX5kNfYiL
<Permitted>k__BackingField
lWyX57IutvJ
<IsExpired>k__BackingField
c6LX5cQEypO
<ExpireDate>k__BackingField
WDBX5ijYsb7
<ExtraInfo>k__BackingField
ajAX5dxFNfD
<Keyname>k__BackingField
o9SX5welD9T
<Note>k__BackingField
oXVX5ynrJ5E
<BranchID>k__BackingField
HHtX5hLFSkA
<CreatedBy>k__BackingField
NOnX5FwH9rJ
<CreatedTime>k__BackingField
S6jX5lW8J6i
<Branch>k__BackingField
TZtX5YBrECo
<UserInfo>k__BackingField
FntX5Jv98Y3
<UserInfo1>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserInfo
AppTech.MSMS.Domain.Models.UserInfo
UserInfo
UserInfo
vL8X5K5qjmP
<ID>k__BackingField
FlnX59WXnrS
<RowVersion>k__BackingField
GWEX5OvhfSD
<Number>k__BackingField
fxuX5U7MtAB
<UserName>k__BackingField
cPrX5EORKOu
<Note>k__BackingField
mBFX5SHvtEH
<Password>k__BackingField
Qs4X5plgYGq
<Type>k__BackingField
M8JX5LbOsAV
<CreatedBy>k__BackingField
rybX5axbmwF
<BranchID>k__BackingField
bc2X50v22vq
<CreatedTime>k__BackingField
qfcX5QdwGjJ
<LastLoginDate>k__BackingField
W6hX53lbItm
<Status>k__BackingField
nRtX5vxIJvJ
<AccessFailedCount>k__BackingField
DPxX5Zlx6NN
<ConcurrencyStamp>k__BackingField
MltX5mYgFUE
<SecurityStamp>k__BackingField
sSGX5CIHSEs
<TwoFactorEnabled>k__BackingField
OiKX5ziM616
<ClaimGroupID>k__BackingField
hGGXr2CKZ4B
<Accounts>k__BackingField
drsXrXuIM5b
<AccountApis>k__BackingField
z6SXrMPjUd0
<AccountDocuments>k__BackingField
SEFXrjfepOO
<AccountRegions>k__BackingField
MZdXruMYoQp
<AccountSlatings>k__BackingField
qwAXrqTL2JB
<AccountUsers>k__BackingField
emcXrWpj6Eq
<AccountUsers1>k__BackingField
CSXXr4F5xIS
<AdminNotifications>k__BackingField
sapXrDSnAcd
<Agents>k__BackingField
FrXXrsi0NIY
<AgentPoints>k__BackingField
fjBXr1dPYIY
<AgentPointUsers>k__BackingField
LXVXr6KXfRs
<AgentPointUsers1>k__BackingField
hX7XrfKgy8b
<Bagats>k__BackingField
zkKXrHm7BfS
<BagatPayments>k__BackingField
IIRXrBD6f1f
<Banks>k__BackingField
LBtXrtP9wQw
<BankDeposits>k__BackingField
ot7XrxrAcLG
<Branch>k__BackingField
iYHXr5625st
<BranchTargets>k__BackingField
PbkXrrralaE
<Brochures>k__BackingField
CZRXrGTnn9u
<BuyCurrencies>k__BackingField
sdGXrg8yT3R
<Cards>k__BackingField
bEYXrVtPt8V
<CardFactions>k__BackingField
F38XrehNPGx
<CardOrders>k__BackingField
eVcXrnHfmMa
<CardPayments>k__BackingField
cNqXr8Dakxw
<CardTypes>k__BackingField
OaHXroFWBkr
<CashDeposits>k__BackingField
EyEXrTPgZ5N
<CashIns>k__BackingField
YokXrPqDaJ0
<CashOuts>k__BackingField
BRiXrRpudNi
<CashTransfers>k__BackingField
cJOXrAiSdbu
<CashWithdraws>k__BackingField
lLZXrbHgDqs
<Cheques>k__BackingField
c3FXrIaPkDN
<ClaimGroups>k__BackingField
q87XrNdYps1
<ClaimGroup>k__BackingField
XQ4Xrks5LsD
<Clients>k__BackingField
Hf1Xr72mcaZ
<CommissionReceipts>k__BackingField
sBlXrckHH0h
<ConsumeInvoices>k__BackingField
xfGXriJml88
<Countries>k__BackingField
b95XrdHgpwC
<CoverageOrders>k__BackingField
GqjXrwhSRyv
<Currencies>k__BackingField
F3LXry609nH
<Currencies1>k__BackingField
xsSXrh411Zj
<CurrencyExchanges>k__BackingField
hc2XrFI1Q19
<CurrencyRates>k__BackingField
j9lXrlSOyM5
<CurrencyRateAccounts>k__BackingField
sG7XrYmpmyM
<DbBackups>k__BackingField
uWuXrJGuhn3
<DepositOrders>k__BackingField
mL5XrKwssGB
<Devices>k__BackingField
cZnXr9UJrbH
<Distributors>k__BackingField
rUBXrOCWHGC
<Exchangers>k__BackingField
lB2XrUZEfdg
<ExchangerTargets>k__BackingField
wi5XrEf13FL
<Factions>k__BackingField
W98XrSs74cI
<Feedbacks>k__BackingField
pXtXrpholkh
<Funds>k__BackingField
Jq4XrL4JEB9
<Funds1>k__BackingField
LWqXraMNoOn
<FundUsers>k__BackingField
rh2Xr0Csjf7
<FundUsers1>k__BackingField
tcNXrQ7CIZf
<GeneralInfoes>k__BackingField
IaRXr3qF7QC
<GroupItems>k__BackingField
IohXrv5MLIA
<Gsms>k__BackingField
wBXXrZs4M13
<Instructions>k__BackingField
T06XrmDpVOq
<Items>k__BackingField
g47XrCudp36
<ItemCosts>k__BackingField
GirXrz4CyWc
<Journals>k__BackingField
rVMXG2gT5mp
<LiveTopups>k__BackingField
XqrXGX0KE4T
<LoanOrders>k__BackingField
OftXGMKuZHX
<Merchants>k__BackingField
oH9XGjViRQm
<MerchantCategories>k__BackingField
J4ZXGuKJg6P
<MerchantPayments>k__BackingField
BoJXGqD6f2c
<MoneyMasters>k__BackingField
MiVXGWKGNs3
<OfferOrders>k__BackingField
pulXG4Q3RO5
<OpeningBalances>k__BackingField
hlnXGDNEZGy
<OrderInfoes>k__BackingField
ujmXGswXp4t
<OrderInfoes1>k__BackingField
pjvXG1jEZk4
<OrderInfoes2>k__BackingField
KYWXG6VgdDp
<OrderSatelliteQuotas>k__BackingField
UU5XGfO4Sn6
<Parties>k__BackingField
MKwXGH0g5p4
<PartyGroups>k__BackingField
NauXGBwMEqe
<Payments>k__BackingField
uwbXGtb36l6
<Payments1>k__BackingField
HG5XGxVtbAT
<PaymentCommissions>k__BackingField
XsHXG5BghYd
<People>k__BackingField
eStXGr9iVWh
<Products>k__BackingField
qbyXGGfVO5H
<ProductCategories>k__BackingField
XnLXGglP7oL
<ProductImages>k__BackingField
RVwXGVnmbbv
<Provinces>k__BackingField
DmiXGegIeEV
<PurchaseInvoices>k__BackingField
SyJXGnRIGPo
<PurchaseInvoiceLines>k__BackingField
CrQXG8l7dHO
<Quotations>k__BackingField
lqNXGoaioNg
<ReceiptCreditors>k__BackingField
SByXGTAN3oA
<ReceiptDebitors>k__BackingField
wlDXGParXWm
<Regions>k__BackingField
jfoXGRF6y3D
<RemittanceCommissions>k__BackingField
FFiXGAcDNus
<RemittanceIns>k__BackingField
PeJXGbqM1mI
<RemittanceOuts>k__BackingField
KiMXGID3Hh0
<RemittancePoints>k__BackingField
DwlXGN5D0j4
<RemittanceRegions>k__BackingField
xFdXGkwEjOG
<RiyalMobiles>k__BackingField
zaPXG7r0R09
<RSSes>k__BackingField
NMvXGcRvRNV
<SaleCurrencies>k__BackingField
FsPXGiBYRCc
<SaleInvoices>k__BackingField
ctPXGd7W0wQ
<SaleInvoiceLines>k__BackingField
S3FXGwQMhh1
<SatelliteFactions>k__BackingField
WPhXGylSV5c
<SatellitePayments>k__BackingField
vsbXGhXoVQ4
<SatelliteProviders>k__BackingField
juKXGFlvm4N
<ServiceClaims>k__BackingField
jS0XGlEQrEg
<ServiceInfoes>k__BackingField
JEQXGYkoJhq
<Sims>k__BackingField
yWZXGJBYA0m
<SimCardOrders>k__BackingField
qjHXGK4m4f2
<SimInvoices>k__BackingField
BQMXG92ogY6
<SimpleEntries>k__BackingField
t9KXGODbEk1
<SimPurchases>k__BackingField
BaXXGUj2dYE
<SMSDispatches>k__BackingField
VR0XGELchEG
<SMSLogs>k__BackingField
aCSXGSCY0aC
<SMSMessages>k__BackingField
pJMXGpSuWWV
<SpecialSims>k__BackingField
d0BXGLjTa9E
<Subscribers>k__BackingField
YJJXGaZlVJ2
<Suppliers>k__BackingField
KruXG0IuBeK
<Topups>k__BackingField
CfSXGQMf1pO
<TopupCommissions>k__BackingField
SOPXG3yCcRX
<TopupOrders>k__BackingField
cytXGvlQH7E
<TopupProviders>k__BackingField
LvkXGZGuK2a
<TrailToupCommissions>k__BackingField
pYKXGmKbSE7
<TrailToupOrders>k__BackingField
hwmXGCyqHRf
<TransferIns>k__BackingField
NRgXGzUSXkE
<TransferOrders>k__BackingField
PAfXg2IurhN
<TransferOuts>k__BackingField
TTAXgXUdaQZ
<Transporters>k__BackingField
Qo5XgMB50yk
<TransportOrders>k__BackingField
gcqXgj6UqKM
<UserDevices>k__BackingField
Mr4XguUnTiy
<UserDevices1>k__BackingField
Ay5XgqiUW5Z
<UserRoles>k__BackingField
yFOXgWSkCUF
<WERegions>k__BackingField
yvRXg4onqKk
<WifiCards>k__BackingField
TnRXgDc4VK5
<WifiFactions>k__BackingField
B52Xgs3H4Ul
<WifiPayments>k__BackingField
yleXg1fWPcH
<WifiProviders>k__BackingField
CIFXg6obiBQ
<WithdrawOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserPage
AppTech.MSMS.Domain.Models.UserPage
UserPage
UserPage
djOXgf7EP6L
<ID>k__BackingField
Wc6XgHpgFkH
<UserID>k__BackingField
u1dXgBg5jc5
<PageID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserPermission
AppTech.MSMS.Domain.Models.UserPermission
UserPermission
UserPermission
XZ8XgtayUnG
<ID>k__BackingField
W4XXgxBvn7V
<UserID>k__BackingField
q9EXg5NnQGs
<PageID>k__BackingField
Gn8XgriAZeb
<ActionID>k__BackingField
s8ZXgGDPfwJ
<ClaimGroupID>k__BackingField
xpSXgguEtu4
<ClaimGroup>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserRole
AppTech.MSMS.Domain.Models.UserRole
UserRole
UserRole
GAYXgVVls2Y
<ID>k__BackingField
ox0XgekPZEa
<UserID>k__BackingField
PDTXgnytsJx
<RoleID>k__BackingField
nWRXg8S0N76
<RoleInfo>k__BackingField
NS8XgoTEIXj
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserToken
AppTech.MSMS.Domain.Models.UserToken
UserToken
UserToken
mCgXgTvIQD8
<UserID>k__BackingField
Yg6XgPCVBt8
<Provider>k__BackingField
gntXgRdlFBX
<Name>k__BackingField
qvLXgAQVECI
<Value>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Voucher
AppTech.MSMS.Domain.Models.Voucher
Voucher
Voucher
yR5XgbOFaOq
<ID>k__BackingField
uGuXgIUsmoK
<Name>k__BackingField
LNHXgNEMDQK
<Entity>k__BackingField
FCuXgkS65Nd
<Note>k__BackingField
QOFXg7ERBIV
<CreatedBy>k__BackingField
jc7XgceJV5I
<BranchID>k__BackingField
SWbXgiRPNOX
<CreatedTime>k__BackingField
Wf4XgdKCOtK
<Active>k__BackingField
watXgwkscCu
<CrOrDr>k__BackingField
nLxXgyNFyOK
<Module>k__BackingField
fDgXghnngdR
<Branch>k__BackingField
lnJXgFxLSBr
<Cheques>k__BackingField
sFjXgl5FdVh
<Journals>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WERegion
AppTech.MSMS.Domain.Models.WERegion
WERegion
WERegion
XW3XgYcAhJ8
<ID>k__BackingField
T2PXgJKBRW3
<RowVersion>k__BackingField
wVPXgKkV0ZD
<Name>k__BackingField
DOCXg9dhy7y
<ServiceID>k__BackingField
QIjXgOUqYZg
<CreatedBy>k__BackingField
v8HXgU1HgPQ
<BranchID>k__BackingField
gsLXgE9aARf
<CreatedTime>k__BackingField
NnuXgSjxxX7
<Code>k__BackingField
OASXgp6YWaA
<ServiceKeyName>k__BackingField
uEEXgLE7YZf
<Branch>k__BackingField
b24Xgaa8MvW
<RiyalMobiles>k__BackingField
WktXg0yQFDy
<ServiceInfo>k__BackingField
DdpXgQENoQT
<TransportOrders>k__BackingField
fL4Xg3iMQou
<TransportOrders1>k__BackingField
tQ1XgvfiCiN
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiCard
AppTech.MSMS.Domain.Models.WifiCard
WifiCard
WifiCard
MgjXgZ1VV1h
<ID>k__BackingField
bVFXgm116ih
<RowVersion>k__BackingField
d41XgCiDo5K
<Password>k__BackingField
tOhXgzl7wIK
<Username>k__BackingField
MbVXV2iU3Jh
<FactionID>k__BackingField
c4wXVXVrSSI
<Active>k__BackingField
XiHXVM8vROo
<SerialNo>k__BackingField
d0jXVjmmRCb
<CostPrice>k__BackingField
uTKXVuh1VNj
<SalePrice>k__BackingField
L3cXVqxxyau
<SalePrice2>k__BackingField
ou2XVW26Zv2
<Status>k__BackingField
qsgXV466vhv
<Note>k__BackingField
iEsXVDEBndY
<Number>k__BackingField
HnxXVsYRvwE
<Peroid>k__BackingField
UDqXV14AF90
<Quantity>k__BackingField
KEMXV60wFP4
<AccountID>k__BackingField
J6uXVfMCkWW
<IsExternal>k__BackingField
vmMXVHEj32g
<Description>k__BackingField
TLpXVBPhB39
<OwnerType>k__BackingField
OVAXVtB930a
<ProviderID>k__BackingField
mTbXVx2NcuC
<BranchID>k__BackingField
tttXV5ChnVC
<CreatedBy>k__BackingField
kISXVr6b44b
<CreatedTime>k__BackingField
GJ4XVG33vrO
<Branch>k__BackingField
eeKXVgwNVHB
<UserInfo>k__BackingField
hWGXVVpP1EK
<ViaExcel>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiFaction
AppTech.MSMS.Domain.Models.WifiFaction
WifiFaction
WifiFaction
fO0XVeU3A44
<ID>k__BackingField
WFoXVntcSxy
<RowVersion>k__BackingField
ROaXV8G8572
<Number>k__BackingField
RPYXVo00A5E
<OrderNO>k__BackingField
NDUXVTSbj4E
<Name>k__BackingField
ovCXVPtrPli
<Description>k__BackingField
NqfXVR1MwmI
<Price>k__BackingField
iuwXVAvowfb
<Note>k__BackingField
Lr6XVbvHyO8
<WifiProviderID>k__BackingField
RUtXVIVEBBk
<BranchID>k__BackingField
qjjXVNQDqpx
<CreatedBy>k__BackingField
XQLXVksKUni
<CreatedTime>k__BackingField
lXnXV76WSQA
<Branch>k__BackingField
FohXVcmowM6
<UserInfo>k__BackingField
nt5XViFrm9P
<WifiProvider>k__BackingField
SZrXVdqkURc
<TotalAll>k__BackingField
sDRXVwp4jBU
<TotalSold>k__BackingField
psxXVyMXU9v
<TotalRemian>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiPayment
AppTech.MSMS.Domain.Models.WifiPayment
WifiPayment
WifiPayment
fg9XVhkIYOF
<ID>k__BackingField
vA9XVFRrLAR
<RowVersion>k__BackingField
tELXVlHHZTL
<Number>k__BackingField
uNoXVYMlW3P
<ProviderID>k__BackingField
MhgXVJpTSIv
<FactionID>k__BackingField
WtlXVKgQJpN
<WifiCardID>k__BackingField
ATIXV9M8XU0
<WifiCardIDs>k__BackingField
zMqXVOIHLHh
<ProfitAmount>k__BackingField
IasXVUWSbjU
<ProviderAmount>k__BackingField
fj1XVEWgHvy
<Amount>k__BackingField
utFXVS0yAxP
<Quantity>k__BackingField
Wx6XVpsiaFC
<CurrencyID>k__BackingField
bmWXVLvu5xX
<CreditorAccountID>k__BackingField
n5XXVauJwJS
<DebitorAccountID>k__BackingField
kOPXV0I7NCR
<Date>k__BackingField
Ro7XVQtR0DD
<Note>k__BackingField
RhUXV3HltC7
<EntryID>k__BackingField
xhtXVviLv9E
<RefNumber>k__BackingField
Eb4XVZaKxOp
<IsDebited>k__BackingField
XPCXVmUgDOT
<Year>k__BackingField
Do3XVCi3cGP
<CreatedBy>k__BackingField
Q3bXVzNF309
<CreatedTime>k__BackingField
AHnXe2oyZ1L
<BranchID>k__BackingField
yWDXeXIaUQw
<Status>k__BackingField
XloXeMwlrEM
<ServiceID>k__BackingField
JkpXejOj9uC
<Account>k__BackingField
dDNXeunEhi9
<Account1>k__BackingField
tOSXeqAOZMf
<Branch>k__BackingField
KBbXeW4e0NQ
<Currency>k__BackingField
WrYXe4Zmta5
<ServiceInfo>k__BackingField
AdEXeDgReNB
<UserInfo>k__BackingField
JbkXessvQId
<WifiNumber>k__BackingField
tOyXe1QtDYa
<PhoneNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiProvider
AppTech.MSMS.Domain.Models.WifiProvider
WifiProvider
WifiProvider
ElKXe6DSsa2
<ID>k__BackingField
w65Xef9IM8D
<RowVersion>k__BackingField
ylSXeHYrcvp
<Number>k__BackingField
xK5XeBDSpIr
<Name>k__BackingField
bkLXetRBpl1
<Image>k__BackingField
RhUXexWN5UK
<Address>k__BackingField
DHPXe5gPkAQ
<URL>k__BackingField
osXXerCUHhl
<Phone>k__BackingField
RooXeGI4YBU
<Note>k__BackingField
xdbXegFK20b
<PrafitStatus>k__BackingField
kdgXeVfYsv7
<PrafitAmount>k__BackingField
S90XeeuKIAB
<AccountID>k__BackingField
U8jXenCE607
<RegionID>k__BackingField
c6rXe8lZAlR
<BranchID>k__BackingField
XaIXeotc1X3
<CreatedBy>k__BackingField
eH0XeTfAiTB
<CreatedTime>k__BackingField
mwHXePN2mWj
<Account>k__BackingField
ppyXeRGHDwP
<Branch>k__BackingField
dHNXeAMg0IU
<Region>k__BackingField
OsbXebF7yQu
<UserInfo>k__BackingField
KxIXeIuFCm3
<WifiFactions>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WithdrawOrder
AppTech.MSMS.Domain.Models.WithdrawOrder
WithdrawOrder
WithdrawOrder
FhuXeNpVU9O
<ID>k__BackingField
MKMXekEJSaV
<RowVersion>k__BackingField
qp9Xe7rvIcU
<ClientID>k__BackingField
nJPXec4Ddme
<AgentID>k__BackingField
y5vXeifsTo1
<Amount>k__BackingField
LNFXedXvxUy
<CurrencyID>k__BackingField
xBSXewxqoTT
<SecretCode>k__BackingField
bSvXeyKTlLo
<State>k__BackingField
KJQXehxfFg1
<Debited>k__BackingField
z2tXeFuPtgV
<Cancelled>k__BackingField
amUXelwBuEa
<Date>k__BackingField
cT8XeYjxwnf
<Note>k__BackingField
bZVXeJGrWkW
<Channel>k__BackingField
rjAXeKDpbHQ
<ParentID>k__BackingField
olRXe9vZkQu
<ServiceID>k__BackingField
i4vXeONLfM8
<CreatedBy>k__BackingField
l8vXeUFqw8t
<BranchID>k__BackingField
MeIXeEGMrqs
<CreatedTime>k__BackingField
WKgXeSGYRNK
<Status>k__BackingField
GoMXeplI8ST
<Branch>k__BackingField
WF0XeLIW0OM
<Client>k__BackingField
ULtXeaiKd4m
<Currency>k__BackingField
OfYXe0l8d2r
<ServiceInfo>k__BackingField
VJpXeQtINI7
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Dashboard
AppTech.MSMS.Domain.Models.Dashboard
Dashboard
Dashboard
bgDXe3b7gcH
Init
UP0XevxSPWM
_accountId
pUbXeZOKL3w
<PieChartData>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PieChart
AppTech.MSMS.Domain.Models.PieChart
PieChart
PieChart
tZCXemNqF5b
<Vouchers>k__BackingField
QXoXeCpeWDU
<Values>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.IMsAuditable
AppTech.MSMS.Domain.Models.IMsAuditable
IMsAuditable
IMsAuditable
<<type>>
AppTech.MSMS.Domain.Models.SpecialSimMetadata
AppTech.MSMS.Domain.Models.SpecialSimMetadata
SpecialSimMetadata
SpecialSimMetadata
RpEXezSdYWg
<Number>k__BackingField
e3VXn2w47tX
<Type>k__BackingField
zQUXnXuCBNF
<Price>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ItemMetadata
AppTech.MSMS.Domain.Models.ItemMetadata
ItemMetadata
ItemMetadata
vnnXnM81QMe
<ServiceID>k__BackingField
BYwXnj08nTB
<CostPrice>k__BackingField
vSbXnu9yjWV
<SalePrice>k__BackingField
pshXnq0TW58
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ExchangerTargetMetadata
AppTech.MSMS.Domain.Models.ExchangerTargetMetadata
ExchangerTargetMetadata
ExchangerTargetMetadata
Ww0XnWCbxlc
<ExchangerID>k__BackingField
qyaXn4FKjVe
<RemittancePointID>k__BackingField
HA6XnDkJXXm
<ProviderID>k__BackingField
ekWXnsd6gYD
<SyncID>k__BackingField
vPqXn19dHaQ
<Active>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountApiMetadata
AppTech.MSMS.Domain.Models.AccountApiMetadata
AccountApiMetadata
AccountApiMetadata
m0QXn6Z8aQK
<Number>k__BackingField
tnDXnfbvjrk
<AccountId>k__BackingField
hfjXnH2LPV4
<IsAllowed>k__BackingField
qpvXnB2wJTl
<IpAddress>k__BackingField
H2SXntpaNet
<Binded>k__BackingField
KuOXnxhDgic
<Binding>k__BackingField
sJYXn5j6nGO
<PublicKey>k__BackingField
gHYXnrrFYuU
<Permitted>k__BackingField
PaSXnGdSeK1
<Primary>k__BackingField
X4EXngYiZkN
<Synced>k__BackingField
D5CXnVR4A0o
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BrochureMetadata
AppTech.MSMS.Domain.Models.BrochureMetadata
BrochureMetadata
BrochureMetadata
d6pXnesH1qU
<ImageUrl>k__BackingField
dqVXnngxVNg
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PersonalInfoMetadata
AppTech.MSMS.Domain.Models.PersonalInfoMetadata
PersonalInfoMetadata
PersonalInfoMetadata
<<type>>
AppTech.MSMS.Domain.Models.RemittanceRegionMetadata
AppTech.MSMS.Domain.Models.RemittanceRegionMetadata
RemittanceRegionMetadata
RemittanceRegionMetadata
s0PXn8933bC
<Name>k__BackingField
nMPXnol2eN0
<ProvinceID>k__BackingField
WiAXnTe7nF7
<CreatedBy>k__BackingField
ExSXnPiD0Oc
<BranchID>k__BackingField
MYnXnRcxpl5
<CreatedTime>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittancePointMetadata
AppTech.MSMS.Domain.Models.RemittancePointMetadata
RemittancePointMetadata
RemittancePointMetadata
gQhXnAaDbFP
<Number>k__BackingField
xHlXnbdJ128
<Name>k__BackingField
ePRXnIQkVJT
<RegionID>k__BackingField
hJxXnNQQhA9
<Description>k__BackingField
F4iXnki3JDn
<Type>k__BackingField
lMaXn7TXFZ7
<Phone>k__BackingField
FlcXncTkq7c
<Active>k__BackingField
ur8Xniqf8hq
<Address>k__BackingField
lg0XndIJ2Lw
<Fax>k__BackingField
bOSXnwgQDkA
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceOutMetadata
AppTech.MSMS.Domain.Models.RemittanceOutMetadata
RemittanceOutMetadata
RemittanceOutMetadata
O1xXnyd3TXC
<Number>k__BackingField
X00XnhPCGCY
<RemittanceNumber>k__BackingField
oJEXnFLGKUU
<RemittanceID>k__BackingField
pNDXnl8Ovdh
<Amount>k__BackingField
j58XnYeIS7s
<CurrencyID>k__BackingField
yV3XnJA1Nma
<CreditorAccountID>k__BackingField
FKTXnKqtS50
<RemittancePointID>k__BackingField
KAEXn9A0uK2
<AgentID>k__BackingField
qI8XnO1785x
<RegionID>k__BackingField
upGXnUnb90f
<Date>k__BackingField
ArdXnEbaPVw
<Note>k__BackingField
wLbXnSks3Bd
<Prints>k__BackingField
fm7Xnp3HYXi
<CommissionAmount>k__BackingField
LwYXnLqVLn8
<CommissionCurrencyID>k__BackingField
inoXnaTs3kN
<BeneficiaryCardID>k__BackingField
D3eXn0ZvXZf
<RefNumber>k__BackingField
TflXnQ4DEBC
<CenterCommission>k__BackingField
DkqXn3cTxsW
<PointCommission>k__BackingField
OZKXnv0JFHw
<CommissionType>k__BackingField
wyEXnZEbAIu
<BenficiaryID>k__BackingField
CFVXnmAoSSP
<BenficiaryCard>k__BackingField
aiJXnCPLew4
<Channel>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceInMetadata
AppTech.MSMS.Domain.Models.RemittanceInMetadata
RemittanceInMetadata
RemittanceInMetadata
RP6XnzyHWUW
<Number>k__BackingField
ySsX82FBsQ6
<SyncNumber>k__BackingField
BOeX8X4MZwl
<RemittanceNumber>k__BackingField
YTjX8MZtJWc
<Amount>k__BackingField
SkBX8j9cKx3
<CurrencyID>k__BackingField
bVqX8uYGwO6
<DebitorAccountID>k__BackingField
mcmX8qumRNG
<BeneficiaryName>k__BackingField
nhvX8WNgjW9
<BeneficiaryPhone>k__BackingField
R0pX84F7iCL
<SenderName>k__BackingField
bpAX8Dm0ZYM
<SenderPhone>k__BackingField
XWRX8stsoqR
<TargetPointID>k__BackingField
CkJX81WSthY
<AgentID>k__BackingField
I00X86i07fS
<SourceRegionID>k__BackingField
zFdX8fiapex
<TargetRegionID>k__BackingField
G9EX8H7jZxs
<Date>k__BackingField
PryX8BWvpEF
<Note>k__BackingField
DVpX8tUxb2x
<Purpose>k__BackingField
WMEX8xWjcov
<Prints>k__BackingField
EBwX8524FVP
<Status>k__BackingField
t4wX8rtx4Q8
<CommissionAmount>k__BackingField
tKeX8GFQR4l
<CommissionCurrencyID>k__BackingField
BdSX8goM8KJ
<RefNumber>k__BackingField
gg6X8V9m5M5
<CenterCommission>k__BackingField
Sl4X8eRNwfb
<PointCommission>k__BackingField
BE1X8nBsomi
<CommissionType>k__BackingField
zg1X88alcyx
<QueriedBy>k__BackingField
cPcX8oLeFfO
<IsSync>k__BackingField
NSqX8Tb7Vse
<SyncRemittanceID>k__BackingField
yxwX8PhGyrF
<Imported>k__BackingField
gD0X8RKElbe
<NetworkTarget>k__BackingField
TOnX8A33bNY
<ExchangeAmount>k__BackingField
nUIX8b8dOS5
<ExchangeCurrencyID>k__BackingField
By3X8IB6oWn
<Channel>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransferInMetadata
AppTech.MSMS.Domain.Models.TransferInMetadata
TransferInMetadata
TransferInMetadata
rhcX8N2gTWV
<Number>k__BackingField
cV2X8kOQ8gN
<TransferNumber>k__BackingField
xqxX87IlWgq
<Amount>k__BackingField
MSEX8cCKWQ4
<CurrencyID>k__BackingField
Wg3X8ikjHTN
<AccountID>k__BackingField
BHFX8dSmxF0
<BeneficiaryName>k__BackingField
R4nX8wFRaRy
<BeneficiaryPhone>k__BackingField
BobX8y2TWbK
<SenderName>k__BackingField
rOfX8h1NZq4
<SenderPhone>k__BackingField
y1KX8Fggcn4
<ExchangerID>k__BackingField
hoQX8l9IXgj
<Date>k__BackingField
IYlX8YBjc9U
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransferOutMetadata
AppTech.MSMS.Domain.Models.TransferOutMetadata
TransferOutMetadata
TransferOutMetadata
zWHX8JHxy9K
<Number>k__BackingField
suqX8KBX2dS
<TransferNumber>k__BackingField
W1cX89eDses
<Amount>k__BackingField
BwnX8OadMRn
<CurrencyID>k__BackingField
TLUX8UV0W0y
<AccountID>k__BackingField
JxGX8EY8uVI
<BeneficiaryName>k__BackingField
THkX8S7Kcty
<BeneficiaryPhone>k__BackingField
I9jX8pVPELH
<SenderName>k__BackingField
tFMX8LpGCHv
<SenderPhone>k__BackingField
qkyX8aDCDFZ
<ExchangerID>k__BackingField
JXeX80vT67o
<Date>k__BackingField
GjfX8QWGDuM
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AgentPointMetadata
AppTech.MSMS.Domain.Models.AgentPointMetadata
AgentPointMetadata
AgentPointMetadata
RJ8X83Lvvea
<AgentID>k__BackingField
ESVX8vaR7no
<RemittancePointID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BranchTargetMetadata
AppTech.MSMS.Domain.Models.BranchTargetMetadata
BranchTargetMetadata
BranchTargetMetadata
XXLX8ZEt3KJ
<BranchPintID>k__BackingField
OqJX8mHk8yO
<RemittancePointID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.InvoiceMetadata
AppTech.MSMS.Domain.Models.InvoiceMetadata
InvoiceMetadata
InvoiceMetadata
G5GX8CEbdIG
<Date>k__BackingField
bSlX8zEf0qE
<Amount>k__BackingField
G2EXo2AruAU
<CurrencyID>k__BackingField
CyVXoXRCnb8
<DebitorAccountID>k__BackingField
M6NXoMYBGQ8
<CreditorAccountID>k__BackingField
X2hXojvtuLk
<Note>k__BackingField
UL9Xoumrmpn
<RefNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ConsumeInvoiceMetadata
AppTech.MSMS.Domain.Models.ConsumeInvoiceMetadata
ConsumeInvoiceMetadata
ConsumeInvoiceMetadata
m28XoqJ3yiG
<StartDate>k__BackingField
i4bXoWhPPpf
<EndDate>k__BackingField
EjlXo4iEFBc
<UnitPrice>k__BackingField
LLNXoDYVkdS
<Quantity>k__BackingField
NsuXosrsjCi
<Amount>k__BackingField
vXNXo1oQvSL
<TotalAmount>k__BackingField
tKUXo6A4vAM
<FineAmount>k__BackingField
rvoXofrXmd4
<LateAmount>k__BackingField
Q4lXoH3a3E3
<ExtraAmount>k__BackingField
bJFXoBUbP1u
<SubscriptionFee>k__BackingField
HcjXotlLSX5
<FineNote>k__BackingField
MMTXoxYtg8k
<CurrencyID>k__BackingField
Af8Xo5fs1C8
<DebitorAccountID>k__BackingField
vhtXorTcuRy
<Note>k__BackingField
KMkXoGyo953
<RefNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SubscriberMetadata
AppTech.MSMS.Domain.Models.SubscriberMetadata
SubscriberMetadata
SubscriberMetadata
bZmXogVWty6
<Name>k__BackingField
SUIXoVHjqBI
<SubscriberNo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PartyGroupMetadata
AppTech.MSMS.Domain.Models.PartyGroupMetadata
PartyGroupMetadata
PartyGroupMetadata
jZGXoefkstM
<Name>k__BackingField
ou8XonHP7aB
<Description>k__BackingField
xqLXo8FN4fr
<SelectedItems>k__BackingField
TenXooUU5eN
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ClaimGroupMetadata
AppTech.MSMS.Domain.Models.ClaimGroupMetadata
ClaimGroupMetadata
ClaimGroupMetadata
rd0XoTLmykW
<Name>k__BackingField
RWvXoPdWAkR
<Note>k__BackingField
GUoXoRbgKpc
<SelectedServices>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.GroupItemrMetadata
AppTech.MSMS.Domain.Models.GroupItemrMetadata
GroupItemrMetadata
GroupItemrMetadata
ufDXoACJ2WL
<Name>k__BackingField
tPlXobLhZNB
<SubscriberNo>k__BackingField
iodXoI9TV6I
<PhoneNumber>k__BackingField
IHMXoNiMCLH
<OpeningBalance>k__BackingField
cXGXokHIPdp
<ProviderID>k__BackingField
rmIXo7CUjN9
<ServiceID>k__BackingField
dRAXocRVrCd
<Address>k__BackingField
UFcXoiCSF4j
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiCardMetadata
AppTech.MSMS.Domain.Models.WifiCardMetadata
WifiCardMetadata
WifiCardMetadata
W5hXodQdUPS
<Number>k__BackingField
kSdXowoZMO0
<FactionID>k__BackingField
AxUXoyrGxM9
<ProviderID>k__BackingField
UPTXohKIFeC
<SerialNo>k__BackingField
I8tXoFTKpZQ
<Username>k__BackingField
xRPXolIsFOL
<Password>k__BackingField
jcUXoYByCLn
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BuyCurrencyMetadata
AppTech.MSMS.Domain.Models.BuyCurrencyMetadata
BuyCurrencyMetadata
BuyCurrencyMetadata
udAXoJcgLO9
<Number>k__BackingField
G7LXoKPQZn1
<Date>k__BackingField
ufNXo9K2sn1
<Amount>k__BackingField
cn4XoORe1lw
<ExchangeAmount>k__BackingField
WLoXoUOVlyq
<ExchangePrice>k__BackingField
oQsXoESBsNN
<CurrencyID>k__BackingField
n5AXoSJ2AuV
<DebitorAccountID>k__BackingField
kVtXopqd0IS
<CreditorAccountID>k__BackingField
TNkXoLJYxiv
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ClientSMSMetadata
AppTech.MSMS.Domain.Models.ClientSMSMetadata
ClientSMSMetadata
ClientSMSMetadata
ywRXoawHqb9
<ServiceID>k__BackingField
G8vXo008VQL
<ClientID>k__BackingField
K4cXoQsC1hN
<Active>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountMetadata
AppTech.MSMS.Domain.Models.AccountMetadata
AccountMetadata
AccountMetadata
adAXo3jI5f0
<Name>k__BackingField
BwHXovlys5u
<ParentNumber>k__BackingField
U5vXoZlVPhF
<Number>k__BackingField
FO8XomIlKSj
<Type>k__BackingField
vCKXoCp8mtF
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DistributorMetadata
AppTech.MSMS.Domain.Models.DistributorMetadata
DistributorMetadata
DistributorMetadata
ik0XozrXTI7
<Name>k__BackingField
JtVXT2ngLhx
<PhoneNumber>k__BackingField
B1nXTX499qB
<ContactNumber>k__BackingField
oeuXTMSCmsP
<Address>k__BackingField
TWjXTjc4P9r
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AgentMetadata
AppTech.MSMS.Domain.Models.AgentMetadata
AgentMetadata
AgentMetadata
E0xXTunUXal
<Name>k__BackingField
HTNXTqGPmUA
<PhoneNumber>k__BackingField
OipXTWJ43cx
<ContactNumber>k__BackingField
cgOXT4nEYci
<Address>k__BackingField
YmhXTDv3Xrp
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ExternalBranchMetadata
AppTech.MSMS.Domain.Models.ExternalBranchMetadata
ExternalBranchMetadata
ExternalBranchMetadata
JZvXTsQrHHY
<Name>k__BackingField
XdmXT1tXuTr
<PhoneNumber>k__BackingField
P1lXT6kdu70
<Fax>k__BackingField
zVGXTfeGM5P
<Address>k__BackingField
OLhXTHdBPaY
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AgentUsersMetadata
AppTech.MSMS.Domain.Models.AgentUsersMetadata
AgentUsersMetadata
AgentUsersMetadata
<<type>>
AppTech.MSMS.Domain.Models.BranchMetadata
AppTech.MSMS.Domain.Models.BranchMetadata
BranchMetadata
BranchMetadata
<<type>>
AppTech.MSMS.Domain.Models.CashDepositMetadata
AppTech.MSMS.Domain.Models.CashDepositMetadata
CashDepositMetadata
CashDepositMetadata
l6DXTBBUdsh
<Number>k__BackingField
Hc6XTtXouQQ
<Date>k__BackingField
zNUXTx6gmUk
<Amount>k__BackingField
r8rXT5heagI
<CurrencyID>k__BackingField
owdXTrIR0OY
<Depositor>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashWithdrawMetadata
AppTech.MSMS.Domain.Models.CashWithdrawMetadata
CashWithdrawMetadata
CashWithdrawMetadata
G7FXTGp2jRG
<Date>k__BackingField
cjqXTgYxAdS
<Amount>k__BackingField
SPnXTVHDTlZ
<CurrencyID>k__BackingField
xGOXTeGuUn4
<Delivery>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ChequeMetadata
AppTech.MSMS.Domain.Models.ChequeMetadata
ChequeMetadata
ChequeMetadata
<<type>>
AppTech.MSMS.Domain.Models.ClientMetadata
AppTech.MSMS.Domain.Models.ClientMetadata
ClientMetadata
ClientMetadata
sJmXTn0ljZt
<Name>k__BackingField
dQeXT8c3aQD
<Username>k__BackingField
UFEXToGk117
<Password>k__BackingField
k2jXTTb0xrU
<ShopName>k__BackingField
yPBXTPxO1jY
<PhoneNumber>k__BackingField
PTyXTR1pZPU
<Address>k__BackingField
L0YXTAtgiWZ
<BirthDate>k__BackingField
jEGXTbfG6Cs
<ContactNumber>k__BackingField
P4dXTITNSB0
<Email>k__BackingField
iI1XTNBvBi7
<CardType>k__BackingField
EMTXTk0sXdC
<CardNumber>k__BackingField
Qa3XT7qlEqx
<CardIssuePlace>k__BackingField
oKQXTceUWKP
<CardIssueDate>k__BackingField
WPfXTi1ciE1
<ImageName>k__BackingField
dcFXTdTvO1Y
<Note>k__BackingField
pdfXTwsl71F
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountNotificationMetadata
AppTech.MSMS.Domain.Models.AccountNotificationMetadata
AccountNotificationMetadata
AccountNotificationMetadata
y3UXTy5D43b
<AccountID>k__BackingField
C0sXThwSfoH
<Message>k__BackingField
MPAXTFDUmih
<Title>k__BackingField
QNlXTlHI8De
<RealTime>k__BackingField
r3bXTY1j2UH
<AccountState>k__BackingField
LMDXTJE3c1t
<AccountGroupID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ClientPermissionMetadata
AppTech.MSMS.Domain.Models.ClientPermissionMetadata
ClientPermissionMetadata
ClientPermissionMetadata
<<type>>
AppTech.MSMS.Domain.Models.AccountSlatingMetadata
AppTech.MSMS.Domain.Models.AccountSlatingMetadata
AccountSlatingMetadata
AccountSlatingMetadata
nLMXTKmOvuB
<AccountID>k__BackingField
KAJXT9pqTx0
<Amount>k__BackingField
a9WXTOqNlFN
<CurrencyID>k__BackingField
FgXXTUdj6Ej
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ClientUsersMetadata
AppTech.MSMS.Domain.Models.ClientUsersMetadata
ClientUsersMetadata
ClientUsersMetadata
<<type>>
AppTech.MSMS.Domain.Models.CurrencyMetadata
AppTech.MSMS.Domain.Models.CurrencyMetadata
CurrencyMetadata
CurrencyMetadata
gjGXTEppiVG
<Name>k__BackingField
Tv3XTS2HxM4
<Symbol>k__BackingField
BddXTpEqkat
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CurrencyExchangeMetadata
AppTech.MSMS.Domain.Models.CurrencyExchangeMetadata
CurrencyExchangeMetadata
CurrencyExchangeMetadata
<<type>>
AppTech.MSMS.Domain.Models.CurrencyRateMetadata
AppTech.MSMS.Domain.Models.CurrencyRateMetadata
CurrencyRateMetadata
CurrencyRateMetadata
BjJXTLy3E71
<SourceCurrencyID>k__BackingField
iK9XTa9Ut1u
<ExchangeRate>k__BackingField
hFdXT0ojC8E
<BuyPrice>k__BackingField
bu8XTQKLn1T
<SellPrice>k__BackingField
ItcXT3stj5v
<ExBuyPrice>k__BackingField
RxAXTv0v1fo
<ExSalePrice>k__BackingField
sxnXTZZ7AVU
<UpDown>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CurrencyRateAccountMetadata
AppTech.MSMS.Domain.Models.CurrencyRateAccountMetadata
CurrencyRateAccountMetadata
CurrencyRateAccountMetadata
x2ZXTmgQMLk
<SourceCurrencyID>k__BackingField
Ft6XTCRUilL
<TargetCurrencyID>k__BackingField
Ve4XTzqKt0m
<AccountState>k__BackingField
hQtXP2LGvKq
<AccountID>k__BackingField
qeaXPXbFJhI
<AccountGroupID>k__BackingField
ajmXPMETZei
<ExchangeRate>k__BackingField
xSGXPjBpFIn
<BuyPrice>k__BackingField
Pn7XPunTKPV
<SellPrice>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DepositOrderMetadata
AppTech.MSMS.Domain.Models.DepositOrderMetadata
DepositOrderMetadata
DepositOrderMetadata
JHAXPqNWpPZ
<Amount>k__BackingField
ODKXPWV6iOX
<CurrencyID>k__BackingField
TmuXP4uMbKy
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DocumentMetadata
AppTech.MSMS.Domain.Models.DocumentMetadata
DocumentMetadata
DocumentMetadata
<<type>>
AppTech.MSMS.Domain.Models.EntryMetadata
AppTech.MSMS.Domain.Models.EntryMetadata
EntryMetadata
EntryMetadata
<<type>>
AppTech.MSMS.Domain.Models.EntryDetailMetadata
AppTech.MSMS.Domain.Models.EntryDetailMetadata
EntryDetailMetadata
EntryDetailMetadata
<<type>>
AppTech.MSMS.Domain.Models.ExchangerMetadata
AppTech.MSMS.Domain.Models.ExchangerMetadata
ExchangerMetadata
ExchangerMetadata
qJrXPDuvK9e
<Number>k__BackingField
VbTXPs8rqgt
<Name>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.FactionMetadata
AppTech.MSMS.Domain.Models.FactionMetadata
FactionMetadata
FactionMetadata
cjeXP1mfIIK
<Name>k__BackingField
LUwXP6UKs1I
<Number>k__BackingField
ToQXPfwjxeD
<Quantity>k__BackingField
piSXPHiB2oY
<Price>k__BackingField
Ss2XPBfgC7P
<PersonnalPrice>k__BackingField
C9UXPtZRt81
<ServiceID>k__BackingField
IENXPxy4Vnn
<ProviderPrice>k__BackingField
RPBXP52CEsj
<OrderNo>k__BackingField
qgxXPrIZY51
<ProviderCode>k__BackingField
MvxXPGYgeqe
<Note>k__BackingField
RjLXPgMQM8d
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DeviceMetadata
AppTech.MSMS.Domain.Models.DeviceMetadata
DeviceMetadata
DeviceMetadata
VbwXPVQtga0
<Number>k__BackingField
sAZXPeSsaVi
<AccountID>k__BackingField
aFaXPndd6pL
<Active>k__BackingField
ffeXP8jIla4
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserDeviceMetadata
AppTech.MSMS.Domain.Models.UserDeviceMetadata
UserDeviceMetadata
UserDeviceMetadata
FfaXPo4FCcb
<Identifier>k__BackingField
MXOXPTLEmfx
<UserID>k__BackingField
aQTXPPhbnQP
<Permitted>k__BackingField
en7XPRkpQHx
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DbBackupMetadata
AppTech.MSMS.Domain.Models.DbBackupMetadata
DbBackupMetadata
DbBackupMetadata
bT8XPAnxUYB
<Name>k__BackingField
iuGXPbZrAGf
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BankMetadata
AppTech.MSMS.Domain.Models.BankMetadata
BankMetadata
BankMetadata
YQdXPIrdKZZ
<Name>k__BackingField
f9vXPNqsLDD
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.FundMetadata
AppTech.MSMS.Domain.Models.FundMetadata
FundMetadata
FundMetadata
M9hXPkPPicB
<Number>k__BackingField
KrpXP7v3PTu
<Name>k__BackingField
BroXPcUsDnH
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.FundUserMetadata
AppTech.MSMS.Domain.Models.FundUserMetadata
FundUserMetadata
FundUserMetadata
FLSXPiq7aMj
<FundID>k__BackingField
Me8XPdhINt3
<UserID>k__BackingField
qiIXPwZB3GE
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.GeneralInfoMetadata
AppTech.MSMS.Domain.Models.GeneralInfoMetadata
GeneralInfoMetadata
GeneralInfoMetadata
RbaXPyJNP7D
<Title>k__BackingField
MPaXPhB6p5x
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.InstructionMetadata
AppTech.MSMS.Domain.Models.InstructionMetadata
InstructionMetadata
InstructionMetadata
AukXPF5xEyi
<Number>k__BackingField
mVpXPll8V0O
<Text>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.MerchantMetadata
AppTech.MSMS.Domain.Models.MerchantMetadata
MerchantMetadata
MerchantMetadata
RsGXPYjnYHo
<Name>k__BackingField
gETXPJ3YUT1
<CategoryID>k__BackingField
zNGXPKD0Dar
<Description>k__BackingField
WrHXP9aikkT
<OwnerName>k__BackingField
TWwXPOWmqyG
<PhoneNumber>k__BackingField
BL3XPU1XWbQ
<ContactNumber>k__BackingField
Jp3XPEfqDFM
<Address>k__BackingField
BVeXPSnQX03
<Note>k__BackingField
mhDXPpnNgUD
<ImageName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.MerchantCategoryMetadata
AppTech.MSMS.Domain.Models.MerchantCategoryMetadata
MerchantCategoryMetadata
MerchantCategoryMetadata
dZiXPLrPB99
<Name>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.MerchantPaymentMetadata
AppTech.MSMS.Domain.Models.MerchantPaymentMetadata
MerchantPaymentMetadata
MerchantPaymentMetadata
KiQXPahdTST
<MerchantID>k__BackingField
eMNXP0KXgkn
<Amount>k__BackingField
TOoXPQhEDVT
<CurrencyID>k__BackingField
C6dXP32eL0g
<InvoiceNumber>k__BackingField
R83XPvSfm68
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OfferPaymentMetadata
AppTech.MSMS.Domain.Models.OfferPaymentMetadata
OfferPaymentMetadata
OfferPaymentMetadata
<<type>>
AppTech.MSMS.Domain.Models.TopupMetadata
AppTech.MSMS.Domain.Models.TopupMetadata
TopupMetadata
TopupMetadata
bl5XPZM6wCe
get_Date
DyyXPmfG0UM
set_Date
uXvXPCfbwML
<Amount>k__BackingField
HQGXPz3APhT
<SubscriberNumber>k__BackingField
ACIXR2g3iFd
<Note>k__BackingField
sY2XRXeTJTj
<LineType>k__BackingField
lXtXRMCEFwP
<ServiceID>k__BackingField
IHSXRjyOoA9
<FactionID>k__BackingField
b0LXRu11sxD
<Number>k__BackingField
QjJXRq8g2nq
<RefNumber>k__BackingField
FKlXRWbvMhg
<TransNumber>k__BackingField
CMnXR4cgR2L
<TransactionID>k__BackingField
pj6XRDm7rDm
<Status>k__BackingField
NbHXRsrmUBy
<StateClass>k__BackingField
H4lXR1DD4CA
<ProviderRM>k__BackingField
y7oXR6ZPR2F
<Date>k__BackingField
XsSXRfDgYNL
<Quantity>k__BackingField
nXtXRHINIWn
<UnitPrice>k__BackingField
kjqXRBCEeSF
<DifferentialAmount>k__BackingField
TNQXRtiY3hA
<TotalAmount>k__BackingField
aoFXRxIwcrB
<UnitCost>k__BackingField
hNPXR5DZbk7
<CostAmount>k__BackingField
JRQXRrjSKBe
<TotalCost>k__BackingField
ps3XRGuIcn3
<CommissionAmount>k__BackingField
HJnXRghTVxf
<Discount>k__BackingField
wuhXRVEB6Lg
<Profits>k__BackingField
a81XRekDk2r
<Description>k__BackingField
iGSXRnj5D6j
<QuotaionID>k__BackingField
F9RXR8iYOsY
<RequestInfo>k__BackingField
zlOXRoobchA
<ResponseInfo>k__BackingField
Ax9XRTCDB22
<ResponseTime>k__BackingField
vtSXRPBSk3d
<ExecutionPeroid>k__BackingField
bnvXRRNXeZO
<Responded>k__BackingField
MFqXRATBnsv
<FaildRequest>k__BackingField
tUMXRbUwZre
<FailedReason>k__BackingField
RDxXRIlFqf9
<FailedType>k__BackingField
XpyXRNagbyV
<AppTechApi>k__BackingField
uvAXRkpEg56
<ResponseStatus>k__BackingField
dVVXR7TngUK
<Cured>k__BackingField
nvRXRcSuyrT
<CuredBy>k__BackingField
w0eXRiiSKD8
<InspectInfo>k__BackingField
pRrXRdxhfsQ
<Debited>k__BackingField
aq0XRwYlGwv
<IsDirect>k__BackingField
tlMXRyCyAW3
<OperatorID>k__BackingField
nYRXRhfYL4x
<BillNumber>k__BackingField
jsfXRF9Gitu
<Channel>k__BackingField
t4SXRlc80Oo
<Method>k__BackingField
iQPXRYWk9G6
<Type>k__BackingField
QhJXRJjrhNZ
<Identifier>k__BackingField
m4RXRKvghNa
<BundleCode>k__BackingField
FgRXR9kOa0H
<BundleName>k__BackingField
pZlXROgHuHj
<AdminNote>k__BackingField
xrZXRU1esLu
<AccountNote>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PaymentOrderMetadata
AppTech.MSMS.Domain.Models.PaymentOrderMetadata
PaymentOrderMetadata
PaymentOrderMetadata
v95XREVfYsH
<ServiceID>k__BackingField
pcyXRSUIsiK
<SubscriberNumber>k__BackingField
lG4XRprX19G
<Amount>k__BackingField
heLXRLfMAas
<Description>k__BackingField
D0oXRaDVDyn
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupProviderMetadata
AppTech.MSMS.Domain.Models.TopupProviderMetadata
TopupProviderMetadata
TopupProviderMetadata
wgcXR0WaNXo
<AutoBalance>k__BackingField
F90XRQDuCmQ
<Number>k__BackingField
YbJXR3Ybhmx
<Name>k__BackingField
VnbXRvelEfi
<Type>k__BackingField
cHrXRZMaB4N
<Note>k__BackingField
tlkXRmgyA52
<Username>k__BackingField
RRuXRCWe6ot
<Password>k__BackingField
Kh1XRzxsCb0
<BaseUrl>k__BackingField
p95XA2DKuKF
<Token>k__BackingField
NKlXAXQWJhk
<UserId>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.LoanOrderMetadata
AppTech.MSMS.Domain.Models.LoanOrderMetadata
LoanOrderMetadata
LoanOrderMetadata
<<type>>
AppTech.MSMS.Domain.Models.OfferOrderMetadata
AppTech.MSMS.Domain.Models.OfferOrderMetadata
OfferOrderMetadata
OfferOrderMetadata
<<type>>
AppTech.MSMS.Domain.Models.SimInvoiceMetadata
AppTech.MSMS.Domain.Models.SimInvoiceMetadata
SimInvoiceMetadata
SimInvoiceMetadata
imZXAMuXdY4
<NetworkID>k__BackingField
rELXAjJYE4x
<Date>k__BackingField
spdXAuXr1t1
<StartNumber>k__BackingField
zbuXAqr98WM
<EndNumber>k__BackingField
NF4XAWJPMe3
<UnitPrice>k__BackingField
HU2XA4KpSI2
<TotalUnits>k__BackingField
uqvXAD8LIgH
<Amount>k__BackingField
FuJXAs3aaYs
<CreditorAccountID>k__BackingField
XgBXA1fB7s5
<DebitorAccountID>k__BackingField
PAAXA6egVHy
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupCommissionMetadata
AppTech.MSMS.Domain.Models.TopupCommissionMetadata
TopupCommissionMetadata
TopupCommissionMetadata
SEUXAfYGytt
<ServiceID>k__BackingField
hJVXAHV3wCc
<CreditorAccountID>k__BackingField
QAXXABZLI0h
<Percentage>k__BackingField
Uw7XAtID0G8
<StartDate>k__BackingField
uGjXAx2CjQM
<EndDate>k__BackingField
eiOXA5CdRdp
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.LiveTopupMetadata
AppTech.MSMS.Domain.Models.LiveTopupMetadata
LiveTopupMetadata
LiveTopupMetadata
n67XArN4DkZ
<ProviderID>k__BackingField
J9lXAGwpfB0
<ServiceID>k__BackingField
UYHXAganfcA
<Active>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.QuotationMetadata
AppTech.MSMS.Domain.Models.QuotationMetadata
QuotationMetadata
QuotationMetadata
TyKXAVrYnMN
<ServiceID>k__BackingField
fIqXAewocQh
<Note>k__BackingField
aF3XAng1Zte
<Price>k__BackingField
rjgXA8OLbOY
<AccountState>k__BackingField
zJDXAoBn9jG
<AccountID>k__BackingField
C8mXATDmXpw
<AccountGroupID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ItemCostMetadata
AppTech.MSMS.Domain.Models.ItemCostMetadata
ItemCostMetadata
ItemCostMetadata
biIXAPZsF4Z
<ProviderID>k__BackingField
xXKXARQ56rX
<ServiceID>k__BackingField
EZiXAAUsx46
<Price>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashInMetadata
AppTech.MSMS.Domain.Models.CashInMetadata
CashInMetadata
CashInMetadata
VRuXAb3OeWm
<Date>k__BackingField
CiiXAIgOpbq
<Method>k__BackingField
CUlXAN4sU59
<Amount>k__BackingField
j3wXAkLYQ0m
<CurrencyID>k__BackingField
g6aXA7r6dTO
<CreditorAccountID>k__BackingField
NPVXAcCc1Lv
<DebitorAccountID>k__BackingField
ap7XAi1MDUn
<Note>k__BackingField
TjHXAd509wG
<RefNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RegionMetadata
AppTech.MSMS.Domain.Models.RegionMetadata
RegionMetadata
RegionMetadata
IiHXAwqi3UP
<Name>k__BackingField
v0OXAyA6H4I
<ServiceID>k__BackingField
VjpXAhM81h2
<Code>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RoleMetadata
AppTech.MSMS.Domain.Models.RoleMetadata
RoleMetadata
RoleMetadata
<<type>>
AppTech.MSMS.Domain.Models.RSSMetadata
AppTech.MSMS.Domain.Models.RSSMetadata
RSSMetadata
RSSMetadata
pSPXAF5idmP
<Feed>k__BackingField
ac7XAlgUyOF
<Active>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ServiceMetadata
AppTech.MSMS.Domain.Models.ServiceMetadata
ServiceMetadata
ServiceMetadata
<<type>>
AppTech.MSMS.Domain.Models.ServiceCategoryMetadata
AppTech.MSMS.Domain.Models.ServiceCategoryMetadata
ServiceCategoryMetadata
ServiceCategoryMetadata
<<type>>
AppTech.MSMS.Domain.Models.PaymentCommissionMetadata
AppTech.MSMS.Domain.Models.PaymentCommissionMetadata
PaymentCommissionMetadata
PaymentCommissionMetadata
AepXAYQQiWG
<ServiceID>k__BackingField
speXAJYKTZ7
<CurrencyID>k__BackingField
wexXAK0So2y
<CommissionCurrencyID>k__BackingField
VMXXA9lxp25
<FromAmount>k__BackingField
EK2XAOy3uU1
<ToAmount>k__BackingField
jKUXAUrvt4c
<TraderAmount>k__BackingField
nikXAEkups7
<Note>k__BackingField
UnbXASLBPtY
<PersonalAmount>k__BackingField
MtEXApj8hBC
<IsAgainst>k__BackingField
xUpXALKrvVI
<AccountState>k__BackingField
usVXAa2rpOS
<AccountID>k__BackingField
cLIXA0vsO0T
<AccountGroupID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ServiceEntryMetadata
AppTech.MSMS.Domain.Models.ServiceEntryMetadata
ServiceEntryMetadata
ServiceEntryMetadata
<<type>>
AppTech.MSMS.Domain.Models.SIMCardOrderMetadata
AppTech.MSMS.Domain.Models.SIMCardOrderMetadata
SIMCardOrderMetadata
SIMCardOrderMetadata
MIyXAQ4UFdJ
<SimNumber>k__BackingField
QDvXA3k5eaE
<CardIssuePlace>k__BackingField
vY2XAvjAJRc
<SimType>k__BackingField
qdPXAZAfB8d
<ContractNumber>k__BackingField
odlXAm1XlIZ
<ESDN>k__BackingField
QAKXACoEa4l
<MSISDN>k__BackingField
rd0XAzJTskt
<PersonalCardType>k__BackingField
R0KXb2WEUug
<IssueDate>k__BackingField
iUMXbXVBhwv
<BirthDate>k__BackingField
hbnXbMYWKcK
<CustomerName>k__BackingField
AEJXbjTC5Hf
<CustomerAddress>k__BackingField
yt3XbuxjQe8
<SubscriberNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SimpleEntryMetadata
AppTech.MSMS.Domain.Models.SimpleEntryMetadata
SimpleEntryMetadata
SimpleEntryMetadata
XOhXbqn4lMG
<Amount>k__BackingField
c7KXbWjpsK5
<Date>k__BackingField
ojbXb4cxYp4
<CurrencyID>k__BackingField
adPXbDxyLZ8
<CreditorAccountID>k__BackingField
HhlXbsqa8Ng
<DebitorAccountID>k__BackingField
RICXb17KS6r
<Note>k__BackingField
FmUXb6yl5w5
<RefNumber>k__BackingField
PuxXbfqNd09
<AttachmentNumbers>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OpeningBalanceMetadata
AppTech.MSMS.Domain.Models.OpeningBalanceMetadata
OpeningBalanceMetadata
OpeningBalanceMetadata
kFRXbHO6edC
<Amount>k__BackingField
fKYXbBBZVJZ
<CurrencyID>k__BackingField
I59XbtfX0tq
<AccountID>k__BackingField
sLMXbxwpITk
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SMSMetadata
AppTech.MSMS.Domain.Models.SMSMetadata
SMSMetadata
SMSMetadata
w7JXb52EpCH
<PhoneNumber>k__BackingField
khpXbrd9SnZ
<Message>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashOutMetadata
AppTech.MSMS.Domain.Models.CashOutMetadata
CashOutMetadata
CashOutMetadata
zVLXbG2GMFK
<Date>k__BackingField
VpwXbgIwrYF
<Method>k__BackingField
n2YXbVp1c1O
<Amount>k__BackingField
dEMXbelVdlA
<CurrencyID>k__BackingField
RanXbnu3BMO
<CreditorAccountID>k__BackingField
t5XXb8xc0wD
<DebitorAccountID>k__BackingField
FFgXbo0Udi4
<Note>k__BackingField
zOwXbTiJtNY
<Delivery>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TrailToupCommissionMetadata
AppTech.MSMS.Domain.Models.TrailToupCommissionMetadata
TrailToupCommissionMetadata
TrailToupCommissionMetadata
QfFXbPHXVcX
<MobileNetworkID>k__BackingField
QHrXbRZ37Dq
<FromAmount>k__BackingField
SBRXbApwfkY
<ToAmount>k__BackingField
YRuXbbl9o5J
<Percentage>k__BackingField
g2TXbIFtKgq
<PersonnalPrice>k__BackingField
uM7XbNQCboF
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TrailToupOrderMetadata
AppTech.MSMS.Domain.Models.TrailToupOrderMetadata
TrailToupOrderMetadata
TrailToupOrderMetadata
oYlXbk5agxl
<MobileNetworkID>k__BackingField
pI4Xb7MEOmt
<SubscriberNumber>k__BackingField
ciQXbcWHDQ3
<Amount>k__BackingField
SLEXbiBF5v0
<Percentage>k__BackingField
qulXbdf8v31
<ExchangeAmount>k__BackingField
elwXbwZX6mH
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashTransferMetadata
AppTech.MSMS.Domain.Models.CashTransferMetadata
CashTransferMetadata
CashTransferMetadata
F8hXbyPWxuL
<Amount>k__BackingField
GyBXbhvOelF
<CurrencyID>k__BackingField
pNbXbF2mNal
<CreditorAccountID>k__BackingField
BCOXblDSbYn
<DebitorAccountID>k__BackingField
oGFXbY89Yd0
<Note>k__BackingField
clYXbJ8BB5i
<RefNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransferOrderMetadata
AppTech.MSMS.Domain.Models.TransferOrderMetadata
TransferOrderMetadata
TransferOrderMetadata
v95XbK3iWBM
<TransferNumber>k__BackingField
myqXb9vAHw6
<ServiceID>k__BackingField
ymlXbOHvQDV
<IsIncoming>k__BackingField
veBXbUKcdM0
<Amount>k__BackingField
OG4XbEAm89d
<CurrencyID>k__BackingField
BjdXbSG0xK6
<ReceiverName>k__BackingField
IlVXbpXOUuC
<ReceiverMobile>k__BackingField
nxVXbLstZHh
<ReceiverCardNo>k__BackingField
sdgXbaUBxZJ
<ReceiverCardIssuerPlace>k__BackingField
vUtXb0CJROZ
<ReceiverCardIssuerDate>k__BackingField
l4bXbQ0hPJD
<SenderName>k__BackingField
nREXb3nFOLN
<SenderMobile>k__BackingField
J3VXbvT3xdM
<Note>k__BackingField
fHnXbZ79o8P
<ImageName>k__BackingField
DCfXbm9NwgY
<ExchangerID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserMetadata
AppTech.MSMS.Domain.Models.UserMetadata
UserMetadata
UserMetadata
oZyXbCiaeSc
<UserName>k__BackingField
VyZXbz6RGhM
<Password>k__BackingField
fGoXI24g0Rp
<Status>k__BackingField
oEHXIXuE40T
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WithdrawOrderMetadata
AppTech.MSMS.Domain.Models.WithdrawOrderMetadata
WithdrawOrderMetadata
WithdrawOrderMetadata
<<type>>
AppTech.MSMS.Domain.Models.BagatMetadata
AppTech.MSMS.Domain.Models.BagatMetadata
BagatMetadata
BagatMetadata
PeGXIMGFP5K
<Name>k__BackingField
AcyXIj8dEO0
<Number>k__BackingField
xwZXIufFuX3
<Code>k__BackingField
LTMXIqIIkBR
<LineType>k__BackingField
jUbXIWXkQLr
<OrderNo>k__BackingField
KAvXI4biYZG
<Quantity>k__BackingField
EeWXIDxH5MV
<ProviderPrice>k__BackingField
LjjXIsm97Zv
<TelPrice>k__BackingField
rtqXI1Ucq8M
<Price>k__BackingField
CM7XI6fURaG
<PersonnalPrice>k__BackingField
FcmXIfEByuY
<Description>k__BackingField
S4NXIHbXffO
<Mode>k__BackingField
ljXXIB1Y1LW
<ByProvider>k__BackingField
yh5XItdZcua
<ProviderID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BagatPaymentMetadata
AppTech.MSMS.Domain.Models.BagatPaymentMetadata
BagatPaymentMetadata
BagatPaymentMetadata
lEcXIxjrWKv
<SubscriberNumber>k__BackingField
ITaXI5pMTh7
<LineType>k__BackingField
vsIXIrXUjMd
<SimType>k__BackingField
rojXIGqKs5x
<Note>k__BackingField
y3AXIg9IpV8
<Channel>k__BackingField
SCAXIVjWnaB
<ServiceEntryID>k__BackingField
xOGXIeIbGFL
<IncludePay>k__BackingField
sdtXIn9AWbd
<ActionType>k__BackingField
zOpXI8UWWUB
<OfferID>k__BackingField
EbtXIoM9iYa
<ServiceID>k__BackingField
nmVXITZKN7r
<ProviderID>k__BackingField
WKYXIPxUKiS
<Amount>k__BackingField
jSPXIRWBiUu
<HasLoan>k__BackingField
fRNXIAvFvfx
<OfferCode>k__BackingField
HxnXIb1CHPl
<OfferName>k__BackingField
oPVXIIhmunE
<OfferAction>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserTargetMetadata
AppTech.MSMS.Domain.Models.UserTargetMetadata
UserTargetMetadata
UserTargetMetadata
GS7XINnTyYK
<CreatedTime>k__BackingField
ep5XIksCUDx
<UserID>k__BackingField
OSIXI7RU7nN
<PrimaryUser>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceCommissionMetadata
AppTech.MSMS.Domain.Models.RemittanceCommissionMetadata
RemittanceCommissionMetadata
RemittanceCommissionMetadata
XUnXIc5iNOE
<RemittanceType>k__BackingField
xm2XIiovrTD
<CurrencyID>k__BackingField
WLGXIdSyJbH
<CommissionCurrencyID>k__BackingField
rTGXIwgofM1
<StartAmount>k__BackingField
RhIXIyFaXXX
<EndAmount>k__BackingField
idFXIhLRgnM
<PointCommission>k__BackingField
hwkXIF2yNvW
<CenterCommission>k__BackingField
v8FXIlfXSRZ
<TargetState>k__BackingField
qoEXIYtTvsE
<TargetID>k__BackingField
dJdXIJRE4lY
<TargetGroupID>k__BackingField
EdeXIKNOV8T
<IsAgainst>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CountryMetadata
AppTech.MSMS.Domain.Models.CountryMetadata
CountryMetadata
CountryMetadata
<<type>>
AppTech.MSMS.Domain.Models.ProvinceMetadata
AppTech.MSMS.Domain.Models.ProvinceMetadata
ProvinceMetadata
ProvinceMetadata
<<type>>
AppTech.MSMS.Domain.Models.CommissionReceiptMetadata
AppTech.MSMS.Domain.Models.CommissionReceiptMetadata
CommissionReceiptMetadata
CommissionReceiptMetadata
bERXI9bSpe6
<ServiceID>k__BackingField
b2fXIOtwu9t
<AccountGroupID>k__BackingField
qLZXIUUouVS
<Percentage>k__BackingField
ILhXIErtahu
<StartDate>k__BackingField
z7MXISTmEh9
<EndDate>k__BackingField
gZdXIp2K5RB
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardMetadata
AppTech.MSMS.Domain.Models.CardMetadata
CardMetadata
CardMetadata
NUQXILkkNFx
<Number>k__BackingField
FT4XIabtaI5
<Name>k__BackingField
AuHXI0NkiTH
<Password>k__BackingField
nPnXIQv9PHs
<CardTypeID>k__BackingField
KoVXI3VlRph
<CardFactionID>k__BackingField
dsAXIvpLobY
<SerialNo>k__BackingField
pl4XIZIUFrA
<Description>k__BackingField
krvXImGjU0A
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardTypeMetadata
AppTech.MSMS.Domain.Models.CardTypeMetadata
CardTypeMetadata
CardTypeMetadata
FvUXICVrDyg
<Number>k__BackingField
P9kXIzmDIVh
<Name>k__BackingField
bnbXN2E16pp
<AccountID>k__BackingField
HoJXNX5JHMf
<Image>k__BackingField
c3iXNMP6cBw
<Type>k__BackingField
JCLXNjOXwvR
<Description>k__BackingField
k5tXNupZi9L
<Note>k__BackingField
w37XNqt6nDY
<Active>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardFactionMetadata
AppTech.MSMS.Domain.Models.CardFactionMetadata
CardFactionMetadata
CardFactionMetadata
a5JXNWWOw5R
<Number>k__BackingField
FENXN4NhroF
<Name>k__BackingField
gkDXNDP9cl6
<CostPrice>k__BackingField
Q8GXNsk5qGb
<SelePrice>k__BackingField
i64XN1cGg2J
<CardTypeID>k__BackingField
EKIXN6wBbU0
<Active>k__BackingField
KtmXNfem0Cn
<Status>k__BackingField
JaNXNH4yoDi
<Description>k__BackingField
hpfXNBT4Jsr
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardOrderMetadata
AppTech.MSMS.Domain.Models.CardOrderMetadata
CardOrderMetadata
CardOrderMetadata
jEkXNt8GxaE
<Number>k__BackingField
FOMXNx5E3Lm
<CardFactionID>k__BackingField
DndXN5eU6QT
<CardTypeID>k__BackingField
YpdXNrkF0Ka
<Amount>k__BackingField
Ki7XNGfjcb6
<Username>k__BackingField
TDNXNggiJxG
<Password>k__BackingField
qYKXNVUMnms
<Email>k__BackingField
gmiXNeQskmr
<Phone>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardPaymentMetadata
AppTech.MSMS.Domain.Models.CardPaymentMetadata
CardPaymentMetadata
CardPaymentMetadata
diMXNn0G250
<Number>k__BackingField
wSgXN8aNp51
<Name>k__BackingField
kfMXNooh0IH
<CardFactionID>k__BackingField
wnUXNT0w1tB
<CardTypeID>k__BackingField
dUcXNP4yqtB
<Amount>k__BackingField
inQXNR3EKOd
<Username>k__BackingField
SwsXNAAqpq2
<Password>k__BackingField
v0yXNb8toGV
<Email>k__BackingField
pCYXNIR03Jg
<Phone>k__BackingField
J65XNNlQ3dU
<Description>k__BackingField
pRoXNkWAHTm
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RegionsMetadata
AppTech.MSMS.Domain.Models.RegionsMetadata
RegionsMetadata
RegionsMetadata
vsXXN7bLuPG
<Number>k__BackingField
t2wXNcUNxlh
<Name>k__BackingField
AorXNiw4SkA
<ProvinceID>k__BackingField
kyvXNdlVGYD
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountRegionMetadata
AppTech.MSMS.Domain.Models.AccountRegionMetadata
AccountRegionMetadata
AccountRegionMetadata
EoKXNwfSB8N
<Number>k__BackingField
ppOXNyxQhK4
<RegionID>k__BackingField
pP6XNh08cNt
<AccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiProviderMetadata
AppTech.MSMS.Domain.Models.WifiProviderMetadata
WifiProviderMetadata
WifiProviderMetadata
GahXNF09u6b
<Number>k__BackingField
bwpXNl9C2gj
<AccountID>k__BackingField
BUvXNYZC0cd
<Name>k__BackingField
WmnXNJUqyKQ
<RegionID>k__BackingField
osXXNKEB1y6
<Image>k__BackingField
bF7XN9l4uhO
<Address>k__BackingField
QH4XNOiHo50
<URL>k__BackingField
yMCXNUX2WBO
<Phone>k__BackingField
fjVXNE437bI
<PrafitAmount>k__BackingField
mmZXNS4fxAY
<PrafitStatus>k__BackingField
CuJXNpSnvu3
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiFactionMetadata
AppTech.MSMS.Domain.Models.WifiFactionMetadata
WifiFactionMetadata
WifiFactionMetadata
hvMXNLPQkq5
<Number>k__BackingField
lvxXNaE0vp2
<Name>k__BackingField
AP6XN0MOLdL
<OrderNO>k__BackingField
v5XXNQb6o9Q
<WifiProviderID>k__BackingField
GYrXN3dyfBW
<Note>k__BackingField
k3nXNvdHtxC
<Price>k__BackingField
DOFXNZtpNKO
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransporterMetadata
AppTech.MSMS.Domain.Models.TransporterMetadata
TransporterMetadata
TransporterMetadata
UglXNmnMJwD
<Number>k__BackingField
WgcXNCOgTrk
<Name>k__BackingField
sd2XNzlOLIM
<ImageName>k__BackingField
nSlXk2GuVpy
<AccountID>k__BackingField
VBUXkXerwKW
<Note>k__BackingField
cEwXkM5cu99
<Description>k__BackingField
kgNXkjsnehC
<Status>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CoverageOrderMetadata
AppTech.MSMS.Domain.Models.CoverageOrderMetadata
CoverageOrderMetadata
CoverageOrderMetadata
b0HXku6dCDT
<AccountID>k__BackingField
msyXkqnYtFO
<ExchangeAccountID>k__BackingField
S4PXkWGHHdk
<Amount>k__BackingField
i3KXk4MWJkK
<Purpose>k__BackingField
zXbXkDOUjAY
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ReceiptCreditorMetadata
AppTech.MSMS.Domain.Models.ReceiptCreditorMetadata
ReceiptCreditorMetadata
ReceiptCreditorMetadata
WCpXksDU8Xo
<Amount>k__BackingField
QdfXk170enZ
<Date>k__BackingField
pRPXk69OUDV
<CurrencyID>k__BackingField
VAFXkfXHLwf
<CreditorAccountID>k__BackingField
mVNXkHy4r7v
<DebitorAccountID>k__BackingField
SsuXkBRffUo
<Note>k__BackingField
n4kXktDRgR9
<RefNumber>k__BackingField
VDLXkxujaPm
<AttachmentNumbers>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ReceiptDebitorMetadata
AppTech.MSMS.Domain.Models.ReceiptDebitorMetadata
ReceiptDebitorMetadata
ReceiptDebitorMetadata
ArvXk5O4lKJ
<Amount>k__BackingField
JCHXkrMEKpD
<Date>k__BackingField
YGMXkGY3ORF
<CurrencyID>k__BackingField
sPrXkgAX7uw
<CreditorAccountID>k__BackingField
hqQXkVGZy1u
<DebitorAccountID>k__BackingField
EQFXkee77sr
<Note>k__BackingField
wmVXknTabPj
<RefNumber>k__BackingField
VyYXk8bBGtX
<AttachmentNumbers>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SatelliteProviderMetadata
AppTech.MSMS.Domain.Models.SatelliteProviderMetadata
SatelliteProviderMetadata
SatelliteProviderMetadata
GukXkoYnqQN
<Number>k__BackingField
zi8XkT4FqNq
<AccountID>k__BackingField
jvtXkPminj5
<Name>k__BackingField
FkFXkRnqqrt
<MinSubscribe>k__BackingField
FdGXkAKUrBa
<RegionID>k__BackingField
D9lXkbecMOO
<Phone>k__BackingField
sCQXkIx79Wj
<PrafitAmount>k__BackingField
D63XkNRU2te
<PrafitStatus>k__BackingField
U3xXkkcwDY3
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SatelliteFactionMetadata
AppTech.MSMS.Domain.Models.SatelliteFactionMetadata
SatelliteFactionMetadata
SatelliteFactionMetadata
v90Xk7xuhim
<Number>k__BackingField
bVhXkc2FjfJ
<Name>k__BackingField
pVRXkiQ1pT0
<SatelliteProviderID>k__BackingField
WwCXkdlJ97X
<Note>k__BackingField
pyhXkwodxUh
<Price>k__BackingField
aCgXky52Xiu
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OrderSatelliteQuotaMetadata
AppTech.MSMS.Domain.Models.OrderSatelliteQuotaMetadata
OrderSatelliteQuotaMetadata
OrderSatelliteQuotaMetadata
R8UXkhxS7DX
<Description>k__BackingField
KY7XkFLj69C
<ProviderID>k__BackingField
df5XklA7vcC
<Note>k__BackingField
cUVXkYtObNJ
<ProviderNote>k__BackingField
AO7XkJDvSej
<Status>k__BackingField
<<type>>
AppTech.MSMS.Domain.Security.FrecnhiLogin
AppTech.MSMS.Domain.Security.FrecnhiLogin
FrecnhiLogin
FrecnhiLogin
<<type>>
AppTech.MSMS.Domain.Security.IpAddressManager
AppTech.MSMS.Domain.Security.IpAddressManager
IpAddressManager
IpAddressManager
<<type>>
AppTech.MSMS.Domain.Security.RijndaelHelper
AppTech.MSMS.Domain.Security.RijndaelHelper
RijndaelHelper
RijndaelHelper
dgCXkKseO22
DecryptStringFromBytes
QwpXk9uPA7P
EncryptStringToBytes
<<type>>
AppTech.MSMS.Domain.Security.Models.LicenseSetting
AppTech.MSMS.Domain.Security.Models.LicenseSetting
LicenseSetting
LicenseSetting
EAoXkO47N8Z
<Distributor>k__BackingField
JWZXkUe2L4L
<Branch>k__BackingField
BIjXkEbutVP
<SmsNo>k__BackingField
f9rXkSDYO3L
<DT>k__BackingField
W4JXkpBOPgw
<HeaderType>k__BackingField
MCSXkL3CM3s
<SrvNo>k__BackingField
YrGXkaljUvL
<CEAU>k__BackingField
<<type>>
AppTech.MSMS.Domain.Helpers.BalanceClosure
AppTech.MSMS.Domain.Helpers.BalanceClosure
BalanceClosure
BalanceClosure
pXoXk0PyOeS
CloseAccountName
tDGXkQgc6Ta
CopyBalanceDate
ti0Xk3NOrlr
copyBalanceDate
iXZXkvmFUsp
TargetDBName
l0GXkZWHy2l
SourceDbName
<<type>>
AppTech.MSMS.Domain.Helpers.ExcelHelper
AppTech.MSMS.Domain.Helpers.ExcelHelper
ExcelHelper
ExcelHelper
OVeXkmGP12m
_path
<<type>>
AppTech.MSMS.Domain.Helpers.JsonHelper
AppTech.MSMS.Domain.Helpers.JsonHelper
JsonHelper
JsonHelper
<<type>>
AppTech.MSMS.Domain.Helpers.Pdf
AppTech.MSMS.Domain.Helpers.Pdf
Pdf
Pdf
AtHXkCYxrQn
doc
<<type>>
AppTech.MSMS.Domain.Helpers.DbBackupHelper
AppTech.MSMS.Domain.Helpers.DbBackupHelper
DbBackupHelper
DbBackupHelper
<<type>>
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy
ExpressRemittanceProxy
ExpressRemittanceProxy
YVNXkzcCC1w
GetCurrencyId
AuxX72sBQ85
Log
ooeX7XstVxB
Log
JbSX7MxWpTu
Log
<<type>>
AppTech.MSMS.Domain.Helpers.FrenchiClient
AppTech.MSMS.Domain.Helpers.FrenchiClient
FrenchiClient
FrenchiClient
MsAX7jUewVw
_baseUrl
<<type>>
AppTech.MSMS.Domain.Helpers.PhoneNumber
AppTech.MSMS.Domain.Helpers.PhoneNumber
PhoneNumber
PhoneNumber
<<type>>
AppTech.MSMS.Domain.Entities.Balance
AppTech.MSMS.Domain.Entities.Balance
Balance
Balance
M2PX7uo4p4n
<CurrencyID>k__BackingField
ni0X7q1KV0p
<VoucherName>k__BackingField
wmhX7WmOuPj
<CurrencyName>k__BackingField
ex7X74FIS64
<AccountName>k__BackingField
VyNX7D5jrSh
<CurrencySymbol>k__BackingField
K0XX7sColFT
<Amount>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.Commission
AppTech.MSMS.Domain.Entities.Commission
Commission
Commission
DeyX719L1rN
<ParentAccountID>k__BackingField
ctxX76rAu72
<IsChild>k__BackingField
LIqX7fxRs3C
<Amount>k__BackingField
CRkX7H8cSfS
<DifferentailAmount>k__BackingField
jRcX7BBYQIh
<CurrencyID>k__BackingField
gJDX7tFyj17
<CurrencyName>k__BackingField
krwX7xtWlne
<InPercentage>k__BackingField
RnUX75DefVY
<ExtraInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.Entity
AppTech.MSMS.Domain.Entities.Entity
Entity
Entity
jlOX7rnsChq
<ID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.FcmToken
AppTech.MSMS.Domain.Entities.FcmToken
FcmToken
FcmToken
W2SX7GbvrND
<Token>k__BackingField
fwlX7gdY6UJ
<AccountID>k__BackingField
PPnX7VIPSJG
<Key>k__BackingField
E9sX7erc3vf
<DeviceID>k__BackingField
noyX7nZvqmY
<UserAgent>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.FmsCacheResponse
AppTech.MSMS.Domain.Entities.FmsCacheResponse
FmsCacheResponse
FmsCacheResponse
WfQX7880S3x
<GroupID>k__BackingField
dlEX7oLRvXR
<LSetting>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.IItem
AppTech.MSMS.Domain.Entities.IItem
IItem
IItem
<<type>>
AppTech.MSMS.Domain.Entities.IPerson
AppTech.MSMS.Domain.Entities.IPerson
IPerson
IPerson
<<type>>
AppTech.MSMS.Domain.Entities.Parent
AppTech.MSMS.Domain.Entities.Parent
Parent
Parent
K9ZX7TraO5U
<ID>k__BackingField
FgeX7PUvpoW
<Number>k__BackingField
KfxX7RxI8S2
<Name>k__BackingField
E0PX7AFlvHa
<AccountID>k__BackingField
NfpX7bxYLux
<PhoneNumber>k__BackingField
PKtX7I9Chl0
<ContactNumber>k__BackingField
QPMX7NMvuOU
<Address>k__BackingField
XbXX7kIfKcu
<Note>k__BackingField
hiqX771aJHw
<BranchID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.RemittancePointInfo
AppTech.MSMS.Domain.Entities.RemittancePointInfo
RemittancePointInfo
RemittancePointInfo
RcyX7cInWhk
<ID>k__BackingField
OKkX7iJl6MK
<Name>k__BackingField
YGMX7dLQYTQ
<RegionID>k__BackingField
WejX7wKGjWD
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.SourceInfo
AppTech.MSMS.Domain.Entities.SourceInfo
SourceInfo
SourceInfo
EfiX7y0ivXe
<Host>k__BackingField
SoBX7hP38BF
<IpAddress>k__BackingField
ohCX7FvKeei
<UserAgent>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.RequestAuth
AppTech.MSMS.Domain.Entities.RequestAuth
RequestAuth
RequestAuth
s0PX7l4CkFg
<VC>k__BackingField
JnNX7Y7SKPP
<VERSION_CODE>k__BackingField
FPdX7JEnMYR
<SessionID>k__BackingField
twrX7KZVQdB
<DeviceID>k__BackingField
j1YX79EOb3Q
<TransactionID>k__BackingField
smNX7OGq3ZW
<DeviceInfo>k__BackingField
GLZX7UY1SlZ
<Location>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TableColumn
AppTech.MSMS.Domain.Entities.TableColumn
TableColumn
TableColumn
UlmX7EncVER
<Type>k__BackingField
DoxX7S1HIqk
<Name>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.GomalaCommission
AppTech.MSMS.Domain.Entities.GomalaCommission
GomalaCommission
GomalaCommission
B5HX7phgvx8
<ID>k__BackingField
hAxX7L1aWGy
<CostID>k__BackingField
yC1X7a8hLjG
<SalePrice>k__BackingField
FjPX705xw2w
<ProviderPrice>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupQuery
AppTech.MSMS.Domain.Entities.TopupQuery
TopupQuery
TopupQuery
LLJX7QvJtSl
<QueryType>k__BackingField
LGvX73Pbm2s
<SID>k__BackingField
LYTX7vB2pLa
<SNO>k__BackingField
Le5X7ZSo41M
<Number>k__BackingField
r2QX7miYJaL
<Type>k__BackingField
FB6X7CvqZo3
<RID>k__BackingField
hXqX7z41jt6
<UID>k__BackingField
gVKXc2bBpLE
<IsApi>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.ITopup
AppTech.MSMS.Domain.Entities.ITopup
ITopup
ITopup
<<type>>
AppTech.MSMS.Domain.Entities.TopupModel
AppTech.MSMS.Domain.Entities.TopupModel
TopupModel
TopupModel
BfLXcXn1q2U
<ID>k__BackingField
rxXXcMWVxPK
<Number>k__BackingField
IowXcjfk1hm
<ServiceID>k__BackingField
XJpXcuEcQWK
<SubscriberNumber>k__BackingField
warXcqoOsmE
<RefNumber>k__BackingField
Fw9XcWW4L5X
<TransactionID>k__BackingField
A4MXc4p6J4y
<Status>k__BackingField
yCXXcDTMoHi
<EntryID>k__BackingField
ainXcsLMZnP
<UniqueNo>k__BackingField
afvXc1KqShe
<Note>k__BackingField
TrtXc6Jq19q
<ProviderRM>k__BackingField
tTRXcf3ndvR
<SubNote>k__BackingField
uXKXcHmANGe
<Quantity>k__BackingField
CrCXcBBAIFI
<UnitPrice>k__BackingField
mG8XctpRVn4
<CostAmount>k__BackingField
d57XcxvX87G
<AppTech.MSMS.Domain.Entities.ITopup.Date>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupRequest
AppTech.MSMS.Domain.Entities.TopupRequest
TopupRequest
TopupRequest
Pe4Xc5Jk0wq
<UN>k__BackingField
bkDXcrt4b0C
<PSS>k__BackingField
DjJXcGM65fY
<TKN>k__BackingField
tlHXcgNYHaI
<Note>k__BackingField
MHBXcVmOvmk
<SID>k__BackingField
kmlXcepryL4
<SNO>k__BackingField
Y5bXcnlHcQJ
<AMT>k__BackingField
nhrXc8KDVmv
<Cost>k__BackingField
bWgXcoMZNJD
<FID>k__BackingField
Q49XcTtUqjf
<RID>k__BackingField
C8fXcPEgJdQ
<LType>k__BackingField
WD5XcRbOwGN
<CType>k__BackingField
G00XcASkKhP
<Ref>k__BackingField
FVgXcbwXAWi
<Channel>k__BackingField
sQIXcIcWeBQ
<Identifier>k__BackingField
nwKXcNWsZUA
<Action>k__BackingField
Yk7XckhLyxc
<Type>k__BackingField
leLXc7bCYTW
<State>k__BackingField
UNDXccgM0Pm
<Flag>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupRespose
AppTech.MSMS.Domain.Entities.TopupRespose
TopupRespose
TopupRespose
Q1LXcilyvxU
<Success>k__BackingField
NgCXcd0pA1q
<Status>k__BackingField
xVgXcwK26uA
<MSG>k__BackingField
ATmXcy45Beg
<TNO>k__BackingField
KphXchQQO76
<ID>k__BackingField
W3CXcF85b4I
<Quantity>k__BackingField
kP9XclVg8gZ
<UnitCost>k__BackingField
cuYXcYyH2wY
<CostAmount>k__BackingField
zx5XcJ1nsRT
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupRequestInfo
AppTech.MSMS.Domain.Entities.TopupRequestInfo
TopupRequestInfo
TopupRequestInfo
wXCXcKd8FXk
<TransactionID>k__BackingField
wk3Xc95Fqo8
<ServiceID>k__BackingField
r2dXcOET9QN
<Number>k__BackingField
gLZXcUOHfsX
<Amount>k__BackingField
tgxXcEQF8LH
<BundleID>k__BackingField
p3UXcSWuogt
<LineType>k__BackingField
zdVXcpsaEbd
<Type>k__BackingField
FLLXcL1mi7T
<Action>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupResponseInfo
AppTech.MSMS.Domain.Entities.TopupResponseInfo
TopupResponseInfo
TopupResponseInfo
U3cXcae1o8W
<Status>k__BackingField
CLZXc0c9kl6
<PaymentID>k__BackingField
LXPXcQ5Kgjv
<Message>k__BackingField
koTXc3MSGiJ
<CostAmount>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.StatusInfo
AppTech.MSMS.Domain.Entities.StatusInfo
StatusInfo
StatusInfo
DKvXcvoZ4Sa
<Status>k__BackingField
EmfXcZOp05l
<Success>k__BackingField
EbtXcmi6nnd
<Message>k__BackingField
yVuXcCvPw9u
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupStatusInfo
AppTech.MSMS.Domain.Entities.TopupStatusInfo
TopupStatusInfo
TopupStatusInfo
MFhXcz0aLAV
<Status>k__BackingField
BNOXi2pFDmW
<Success>k__BackingField
HwbXiXO6PwI
<Message>k__BackingField
VwUXiMKDMhW
<RequestInfo>k__BackingField
VwTXijilHr8
<Note>k__BackingField
sC9XiunsRXy
<Flag>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupStatusRequest
AppTech.MSMS.Domain.Entities.TopupStatusRequest
TopupStatusRequest
TopupStatusRequest
Of9XiqtqHXp
<UN>k__BackingField
P82XiWeNrtB
<PSS>k__BackingField
AhIXi4xriCA
<TKN>k__BackingField
i8oXiDvXY6U
<SID>k__BackingField
RFpXisSM9pT
<SNO>k__BackingField
U8iXi1Sg4Wo
<TID>k__BackingField
POcXi6JRfle
<REF>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.WifiInventoryModel
AppTech.MSMS.Domain.Entities.WifiInventoryModel
WifiInventoryModel
WifiInventoryModel
GvjXifwQZEO
<ID>k__BackingField
L7sXiH00QhL
<Number>k__BackingField
AyUXiBVQSDv
<Name>k__BackingField
pEVXitPpCtV
<OrderNO>k__BackingField
CshXixcO05l
<Description>k__BackingField
GZ9Xi5ODgNU
<Price>k__BackingField
SeGXirjRPM6
<TotalAll>k__BackingField
kWKXiG6kV40
<TotalSold>k__BackingField
RAEXig3kxeI
<TotalRemain>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.Password
AppTech.MSMS.Domain.Entities.Password
Password
Password
BQrXiVgJs12
<CurrentPasswrod>k__BackingField
noXXieZfEB8
<NewPasswrod>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.ServiceCode
AppTech.MSMS.Domain.Entities.ServiceCode
ServiceCode
ServiceCode
<<type>>
AppTech.MSMS.Domain.Entities.AlconPaymentRequest
AppTech.MSMS.Domain.Entities.AlconPaymentRequest
AlconPaymentRequest
AlconPaymentRequest
zwuXinWVTgY
<USR>k__BackingField
gwuXi8S5xlu
<TKN>k__BackingField
pHgXiogiDkO
<AC>k__BackingField
Ci2XiTofQQs
<MT>k__BackingField
CkPXiPulqIC
<SC>k__BackingField
iILXiRgfguk
<SNO>k__BackingField
t0CXiAFNMuI
<AMT>k__BackingField
aNFXibhHHbo
<REF>k__BackingField
uVGXiIguaTZ
<SAC>k__BackingField
s0fXiNYY77h
<REM>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlconPaymentRespone
AppTech.MSMS.Domain.Entities.AlconPaymentRespone
AlconPaymentRespone
AlconPaymentRespone
ICxXikIfNIp
<RC>k__BackingField
GS9Xi7DQBn7
<MSG>k__BackingField
oTwXicrPBWN
<REF>k__BackingField
FwbXiinOO6W
<TRX>k__BackingField
ilJXid3Lute
<MT>k__BackingField
yYLXiwWypDM
<SD>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConInqueryRequest
AppTech.MSMS.Domain.Entities.AlConInqueryRequest
AlConInqueryRequest
AlConInqueryRequest
CkPXiy8ADhU
<AC>k__BackingField
VGZXihtMakm
<SC>k__BackingField
AN2XiFaFoEt
<SNO>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConLoan
AppTech.MSMS.Domain.Entities.AlConLoan
AlConLoan
AlConLoan
W29Xil5HGEB
<loanStatus>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConInqueryResponse
AppTech.MSMS.Domain.Entities.AlConInqueryResponse
AlConInqueryResponse
AlConInqueryResponse
bYaXiYwqqUx
<RC>k__BackingField
NPNXiJ9Kve9
<MSG>k__BackingField
Pg6XiKNOFAt
<BAL>k__BackingField
WNVXi9Fp0UQ
<MT>k__BackingField
G0rXiO4oqMM
<SD>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.S2DStatus
AppTech.MSMS.Domain.Entities.S2DStatus
S2DStatus
S2DStatus
OXgXiU6IJLw
<Status>k__BackingField
bs0XiEGCh7E
<Phone>k__BackingField
mNOXiSZyIfZ
<Rem>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConInqueryResponse2
AppTech.MSMS.Domain.Entities.AlConInqueryResponse2
AlConInqueryResponse2
AlConInqueryResponse2
vTFXipNMeLn
<RC>k__BackingField
phdXiLs6vch
<MSG>k__BackingField
anJXiaIXh7C
<BAL>k__BackingField
w7RXi0ogxRE
<MT>k__BackingField
xOqXiQh7Mxh
<SD>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConSD
AppTech.MSMS.Domain.Entities.AlConSD
AlConSD
AlConSD
kBhXi3iCo88
<BaqaAmt>k__BackingField
pZ8XivRPXBw
<remain>k__BackingField
mBNXiZ2p202
<Balance>k__BackingField
OOwXimguCTV
<CREDIT>k__BackingField
KQhXiCyRY5h
<ExpDate>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.PostPaidAlConSD
AppTech.MSMS.Domain.Entities.PostPaidAlConSD
PostPaidAlConSD
PostPaidAlConSD
yQFXizN05Bh
<CREDIT>k__BackingField
t58Xd2fSFGF
<Balance>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConOfferRequest
AppTech.MSMS.Domain.Entities.AlConOfferRequest
AlConOfferRequest
AlConOfferRequest
IksXdX9sa5L
<USR>k__BackingField
iNQXdM7lBuX
<TKN>k__BackingField
GdqXdj9J7IY
<AC>k__BackingField
ssdXduQ7IKJ
<SC>k__BackingField
VayXdqjauaB
<SNO>k__BackingField
I2fXdWZP4BU
<SAC>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConOfferResponse
AppTech.MSMS.Domain.Entities.AlConOfferResponse
AlConOfferResponse
AlConOfferResponse
PPuXd4WcaLk
<RC>k__BackingField
VNuXdDFx3ir
<MSG>k__BackingField
XPuXds8oPl4
<SD>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConOffer
AppTech.MSMS.Domain.Entities.AlConOffer
AlConOffer
AlConOffer
ksmXd1KhPSl
<offer_id>k__BackingField
uP0Xd6Bg5gL
<offer_name>k__BackingField
BeKXdfc7jw7
<eff_date>k__BackingField
uqpXdHahLuJ
<exp_date>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConBalanceRequest
AppTech.MSMS.Domain.Entities.AlConBalanceRequest
AlConBalanceRequest
AlConBalanceRequest
mWwXdBUiVUs
<AC>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConBalanceResponse
AppTech.MSMS.Domain.Entities.AlConBalanceResponse
AlConBalanceResponse
AlConBalanceResponse
CZBXdtl4mxa
<RC>k__BackingField
niRXdx0Aouj
<MSG>k__BackingField
wupXd5itlKC
<BAL>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConOfferActionType
AppTech.MSMS.Domain.Entities.AlConOfferActionType
AlConOfferActionType
AlConOfferActionType
<<type>>
AppTech.MSMS.Domain.Entities.AlConActionType
AppTech.MSMS.Domain.Entities.AlConActionType
AlConActionType
AlConActionType
<<type>>
AppTech.MSMS.Domain.Entities.PaymentRequest
AppTech.MSMS.Domain.Entities.PaymentRequest
PaymentRequest
PaymentRequest
rUDXdrHKw0J
<PaymentType>k__BackingField
jx8XdGUcWuj
<RecordID>k__BackingField
g3fXdgVX6pK
<ServiceID>k__BackingField
scPXdV42tSU
<SNO>k__BackingField
NQaXde683F6
<CostAmount>k__BackingField
KbDXdnMDsQi
<Amount>k__BackingField
HMSXd8T2Dik
<FactionID>k__BackingField
WSGXdo3IprY
<BundleCode>k__BackingField
vEWXdTlRkIo
<LineType>k__BackingField
K4fXdP1xkmO
<CType>k__BackingField
LGeXdREfJdq
<SubCode>k__BackingField
DoxXdAf3K1S
<TransactionID>k__BackingField
Wv0XdbOTw0d
<ActionType>k__BackingField
VTvXdIlhkdx
<Flag>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.PaymentResponse
AppTech.MSMS.Domain.Entities.PaymentResponse
PaymentResponse
PaymentResponse
X1pXdN7tQgX
set_RequestInfo
hC9XdkuS5KB
<Status>k__BackingField
bE6Xd7j5CrN
<Success>k__BackingField
JS7XdcOgN9G
<LineType>k__BackingField
aQ6XdiFbB9a
<SubDetail>k__BackingField
RgwXddf5GTc
<SubBalance>k__BackingField
pvKXdwhfqmB
<Message>k__BackingField
daIXdyVH2Xn
<ProviderResponse>k__BackingField
leHXdhljOIp
<RequestInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.OfferQueryRequest
AppTech.MSMS.Domain.Entities.OfferQueryRequest
OfferQueryRequest
OfferQueryRequest
sy0XdFeZyyT
<ServiceID>k__BackingField
wy3XdlQtFY1
<SNO>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.OfferQueryResponse
AppTech.MSMS.Domain.Entities.OfferQueryResponse
OfferQueryResponse
OfferQueryResponse
RsBXdYiJ77P
<Success>k__BackingField
DtNXdJAhlCi
<MSG>k__BackingField
JGGXdKvSL5q
<SD>k__BackingField
s3eXd98thVm
<Offers>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.BagatRequest
AppTech.MSMS.Domain.Entities.BagatRequest
BagatRequest
BagatRequest
y8iXdOJm7fs
<UN>k__BackingField
SQwXdULvC95
<PSS>k__BackingField
ykaXdEYoG9Q
<TKN>k__BackingField
JPWXdSdEISX
<Ref>k__BackingField
ucTXdp87TWk
<SNO>k__BackingField
bxLXdLjVEbA
<AMT>k__BackingField
tvgXdaugv81
<SID>k__BackingField
hyXXd0p2ux9
<FID>k__BackingField
TOJXdQE3qV8
<LType>k__BackingField
AQ1Xd3mxjjo
<Code>k__BackingField
dtRXdvCpT7q
<Action>k__BackingField
XENXdZFfPOw
<Channel>k__BackingField
kkvXdmuQZpf
<Note>k__BackingField
heVXdCxQVvn
<UID>k__BackingField
geOXdzyPqdo
<IncludePay>k__BackingField
SN5Xw2vx9fB
<RecordID>k__BackingField
N6vXwXs2D8m
<Identifier>k__BackingField
I54XwMiB5h0
<OfferName>k__BackingField
WfhXwj4ltvk
<Type>k__BackingField
X1gXwuenMqp
<State>k__BackingField
XNgXwqocwJ7
<CostAmount>k__BackingField
Y1BXwWn4IZ8
<Flag>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.BagatRespose
AppTech.MSMS.Domain.Entities.BagatRespose
BagatRespose
BagatRespose
XKsXw4GC9Dc
<Success>k__BackingField
LXOXwDCcXQ2
<Status>k__BackingField
j4FXwsp5DZe
<MSG>k__BackingField
pmGXw1iBUhj
<TNO>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.OfferAction
AppTech.MSMS.Domain.Entities.OfferAction
OfferAction
OfferAction
<<type>>
AppTech.MSMS.Domain.Entities.PaymentType
AppTech.MSMS.Domain.Entities.PaymentType
PaymentType
PaymentType
<<type>>
AppTech.MSMS.Domain.Entities.QueryType
AppTech.MSMS.Domain.Entities.QueryType
QueryType
QueryType
<<type>>
AppTech.MSMS.Domain.Entities.QueryRequest
AppTech.MSMS.Domain.Entities.QueryRequest
QueryRequest
QueryRequest
o9EXw6l5uSc
<Query>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.QueryResponse
AppTech.MSMS.Domain.Entities.QueryResponse
QueryResponse
QueryResponse
TwRXwfKm8Ot
<Success>k__BackingField
IR7XwHY9dOC
<Message>k__BackingField
PKgXwBYMoWZ
<Result>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.QuotationResponse
AppTech.MSMS.Domain.Entities.QuotationResponse
QuotationResponse
QuotationResponse
QWAXwttro6q
Init
V2EXwxfgGcc
_request
KElXw5GkbPu
_session
r1YXwrnamn9
<Items>k__BackingField
XuYXwG5DHp3
<Success>k__BackingField
wPgXwgGhwC2
<Message>k__BackingField
xScXwVtmjsJ
<UnitPrice>k__BackingField
I5HXwe0IlDF
<Quantity>k__BackingField
GM6XwnXukfc
<Amount>k__BackingField
YmnXw8u9HAh
<Commission>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.QuotationRequest
AppTech.MSMS.Domain.Entities.QuotationRequest
QuotationRequest
QuotationRequest
AKrXwotcfOX
<ServiceID>k__BackingField
hKaXwT6nNIa
<Type>k__BackingField
BAQXwPf6Qot
<FactionID>k__BackingField
aEXXwRHfmLR
<Quantity>k__BackingField
gP1XwAXPhVD
<Amount>k__BackingField
XwYXwbcmAPQ
<Flag>k__BackingField
KmrXwIcNQIc
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.Pair
AppTech.MSMS.Domain.Entities.Pair
Pair
Pair
eugXwNW0TAZ
<Key>k__BackingField
yknXwkL5pT7
<Value>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.RegisterationInfo
AppTech.MSMS.Domain.Entities.RegisterationInfo
RegisterationInfo
RegisterationInfo
z8XXw7V4Iwg
<Username>k__BackingField
sRCXwcsh2fL
<Password>k__BackingField
V7KXwifKAaM
<ClientName>k__BackingField
LjgXwdvsR3j
<ShopName>k__BackingField
gSpXwwhPKhH
<FcmToken>k__BackingField
FcbXwyNbdSZ
<PhoneNumber>k__BackingField
HPVXwh6R3pK
<Address>k__BackingField
kptXwFaAjpO
<Email>k__BackingField
rrFXwlgdJft
<Image>k__BackingField
pNgXwYQ3fcw
<Device>k__BackingField
yURXwJUS2FE
<DeviceID>k__BackingField
k4eXwKaAtnS
<Channel>k__BackingField
RjcXw9F9VXn
<ApiKey>k__BackingField
wJOXwO8SQ0P
<CardType>k__BackingField
JVVXwUhcvQi
<CardNumber>k__BackingField
h7wXwE1mLiV
<CardIssuePlace>k__BackingField
cinXwS9b5a3
<CardIssueDate>k__BackingField
Wd6XwplHKWd
<DeviceInfo>k__BackingField
lNVXwLMD4Eh
<Flavor>k__BackingField
D3SXwatbXMK
<ID>k__BackingField
O6GXw0KN77p
<BranchID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.BalanceResponseInfo
AppTech.MSMS.Domain.Entities.BalanceResponseInfo
BalanceResponseInfo
BalanceResponseInfo
RX8XwQN43vg
<Success>k__BackingField
Wi2Xw3AcwR4
<Balance>k__BackingField
IYjXwvcfxw0
<Message>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.InqueryResponse
AppTech.MSMS.Domain.Entities.InqueryResponse
InqueryResponse
InqueryResponse
gQPXwZqYNx4
<Success>k__BackingField
OcgXwm6YlAa
<Message>k__BackingField
LBQXwCWG00m
<Balance>k__BackingField
QqXXwzKEwmx
<LineType>k__BackingField
YsEXy2aVJSY
<Credit>k__BackingField
Y6HXyXabUaK
<SubDetail>k__BackingField
rIqXyMIXDLL
<Offers>k__BackingField
jgHXyjP6qVd
<LoanStatus>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.Offer
AppTech.MSMS.Domain.Entities.Offer
Offer
Offer
FpjXyuHB9RT
<ID>k__BackingField
bjjXyq676mI
<Name>k__BackingField
eNcXyW6GWGP
<StartDate>k__BackingField
zTFXy4QAGr4
<ExpireDate>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AgentModel
AppTech.MSMS.Domain.Entities.AgentModel
AgentModel
AgentModel
ariXyDpWjFt
<ID>k__BackingField
NZfXyssBcq0
<Number>k__BackingField
oy8Xy1kmsDd
<Name>k__BackingField
rLrXy6HhNuC
<AccountID>k__BackingField
P0bXyfvtJv6
<PhoneNumber>k__BackingField
zf6XyH7tl4A
<Address>k__BackingField
zIwXyBtc4mJ
<IsRemittancePoint>k__BackingField
Og0Xytn6nVs
<RemittancePoint>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.Party
AppTech.MSMS.Domain.Entities.Party
Party
Party
vewXyxdWBW9
Init
vCtXy5bWtZd
ValidateClient
E5fXyr68B0I
ValidateAgent
oBTXyG0i2Om
ValidateDistributor
IoVXygcA3I8
ValidateBranch
AJlXyVB7UFd
ValidateAdmin
u4BXyevridS
ValidateMerchant
YPkXyn5iA9d
_unitOfWork
qr2Xy80Fco1
_userParty
VykXyoTbGau
<Status>k__BackingField
e6fXyTGXbkI
<ID>k__BackingField
paMXyPgRLQZ
<Number>k__BackingField
hq3XyR0ku4m
<Name>k__BackingField
iUhXyATa53C
<Email>k__BackingField
i9AXybar7Ak
<AccountID>k__BackingField
bk7XyIi0d1W
<ParentAccountID>k__BackingField
GYqXyN9dbQK
<IsChild>k__BackingField
A7hXykdIv03
<PhoneNumber>k__BackingField
lJ5Xy7ZCwXt
<Address>k__BackingField
gFQXyc0yq5e
<IsPerson>k__BackingField
sWrXyiPG8qh
<IsWifiProvider>k__BackingField
kRxXydjCbod
<IsSatelliteProvider>k__BackingField
eKXXywcKyRP
<IsCardProvider>k__BackingField
oKbXyyfkY3t
<Parent>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.MerchantModel
AppTech.MSMS.Domain.Entities.MerchantModel
MerchantModel
MerchantModel
WKnXyhqAPaG
<ID>k__BackingField
vIxXyFy4yWu
<Number>k__BackingField
mmtXylXqmbL
<Name>k__BackingField
HYvXyYvJUIq
<OwnerName>k__BackingField
bB7XyJbqRui
<AccountID>k__BackingField
SCHXyK5qy0k
<PhoneNumber>k__BackingField
groXy9L2exI
<Address>k__BackingField
<<type>>
AppTech.MSMS.Domain.Core.LicensedServices
AppTech.MSMS.Domain.Core.LicensedServices
LicensedServices
LicensedServices
R4PXyOrUyt2
<Sim>k__BackingField
kvcXyU5jamR
<Gomala>k__BackingField
xp9XyE2rp2O
<Merchant>k__BackingField
LdFXySmchht
<Transfers>k__BackingField
<<type>>
AppTech.MSMS.Domain.YemenPostReference.billpaymentws
AppTech.MSMS.Domain.YemenPostReference.billpaymentws
billpaymentws
billpaymentws
<<type>>
AppTech.MSMS.Domain.YemenPostReference.ypPaymentRequest
AppTech.MSMS.Domain.YemenPostReference.ypPaymentRequest
ypPaymentRequest
ypPaymentRequest
<<type>>
AppTech.MSMS.Domain.YemenPostReference.ypPaymentRequestBody
AppTech.MSMS.Domain.YemenPostReference.ypPaymentRequestBody
ypPaymentRequestBody
ypPaymentRequestBody
<<type>>
AppTech.MSMS.Domain.YemenPostReference.ypPaymentResponse
AppTech.MSMS.Domain.YemenPostReference.ypPaymentResponse
ypPaymentResponse
ypPaymentResponse
<<type>>
AppTech.MSMS.Domain.YemenPostReference.ypPaymentResponseBody
AppTech.MSMS.Domain.YemenPostReference.ypPaymentResponseBody
ypPaymentResponseBody
ypPaymentResponseBody
<<type>>
AppTech.MSMS.Domain.YemenPostReference.billpaymentwsChannel
AppTech.MSMS.Domain.YemenPostReference.billpaymentwsChannel
billpaymentwsChannel
billpaymentwsChannel
<<type>>
AppTech.MSMS.Domain.YemenPostReference.billpaymentwsClient
AppTech.MSMS.Domain.YemenPostReference.billpaymentwsClient
billpaymentwsClient
billpaymentwsClient
<<type>>
AppTech.MSMS.Domain.TadawulReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.TadawulReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.TadawulReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.TadawulReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.TadawulReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.TadawulReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.ServiceReference1.paymenttransactionresponse
AppTech.MSMS.Domain.ServiceReference1.paymenttransactionresponse
paymenttransactionresponse
paymenttransactionresponse
xtAXypfcLjV
extensionDataField
rgJXyLHnHI3
response_codeField
xd4XyaiR0On
response_messageField
m82Xy0Vug99
PropertyChanged
<<type>>
AppTech.MSMS.Domain.ServiceReference1.Ialbayanmtnclientservice
AppTech.MSMS.Domain.ServiceReference1.Ialbayanmtnclientservice
Ialbayanmtnclientservice
Ialbayanmtnclientservice
<<type>>
AppTech.MSMS.Domain.ServiceReference1.IalbayanmtnclientserviceChannel
AppTech.MSMS.Domain.ServiceReference1.IalbayanmtnclientserviceChannel
IalbayanmtnclientserviceChannel
IalbayanmtnclientserviceChannel
<<type>>
AppTech.MSMS.Domain.ServiceReference1.IalbayanmtnclientserviceClient
AppTech.MSMS.Domain.ServiceReference1.IalbayanmtnclientserviceClient
IalbayanmtnclientserviceClient
IalbayanmtnclientserviceClient
<<type>>
AppTech.MSMS.Domain.QulaidiServiceReference2.ICSDServiceLinkLib
AppTech.MSMS.Domain.QulaidiServiceReference2.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.QulaidiServiceReference2.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.QulaidiServiceReference2.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.QulaidiServiceReference2.CSDServiceLinkLibClient
AppTech.MSMS.Domain.QulaidiServiceReference2.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.JuzaifaServiceReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.JuzaifaServiceReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.JuzaifaServiceReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.JuzaifaServiceReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.JuzaifaServiceReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.JuzaifaServiceReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.ForMeServiceReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.ForMeServiceReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.ForMeServiceReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.ForMeServiceReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.ForMeServiceReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.ForMeServiceReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.DerhimApiReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.DerhimApiReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.DerhimApiReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.DerhimApiReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.DerhimApiReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.DerhimApiReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.BaAmerReference.IService
AppTech.MSMS.Domain.BaAmerReference.IService
IService
IService
<<type>>
AppTech.MSMS.Domain.BaAmerReference.IServiceChannel
AppTech.MSMS.Domain.BaAmerReference.IServiceChannel
IServiceChannel
IServiceChannel
<<type>>
AppTech.MSMS.Domain.BaAmerReference.ServiceClient
AppTech.MSMS.Domain.BaAmerReference.ServiceClient
ServiceClient
ServiceClient
<<type>>
AppTech.MSMS.Domain.AtheerServiceReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.AtheerServiceReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.AtheerServiceReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.AtheerServiceReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.AtheerServiceReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.AtheerServiceReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.AtheerServiceReference2.ICSDServiceLinkLib
AppTech.MSMS.Domain.AtheerServiceReference2.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.AtheerServiceReference2.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.AtheerServiceReference2.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.AtheerServiceReference2.CSDServiceLinkLibClient
AppTech.MSMS.Domain.AtheerServiceReference2.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.AlSareeaOnLineServiceReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.AlSareeaOnLineServiceReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.AlSareeaOnLineServiceReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.AlSareeaOnLineServiceReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.AlSareeaOnLineServiceReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.AlSareeaOnLineServiceReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.AlmutarebReference.IService
AppTech.MSMS.Domain.AlmutarebReference.IService
IService
IService
<<type>>
AppTech.MSMS.Domain.AlmutarebReference.IServiceChannel
AppTech.MSMS.Domain.AlmutarebReference.IServiceChannel
IServiceChannel
IServiceChannel
<<type>>
AppTech.MSMS.Domain.AlmutarebReference.ServiceClient
AppTech.MSMS.Domain.AlmutarebReference.ServiceClient
ServiceClient
ServiceClient
<<type>>
AppTech.MSMS.Domain.AlbayanReference.paymenttransactionresponse
AppTech.MSMS.Domain.AlbayanReference.paymenttransactionresponse
paymenttransactionresponse
paymenttransactionresponse
TNpXyQVSBBF
extensionDataField
jbVXy3lbV4F
response_codeField
k6tXyvps6aW
response_messageField
TOSXyZreObJ
PropertyChanged
<<type>>
AppTech.MSMS.Domain.AlbayanReference.Ialbayanmtnclientservice
AppTech.MSMS.Domain.AlbayanReference.Ialbayanmtnclientservice
Ialbayanmtnclientservice
Ialbayanmtnclientservice
<<type>>
AppTech.MSMS.Domain.AlbayanReference.IalbayanmtnclientserviceChannel
AppTech.MSMS.Domain.AlbayanReference.IalbayanmtnclientserviceChannel
IalbayanmtnclientserviceChannel
IalbayanmtnclientserviceChannel
<<type>>
AppTech.MSMS.Domain.AlbayanReference.IalbayanmtnclientserviceClient
AppTech.MSMS.Domain.AlbayanReference.IalbayanmtnclientserviceClient
IalbayanmtnclientserviceClient
IalbayanmtnclientserviceClient
<<type>>
AppTech.MSMS.Domain.AbsiReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.AbsiReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.AbsiReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.AbsiReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.AbsiReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.AbsiReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.BaseModels.IEntry
AppTech.MSMS.Domain.BaseModels.IEntry
IEntry
IEntry
<<type>>
AppTech.MSMS.Domain.BaseModels.IPayment
AppTech.MSMS.Domain.BaseModels.IPayment
IPayment
IPayment
<<type>>
AppTech.MSMS.Domain.BaseModels.IRemittanceEntity
AppTech.MSMS.Domain.BaseModels.IRemittanceEntity
IRemittanceEntity
IRemittanceEntity
<<type>>
AppTech.MSMS.Domain.BaseModels.IDoubleEntry
AppTech.MSMS.Domain.BaseModels.IDoubleEntry
IDoubleEntry
IDoubleEntry
<<type>>
AppTech.MSMS.Domain.BaseModels.IAccountEntry
AppTech.MSMS.Domain.BaseModels.IAccountEntry
IAccountEntry
IAccountEntry
<<type>>
AppTech.MSMS.Domain.BaseModels.OrderDetail
AppTech.MSMS.Domain.BaseModels.OrderDetail
OrderDetail
OrderDetail
<<type>>
AppTech.MSMS.Domain.BaseModels.RemoteOrderDetail
AppTech.MSMS.Domain.BaseModels.RemoteOrderDetail
RemoteOrderDetail
RemoteOrderDetail
a76XymYeFsT
<TransactionID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Attributes.Digits
AppTech.MSMS.Domain.Attributes.Digits
Digits
Digits
<<type>>
AppTech.MSMS.Domain.Attributes.Money
AppTech.MSMS.Domain.Attributes.Money
Money
Money
<<type>>
AppTech.MSMS.Domain.Attributes.MsDisplayNameAttribute
AppTech.MSMS.Domain.Attributes.MsDisplayNameAttribute
MsDisplayNameAttribute
MsDisplayNameAttribute
<<type>>
AppTech.MSMS.Domain.Attributes.MsRequired
AppTech.MSMS.Domain.Attributes.MsRequired
MsRequired
MsRequired
<<type>>
<PrivateImplementationDetails>
<PrivateImplementationDetails>
<PrivateImplementationDetails>
<PrivateImplementationDetails>
<<type>>
UeQGZjwRymuEF6kgsG.rTWQmh0lHIxON1kYCX/rnTQgkXvUA8Z28Di3q
Derhim.DerhimApi/DerhimServices
rnTQgkXvUA8Z28Di3q
DerhimServices
<<type>>
UeQGZjwRymuEF6kgsG.rTWQmh0lHIxON1kYCX/<InqueryAsync>d__19
Derhim.DerhimApi/<InqueryAsync>d__19
<InqueryAsync>d__19
<InqueryAsync>d__19
<<type>>
UeQGZjwRymuEF6kgsG.rTWQmh0lHIxON1kYCX/<MakeBagaAsyn>d__21
Derhim.DerhimApi/<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<<type>>
UeQGZjwRymuEF6kgsG.rTWQmh0lHIxON1kYCX/<PaymentAsync>d__23
Derhim.DerhimApi/<PaymentAsync>d__23
<PaymentAsync>d__23
<PaymentAsync>d__23
<<type>>
UeQGZjwRymuEF6kgsG.rTWQmh0lHIxON1kYCX/<PaymentGomlaAsync>d__24
Derhim.DerhimApi/<PaymentGomlaAsync>d__24
<PaymentGomlaAsync>d__24
<PaymentGomlaAsync>d__24
<<type>>
AppTech.MSMS.Domain.DomainManager/<>c
AppTech.MSMS.Domain.DomainManager/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteAsync>d__3
AppTech.MSMS.Domain.FrancyGateway/<ExecuteAsync>d__3
<ExecuteAsync>d__3
<ExecuteAsync>d__3
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteGomalaAsync>d__4
AppTech.MSMS.Domain.FrancyGateway/<ExecuteGomalaAsync>d__4
<ExecuteGomalaAsync>d__4
<ExecuteGomalaAsync>d__4
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteTopupAsync>d__5
AppTech.MSMS.Domain.FrancyGateway/<ExecuteTopupAsync>d__5
<ExecuteTopupAsync>d__5
<ExecuteTopupAsync>d__5
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteBagatAsync>d__6
AppTech.MSMS.Domain.FrancyGateway/<ExecuteBagatAsync>d__6
<ExecuteBagatAsync>d__6
<ExecuteBagatAsync>d__6
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<>c__DisplayClass7_0
AppTech.MSMS.Domain.FrancyGateway/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteAdenNetAsync>d__7
AppTech.MSMS.Domain.FrancyGateway/<ExecuteAdenNetAsync>d__7
<ExecuteAdenNetAsync>d__7
<ExecuteAdenNetAsync>d__7
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteRiyalBagat>d__8
AppTech.MSMS.Domain.FrancyGateway/<ExecuteRiyalBagat>d__8
<ExecuteRiyalBagat>d__8
<ExecuteRiyalBagat>d__8
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteRiyalTopup>d__9
AppTech.MSMS.Domain.FrancyGateway/<ExecuteRiyalTopup>d__9
<ExecuteRiyalTopup>d__9
<ExecuteRiyalTopup>d__9
<<type>>
xKMQlaTSDQcoE6k1iU.RsY7D4su80hYuogeWW/cLsX4PoObbhdPduE13
AppTech.MSMS.Domain.TestClass/TransType
cLsX4PoObbhdPduE13
TransType
<<type>>
xKMQlaTSDQcoE6k1iU.RsY7D4su80hYuogeWW/wFHwm6loDs6L9VE5Bt
AppTech.MSMS.Domain.TestClass/Client
wFHwm6loDs6L9VE5Bt
Client
KwxXyC7uW2c
get_ID
QiFXyzKxX29
set_ID
o87Xh2VAUlT
get_Name
Ov2XhXQEeFL
set_Name
HIPXhMlHAUq
get_Code
A4bXhjDiaDo
set_Code
f8UXhq8EDpj
get_BaseUrl
IcuXhW9ttWa
set_BaseUrl
lNWXhDDfCOC
<ID>k__BackingField
BuJXhstAjde
<Name>k__BackingField
MZjXh1XJp8E
<Code>k__BackingField
dugXh6vpQdy
<BaseUrl>k__BackingField
SWVXhu8Cqdb
Code
Gv1Xh4C6xhy
BaseUrl
<<type>>
AppTech.MSMS.Domain.TestHelper/<>c__DisplayClass0_0
AppTech.MSMS.Domain.TestHelper/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Topup.TopupFactory/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Topup.TopupFactory/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.JournalEntries.JournalEntryManager/<>c
AppTech.MSMS.Domain.JournalEntries.JournalEntryManager/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Providers.AlAssedApi/<>c__DisplayClass17_0
AppTech.MSMS.Domain.Providers.AlAssedApi/<>c__DisplayClass17_0
<>c__DisplayClass17_0
<>c__DisplayClass17_0
<<type>>
AppTech.MSMS.Domain.Providers.AlAssedApi/<>c__DisplayClass17_1
AppTech.MSMS.Domain.Providers.AlAssedApi/<>c__DisplayClass17_1
<>c__DisplayClass17_1
<>c__DisplayClass17_1
<<type>>
AppTech.MSMS.Domain.Providers.AlAssedApi/<>c
AppTech.MSMS.Domain.Providers.AlAssedApi/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Providers.AlAssedApi/<InqueryAsync>d__23
AppTech.MSMS.Domain.Providers.AlAssedApi/<InqueryAsync>d__23
<InqueryAsync>d__23
<InqueryAsync>d__23
<<type>>
AppTech.MSMS.Domain.Providers.AlAssedApi/<PaymentGomlaAsync>d__28
AppTech.MSMS.Domain.Providers.AlAssedApi/<PaymentGomlaAsync>d__28
<PaymentGomlaAsync>d__28
<PaymentGomlaAsync>d__28
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi/AlbayanServices
AppTech.MSMS.Domain.Providers.AlbayanApi/AlbayanServices
AlbayanServices
AlbayanServices
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi/<PaymentAsync>d__13
AppTech.MSMS.Domain.Providers.AlbayanApi/<PaymentAsync>d__13
<PaymentAsync>d__13
<PaymentAsync>d__13
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi/<MakeBagaAsyn>d__17
AppTech.MSMS.Domain.Providers.AlbayanApi/<MakeBagaAsyn>d__17
<MakeBagaAsyn>d__17
<MakeBagaAsyn>d__17
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi_v2/AlbayanServices
AppTech.MSMS.Domain.Providers.AlbayanApi_v2/AlbayanServices
AlbayanServices
AlbayanServices
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi_v2/<PaymentAsync>d__12
AppTech.MSMS.Domain.Providers.AlbayanApi_v2/<PaymentAsync>d__12
<PaymentAsync>d__12
<PaymentAsync>d__12
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi_v2/<MakeBagaAsyn>d__16
AppTech.MSMS.Domain.Providers.AlbayanApi_v2/<MakeBagaAsyn>d__16
<MakeBagaAsyn>d__16
<MakeBagaAsyn>d__16
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/AbsiOffer
AppTech.MSMS.Domain.Providers.AlhashediApi/AbsiOffer
AbsiOffer
AbsiOffer
yvRXhfj5LEx
<offerId>k__BackingField
HI9XhHskHZ4
<offerName>k__BackingField
rMOXhBI8FDY
<offerStartDate>k__BackingField
PNaXhtAHx5S
<offerEndDate>k__BackingField
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/AbsiOfferResponse
AppTech.MSMS.Domain.Providers.AlhashediApi/AbsiOfferResponse
AbsiOfferResponse
AbsiOfferResponse
FSUXhxaQXEZ
<offers>k__BackingField
MtPXh5GD9SX
<resultCode>k__BackingField
ClCXhr8uYBU
<resultDesc>k__BackingField
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<PaymentAsync>d__6
AppTech.MSMS.Domain.Providers.AlhashediApi/<PaymentAsync>d__6
<PaymentAsync>d__6
<PaymentAsync>d__6
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<PaymentGomlaAsync>d__7
AppTech.MSMS.Domain.Providers.AlhashediApi/<PaymentGomlaAsync>d__7
<PaymentGomlaAsync>d__7
<PaymentGomlaAsync>d__7
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<InqueryAsync>d__18
AppTech.MSMS.Domain.Providers.AlhashediApi/<InqueryAsync>d__18
<InqueryAsync>d__18
<InqueryAsync>d__18
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<MakeBagaAsyn>d__23
AppTech.MSMS.Domain.Providers.AlhashediApi/<MakeBagaAsyn>d__23
<MakeBagaAsyn>d__23
<MakeBagaAsyn>d__23
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<>c__DisplayClass29_0
AppTech.MSMS.Domain.Providers.AlhashediApi/<>c__DisplayClass29_0
<>c__DisplayClass29_0
<>c__DisplayClass29_0
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<>c__DisplayClass29_1
AppTech.MSMS.Domain.Providers.AlhashediApi/<>c__DisplayClass29_1
<>c__DisplayClass29_1
<>c__DisplayClass29_1
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<>c
AppTech.MSMS.Domain.Providers.AlhashediApi/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<QueryYmOffersAsync>d__31
AppTech.MSMS.Domain.Providers.AlhashediApi/<QueryYmOffersAsync>d__31
<QueryYmOffersAsync>d__31
<QueryYmOffersAsync>d__31
<<type>>
AppTech.MSMS.Domain.Providers.EasyConnectAPI/EasyConnectServices
AppTech.MSMS.Domain.Providers.EasyConnectAPI/EasyConnectServices
EasyConnectServices
EasyConnectServices
<<type>>
AppTech.MSMS.Domain.Providers.EasyConnectAPI/<PaymentGomlaAsync>d__18
AppTech.MSMS.Domain.Providers.EasyConnectAPI/<PaymentGomlaAsync>d__18
<PaymentGomlaAsync>d__18
<PaymentGomlaAsync>d__18
<<type>>
AppTech.MSMS.Domain.Providers.EasyConnectAPI/<PaymentAsync>d__19
AppTech.MSMS.Domain.Providers.EasyConnectAPI/<PaymentAsync>d__19
<PaymentAsync>d__19
<PaymentAsync>d__19
<<type>>
AppTech.MSMS.Domain.Providers.EasyConnectAPI/<MakeBagaAsyn>d__20
AppTech.MSMS.Domain.Providers.EasyConnectAPI/<MakeBagaAsyn>d__20
<MakeBagaAsyn>d__20
<MakeBagaAsyn>d__20
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM_YRM/EasySIMServices
AppTech.MSMS.Domain.Providers.EasySIM_YRM/EasySIMServices
EasySIMServices
EasySIMServices
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<PaymentGomlaAsync>d__16
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<PaymentGomlaAsync>d__16
<PaymentGomlaAsync>d__16
<PaymentGomlaAsync>d__16
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<PaymentAsync>d__17
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<PaymentAsync>d__17
<PaymentAsync>d__17
<PaymentAsync>d__17
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<MakeBagaAsyn>d__18
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<MakeBagaAsyn>d__18
<MakeBagaAsyn>d__18
<MakeBagaAsyn>d__18
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/EasySIMServices
AppTech.MSMS.Domain.Providers.GMaxApi/EasySIMServices
EasySIMServices
EasySIMServices
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<PaymentGomlaAsync>d__21
AppTech.MSMS.Domain.Providers.GMaxApi/<PaymentGomlaAsync>d__21
<PaymentGomlaAsync>d__21
<PaymentGomlaAsync>d__21
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<PaymentAsync>d__22
AppTech.MSMS.Domain.Providers.GMaxApi/<PaymentAsync>d__22
<PaymentAsync>d__22
<PaymentAsync>d__22
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<InqueryAsync>d__24
AppTech.MSMS.Domain.Providers.GMaxApi/<InqueryAsync>d__24
<InqueryAsync>d__24
<InqueryAsync>d__24
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<>c__DisplayClass26_0
AppTech.MSMS.Domain.Providers.GMaxApi/<>c__DisplayClass26_0
<>c__DisplayClass26_0
<>c__DisplayClass26_0
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<>c__DisplayClass26_1
AppTech.MSMS.Domain.Providers.GMaxApi/<>c__DisplayClass26_1
<>c__DisplayClass26_1
<>c__DisplayClass26_1
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<>c
AppTech.MSMS.Domain.Providers.GMaxApi/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<MakeBagaAsyn>d__29
AppTech.MSMS.Domain.Providers.GMaxApi/<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM/EasySIMServices
AppTech.MSMS.Domain.Providers.EasySIM/EasySIMServices
EasySIMServices
EasySIMServices
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM/<PaymentGomlaAsync>d__15
AppTech.MSMS.Domain.Providers.EasySIM/<PaymentGomlaAsync>d__15
<PaymentGomlaAsync>d__15
<PaymentGomlaAsync>d__15
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM/<PaymentAsync>d__16
AppTech.MSMS.Domain.Providers.EasySIM/<PaymentAsync>d__16
<PaymentAsync>d__16
<PaymentAsync>d__16
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM/<MakeBagaAsyn>d__17
AppTech.MSMS.Domain.Providers.EasySIM/<MakeBagaAsyn>d__17
<MakeBagaAsyn>d__17
<MakeBagaAsyn>d__17
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/AbsiOffer
AppTech.MSMS.Domain.Providers.AbuOsamaApi/AbsiOffer
AbsiOffer
AbsiOffer
dcuXhG8mEgq
<offerId>k__BackingField
WO6XhgWWovh
<offerName>k__BackingField
hfgXhV5jAWJ
<offerStartDate>k__BackingField
gDdXheQdVbS
<offerEndDate>k__BackingField
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/AbsiOfferResponse
AppTech.MSMS.Domain.Providers.AbuOsamaApi/AbsiOfferResponse
AbsiOfferResponse
AbsiOfferResponse
YqWXhn8jJFS
<offers>k__BackingField
XV1Xh8XXaSc
<resultCode>k__BackingField
mobXhoHiI3q
<resultDesc>k__BackingField
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<MakeBagaAsyn>d__22
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<MakeBagaAsyn>d__22
<MakeBagaAsyn>d__22
<MakeBagaAsyn>d__22
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<PaymentAsync>d__32
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<PaymentAsync>d__32
<PaymentAsync>d__32
<PaymentAsync>d__32
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<InqueryAsync>d__33
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<InqueryAsync>d__33
<InqueryAsync>d__33
<InqueryAsync>d__33
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<BalanceQueryAsync>d__34
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<BalanceQueryAsync>d__34
<BalanceQueryAsync>d__34
<BalanceQueryAsync>d__34
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<QueryYmOffersAsync>d__35
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<QueryYmOffersAsync>d__35
<QueryYmOffersAsync>d__35
<QueryYmOffersAsync>d__35
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<PaymentAsync>d__11
AppTech.MSMS.Domain.Providers.AlConApi/<PaymentAsync>d__11
<PaymentAsync>d__11
<PaymentAsync>d__11
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<QueryAsync>d__13
AppTech.MSMS.Domain.Providers.AlConApi/<QueryAsync>d__13
<QueryAsync>d__13
<QueryAsync>d__13
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<InqueryAsync>d__18
AppTech.MSMS.Domain.Providers.AlConApi/<InqueryAsync>d__18
<InqueryAsync>d__18
<InqueryAsync>d__18
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<PaymentGomlaAsync>d__21
AppTech.MSMS.Domain.Providers.AlConApi/<PaymentGomlaAsync>d__21
<PaymentGomlaAsync>d__21
<PaymentGomlaAsync>d__21
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<MakeBagaAsyn>d__22
AppTech.MSMS.Domain.Providers.AlConApi/<MakeBagaAsyn>d__22
<MakeBagaAsyn>d__22
<MakeBagaAsyn>d__22
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<>c__DisplayClass27_0
AppTech.MSMS.Domain.Providers.AlConApi/<>c__DisplayClass27_0
<>c__DisplayClass27_0
<>c__DisplayClass27_0
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<>c__DisplayClass27_1
AppTech.MSMS.Domain.Providers.AlConApi/<>c__DisplayClass27_1
<>c__DisplayClass27_1
<>c__DisplayClass27_1
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<>c
AppTech.MSMS.Domain.Providers.AlConApi/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Providers.RialMobileAPI/<GetRequestResponseAsync>d__12
AppTech.MSMS.Domain.Providers.RialMobileAPI/<GetRequestResponseAsync>d__12
<GetRequestResponseAsync>d__12
<GetRequestResponseAsync>d__12
<<type>>
AppTech.MSMS.Domain.Providers.RialMobileAPI/<InqueryAsync>d__19
AppTech.MSMS.Domain.Providers.RialMobileAPI/<InqueryAsync>d__19
<InqueryAsync>d__19
<InqueryAsync>d__19
<<type>>
AppTech.MSMS.Domain.Providers.RialMobileAPI/<PaymentAsync>d__23
AppTech.MSMS.Domain.Providers.RialMobileAPI/<PaymentAsync>d__23
<PaymentAsync>d__23
<PaymentAsync>d__23
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/AbsiOffer
AppTech.MSMS.Domain.Providers.SharabiApi/AbsiOffer
AbsiOffer
AbsiOffer
HeOXhTehtPG
<offerId>k__BackingField
r7SXhPasZpq
<offerName>k__BackingField
BT5XhR4fGmB
<offerStartDate>k__BackingField
TY3XhAakWW4
<offerEndDate>k__BackingField
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/AbsiOfferResponse
AppTech.MSMS.Domain.Providers.SharabiApi/AbsiOfferResponse
AbsiOfferResponse
AbsiOfferResponse
O2wXhblVTDi
<offers>k__BackingField
rqTXhIasXJn
<resultCode>k__BackingField
txKXhNNBgMm
<resultDesc>k__BackingField
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/<MakeBagaAsyn>d__22
AppTech.MSMS.Domain.Providers.SharabiApi/<MakeBagaAsyn>d__22
<MakeBagaAsyn>d__22
<MakeBagaAsyn>d__22
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/<PaymentAsync>d__32
AppTech.MSMS.Domain.Providers.SharabiApi/<PaymentAsync>d__32
<PaymentAsync>d__32
<PaymentAsync>d__32
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/<InqueryAsync>d__33
AppTech.MSMS.Domain.Providers.SharabiApi/<InqueryAsync>d__33
<InqueryAsync>d__33
<InqueryAsync>d__33
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/<BalanceQueryAsync>d__34
AppTech.MSMS.Domain.Providers.SharabiApi/<BalanceQueryAsync>d__34
<BalanceQueryAsync>d__34
<BalanceQueryAsync>d__34
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/<QueryYmOffersAsync>d__35
AppTech.MSMS.Domain.Providers.SharabiApi/<QueryYmOffersAsync>d__35
<QueryYmOffersAsync>d__35
<QueryYmOffersAsync>d__35
<<type>>
AppTech.MSMS.Domain.Providers.TopupProviderApi/<QueryAsync>d__34
AppTech.MSMS.Domain.Providers.TopupProviderApi/<QueryAsync>d__34
<QueryAsync>d__34
<QueryAsync>d__34
<<type>>
AppTech.MSMS.Domain.Providers.TopupProviderApi/<>c__DisplayClass40_0
AppTech.MSMS.Domain.Providers.TopupProviderApi/<>c__DisplayClass40_0
<>c__DisplayClass40_0
<>c__DisplayClass40_0
<<type>>
AppTech.MSMS.Domain.Providers.TopupProccessor/<MakePaymentAync>d__3
AppTech.MSMS.Domain.Providers.TopupProccessor/<MakePaymentAync>d__3
<MakePaymentAync>d__3
<MakePaymentAync>d__3
<<type>>
AppTech.MSMS.Domain.Providers.TopupProccessor/<MakeOfferAsync>d__5
AppTech.MSMS.Domain.Providers.TopupProccessor/<MakeOfferAsync>d__5
<MakeOfferAsync>d__5
<MakeOfferAsync>d__5
<<type>>
AppTech.MSMS.Domain.Providers.TopupProccessor/<MakeGomlaAync>d__6
AppTech.MSMS.Domain.Providers.TopupProccessor/<MakeGomlaAync>d__6
<MakeGomlaAync>d__6
<MakeGomlaAync>d__6
<<type>>
AppTech.MSMS.Domain.Providers.TopupProccessor/<QueryAsync>d__7
AppTech.MSMS.Domain.Providers.TopupProccessor/<QueryAsync>d__7
<QueryAsync>d__7
<QueryAsync>d__7
<<type>>
c0MjUnF9jyXPDrlDNy.ok9FISIqLj0HEoKoEK/<InqueryAsync>d__15
AppTech.MSMS.Domain.Topuping.AlanwarAPI/<InqueryAsync>d__15
<InqueryAsync>d__15
<InqueryAsync>d__15
<<type>>
c0MjUnF9jyXPDrlDNy.ok9FISIqLj0HEoKoEK/<PaymentAsync>d__19
AppTech.MSMS.Domain.Topuping.AlanwarAPI/<PaymentAsync>d__19
<PaymentAsync>d__19
<PaymentAsync>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<PaymentAsync>d__6
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<PaymentAsync>d__6
<PaymentAsync>d__6
<PaymentAsync>d__6
<<type>>
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<InqueryAsync>d__12
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<InqueryAsync>d__12
<InqueryAsync>d__12
<InqueryAsync>d__12
<<type>>
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<MakeBagaAsyn>d__17
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<MakeBagaAsyn>d__17
<MakeBagaAsyn>d__17
<MakeBagaAsyn>d__17
<<type>>
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<BalanceQueryAsync>d__25
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<BalanceQueryAsync>d__25
<BalanceQueryAsync>d__25
<BalanceQueryAsync>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<QueryYmOffersAsync>d__26
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<QueryYmOffersAsync>d__26
<QueryYmOffersAsync>d__26
<QueryYmOffersAsync>d__26
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/AlmoheetServices
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/AlmoheetServices
AlmoheetServices
AlmoheetServices
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<PostRequestAsync>d__15
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<PostRequestAsync>d__15
<PostRequestAsync>d__15
<PostRequestAsync>d__15
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<InqueryAsync>d__24
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<InqueryAsync>d__24
<InqueryAsync>d__24
<InqueryAsync>d__24
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<MakeBagaAsyn>d__27
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<MakeBagaAsyn>d__27
<MakeBagaAsyn>d__27
<MakeBagaAsyn>d__27
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<PaymentAsync>d__30
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<PaymentAsync>d__30
<PaymentAsync>d__30
<PaymentAsync>d__30
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<PaymentAsync>d__19
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<PaymentAsync>d__19
<PaymentAsync>d__19
<PaymentAsync>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<QueryAsync>d__21
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<QueryAsync>d__21
<QueryAsync>d__21
<QueryAsync>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<InqueryAsync>d__23
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<InqueryAsync>d__23
<InqueryAsync>d__23
<InqueryAsync>d__23
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<InqueryAsync>d__25
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<InqueryAsync>d__25
<InqueryAsync>d__25
<InqueryAsync>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<MakeBagaAsyn>d__29
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<PaymentAsync>d__19
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<PaymentAsync>d__19
<PaymentAsync>d__19
<PaymentAsync>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<QueryAsync>d__21
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<QueryAsync>d__21
<QueryAsync>d__21
<QueryAsync>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<InqueryAsync>d__23
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<InqueryAsync>d__23
<InqueryAsync>d__23
<InqueryAsync>d__23
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<InqueryAsync>d__25
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<InqueryAsync>d__25
<InqueryAsync>d__25
<InqueryAsync>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<MakeBagaAsyn>d__29
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<<type>>
AppTech.MSMS.Domain.Topuping.AlSareeaOnLineApi/activeSIMType_IDs
AppTech.MSMS.Domain.Topuping.AlSareeaOnLineApi/activeSIMType_IDs
activeSIMType_IDs
activeSIMType_IDs
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi_v2/<MakeBagaAsyn>d__2
AppTech.MSMS.Domain.Topuping.AppTechApi_v2/<MakeBagaAsyn>d__2
<MakeBagaAsyn>d__2
<MakeBagaAsyn>d__2
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi/AtheerServices
AppTech.MSMS.Domain.Topuping.AtheerApi/AtheerServices
AtheerServices
AtheerServices
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi/<QueryAsync>d__18
AppTech.MSMS.Domain.Topuping.AtheerApi/<QueryAsync>d__18
<QueryAsync>d__18
<QueryAsync>d__18
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi/<MakeBagaAsyn>d__19
AppTech.MSMS.Domain.Topuping.AtheerApi/<MakeBagaAsyn>d__19
<MakeBagaAsyn>d__19
<MakeBagaAsyn>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi/<PaymentAsync>d__23
AppTech.MSMS.Domain.Topuping.AtheerApi/<PaymentAsync>d__23
<PaymentAsync>d__23
<PaymentAsync>d__23
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi/<PaymentGomlaAsync>d__24
AppTech.MSMS.Domain.Topuping.AtheerApi/<PaymentGomlaAsync>d__24
<PaymentGomlaAsync>d__24
<PaymentGomlaAsync>d__24
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi/<InqueryAsync>d__28
AppTech.MSMS.Domain.Topuping.AtheerApi/<InqueryAsync>d__28
<InqueryAsync>d__28
<InqueryAsync>d__28
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South/AtheerServices
AppTech.MSMS.Domain.Topuping.AtheerApi_South/AtheerServices
AtheerServices
AtheerServices
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<InqueryAsync>d__20
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<InqueryAsync>d__20
<InqueryAsync>d__20
<InqueryAsync>d__20
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<MakeBagaAsyn>d__21
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<PaymentAsync>d__25
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<PaymentAsync>d__25
<PaymentAsync>d__25
<PaymentAsync>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<PaymentGomlaAsync>d__26
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<PaymentGomlaAsync>d__26
<PaymentGomlaAsync>d__26
<PaymentGomlaAsync>d__26
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/AtheerServices
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/AtheerServices
AtheerServices
AtheerServices
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<InqueryAsync>d__20
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<InqueryAsync>d__20
<InqueryAsync>d__20
<InqueryAsync>d__20
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<MakeBagaAsyn>d__21
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<PaymentAsync>d__25
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<PaymentAsync>d__25
<PaymentAsync>d__25
<PaymentAsync>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<PaymentGomlaAsync>d__26
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<PaymentGomlaAsync>d__26
<PaymentGomlaAsync>d__26
<PaymentGomlaAsync>d__26
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/AtheerServices
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/AtheerServices
AtheerServices
AtheerServices
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<InqueryAsync>d__18
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<InqueryAsync>d__18
<InqueryAsync>d__18
<InqueryAsync>d__18
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<MakeBagaAsyn>d__19
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<MakeBagaAsyn>d__19
<MakeBagaAsyn>d__19
<MakeBagaAsyn>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<PaymentAsync>d__23
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<PaymentAsync>d__23
<PaymentAsync>d__23
<PaymentAsync>d__23
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<PaymentGomlaAsync>d__24
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<PaymentGomlaAsync>d__24
<PaymentGomlaAsync>d__24
<PaymentGomlaAsync>d__24
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/AtheerServices
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/AtheerServices
AtheerServices
AtheerServices
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<>c__DisplayClass14_0
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<>c__DisplayClass14_0
<>c__DisplayClass14_0
<>c__DisplayClass14_0
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<>c
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<InqueryAsync>d__22
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<InqueryAsync>d__22
<InqueryAsync>d__22
<InqueryAsync>d__22
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<MakeBagaAsyn>d__23
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<MakeBagaAsyn>d__23
<MakeBagaAsyn>d__23
<MakeBagaAsyn>d__23
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<PaymentAsync>d__27
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<PaymentAsync>d__27
<PaymentAsync>d__27
<PaymentAsync>d__27
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<PaymentGomlaAsync>d__28
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<PaymentGomlaAsync>d__28
<PaymentGomlaAsync>d__28
<PaymentGomlaAsync>d__28
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Topuping.BaAmerApi/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi/<PaymentAsync>d__19
AppTech.MSMS.Domain.Topuping.BaAmerApi/<PaymentAsync>d__19
<PaymentAsync>d__19
<PaymentAsync>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi/<QueryAsync>d__21
AppTech.MSMS.Domain.Topuping.BaAmerApi/<QueryAsync>d__21
<QueryAsync>d__21
<QueryAsync>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi/<InqueryAsync>d__23
AppTech.MSMS.Domain.Topuping.BaAmerApi/<InqueryAsync>d__23
<InqueryAsync>d__23
<InqueryAsync>d__23
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi/<InqueryAsync>d__25
AppTech.MSMS.Domain.Topuping.BaAmerApi/<InqueryAsync>d__25
<InqueryAsync>d__25
<InqueryAsync>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi/<MakeBagaAsyn>d__29
AppTech.MSMS.Domain.Topuping.BaAmerApi/<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<<type>>
AppTech.MSMS.Domain.Topuping.FrenchiApi/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Topuping.FrenchiApi/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.MSMS.Domain.Topuping.ForMeAPI/CSDServices
AppTech.MSMS.Domain.Topuping.ForMeAPI/CSDServices
CSDServices
CSDServices
<<type>>
AppTech.MSMS.Domain.Topuping.ForMeAPI/<PaymentGomlaAsync>d__17
AppTech.MSMS.Domain.Topuping.ForMeAPI/<PaymentGomlaAsync>d__17
<PaymentGomlaAsync>d__17
<PaymentGomlaAsync>d__17
<<type>>
AppTech.MSMS.Domain.Topuping.ForMeAPI/<PaymentAsync>d__24
AppTech.MSMS.Domain.Topuping.ForMeAPI/<PaymentAsync>d__24
<PaymentAsync>d__24
<PaymentAsync>d__24
<<type>>
AppTech.MSMS.Domain.Topuping.ForMeAPI/<MakeBagaAsyn>d__25
AppTech.MSMS.Domain.Topuping.ForMeAPI/<MakeBagaAsyn>d__25
<MakeBagaAsyn>d__25
<MakeBagaAsyn>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.ForMeAPI/<InqueryAsync>d__26
AppTech.MSMS.Domain.Topuping.ForMeAPI/<InqueryAsync>d__26
<InqueryAsync>d__26
<InqueryAsync>d__26
<<type>>
syYWI9hEvY44C7NHCm.y2FZ7yxw4qJcqxB11H/ofBFy1i6d7AvJNEEBJ
AppTech.MSMS.Domain.Topuping.JuzaifaAPI/JuzaifaServices
ofBFy1i6d7AvJNEEBJ
JuzaifaServices
<<type>>
syYWI9hEvY44C7NHCm.y2FZ7yxw4qJcqxB11H/<PaymentAsync>d__22
AppTech.MSMS.Domain.Topuping.JuzaifaAPI/<PaymentAsync>d__22
<PaymentAsync>d__22
<PaymentAsync>d__22
<<type>>
AppTech.MSMS.Domain.Topuping.MainCenterApi/<PaymentAsync>d__7
AppTech.MSMS.Domain.Topuping.MainCenterApi/<PaymentAsync>d__7
<PaymentAsync>d__7
<PaymentAsync>d__7
<<type>>
AppTech.MSMS.Domain.Topuping.MainCenterApi/<PaymentGomlaAsync>d__9
AppTech.MSMS.Domain.Topuping.MainCenterApi/<PaymentGomlaAsync>d__9
<PaymentGomlaAsync>d__9
<PaymentGomlaAsync>d__9
<<type>>
AppTech.MSMS.Domain.Topuping.MainCenterApi/<MakeBagaAsyn>d__12
AppTech.MSMS.Domain.Topuping.MainCenterApi/<MakeBagaAsyn>d__12
<MakeBagaAsyn>d__12
<MakeBagaAsyn>d__12
<<type>>
AppTech.MSMS.Domain.Topuping.MainCenterApi/<QueryAsync>d__15
AppTech.MSMS.Domain.Topuping.MainCenterApi/<QueryAsync>d__15
<QueryAsync>d__15
<QueryAsync>d__15
<<type>>
AppTech.MSMS.Domain.Topuping.MainCenterApi/<InqueryAsync>d__21
AppTech.MSMS.Domain.Topuping.MainCenterApi/<InqueryAsync>d__21
<InqueryAsync>d__21
<InqueryAsync>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi/<PaymentAsync>d__4
AppTech.MSMS.Domain.Topuping.AppTechApi/<PaymentAsync>d__4
<PaymentAsync>d__4
<PaymentAsync>d__4
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi/<PaymentGomlaAsync>d__5
AppTech.MSMS.Domain.Topuping.AppTechApi/<PaymentGomlaAsync>d__5
<PaymentGomlaAsync>d__5
<PaymentGomlaAsync>d__5
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi/<MakeBagaAsyn>d__6
AppTech.MSMS.Domain.Topuping.AppTechApi/<MakeBagaAsyn>d__6
<MakeBagaAsyn>d__6
<MakeBagaAsyn>d__6
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi/<QueryAsync>d__7
AppTech.MSMS.Domain.Topuping.AppTechApi/<QueryAsync>d__7
<QueryAsync>d__7
<QueryAsync>d__7
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi/<InqueryAsync>d__8
AppTech.MSMS.Domain.Topuping.AppTechApi/<InqueryAsync>d__8
<InqueryAsync>d__8
<InqueryAsync>d__8
<<type>>
AppTech.MSMS.Domain.Topuping.OnsSoftApi/AtheerServices
AppTech.MSMS.Domain.Topuping.OnsSoftApi/AtheerServices
AtheerServices
AtheerServices
<<type>>
AppTech.MSMS.Domain.Topuping.OnsSoftApi/<MakeBagaAsyn>d__15
AppTech.MSMS.Domain.Topuping.OnsSoftApi/<MakeBagaAsyn>d__15
<MakeBagaAsyn>d__15
<MakeBagaAsyn>d__15
<<type>>
AppTech.MSMS.Domain.Topuping.OnsSoftApi/<PaymentAsync>d__17
AppTech.MSMS.Domain.Topuping.OnsSoftApi/<PaymentAsync>d__17
<PaymentAsync>d__17
<PaymentAsync>d__17
<<type>>
AppTech.MSMS.Domain.Topuping.QulaidiApi/<InqueryAsync>d__6
AppTech.MSMS.Domain.Topuping.QulaidiApi/<InqueryAsync>d__6
<InqueryAsync>d__6
<InqueryAsync>d__6
<<type>>
AppTech.MSMS.Domain.Topuping.QulaidiApi/<MakeBagaAsyn>d__7
AppTech.MSMS.Domain.Topuping.QulaidiApi/<MakeBagaAsyn>d__7
<MakeBagaAsyn>d__7
<MakeBagaAsyn>d__7
<<type>>
AppTech.MSMS.Domain.Topuping.QulaidiApi/<PaymentAsync>d__10
AppTech.MSMS.Domain.Topuping.QulaidiApi/<PaymentAsync>d__10
<PaymentAsync>d__10
<PaymentAsync>d__10
<<type>>
AppTech.MSMS.Domain.Topuping.QulaidiApi/<PaymentGomlaAsync>d__11
AppTech.MSMS.Domain.Topuping.QulaidiApi/<PaymentGomlaAsync>d__11
<PaymentGomlaAsync>d__11
<PaymentGomlaAsync>d__11
<<type>>
AppTech.MSMS.Domain.Topuping.TadawulApi/<PaymentAsync>d__19
AppTech.MSMS.Domain.Topuping.TadawulApi/<PaymentAsync>d__19
<PaymentAsync>d__19
<PaymentAsync>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.TadawulApi/<MakeBagaAsyn>d__21
AppTech.MSMS.Domain.Topuping.TadawulApi/<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<QueryAsync>d__5
AppTech.MSMS.Domain.Topuping.TopupHelper/<QueryAsync>d__5
<QueryAsync>d__5
<QueryAsync>d__5
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<QueryYmBalAndOffers>d__7
AppTech.MSMS.Domain.Topuping.TopupHelper/<QueryYmBalAndOffers>d__7
<QueryYmBalAndOffers>d__7
<QueryYmBalAndOffers>d__7
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass9_1
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass9_1
<>c__DisplayClass9_1
<>c__DisplayClass9_1
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass10_1
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass10_1
<>c__DisplayClass10_1
<>c__DisplayClass10_1
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass17_0
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass17_0
<>c__DisplayClass17_0
<>c__DisplayClass17_0
<<type>>
AppTech.MSMS.Domain.Topuping.YemenPostAPI/YemenPostServices
AppTech.MSMS.Domain.Topuping.YemenPostAPI/YemenPostServices
YemenPostServices
YemenPostServices
<<type>>
AppTech.MSMS.Domain.Topuping.YemenPostAPI/<QueryAsync>d__31
AppTech.MSMS.Domain.Topuping.YemenPostAPI/<QueryAsync>d__31
<QueryAsync>d__31
<QueryAsync>d__31
<<type>>
AppTech.MSMS.Domain.Topuping.YemenPostAPI/<MakeBagaAsyn>d__34
AppTech.MSMS.Domain.Topuping.YemenPostAPI/<MakeBagaAsyn>d__34
<MakeBagaAsyn>d__34
<MakeBagaAsyn>d__34
<<type>>
AppTech.MSMS.Domain.Topuping.YemenPostAPI/<PaymentAsync>d__38
AppTech.MSMS.Domain.Topuping.YemenPostAPI/<PaymentAsync>d__38
<PaymentAsync>d__38
<PaymentAsync>d__38
<<type>>
AppTech.MSMS.Domain.Topuping.YemenSaeedApi/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Topuping.YemenSaeedApi/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure/<>c__DisplayClass0_1
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure/<>c__DisplayClass0_1
<>c__DisplayClass0_1
<>c__DisplayClass0_1
<<type>>
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure/<>c
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Sync.EbsAccountService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Sync.EbsAccountService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Sync.EbsAccountService/<>c__DisplayClass1_0
AppTech.MSMS.Domain.Sync.EbsAccountService/<>c__DisplayClass1_0
<>c__DisplayClass1_0
<>c__DisplayClass1_0
<<type>>
AppTech.MSMS.Domain.Sync.RemittanceSync/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Sync.RemittanceSync/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Sync.RemittanceSync/<>c
AppTech.MSMS.Domain.Sync.RemittanceSync/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Sync.SyncJournalManager/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Sync.SyncJournalManager/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass16_0
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass16_0
<>c__DisplayClass16_0
<>c__DisplayClass16_0
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass17_0
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass17_0
<>c__DisplayClass17_0
<>c__DisplayClass17_0
<<type>>
AppTech.MSMS.Domain.Sessions.AgentSession/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Sessions.AgentSession/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Sessions.AgentSession/<>c__DisplayClass5_1
AppTech.MSMS.Domain.Sessions.AgentSession/<>c__DisplayClass5_1
<>c__DisplayClass5_1
<>c__DisplayClass5_1
<<type>>
AppTech.MSMS.Domain.Sessions.MerchantSession/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Sessions.MerchantSession/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Rest.GenericApi/<>c__1`1
AppTech.MSMS.Domain.Rest.GenericApi/<>c__1`1
<>c__1`1
<>c__1`1
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest/<>c
AppTech.MSMS.Domain.Rest.ApiRequest/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest/<PostAsync>d__16
AppTech.MSMS.Domain.Rest.ApiRequest/<PostAsync>d__16
<PostAsync>d__16
<PostAsync>d__16
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest/<GetAysnc>d__17
AppTech.MSMS.Domain.Rest.ApiRequest/<GetAysnc>d__17
<GetAysnc>d__17
<GetAysnc>d__17
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest/<GetAysnc>d__18
AppTech.MSMS.Domain.Rest.ApiRequest/<GetAysnc>d__18
<GetAysnc>d__18
<GetAysnc>d__18
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest/<PostRequest_FormAsync>d__19
AppTech.MSMS.Domain.Rest.ApiRequest/<PostRequest_FormAsync>d__19
<PostRequest_FormAsync>d__19
<PostRequest_FormAsync>d__19
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest/<PostRequest_FormAsync2>d__20
AppTech.MSMS.Domain.Rest.ApiRequest/<PostRequest_FormAsync2>d__20
<PostRequest_FormAsync2>d__20
<PostRequest_FormAsync2>d__20
<<type>>
AppTech.MSMS.Domain.Reports.AccountReport/<>c
AppTech.MSMS.Domain.Reports.AccountReport/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Reports.AgentsReport/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Reports.AgentsReport/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Reports.PointsBalanceReport/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Reports.PointsBalanceReport/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Reports.WalletReport/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Reports.WalletReport/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Reports.TopupReport/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Reports.TopupReport/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Reports.TopupReport/<>c
AppTech.MSMS.Domain.Reports.TopupReport/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Reports.TransactionReport/<>c__DisplayClass1_0
AppTech.MSMS.Domain.Reports.TransactionReport/<>c__DisplayClass1_0
<>c__DisplayClass1_0
<>c__DisplayClass1_0
<<type>>
AppTech.MSMS.Domain.Reports.BalanceSheetReport/BalanceSheet
AppTech.MSMS.Domain.Reports.BalanceSheetReport/BalanceSheet
BalanceSheet
BalanceSheet
xHxXhknay5n
<ParentID>k__BackingField
G2vXh7fGZdY
<AccountID>k__BackingField
c5QXhcGcrrD
<CurrencyID>k__BackingField
U1VXhisrp9o
<VoucherID>k__BackingField
qnHXhdvbVKb
<Type>k__BackingField
hISXhwbS8xb
<NoPrevBalance>k__BackingField
kyhXhyrogwx
<GroupbyVoucher>k__BackingField
crwXhhhZvku
<BalanceState>k__BackingField
NkqXhFELQZi
<TotalDain>k__BackingField
k1QXhl7xVf8
<IsCredited>k__BackingField
wu4XhYZoDSS
<Balance>k__BackingField
<<type>>
AppTech.MSMS.Domain.Remittances.SyncRemittanceOutService/<>c
AppTech.MSMS.Domain.Remittances.SyncRemittanceOutService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.AccountBindService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Remittances.Intilization.AccountBindService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Notifications.Firebase/<Notify>d__0
AppTech.MSMS.Domain.Notifications.Firebase/<Notify>d__0
<Notify>d__0
<Notify>d__0
<<type>>
AppTech.MSMS.Domain.Notifications.Firebase/<Fire>d__1
AppTech.MSMS.Domain.Notifications.Firebase/<Fire>d__1
<Fire>d__1
<Fire>d__1
<<type>>
AppTech.MSMS.Domain.Notifications.FmcProvider/<NotifyTopicAsync>d__3
AppTech.MSMS.Domain.Notifications.FmcProvider/<NotifyTopicAsync>d__3
<NotifyTopicAsync>d__3
<NotifyTopicAsync>d__3
<<type>>
AppTech.MSMS.Domain.Notifications.FmcProvider/<NotifyAsync>d__4
AppTech.MSMS.Domain.Notifications.FmcProvider/<NotifyAsync>d__4
<NotifyAsync>d__4
<NotifyAsync>d__4
<<type>>
AppTech.MSMS.Domain.Services.DistributorService/<>c
AppTech.MSMS.Domain.Services.DistributorService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass20_0
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass20_0
<>c__DisplayClass20_0
<>c__DisplayClass20_0
<<type>>
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass21_0
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass21_0
<>c__DisplayClass21_0
<>c__DisplayClass21_0
<<type>>
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass22_0
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass22_0
<>c__DisplayClass22_0
<>c__DisplayClass22_0
<<type>>
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass22_1
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass22_1
<>c__DisplayClass22_1
<>c__DisplayClass22_1
<<type>>
AppTech.MSMS.Domain.Services.CurrencyRateAccountService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.CurrencyRateAccountService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.CurrencyRateAccountService/<>c
AppTech.MSMS.Domain.Services.CurrencyRateAccountService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.AsyncBagatService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.AsyncBagatService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.AsyncBagatService/<ExecuteAsync>d__0
AppTech.MSMS.Domain.Services.AsyncBagatService/<ExecuteAsync>d__0
<ExecuteAsync>d__0
<ExecuteAsync>d__0
<<type>>
AppTech.MSMS.Domain.Services.AsyncTopupService/<ExecuteAsync>d__0
AppTech.MSMS.Domain.Services.AsyncTopupService/<ExecuteAsync>d__0
<ExecuteAsync>d__0
<ExecuteAsync>d__0
<<type>>
AppTech.MSMS.Domain.Services.AsyncTopupService/<ProccessByProviderAsync>d__2
AppTech.MSMS.Domain.Services.AsyncTopupService/<ProccessByProviderAsync>d__2
<ProccessByProviderAsync>d__2
<ProccessByProviderAsync>d__2
<<type>>
AppTech.MSMS.Domain.Services.AsyncTopupService/<ExecuteTopupAsync>d__3
AppTech.MSMS.Domain.Services.AsyncTopupService/<ExecuteTopupAsync>d__3
<ExecuteTopupAsync>d__3
<ExecuteTopupAsync>d__3
<<type>>
AppTech.MSMS.Domain.Services.AsyncTopupService/<ExecuteBagatAsync>d__4
AppTech.MSMS.Domain.Services.AsyncTopupService/<ExecuteBagatAsync>d__4
<ExecuteBagatAsync>d__4
<ExecuteBagatAsync>d__4
<<type>>
AppTech.MSMS.Domain.Services.BankDepositService/<>c
AppTech.MSMS.Domain.Services.BankDepositService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ConsumeInvoiceService/<>c
AppTech.MSMS.Domain.Services.ConsumeInvoiceService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.DeviceService/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Services.DeviceService/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Services.DeviceService/<>c
AppTech.MSMS.Domain.Services.DeviceService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ExchangerCommissionService/<>c
AppTech.MSMS.Domain.Services.ExchangerCommissionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.GroupService/GroupItemService
AppTech.MSMS.Domain.Services.GroupService/GroupItemService
GroupItemService
GroupItemService
<<type>>
AppTech.MSMS.Domain.Services.GroupService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.GroupService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.GroupService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.GroupService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.PartyService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.PartyService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.ProviderCommissionService/<>c
AppTech.MSMS.Domain.Services.ProviderCommissionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.CommissionReceiptService/<>c
AppTech.MSMS.Domain.Services.CommissionReceiptService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.CommissionReceiptService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.CommissionReceiptService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.CommissionReceiptService/<>c__DisplayClass5_1
AppTech.MSMS.Domain.Services.CommissionReceiptService/<>c__DisplayClass5_1
<>c__DisplayClass5_1
<>c__DisplayClass5_1
<<type>>
AppTech.MSMS.Domain.Services.GsmService/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Services.GsmService/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Services.TransferCommissionService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.TransferCommissionService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.TransferCommissionService/<>c
AppTech.MSMS.Domain.Services.TransferCommissionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TransferOutService/<>c
AppTech.MSMS.Domain.Services.TransferOutService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TransportOrderService/<>c
AppTech.MSMS.Domain.Services.TransportOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.UserDeviceService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.UserDeviceService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.WifiCardService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.WifiCardService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.WifiCardService/<>c__DisplayClass6_1
AppTech.MSMS.Domain.Services.WifiCardService/<>c__DisplayClass6_1
<>c__DisplayClass6_1
<>c__DisplayClass6_1
<<type>>
AppTech.MSMS.Domain.Services.WifiCardService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.WifiCardService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.WifiFactionService/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Services.WifiFactionService/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Services.WifiFactionService/<>c__DisplayClass12_0
AppTech.MSMS.Domain.Services.WifiFactionService/<>c__DisplayClass12_0
<>c__DisplayClass12_0
<>c__DisplayClass12_0
<<type>>
AppTech.MSMS.Domain.Services.WifiFactionService/<>c__DisplayClass12_1
AppTech.MSMS.Domain.Services.WifiFactionService/<>c__DisplayClass12_1
<>c__DisplayClass12_1
<>c__DisplayClass12_1
<<type>>
AppTech.MSMS.Domain.Services.WifiFactionService/<>c
AppTech.MSMS.Domain.Services.WifiFactionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.BuyCurrencyService/<>c
AppTech.MSMS.Domain.Services.BuyCurrencyService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.LoanOrderService/<>c
AppTech.MSMS.Domain.Services.LoanOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.LoanOrderService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.LoanOrderService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass14_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass14_0
<>c__DisplayClass14_0
<>c__DisplayClass14_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass16_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass16_0
<>c__DisplayClass16_0
<>c__DisplayClass16_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass18_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass18_0
<>c__DisplayClass18_0
<>c__DisplayClass18_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass18_1
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass18_1
<>c__DisplayClass18_1
<>c__DisplayClass18_1
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass20_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass20_0
<>c__DisplayClass20_0
<>c__DisplayClass20_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass23_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass23_0
<>c__DisplayClass23_0
<>c__DisplayClass23_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass24_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass24_0
<>c__DisplayClass24_0
<>c__DisplayClass24_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass32_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass32_0
<>c__DisplayClass32_0
<>c__DisplayClass32_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c
AppTech.MSMS.Domain.Services.AccountService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass39_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass39_0
<>c__DisplayClass39_0
<>c__DisplayClass39_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass42_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass42_0
<>c__DisplayClass42_0
<>c__DisplayClass42_0
<<type>>
AppTech.MSMS.Domain.Services.AccountSlatingService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.AccountSlatingService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.AgentService/<>c
AppTech.MSMS.Domain.Services.AgentService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass20_0
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass20_0
<>c__DisplayClass20_0
<>c__DisplayClass20_0
<<type>>
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass21_0
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass21_0
<>c__DisplayClass21_0
<>c__DisplayClass21_0
<<type>>
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass22_0
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass22_0
<>c__DisplayClass22_0
<>c__DisplayClass22_0
<<type>>
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass23_0
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass23_0
<>c__DisplayClass23_0
<>c__DisplayClass23_0
<<type>>
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass23_1
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass23_1
<>c__DisplayClass23_1
<>c__DisplayClass23_1
<<type>>
AppTech.MSMS.Domain.Services.AgentPointUserService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.AgentPointUserService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_0
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_0
<>c__DisplayClass16_0
<>c__DisplayClass16_0
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_1
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_1
<>c__DisplayClass16_1
<>c__DisplayClass16_1
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_2
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_2
<>c__DisplayClass16_2
<>c__DisplayClass16_2
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_3
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_3
<>c__DisplayClass16_3
<>c__DisplayClass16_3
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_0
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_0
<>c__DisplayClass17_0
<>c__DisplayClass17_0
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_1
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_1
<>c__DisplayClass17_1
<>c__DisplayClass17_1
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_2
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_2
<>c__DisplayClass17_2
<>c__DisplayClass17_2
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_3
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_3
<>c__DisplayClass17_3
<>c__DisplayClass17_3
<<type>>
AppTech.MSMS.Domain.Services.FundUserService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.FundUserService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.FundUserService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.FundUserService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass17_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass17_0
<>c__DisplayClass17_0
<>c__DisplayClass17_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c
AppTech.MSMS.Domain.Services.ClientService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass37_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass37_0
<>c__DisplayClass37_0
<>c__DisplayClass37_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass37_1
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass37_1
<>c__DisplayClass37_1
<>c__DisplayClass37_1
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass38_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass38_0
<>c__DisplayClass38_0
<>c__DisplayClass38_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass38_1
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass38_1
<>c__DisplayClass38_1
<>c__DisplayClass38_1
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass39_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass39_0
<>c__DisplayClass39_0
<>c__DisplayClass39_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass39_1
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass39_1
<>c__DisplayClass39_1
<>c__DisplayClass39_1
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass41_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass41_0
<>c__DisplayClass41_0
<>c__DisplayClass41_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass42_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass42_0
<>c__DisplayClass42_0
<>c__DisplayClass42_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass43_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass43_0
<>c__DisplayClass43_0
<>c__DisplayClass43_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass44_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass44_0
<>c__DisplayClass44_0
<>c__DisplayClass44_0
<<type>>
AppTech.MSMS.Domain.Services.Notification/<>c__DisplayClass14_0
AppTech.MSMS.Domain.Services.Notification/<>c__DisplayClass14_0
<>c__DisplayClass14_0
<>c__DisplayClass14_0
<<type>>
AppTech.MSMS.Domain.Services.Notification/<>c__DisplayClass15_0
AppTech.MSMS.Domain.Services.Notification/<>c__DisplayClass15_0
<>c__DisplayClass15_0
<>c__DisplayClass15_0
<<type>>
AppTech.MSMS.Domain.Services.OfferOrderService/<>c
AppTech.MSMS.Domain.Services.OfferOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.SaleCurrencyService/<>c
AppTech.MSMS.Domain.Services.SaleCurrencyService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass8_1
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass8_1
<>c__DisplayClass8_1
<>c__DisplayClass8_1
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Services.CurrencyExchangeService/<>c__DisplayClass21_0
AppTech.MSMS.Domain.Services.CurrencyExchangeService/<>c__DisplayClass21_0
<>c__DisplayClass21_0
<>c__DisplayClass21_0
<<type>>
AppTech.MSMS.Domain.Services.CurrencyRateService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.CurrencyRateService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.DepositOrderService/<>c
AppTech.MSMS.Domain.Services.DepositOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.JournalService/<>c
AppTech.MSMS.Domain.Services.JournalService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.JournalService/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Services.JournalService/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Services.JournalService/<>c__DisplayClass12_0
AppTech.MSMS.Domain.Services.JournalService/<>c__DisplayClass12_0
<>c__DisplayClass12_0
<>c__DisplayClass12_0
<<type>>
AppTech.MSMS.Domain.Services.JournalEntryService/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Services.JournalEntryService/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.MSMS.Domain.Services.BasicFactionService/<>c
AppTech.MSMS.Domain.Services.BasicFactionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.FactionService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.FactionService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.FactionService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.FactionService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.FactionService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.FactionService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.FundService/<>c__DisplayClass12_0
AppTech.MSMS.Domain.Services.FundService/<>c__DisplayClass12_0
<>c__DisplayClass12_0
<>c__DisplayClass12_0
<<type>>
AppTech.MSMS.Domain.Services.FundService/<>c__DisplayClass13_0
AppTech.MSMS.Domain.Services.FundService/<>c__DisplayClass13_0
<>c__DisplayClass13_0
<>c__DisplayClass13_0
<<type>>
AppTech.MSMS.Domain.Services.MerchantService/<>c
AppTech.MSMS.Domain.Services.MerchantService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.MerchantPaymentService/MerPayResult
AppTech.MSMS.Domain.Services.MerchantPaymentService/MerPayResult
MerPayResult
MerPayResult
YXYXhJjvuut
<ID>k__BackingField
OJdXhKqS1JE
<Number>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.TopupNetworkService/Networks
AppTech.MSMS.Domain.Services.TopupNetworkService/Networks
Networks
Networks
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass19_0
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass19_0
<>c__DisplayClass19_0
<>c__DisplayClass19_0
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass36_0
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass36_0
<>c__DisplayClass36_0
<>c__DisplayClass36_0
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_0
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_0
<>c__DisplayClass40_0
<>c__DisplayClass40_0
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_1
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_1
<>c__DisplayClass40_1
<>c__DisplayClass40_1
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_2
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_2
<>c__DisplayClass40_2
<>c__DisplayClass40_2
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_3
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_3
<>c__DisplayClass40_3
<>c__DisplayClass40_3
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_4
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_4
<>c__DisplayClass40_4
<>c__DisplayClass40_4
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_5
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_5
<>c__DisplayClass40_5
<>c__DisplayClass40_5
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c
AppTech.MSMS.Domain.Services.OrderInfoService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass1_0
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass1_0
<>c__DisplayClass1_0
<>c__DisplayClass1_0
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c
AppTech.MSMS.Domain.Services.PageService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass2_1
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass2_1
<>c__DisplayClass2_1
<>c__DisplayClass2_1
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass2_2
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass2_2
<>c__DisplayClass2_2
<>c__DisplayClass2_2
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass3_1
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass3_1
<>c__DisplayClass3_1
<>c__DisplayClass3_1
<<type>>
AppTech.MSMS.Domain.Services.PersonalInfoService/<>c__DisplayClass1_0
AppTech.MSMS.Domain.Services.PersonalInfoService/<>c__DisplayClass1_0
<>c__DisplayClass1_0
<>c__DisplayClass1_0
<<type>>
AppTech.MSMS.Domain.Services.SimInvoiceService/<>c
AppTech.MSMS.Domain.Services.SimInvoiceService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.SimInvoiceService/<>c__DisplayClass15_0
AppTech.MSMS.Domain.Services.SimInvoiceService/<>c__DisplayClass15_0
<>c__DisplayClass15_0
<>c__DisplayClass15_0
<<type>>
AppTech.MSMS.Domain.Services.TopupCommissionService/<>c
AppTech.MSMS.Domain.Services.TopupCommissionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupService/<>c
AppTech.MSMS.Domain.Services.TopupService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupOrderService/<>c
AppTech.MSMS.Domain.Services.TopupOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Registeration/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.Registeration/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.RemittanceCommissionService/CommissionRequest
AppTech.MSMS.Domain.Services.RemittanceCommissionService/CommissionRequest
CommissionRequest
CommissionRequest
UllXh9Nbt7h
<IsSending>k__BackingField
vSEXhOR8gev
<CurrencyID>k__BackingField
kimXhUUO57x
<ExchangerID>k__BackingField
tsBXhEby4xE
<AccountID>k__BackingField
eiiXhSPchIb
<Amount>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c__DisplayClass8_1
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c__DisplayClass8_1
<>c__DisplayClass8_1
<>c__DisplayClass8_1
<<type>>
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c__DisplayClass19_0
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c__DisplayClass19_0
<>c__DisplayClass19_0
<>c__DisplayClass19_0
<<type>>
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c__DisplayClass20_0
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c__DisplayClass20_0
<>c__DisplayClass20_0
<>c__DisplayClass20_0
<<type>>
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c__DisplayClass21_0
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c__DisplayClass21_0
<>c__DisplayClass21_0
<>c__DisplayClass21_0
<<type>>
AppTech.MSMS.Domain.Services.PaymentCommissionService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.PaymentCommissionService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.PaymentCommissionService/<>c
AppTech.MSMS.Domain.Services.PaymentCommissionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass11_1
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass11_1
<>c__DisplayClass11_1
<>c__DisplayClass11_1
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass12_0
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass12_0
<>c__DisplayClass12_0
<>c__DisplayClass12_0
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService/<>c
AppTech.MSMS.Domain.Services.TopupProviderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass14_0
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass14_0
<>c__DisplayClass14_0
<>c__DisplayClass14_0
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass15_0
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass15_0
<>c__DisplayClass15_0
<>c__DisplayClass15_0
<<type>>
AppTech.MSMS.Domain.Services.LiveTopupService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.LiveTopupService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.LiveTopupService/<>c
AppTech.MSMS.Domain.Services.LiveTopupService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.LiveTopupService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.LiveTopupService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.LiveTopupService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.LiveTopupService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Services.BagatService/<>c
AppTech.MSMS.Domain.Services.BagatService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ServiceInfoService/<>c
AppTech.MSMS.Domain.Services.ServiceInfoService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ServiceInfoService/<>c__DisplayClass12_0
AppTech.MSMS.Domain.Services.ServiceInfoService/<>c__DisplayClass12_0
<>c__DisplayClass12_0
<>c__DisplayClass12_0
<<type>>
AppTech.MSMS.Domain.Services.SimCardOrderService/<>c
AppTech.MSMS.Domain.Services.SimCardOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TrailToupOrderService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.TrailToupOrderService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.TrailToupOrderService/<ExecuteAsyn>d__0
AppTech.MSMS.Domain.Services.TrailToupOrderService/<ExecuteAsyn>d__0
<ExecuteAsyn>d__0
<ExecuteAsyn>d__0
<<type>>
AppTech.MSMS.Domain.Services.TrailToupOrderService/<>c
AppTech.MSMS.Domain.Services.TrailToupOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TransferOrderService/<>c__DisplayClass14_0
AppTech.MSMS.Domain.Services.TransferOrderService/<>c__DisplayClass14_0
<>c__DisplayClass14_0
<>c__DisplayClass14_0
<<type>>
AppTech.MSMS.Domain.Services.TransferOrderService/<>c__DisplayClass15_0
AppTech.MSMS.Domain.Services.TransferOrderService/<>c__DisplayClass15_0
<>c__DisplayClass15_0
<>c__DisplayClass15_0
<<type>>
AppTech.MSMS.Domain.Services.UserService/<>c
AppTech.MSMS.Domain.Services.UserService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass17_0
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass17_0
<>c__DisplayClass17_0
<>c__DisplayClass17_0
<<type>>
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass21_0
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass21_0
<>c__DisplayClass21_0
<>c__DisplayClass21_0
<<type>>
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass25_0
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass25_0
<>c__DisplayClass25_0
<>c__DisplayClass25_0
<<type>>
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass26_0
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass26_0
<>c__DisplayClass26_0
<>c__DisplayClass26_0
<<type>>
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass27_0
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass27_0
<>c__DisplayClass27_0
<>c__DisplayClass27_0
<<type>>
AppTech.MSMS.Domain.Services.PermissionManager/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.PermissionManager/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.UserPermissionService/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Services.UserPermissionService/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Services.UserPermissionService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.UserPermissionService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.AccountUserService/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Services.AccountUserService/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.MSMS.Domain.Services.AccountUserService/<>c
AppTech.MSMS.Domain.Services.AccountUserService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.AccountUserService/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Services.AccountUserService/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Services.VoucherService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.VoucherService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.WithdrawOrderService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.WithdrawOrderService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.BagatPaymentService/<>c
AppTech.MSMS.Domain.Services.BagatPaymentService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c__DisplayClass9_1
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c__DisplayClass9_1
<>c__DisplayClass9_1
<>c__DisplayClass9_1
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.BundleService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.TopupPayments.BundleService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.BundleService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.TopupPayments.BundleService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.GomalaTopupService/<ExecuteAsync>d__1
AppTech.MSMS.Domain.Services.TopupPayments.GomalaTopupService/<ExecuteAsync>d__1
<ExecuteAsync>d__1
<ExecuteAsync>d__1
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.GomalaTopupService/<PushToProvider>d__3
AppTech.MSMS.Domain.Services.TopupPayments.GomalaTopupService/<PushToProvider>d__3
<PushToProvider>d__3
<PushToProvider>d__3
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.ItemCostService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.TopupPayments.ItemCostService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.ItemService/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Services.TopupPayments.ItemService/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.OperatorService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.TopupPayments.OperatorService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.QuotationService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.TopupPayments.QuotationService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.QuotationService/<>c
AppTech.MSMS.Domain.Services.TopupPayments.QuotationService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileBagatService/<TopupAsync>d__0
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileBagatService/<TopupAsync>d__0
<TopupAsync>d__0
<TopupAsync>d__0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileService/<>c
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.TopupCallback/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.TopupPayments.TopupCallback/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimPurchaseService/<>c
AppTech.MSMS.Domain.Services.Sims.SimPurchaseService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimPurchaseService/<>c__DisplayClass15_0
AppTech.MSMS.Domain.Services.Sims.SimPurchaseService/<>c__DisplayClass15_0
<>c__DisplayClass15_0
<>c__DisplayClass15_0
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimTransferService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.Sims.SimTransferService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimTransferService/<>c
AppTech.MSMS.Domain.Services.Sims.SimTransferService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimTransferService/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Services.Sims.SimTransferService/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Services.Security.AccountApiService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.Security.AccountApiService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmBagatService/<ExecuteAsync>d__8
AppTech.MSMS.Domain.Services.Payment.YmBagatService/<ExecuteAsync>d__8
<ExecuteAsync>d__8
<ExecuteAsync>d__8
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmBagatService/<ExecuteBagat>d__9
AppTech.MSMS.Domain.Services.Payment.YmBagatService/<ExecuteBagat>d__9
<ExecuteBagat>d__9
<ExecuteBagat>d__9
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmBagatService/<ProccessByProviderAsync>d__11
AppTech.MSMS.Domain.Services.Payment.YmBagatService/<ProccessByProviderAsync>d__11
<ProccessByProviderAsync>d__11
<ProccessByProviderAsync>d__11
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService/<ExecuteAsync>d__0
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService/<ExecuteAsync>d__0
<ExecuteAsync>d__0
<ExecuteAsync>d__0
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService/<TopupAsync>d__1
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService/<TopupAsync>d__1
<TopupAsync>d__1
<TopupAsync>d__1
<<type>>
AppTech.MSMS.Domain.Services.GeneralLedger.AccountFrozenService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.GeneralLedger.AccountFrozenService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.GeneralLedger.AccountFrozenService/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Services.GeneralLedger.AccountFrozenService/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.MSMS.Domain.Services.GeneralLedger.DoubleEntryBondService/<>c
AppTech.MSMS.Domain.Services.GeneralLedger.DoubleEntryBondService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Cards.CardService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.Cards.CardService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.Branching.BranchService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.Branching.BranchService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.Accounting.PersonService`1/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Services.Accounting.PersonService`1/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Services.Accounting.PartiesService/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Services.Accounting.PartiesService/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.MSMS.Domain.Services.Accounting.PartiesService/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Services.Accounting.PartiesService/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Services.Remittances.BaseRemittanceInService/<>c
AppTech.MSMS.Domain.Services.Remittances.BaseRemittanceInService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Remittances.DirectRemittanceInService/<>c
AppTech.MSMS.Domain.Services.Remittances.DirectRemittanceInService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Satellite.SatelliteFactionService/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Services.Satellite.SatelliteFactionService/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Security.IpAddressManager/<>c__DisplayClass1_0
AppTech.MSMS.Domain.Security.IpAddressManager/<>c__DisplayClass1_0
<>c__DisplayClass1_0
<>c__DisplayClass1_0
<<type>>
AppTech.MSMS.Domain.Helpers.ExcelHelper/<>c
AppTech.MSMS.Domain.Helpers.ExcelHelper/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy/<PushToExchangerAsync>d__0
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy/<PushToExchangerAsync>d__0
<PushToExchangerAsync>d__0
<PushToExchangerAsync>d__0
<<type>>
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy/<>c__DisplayClass1_0
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy/<>c__DisplayClass1_0
<>c__DisplayClass1_0
<>c__DisplayClass1_0
<<type>>
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=16
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=32
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
<<type>>
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c__DisplayClass2_1
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c__DisplayClass2_1
<>c__DisplayClass2_1
<>c__DisplayClass2_1
<<type>>
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
<Module>{D6BA9E8C-9BC8-4118-9C21-1695C72CEC85}
<Module>{D6BA9E8C-9BC8-4118-9C21-1695C72CEC85}
<Module>{D6BA9E8C-9BC8-4118-9C21-1695C72CEC85}
<Module>{D6BA9E8C-9BC8-4118-9C21-1695C72CEC85}
<<type>>
WI1ZOQkCXWZPJCouZU.IRSaW2atVsIJpLmGeT
SOj3wtG2Ob7xEudvw7.CDCWSn7SaPjUwoq2Cc
IRSaW2atVsIJpLmGeT
CDCWSn7SaPjUwoq2Cc
wqLXhpLpwyK
TWp4PNnQc
<<type>>
WI1ZOQkCXWZPJCouZU.IRSaW2atVsIJpLmGeT/SFU4mbT3GMret7THonf
SOj3wtG2Ob7xEudvw7.CDCWSn7SaPjUwoq2Cc/SFU4mbT3GMret7THonf
SFU4mbT3GMret7THonf
SFU4mbT3GMret7THonf
<<type>>
ob50rk5wBQm5xx7B7E.SbARYkpSqgAv0LojmK
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il
SbARYkpSqgAv0LojmK
DyyVDbaRvM1YfIq9il
ETnXhLwl4eh
creoiNvd7
wgnXhaUQ0Ub
jZiU8kt7k
KmsXh0qM5xO
yIEeUuogE
EBfXhQts2Qy
HNMMnrD0K
ptwXh3W7nHt
U6ZIpjiMV
P91XhvLpgt1
TYIaeXNeW
CehXhZeVyON
rI3lmZ9FL
Sl1Xhm69cRT
SuhhReBcy
iWuXhCR7TUJ
QWOOk18h0
EVjXhzlE6pY
BjkXsyRir
abLXF2orRTQ
mCC9ZT9yx
p7tXFX3xgAQ
b82VQ34LR
QsBXFMibLXa
P4kZBQ8Uk
Tb9XFjHXN5M
KX0HrYNeb
w7MXFudAHsN
pvQ2Nvbv9
DCQXFqJyeLm
gVU0QeojF
pLXXFWNBX3H
HK2JaffxR
nI7XF42cG53
ubITRqgdO
RxGXFDIEEqD
vZF7RiFiF
WxYXFslm54j
eM2t2dfoT
SSQXF1iHrSv
vDfq2bW1V
SveXF6MH7yv
B3XRfqih9
N89XFfmSu23
sVk5WFvVV
HVQXFHiCDa6
E3GryunuI
VrpXFBDngeh
yxOcIGI9u
GFxXFtS0esp
Oihu8LNHm
YywXFxZUqRX
ifqQyNVWS
NwjXF57pqeh
hcDmskCdX
hJOXFr1R2kD
mKgSOTjDj
JLiXFGXNoQt
aYTwtN0c5
V2YXFgnFWaJ
udfDaXdkp
pVqXFVBbhLC
NrL10qsNW
lDqXFeyp2Gt
j8hgmZJ7n
udIXFnR5yYG
M6EKmwjSJ
Un6XF8QsvbE
PVVpfAGtG
fr9XFobJ7jB
cQCd71PIW
tvTXFTFodh4
lodECQQVs
W0pXFP2VAue
VvPxdPh3O
hccXFR0dffI
hIsn23p8h
xtIXFAqYurw
dKMLoMpMs
lmIXFbPL8R1
ghLACNa05
zNnXFIWmohR
c9FNce5cf
nlGXFNIgp27
diL3t0peo
B9yXFkXYI7T
sMgC0o5PW
Dc3XF7ZjkLh
S0FvrGWpN
mOLXFcyS1KO
hSjGubHK9
TusXFioDC57
d1uknJpcW
vn1XFdSP1qj
uS9zmJ6WC
EvCXFwAmlNp
i244bikuos
dxfXFyQfF9Q
bFB44BUGlg
FSYXFh1W1YL
x3c4o2PyTx
uaYXFFWJuf9
phV4Uu6SUx
gdQXFlhjP38
Qwp4ejR7FG
GncXFYirMTd
TWn4MujlZv
YTnXFJFL3If
NFL4IGyoc7
e8bXFKV8IOn
WS94a0Vnlv
Tg2XF9Dm2yK
XtL4lyIIgx
SukXFOhGoSe
firstrundone
XdYXFU4uW10
IBe4hEip2A
<<type>>
ob50rk5wBQm5xx7B7E.SbARYkpSqgAv0LojmK/Lp4SJJBbD4vT4RWeaC
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/AXBrnIFfMAfABnJrF9
Lp4SJJBbD4vT4RWeaC
AXBrnIFfMAfABnJrF9
<<type>>
ob50rk5wBQm5xx7B7E.SbARYkpSqgAv0LojmK/Lp4SJJBbD4vT4RWeaC/s6Q98LfIKmALQIo11r`1
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/AXBrnIFfMAfABnJrF9/z0oyxsqySXMDuI4ZyY`1
s6Q98LfIKmALQIo11r`1
z0oyxsqySXMDuI4ZyY`1
<<type>>
ob50rk5wBQm5xx7B7E.SbARYkpSqgAv0LojmK/osucPZQhl04A8SdfRX
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/ay67rn8SHAWRagidNL
osucPZQhl04A8SdfRX
ay67rn8SHAWRagidNL
yZqXFEJRWQC
D4r4O0AxSI
<<type>>
ob50rk5wBQm5xx7B7E.SbARYkpSqgAv0LojmK/VwOJ3MNIEhVnpYRplj
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/rL2N9N6wh7IWY3IC3G
VwOJ3MNIEhVnpYRplj
rL2N9N6wh7IWY3IC3G
<<type>>
ob50rk5wBQm5xx7B7E.SbARYkpSqgAv0LojmK/aL3RDZbhTdFImTgVFJ
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/LhmiV9AUoOr1v5yhIs
aL3RDZbhTdFImTgVFJ
LhmiV9AUoOr1v5yhIs
<<type>>
ob50rk5wBQm5xx7B7E.SbARYkpSqgAv0LojmK/enjNpqLQAiLlCmhdpa
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/Lk7BwHKFmNJY32ZC3n
enjNpqLQAiLlCmhdpa
Lk7BwHKFmNJY32ZC3n
UO8XFScv4ko
bV44XU8KQo
hkCXFpEcpA0
Uu349Vtr47
<<type>>
ob50rk5wBQm5xx7B7E.SbARYkpSqgAv0LojmK/b1lc5LtYyJZC7KhmmZ
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/WDRJe2H6E4HVV6PGZs
b1lc5LtYyJZC7KhmmZ
WDRJe2H6E4HVV6PGZs
<<type>>
cPOPdMcVCPrQQ7yHGS.ecWyXrMdJlv6GKTDGY
cH8IXcwQY4Peh2qpAn.xrUtBVoaXtCT6B0w6a
ecWyXrMdJlv6GKTDGY
xrUtBVoaXtCT6B0w6a
ewTXFLxXk8r
ywq4VEynyU
<<type>>
jiAgM2EkxOnmSc5S0x.o3n8eE1MigvbWSH9v1
c5uHW3cSW8ou55rAF3.KKr6hZkjvwWjdm9A4Z
o3n8eE1MigvbWSH9v1
KKr6hZkjvwWjdm9A4Z
O1hXFa0piWY
Uur4ZuAaiM
<<type>>
LS8bQGORjP2XOBms1B.knuWJQSR2MEDL0MQNv
CvXDWVQi7y2eO3gBAC.OsyMlHJSvCHNZySQs6
knuWJQSR2MEDL0MQNv
OsyMlHJSvCHNZySQs6
<<type>>
TrXMMprkX8l0V6n1Bv.j2dHkd7pgydOnvur4J
zsd5DaYg9lOJGmlIO4.R2mIapWar4cwoqqx6Q
j2dHkd7pgydOnvur4J
R2mIapWar4cwoqqx6Q
KpuXF0Ip4w1
HNM4YkXJs5
bWnXFQniMPU
pfJ40gjxwv
S30XF3vBfqh
eBxqprrF8
nPTXFveYe97
Ypf4J7ba8u
g09XFZS6uh4
CCw4Tb9h3V
p6DXFm1jHkp
n3x46T2MQ2
UNrXFCGyUl0
WP947UZNwy
fZrXFzEoQaH
Fko4i7KTuh
<<type>>
TrXMMprkX8l0V6n1Bv.j2dHkd7pgydOnvur4J/GuU0RDyZ7haTPuBT64
zsd5DaYg9lOJGmlIO4.R2mIapWar4cwoqqx6Q/dde9wksVEKdElHkEKH
GuU0RDyZ7haTPuBT64
dde9wksVEKdElHkEKH
<<type>>
TrXMMprkX8l0V6n1Bv.j2dHkd7pgydOnvur4J/RVnyZBmPWoLn5AWUhM
zsd5DaYg9lOJGmlIO4.R2mIapWar4cwoqqx6Q/T9eZG8XLTT9vNo3j18
RVnyZBmPWoLn5AWUhM
T9eZG8XLTT9vNo3j18
nqcXl2vIbJK
IWZ4FNxMCV
sTyXlXq9Ddc
X4o4BaXNNW
yKZXlMdkbFd
ReR4PkWY9i
AmIXljBVUUx
XZO4yOqtpA
EEXXlu81aHC
pcT48wm9UY
CgJXlqe5Bce
Y9l4jroko9
b6QXlW9odwm
OY84tBcMwd
cc4Xl4QFbIf
JrQ4qkE5mX
k5lXlDRkHMY
iRM4R10ean
FhrXlsIsWWv
AGe45CEX5X
zrdXl1Rxcan
Goe4rkO7Su
HmZXl6pi40P
Tt04cJf5Ud
mEoXlf87eJj
wDU4ucXGpO
DrqXlHaMu6o
HGp4Q5R9ww
ddHXlBrpqre
FvC4mE2qIR
bT2XltTmSxF
iv04SsOrFF
GImXlx0Y8PL
zBi4wdjAN2
xNCXl5ujsce
PN14D93Kyx
o5UXlrNEONR
ulr41vALu8
QoAXlGDVEGs
lQp4gbkEqU
H9eXlgdVn0n
IRA4KTlYfd
<<type>>
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}
<<type>>
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=256
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=256
__StaticArrayInitTypeSize=256
__StaticArrayInitTypeSize=256
<<type>>
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=40
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=40
__StaticArrayInitTypeSize=40
__StaticArrayInitTypeSize=40
<<type>>
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=30
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=30
__StaticArrayInitTypeSize=30
__StaticArrayInitTypeSize=30
<<type>>
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=32
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
<<type>>
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=16
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
<<type>>
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=64
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=64
__StaticArrayInitTypeSize=64
__StaticArrayInitTypeSize=64
<<type>>
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=18
<PrivateImplementationDetails>{EDAF556F-56C2-451A-A9E7-BEC00F698A1F}/__StaticArrayInitTypeSize=18
__StaticArrayInitTypeSize=18
__StaticArrayInitTypeSize=18
