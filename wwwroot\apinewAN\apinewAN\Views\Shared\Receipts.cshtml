﻿@model AppTech.MSMS.Web.Models.ReceiptModel
@Styles.Render("~/Content/print")
<div class="row" style="border: 1px solid black; padding:10px 15px;" onload="window.print();">

    <div class="col-sm-12 align-center " style="color:blue; width: 96%; height: 30px; font: bold 18px Helvetica, Sans-Serif;">
        <h2>
            @Html.DisplayFor(model => model.Type)
        </h2>
    </div>

    <table id="" class="table forTableStyle no-border table-condensed table-sm">
        <tr>
            <td>
                <div class="col-sm-3 pull-right align-right" style="text-align: center !important;margin-top: -17px;">
                    <span class="align-center">
                        @Html.DisplayFor(model => model.CurrencyName)
                    </span>
                    <br />
                    <span class="Amount col-sm-6" style="display: block;width: 100px;background-color: aqua;border: black solid .5px;padding: 2px 0;">
                        @Html.DisplayFor(model => model.Amount)
                    </span>

                </div>
            </td>
            <td></td>
            <td>
                <div class="col-sm-3 align-center" style="color: red;font-size: 25px;">
                    <span class="Number">
                        @Html.DisplayFor(model => model.Number)
                    </span>
                </div>
            </td>
            <td>
                <div class="col-sm-4 pull-left align-center" style="margin-top: 9px;">
                    التاريخ : @Html.DisplayFor(model => model.Date)
                </div>
            </td>
        </tr>
    </table>
    <br />
    <div class="forStyle" style="">
        <div class="col-sm-12 forDivStayle">
            <b>
                @ViewBag.Name :
            </b>
            @Html.DisplayFor(model => model.AccountName)
            <span class="pull-left bolder">
                : Payed To Mr/Mss
            </span>
            <hr />
        </div>
        <div class="col-sm-12 forDivStayle forDivStayle">
            <b>
                مبلغ وقدرة :
            </b>
            @Html.DisplayFor(model => model.AmountInText)
            <span class="pull-left bolder">
                : Amount
            </span>
            <hr />
        </div>
        @{
            if (Model.Method == "بنك")
            {
                <div class="col-sm-12 forDivStayle">
                    <b>
                        البنك :
                    </b>
                    @Html.DisplayFor(model => model.FundName)
                    <span class="pull-left bolder">
                        : Bank
                    </span>
                    <hr />
                </div>
            }
            else if (Model.Method == "صراف")
            {
                <div class="col-sm-12 forDivStayle">
                    <b>
                        الصراف :
                    </b>
                    @Html.DisplayFor(model => model.FundName)
                    <span class="pull-left bolder">
                        : Exchanger
                    </span>
                    <hr />
                </div>
            }
            else
            {
                <div class="col-sm-12 forDivStayle">
                    <b>
                        نقداً/ بنك :
                    </b>
                    ................................................................
                    <span class="pull-left bolder">
                        : Cash / Bank
                    </span>
                    <hr />
                </div>
            }
        }
        <div class="col-sm-12 forDivStayle">
            <b>
                وذلك مقابل :
            </b>
            @Html.DisplayFor(model => model.Note)
            <span class="pull-left bolder">
                : Versus
            </span>
            <hr />
        </div>
        <br />
        <div class="col-sm-12">
            <div class="col-sm-6 pull-right align-center forDivStayle">
                <span class="">
                    المحاسب
                </span>
                ............................
            </div>
            <div class="col-sm-6 pull-left align-center forDivStayle">
                <span class="">
                    المستلم
                </span>
                ............................
            </div><br />
        </div>
        <br />
        <br />
    </div>
</div>
@*<div style="display:block" id="toHide">
    <a href="javascript:window.print();" onclick="myFuncion()">Print</a>
</div>*@
<style>
    .forStyle div {
        margin-top: 10px
    }

    .forTableStyle td {
        border: none;
    }

    .table > tbody > tr > td {
        border-top: none;
    }

    .forTableStyle {
        margin: 0;
    }

    .forDivStayle {
        font-size: 16px;
        padding-bottom: 4px;
    }

    hr {
        margin-top: 5px;
        margin-bottom: 0px;
    }
</style>
<script>
    //$(".toPrinted").click(function(){
    //    $(".toHide").hide();
    //});
    //window.onload = function () {
    //    window.print();
    //}

    function myFuncion() {
        var x = document.getElementById("toHide");
            x.style.display = "none";
        }
</script>