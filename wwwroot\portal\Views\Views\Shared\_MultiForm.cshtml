﻿@*col-sm-offset-1 col-sm-10*@
<div class="">
    <form id="multiform" class="form-horizontal" method="POST" enctype="multipart/form-data">
        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new {@class = "text-danger"})

        <section>
            <div class="form-horizontal">
                @RenderBody()
            </div>
        </section>

        <div class="hr hr32 hr-dotted"></div>

        @Html.Partial("_FormAction")
    </form>
</div>


<script>

    function onCrudSuccess(data) {
     //   hideFormLoading();
       
        var id = Number($('#ID').val());
        if (id > 0) {
            showSuccess("تم تعديل السجل بنجاح");
            fetchData(false, true);
        }
        else {
            showSuccess("تم حفط السجل بنجاح");
            fetchData(false, false);
        }

     closeDialog();
    }

    $(function() {
      //  hideFormLoading();
        $("#multiform").submit(function(event) {
            i('onsubmit click');
            event.preventDefault();
            submitForm();
         //   showFormLoading();

            //var data = { code: "gg" }
            //AjaxCall('/Clients/YMOfferPayment/GetPrice', data)
            //    .done(function (msg) {
            //        if (!confirm(msg)) {
            //            i('not confirmed');
            //            ar('not confirmed');
                        
            //        } else {

            //            i('confirmed');
            //            submitForm();
            //        }
            //    })
            //    .fail(function (xhr, textStatus, errorThrown) {
            //        parseAndShowError(xhr, textStatus, errorThrown);
            //    });


          
        }); 


    });

    function submitForm() {
        i(' submitForm');
        var action =  $("#Controller").val() + "/addoredit";
        if ($("#multiform").attr("enctype") === "multipart/form-data") {
            var dataString = new FormData($("#multiform").get(0));
            $.ajax({
                type: "post",
                url: action,
                data: dataString,
                dataType: "text",
                contentType: false,
                processData: false,
                success: function (data) {
                    onCrudSuccess(data);
                },
                error: function (xhr, textStatus, errorThrown) {
                    resetButton();
                    parseAndShowError(xhr, textStatus, errorThrown);
                
                }
            });

        } else {
            ar('not multipart');
        }
    }

</script>
<script>


    try {
        $('.date-picker').datepicker({
            autoclose: true,
            todayHighlight: true
        })
            .next().on(ace.click_event,
                function () {
                    $(this).prev().focus();
                });
    } catch (e) {
        alert("Couldnt set date-picker: " + e);
    }

</script>