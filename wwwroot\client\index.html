<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ط¨ظˆط§ط¨ط© ط§ظ„ط¹ظ…ظ„ط§ط، - AppTech Client Portal</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            direction: rtl;
        }
        
        .header {
            background: rgba(0,0,0,0.2);
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            font-weight: 700;
        }
        
        .container { 
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .nav-bar {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .nav-bar a {
            color: white;
            text-decoration: none;
            padding: 12px 20px;
            margin: 0 8px;
            border-radius: 10px;
            display: inline-block;
            background: rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.3);
            font-weight: 500;
        }
        
        .nav-bar a:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .main-content {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            margin: 20px 0;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .card {
            background: rgba(255,255,255,0.15);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .card:hover {
            transform: translateY(-8px);
            background: rgba(255,255,255,0.25);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #fff;
            font-size: 1.3em;
            font-weight: 600;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            margin: 8px 4px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.3);
            font-weight: 500;
        }
        
        .btn:hover { 
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .status-bar {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .status-item {
            background: rgba(39, 174, 96, 0.8);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .footer {
            text-align: center;
            padding: 30px 20px;
            border-top: 1px solid rgba(255,255,255,0.2);
            margin-top: 40px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>ًں‘¥ ط¨ظˆط§ط¨ط© ط§ظ„ط¹ظ…ظ„ط§ط، - AppTech Client Portal</h1>
        <p>ظ†ط¸ط§ظ… ط¥ط¯ط§ط±ط© ط­ط³ط§ط¨ط§طھ ط§ظ„ط¹ظ…ظ„ط§ط، ظˆط§ظ„ط®ط¯ظ…ط§طھ ط§ظ„ظ…طھظƒط§ظ…ظ„ط©</p>
    </div>

    <div class="container">
        <div class="nav-bar">
            <a href="/portal">ًںڈ  ط§ظ„ط¨ظˆط§ط¨ط© ط§ظ„ط±ط¦ظٹط³ظٹط©</a>
            <a href="/api">ًں”Œ ظˆط§ط¬ظ‡ط© API</a>
            <a href="/client">ًں‘¥ ط¨ظˆط§ط¨ط© ط§ظ„ط¹ظ…ظ„ط§ط،</a>
            <a href="/apinewAN">ًں†• API ط§ظ„ط¬ط¯ظٹط¯</a>
            <a href="/collections_system">ًں’° ظ†ط¸ط§ظ… ط§ظ„طھط­طµظٹظ„ط§طھ</a>
        </div>

        <div class="status-bar">
            <div class="status-item">âœ… ط§ظ„ظ†ط¸ط§ظ… ظٹط¹ظ…ظ„</div>
            <div class="status-item">ًں”§ طھظ… ط§ظ„ط¥طµظ„ط§ط­</div>
            <div class="status-item">ًںژ¯ ط¬ط§ظ‡ط² ظ„ظ„ط§ط³طھط®ط¯ط§ظ…</div>
            <div class="status-item">ًں”“ ط¨ط¯ظˆظ† ظ‚ظٹظˆط¯ ط£ظ…ظ†ظٹط©</div>
        </div>

        <div class="main-content">
            <h2 style="text-align: center; margin-bottom: 20px;">ظ…ط±ط­ط¨ط§ظ‹ ط¨ظƒ ظپظٹ ط¨ظˆط§ط¨ط© ط§ظ„ط¹ظ…ظ„ط§ط،</h2>
            <p style="text-align: center; font-size: 1.1em; opacity: 0.9;">
                ط¥ط¯ط§ط±ط© ط´ط§ظ…ظ„ط© ظ„ط­ط³ط§ط¨ط§طھ ط§ظ„ط¹ظ…ظ„ط§ط، ظˆط§ظ„ط®ط¯ظ…ط§طھ ط§ظ„ظ…ظ‚ط¯ظ…ط© ظ…ط¹ ظ…طھط§ط¨ط¹ط© ط¯ظ‚ظٹظ‚ط© ظ„ظ„ظ…ط¹ط§ظ…ظ„ط§طھ
            </p>
        </div>

        <div class="dashboard">
            <div class="card">
                <h3>ًں”گ طھط³ط¬ظٹظ„ ط§ظ„ط¯ط®ظˆظ„</h3>
                <p>ط¯ط®ظˆظ„ ط§ظ„ط¹ظ…ظ„ط§ط، ط¥ظ„ظ‰ ط­ط³ط§ط¨ط§طھظ‡ظ… ط§ظ„ط´ط®طµظٹط© ظˆط¥ط¯ط§ط±ط© ظ…ط¹ظ„ظˆظ…ط§طھظ‡ظ…</p>
                <p><strong>ط§ظ„ط­ط§ظ„ط©:</strong> ظ…طھط§ط­ ظ„ظ„ط¬ظ…ظٹط¹</p>
                <a href="#login" class="btn">طھط³ط¬ظٹظ„ ط§ظ„ط¯ط®ظˆظ„</a>
            </div>

            <div class="card">
                <h3>ًں“ٹ ظ„ظˆط­ط© طھط­ظƒظ… ط§ظ„ط¹ظ…ظٹظ„</h3>
                <p>ط¹ط±ط¶ ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„ط­ط³ط§ط¨ ظˆط§ظ„ط®ط¯ظ…ط§طھ ظˆط§ظ„ظ…ط¹ط§ظ…ظ„ط§طھ ط§ظ„ط­ط¯ظٹط«ط©</p>
                <p><strong>ط§ظ„ط­ط§ظ„ط©:</strong> ظ†ط´ط· ظˆظ…ط­ط¯ط«</p>
                <a href="#dashboard" class="btn">ظ„ظˆط­ط© ط§ظ„طھط­ظƒظ…</a>
            </div>

            <div class="card">
                <h3>ًں’³ ط§ظ„ط®ط¯ظ…ط§طھ ط§ظ„ظ…ط§ظ„ظٹط©</h3>
                <p>ط¥ط¯ط§ط±ط© ط§ظ„ظ…ط¹ط§ظ…ظ„ط§طھ ط§ظ„ظ…ط§ظ„ظٹط© ظˆط§ظ„ظ…ط¯ظپظˆط¹ط§طھ ظˆط§ظ„طھط­ظˆظٹظ„ط§طھ</p>
                <p><strong>ط§ظ„ط­ط§ظ„ط©:</strong> ظ…طھط§ط­ 24/7</p>
                <a href="#financial" class="btn">ط§ظ„ط®ط¯ظ…ط§طھ ط§ظ„ظ…ط§ظ„ظٹط©</a>
            </div>

            <div class="card">
                <h3>ًں“‍ ط§ظ„ط¯ط¹ظ… ط§ظ„ظپظ†ظٹ</h3>
                <p>ط§ظ„طھظˆط§طµظ„ ظ…ط¹ ظپط±ظٹظ‚ ط§ظ„ط¯ط¹ظ… ط§ظ„ظپظ†ظٹ ظˆط­ظ„ ط§ظ„ظ…ط´ط§ظƒظ„</p>
                <p><strong>ط§ظ„ط­ط§ظ„ط©:</strong> ظ…طھط§ط­ ط¯ط§ط¦ظ…ط§ظ‹</p>
                <a href="#support" class="btn">ط§ظ„ط¯ط¹ظ… ط§ظ„ظپظ†ظٹ</a>
            </div>

            <div class="card">
                <h3>ًں“ˆ ط§ظ„طھظ‚ط§ط±ظٹط± ط§ظ„ط´ط®طµظٹط©</h3>
                <p>ط¹ط±ط¶ طھظ‚ط§ط±ظٹط± ط§ظ„ط­ط³ط§ط¨ ظˆط§ظ„ظ†ط´ط§ط·ط§طھ ظˆط§ظ„ط¥ط­طµط§ط¦ظٹط§طھ</p>
                <p><strong>ط§ظ„ط­ط§ظ„ط©:</strong> ظ…ط­ط¯ط« ظٹظˆظ…ظٹط§ظ‹</p>
                <a href="#reports" class="btn">ط¹ط±ط¶ ط§ظ„طھظ‚ط§ط±ظٹط±</a>
            </div>

            <div class="card">
                <h3>âڑ™ï¸ڈ ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ط­ط³ط§ط¨</h3>
                <p>طھط®طµظٹطµ ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ط­ط³ط§ط¨ ظˆط§ظ„طھظپط¶ظٹظ„ط§طھ ط§ظ„ط´ط®طµظٹط©</p>
                <p><strong>ط§ظ„ط­ط§ظ„ط©:</strong> ظ‚ط§ط¨ظ„ ظ„ظ„طھط®طµظٹطµ</p>
                <a href="#settings" class="btn">ط§ظ„ط¥ط¹ط¯ط§ط¯ط§طھ</a>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h4>ًںژ‰ ظ…ط±ط­ط¨ط§ظ‹ ط¨ظƒ ظپظٹ ط¨ظˆط§ط¨ط© ط§ظ„ط¹ظ…ظ„ط§ط، ط§ظ„ظ…طھط·ظˆط±ط©</h4>
            <p>ظٹظ…ظƒظ†ظƒ ط§ظ„ط¢ظ† ط§ظ„ظˆطµظˆظ„ ط¥ظ„ظ‰ ط¬ظ…ظٹط¹ ط§ظ„ط®ط¯ظ…ط§طھ ظˆط§ظ„ظ…ظٹط²ط§طھ ط§ظ„ظ…طھط§ط­ط© ظ„ط­ط³ط§ط¨ظƒ ط¨ط³ظ‡ظˆظ„ط© ظˆظٹط³ط±.</p>
            <p>ط§ظ„ظ†ط¸ط§ظ… ظ…ط­ط¯ط« ظˆظٹط¹ظ…ظ„ ط¨ظƒط§ظ…ظ„ ط§ظ„ظˆط¸ط§ط¦ظپ ظ…ط¹ ط¯ط¹ظ… ظƒط§ظ…ظ„ ظ„ظ„ط؛ط© ط§ظ„ط¹ط±ط¨ظٹط©.</p>
        </div>

        <div class="footer">
            <p><strong>آ© 2024 AppTech Client Portal - ط¬ظ…ظٹط¹ ط§ظ„ط­ظ‚ظˆظ‚ ظ…ط­ظپظˆط¸ط©</strong></p>
            <p>طھظ… ط§ظ„طھط·ظˆظٹط± ظˆط§ظ„طھط­ط¯ظٹط« ط¨ظˆط§ط³ط·ط© ظپط±ظٹظ‚ AppTech ط§ظ„ظ…طھط®طµطµ</p>
            <p>ط¢ط®ط± طھط­ط¯ظٹط«: <span id="currentTime"></span></p>
            <p style="color: #ff6b6b; margin-top: 10px;">âڑ ï¸ڈ ط§ظ„ظ†ط¸ط§ظ… ظٹط¹ظ…ظ„ ط¨ط¯ظˆظ† ظ‚ظٹظˆط¯ ط£ظ…ظ†ظٹط© - ظ„ظ„ط§ط®طھط¨ط§ط± ظپظ‚ط·</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // طھط­ط¯ظٹط« ط§ظ„ظˆظ‚طھ
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        }
        updateTime();
        setInterval(updateTime, 1000);

        // طھط£ط«ظٹط±ط§طھ طھظپط§ط¹ظ„ظٹط©
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 150);
            });
        });
    </script>
</body>
</html>