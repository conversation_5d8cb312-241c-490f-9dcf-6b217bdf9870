﻿@model AppTech.MSMS.Domain.Models.Transporter
@{
    Layout = "~/Views/Shared/_MultiForm.cshtml";
}
<div class="form-horizontal">
    @Html.EditorFor(model => model.ID, new { htmlAttributes = new { @style = "display: none" } })
    @Html.ValidationSummary(true)

    <div class="form-group">
        @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Name)
            @Html.ValidationMessageFor(model => model.Name)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.ImageName, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            <input type="file" name="ImageData" size="40" onchange="showImg(this)">
            @Html.ValidationMessageFor(model => model.ImageName)
        </div>
        <img class="img-thumbnail" width="150" height="150" id="preview"
             src="@Url.Action("GetImage", "Transporter",
              new {Model.ID})" />
    </div>

    @*<div class="form-group">
        @Html.LabelFor(model => model.AccountID, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.AccountID, (SelectList)ViewBag.Accounts, new { @class = "select2" })
            @Html.ValidationMessageFor(model => model.AccountID)
        </div>
    </div>*@

    <div class="form-group">
        @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Description, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
            @Html.ValidationMessageFor(model => model.Description)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
            @Html.ValidationMessageFor(model => model.Note)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Status, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.Status, new[]
                {
                    new SelectListItem {Text = "موقف", Value = "0"},
                    new SelectListItem {Text = "نشط", Value = "1"}
                })
            @Html.ValidationMessageFor(model => model.Status)
        </div>
    </div>
</div>

<script>
    $(function () {
        $('.select2').css('width', '200px').select2({ allowClear: false });
        $('#select2-multiple-style .btn').on('click',
            function (e) {
                var target = $(this).find('input[type=radio]');
                var which = parseInt(target.val());
                if (which == 2) $('.select2').addClass('tag-input-style');
                else $('.select2').removeClass('tag-input-style');
            });
    });

</script>