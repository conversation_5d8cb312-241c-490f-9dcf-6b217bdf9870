﻿@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.CashWithdraw

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<div class="row">
    
    
    
<div class="col-xs-12 col-sm-6">
        
    
    <div class="form-group form-inline">
        @Html.LabelFor(model => model.Amount, new {@class = "control-label col-md-2"})
        <div class="col-md-10 form-inline">
            @Html.EditorFor(model => model.Amount, new {@class = "col-md-8"})
            <label id="words" class="red"></label>
            @Html.ValidationMessageFor(model => model.Amount)
        </div>
    </div>
    
    <div class="form-group">
         @Html.LabelFor(model => model.DebitorAccountID, new { @class = "control-label col-md-4" })
        <div class="col-md-8">
            @Html.Obout(new ComboBox("DebitorAccountID")
            {
                Width = 250,
                Height = 100,
                SelectedValue = Model.DebitorAccountID == 0 ? null : Model.DebitorAccountID.ToString(),
                FilterType = ComboBoxFilterType.Contains
            })
            @Html.ValidationMessageFor(model => model.DebitorAccountID)
        </div>
    </div>
    
    
    
    
    <div class="form-group">
        <label class="col-sm-2 control-label no-padding-right" for="Delivery">مناولة</label>

        <div class="col-sm-10">
            @Html.EditorFor(model => model.Delivery, new {@class = "col-xs-12 col-sm-10", id = "form-field-ref"})

            @Html.ValidationMessageFor(model => model.Delivery)
        </div>
    </div>


    <div class="form-group">
        @Html.Label("ملاحظات", new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control", id = "form-field-note"}})
            @Html.ValidationMessageFor(model => model.Note)
        </div>
    </div>
</div>
    
    
    
    
<div class="col-xs-12 col-sm-6">



    <div class="form-group">
        <div class="col-md-12">
            @Html.LabelFor(model => model.CurrencyID, new { @class = "control-label col-md-2" })
            @Html.DropDownListFor(m => m.CurrencyID, (SelectList)ViewBag.Currencies)
        </div>
    </div>



    <div class="form-group">
        <label class="col-sm-2 control-label" for="CreditorAccountID">الصندوق</label>
        @*   @Html.LabelFor(model => model.ExchangeAccountID, new { @class = "control-label col-md-4" })*@
        <div class="col-md-8">
            @Html.Obout(new ComboBox("CreditorAccountID")
            {
                Width = 250,
                SelectedValue = Model.CreditorAccountID == 0 ? null : Model.CreditorAccountID.ToString(),
                FilterType = ComboBoxFilterType.Contains
            })
            @Html.ValidationMessageFor(model => model.CreditorAccountID)
        </div>
    </div>
    
    
    <div class="form-group">
        @Html.Label("التاريخ", new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EditorFor(model => model.Date, new {htmlAttributes = new {@class = "date-picker"}})

        </div>
    </div>
    

</div>









</div>

<script>
    $(function() {
        $("#submit-button").text('سحب نقدي');
    })
</script>