﻿@model AppTech.MSMS.Domain.Models.Province
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    @*@Html.LabelFor(model => model.CountryID, htmlAttributes: new { @class = "control-label col-md-2" })*@
    <div class="col-md-10">
        @Html.HiddenFor(model => model.CountryID, new {htmlAttributes = new {@class = "form-control", value = "1"}})
        @Html.ValidationMessageFor(model => model.CountryID, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Name, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Name, "", new {@class = "text-danger"})
    </div>
</div>