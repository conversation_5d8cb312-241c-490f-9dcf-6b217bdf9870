{"version": 3, "sources": ["src/serviceWorker.js"], "names": ["isFunction", "obj", "toString", "call", "runFunctionString", "funcStr", "trim", "length", "func", "Function", "self", "addEventListener", "event", "client", "source", "onnotificationclose", "notification", "data", "onClose", "postMessage", "JSON", "stringify", "id", "action", "onnotificationclick", "link", "origin", "href", "substring", "indexOf", "close", "waitUntil", "clients", "matchAll", "type", "then", "clientList", "full_url", "i", "url", "focus", "openWindow", "catch", "error", "Error", "message", "onClick"], "mappings": "AAAA,aAEA,SAASA,WAAWC,KAChB,OAAOA,QAAUC,SAASC,KAAKF,OAAS,oBAG5C,SAASG,kBAAkBC,SACvB,GAAIA,QAAQC,OAAOC,OAAS,EAAG,CAC3B,IAAIC,KAAO,IAAIC,SAASJ,SACxB,GAAIL,WAAWQ,MAAO,CAClBA,SAKZE,KAAKC,iBAAiB,UAAW,SAASC,OACtCF,KAAKG,OAASD,MAAME,SAGxBJ,KAAKK,oBAAsB,SAASH,OAChCR,kBAAkBQ,MAAMI,aAAaC,KAAKC,SAG1CR,KAAKG,OAAOM,YACRC,KAAKC,WACDC,GAAIV,MAAMI,aAAaC,KAAKK,GAC5BC,OAAQ,YAKpBb,KAAKc,oBAAsB,SAASZ,OAChC,IAAIa,KAAMC,OAAQC,KAElB,UACWf,MAAMI,aAAaC,KAAKQ,OAAS,aACxCb,MAAMI,aAAaC,KAAKQ,OAAS,KACnC,CACEC,OAASd,MAAMI,aAAaC,KAAKS,OACjCD,KAAOb,MAAMI,aAAaC,KAAKQ,KAC/BE,KAAOD,OAAOE,UAAU,EAAGF,OAAOG,QAAQ,IAAK,IAAM,IAGrD,GAAIJ,KAAK,KAAO,IAAK,CACjBA,KAAOA,KAAKlB,OAAS,EAAIkB,KAAKG,UAAU,EAAGH,KAAKlB,QAAU,GAG9DK,MAAMI,aAAac,QAGnBlB,MAAMmB,UACFC,QACKC,UACGC,KAAM,WAETC,KAAK,SAASC,YACX,IAAIvB,OAAQwB,SAEZ,IAAK,IAAIC,EAAI,EAAGA,EAAIF,WAAW7B,OAAQ+B,IAAK,CACxCzB,OAASuB,WAAWE,GACpBD,SAAWV,KAAOF,KAGlB,GACIY,SAASA,SAAS9B,OAAS,KAAO,KAClCM,OAAO0B,IAAI1B,OAAO0B,IAAIhC,OAAS,KAAO,IACxC,CACE8B,UAAY,IAGhB,GAAIxB,OAAO0B,MAAQF,UAAY,UAAWxB,OAAQ,CAC9C,OAAOA,OAAO2B,SAItB,GAAIR,QAAQS,WAAY,CACpB,OAAOT,QAAQS,WAAW,IAAMhB,SAGvCiB,MAAM,SAASC,OACZ,MAAM,IAAIC,MACN,mCAAqCD,MAAME,YAM/DzC,kBAAkBQ,MAAMI,aAAaC,KAAK6B"}