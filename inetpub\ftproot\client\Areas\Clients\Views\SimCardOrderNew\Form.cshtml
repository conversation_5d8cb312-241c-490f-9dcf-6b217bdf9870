﻿@model AppTech.MSMS.Domain.Models.SimCardOrder

@{
    Layout = "~/Views/Shared/_MultiLayout.cshtml";
}

@*---------------شريحه جديدة --------------*@
<input type="hidden" name="Device" value="Web" />
<div class="form-group required">
    <label class="col-sm-2 control-label"> المشغل</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.NetworkID, new[]
      {
          new SelectListItem {Text = "يمن موبايل", Value = "1"},
          new SelectListItem {Text = "ام تي ان", Value = "2"},
          new SelectListItem {Text = "سبأفون", Value = "3"}
      })
    </div>
    @Html.ValidationMessageFor(model => model.NetworkID)
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">نوع الإشتراك</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.SimType, new[]
        {
            new SelectListItem {Text = "شريحة", Value = "شريحة"},
            new SelectListItem {Text = "برمجة", Value = "برمجة"}
        })
    </div>
    @Html.ValidationMessageFor(model => model.SimType)
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">نوع الخط</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.LineType, new[]
        {
            new SelectListItem {Text = "دفع مسبق", Value = "دفع مسبق"},
            new SelectListItem {Text = "فوترة", Value = "فوترة"}
        })
    </div>
    @Html.ValidationMessageFor(model => model.LineType)
</div>


<div class="form-group">
    @Html.LabelFor(model => model.SimNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.SimNumber)
        @Html.ValidationMessageFor(model => model.SimNumber)
    </div>
</div>


@*<div class="form-group">
        @Html.LabelFor(model => model.SubscriberNumber, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.SubscriberNumber)
            @Html.ValidationMessageFor(model => model.SubscriberNumber)
        </div>
    </div>*@



<div class="form-group">
    @Html.LabelFor(model => model.CustomerName, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.CustomerName)
        @Html.ValidationMessageFor(model => model.CustomerName)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.BirthDate, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.BirthDate)
        @Html.ValidationMessageFor(model => model.BirthDate)
    </div>
</div>

<div class="form-group required">
    <label class="col-sm-2 control-label">رقم البطاقه</label>
    <div class="col-md-10">
        @Html.EditorFor(model => model.CardNumber)
        @Html.ValidationMessageFor(model => model.CardNumber)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.IssueDate, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.IssueDate)
        @Html.ValidationMessageFor(model => model.IssueDate)
    </div>
</div>
@*<div class="form-group">
    @*<div class="form-group">
            <label class="col-sm-2 control-label">نوع البطاقة</label>

            <div class="col-sm-10">
                @Html.DropDownListFor(model => model.PersonalCardType, new[]
                {
                    new SelectListItem {Text = "شخصية", Value = "شخصية"},
                    new SelectListItem {Text = "جواز سفر", Value = "جواز سفر"},
                    new SelectListItem {Text = "عائلية", Value = "عائلية"},
                    new SelectListItem {Text = "عسكرية", Value = "عسكرية"},
                    new SelectListItem {Text = "أخرى", Value = "أخرى"}
                })


            </div>
            @Html.ValidationMessageFor(model => model.PersonalCardType)
        </div>*@