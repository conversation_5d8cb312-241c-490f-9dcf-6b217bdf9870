﻿@model AppTech.MSMS.Domain.Models.Exchanger
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div>

    <div class="form-group">
        @Html.LabelFor(model => model.Number, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Number)
            @Html.ValidationMessageFor(model => model.Number)
        </div>
    </div>
    <div class="form-group">
        @Html.Label("اسم شركة الصرافة", new { @class = "control-label col-md-3" })
        <div class="col-md-9">
            @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Name)
        </div>
    </div>

    <div class="form-group">
        @Html.Label("نوع", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.IsExternal, new[]
            {
                new SelectListItem {Text = "داخلي", Value = bool.FalseString},
                new SelectListItem {Text = "خارجي", Value = bool.TrueString}
            })
        </div>
    </div>

</div>