﻿<div class="widget-box">
    <div class="widget-header">
        <h4 class="widget-title"></h4>
    </div>

    <div class="widget-body">
        <div class="widget-main">

            @using (Html.BeginForm("Show", "ClientSheet"))
            {
                @Html.AntiForgeryToken()

                <div class="form-horizontal">
                    <h4></h4>
                    <hr/>
                    @Html.ValidationSummary(true)

                    <div class="form-group">
                        @Html.LabelFor(model => model.Date, new {@class = "control-label col-md-2"})
                        <div class="col-md-10">
                            @Html.TextBoxFor(model => model.Date, new {@class = "form-control datepicker", placeholder = "Enter  date here..."})
                            @*  @Html.EditorFor(model => model.Date, new {@class = "form-control datepicker", placeholder = "Enter Drop-off date here..."})*@
                            @Html.ValidationMessageFor(model => model.Date)
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.AccountID, new {@class = "control-label col-md-2"})
                        <div class="col-md-10">
                            @Html.EditorFor(model => model.AccountID)
                            @Html.ValidationMessageFor(model => model.AccountID)
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-md-offset-2 col-md-10">
                            <input type="submit" value="Show" class="btn btn-default"/>
                        </div>
                    </div>
                </div>

                <hr/>
            }


        </div>
    </div>
</div>