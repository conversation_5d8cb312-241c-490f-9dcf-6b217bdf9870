﻿@model AppTech.MSMS.Domain.Models.CoverageOrder

<div class="space-6"></div>
<span class="label label-info"> تفاصيل الطلب</span>
<div class="space-6"></div>

<div class="profile-info-row">
    <div class="profile-info-name"> رقم الحساب </div>
    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.AccountNumber)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name">  اسم الحساب </div>
    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Account.Name)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name">  لدى نظام </div>
    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Account1.Name)</span>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> المبلغ </div>

    <div class="profile-info-value">
        <span class="editable" id="signup"> @Html.DisplayFor(model => model.Amount)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name">  العملة </div>
    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Currency.Name)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> الملاحظات </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Note)</span>
    </div>
</div>
