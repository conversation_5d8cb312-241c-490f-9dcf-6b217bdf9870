﻿@model AppTech.MSMS.Domain.Models.LoanOrder
<div class="space-6"></div>
<span class="label label-info"> &nbsp; &nbsp; &nbsp; تفاصيل القرض &nbsp;</span>
<div class="space-6"></div>

<div class="profile-info-row">
    <div class="profile-info-name"> المبلغ </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Amount)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> العملة </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Currency.Name) </span>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> الملاحظات </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Note)</span>
    </div>
</div>

<script>
    
</script>