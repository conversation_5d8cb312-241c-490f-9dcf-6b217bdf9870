﻿@model AppTech.MSMS.Domain.Models.WifiFaction
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-horizontal">
    @Html.EditorFor(model => model.ID, new { htmlAttributes = new { @style = "display: none" } })

    @Html.ValidationSummary(true)

    <div class="form-group">
        @Html.LabelFor(model => model.OrderNO, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.OrderNO)
            @Html.ValidationMessageFor(model => model.OrderNO)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Name)
            @Html.ValidationMessageFor(model => model.Name)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.WifiProviderID, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.WifiProviderID, (SelectList)ViewBag.WifiProviders)
            @Html.ValidationMessageFor(model => model.WifiProviderID)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Price, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Price)
            @Html.ValidationMessageFor(model => model.Price)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Description)
            @Html.ValidationMessageFor(model => model.Description)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Note)
            @Html.ValidationMessageFor(model => model.Note)
        </div>
    </div>
</div>
