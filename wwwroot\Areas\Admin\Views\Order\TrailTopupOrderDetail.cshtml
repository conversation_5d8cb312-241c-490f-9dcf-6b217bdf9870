﻿@model AppTech.MSMS.Domain.Models.TrailToupOrder


<div class="space-6"></div>
<span class="label label-info"> &nbsp; &nbsp; &nbsp; تفاصيل الطلب &nbsp;</span>
@*<div class="hr hr2 hr-double"></div>*@
<div class="space-6"></div>


<div class="profile-info-row">
    <div class="profile-info-name"> الصنف </div>

    <div class="profile-info-value">
        <span class="editable" id="age"> @Html.DisplayFor(model => model.TopupNetwork.Name) </span>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> رقم المشترك </div>

    <div class="profile-info-value">
        <span class="editable" id="age"> @Html.DisplayFor(model => model.SubscriberNumber) </span>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> الوحدات / الرصيد </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.ExchangeAmount)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> النسبة </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Percentage)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> المبلغ المحتسب </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Amount)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> الملاحظات </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Note)</span>
    </div>
</div>