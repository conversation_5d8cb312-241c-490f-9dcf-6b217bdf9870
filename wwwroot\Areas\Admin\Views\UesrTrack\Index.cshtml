﻿@{
    ViewBag.Title =  "حركة المستخدمين";
    Layout = "~/Views/Shared/_CrudLayout.cshtml";
}
<script>
    $("#add-record").hide();
</script>

<div class="search-area well well-sm" style="margin-top:10px">
    <div class="row form-group" style="display:block">
        <div class="col-md-4 ">
            <select id="UsersList" name="Users" style="width:100%;"></select>
        </div>
        <div class="col-md-4 ">
            <select id="PagesList" name="Pages" style="width:100%;"></select>
        </div>
        <div class="col-md-4 ">
            <select id="ActionsList" name="Pages" style="width:100%;">
                <option value="">نوع الحركة</option>
                <option value="إضافة">إضافة</option>
                <option value="حذف">حذف</option>
                <option value="تعديل">تعديل</option>
                <option value="ترحيل">ترحيل</option>
                <option value="إالغاء">إالغاء</option>
            </select>
        </div>
    </div>
    <div class="row form-group" id="searchLine" style="display:block">
        <div class="col-md-3 ">
            <label>التاريخ</label>
            <input type="date" name="Date" id="Date" placeholder="التاريخ" style="width:70%;" />
        </div>
        <div class="col-md-3 ">
            <label>الى </label>
            <input type="date" name="ToDate" id="ToDate" placeholder="التاريخ" style="width:70%;" />
        </div>

        <div class="col-md-3 ">
            <button class="btn btn-white btn-info btn-bold btn-round" id="search1" style="width:30%;">بحث</button>
        </div>
        <div class="col-md-3 ">
        </div>
    </div>
</div>
<script>
    $(function () {
        loadDataTypeList();
        function loadDataTypeList() {
            fillDataList('UsersList', '/Admin/UesrTrack/GetUsers', false, "كافة المستخدمين")
        }
    });
    $(function () {
        loadDataCurrencyList();
        function loadDataCurrencyList() {
            fillDataList('PagesList', '/Admin/UesrTrack/GetPages', false, "كافة النوافذ")
        }
    });
    //  $(function () {
    //    loadDataActionsLis();
    //    function loadDataActionsLis() {
    //        fillDataList('ActionsLis', '/Admin/UesrTrack/GetActions', false, "كافة النوافذ")
    //    }
    //});

    $(function () {
        $("#search1").on("click",
            function (e) {
                //   e.p
                var userID = $("#UsersList").val();
                var pageName = $("#PagesList").val();
                var pageSize = $("#pageSize").val();
                var Date = $("#Date").val();
                var ToDate = $("#ToDate").val();
                var actionVar = $("#ActionsList").val();

                var data = {
                    pageSize: pageSize,
                    userID: userID,
                    pageName: pageName,
                    Date: Date,
                    ToDate: ToDate,
                    actionVar: actionVar
                };
                showLoading();
                AjaxCall('Admin/UesrTrack/Searchs', data).done(function (response) {
                    //    resetButton();
                    //    i('search response' +response)
                    hideLoading();
                    $("#list").replaceWith(response);
                    var pager = Patterns.Art.Pager;
                    pager.activateList();
                    //  ar(response);
                }).fail(function (xhr, textStatus, errorThrown) {
                    hideLoading();
                    //   resetButton();
                    parseAndShowError(xhr, textStatus, errorThrown);

                });
            });
    });
</script>
