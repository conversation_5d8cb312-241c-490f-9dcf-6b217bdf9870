﻿@using System.Data
@using AppTech.MSMS.Web.Security
@helper GetRecordEDR(DataRow row)
{
    <td style="text-align: right;">
        <div id="record_operations" class="hidden-sm hidden-xs btn-group">

            @{
                var mUserPermissions = CurrentUser.UserPermissions.UserPermissions;

                var userPermission = mUserPermissions.SingleOrDefault(x => x.Page.PageName.Equals(ViewBag.PageName));
                if (userPermission != null)
                {
                    if (userPermission.PageActions.Any(x => x.Name.Equals("EDIT") && x.IsAllow))
                    {
                        <a class="blue" style="padding-right: 3px" id="edit-record" href="/@ViewBag.Controller/AddOrEdit/@( row["ID"])">
                            <i class="ace-icon fa fa-pencil bigger-130"></i>
                        </a>
                    }
                    if (userPermission.PageActions.Any(x => x.Name.Equals("DELETE") && x.Is<PERSON>llow))
                    {
                        <a class="red padding-5" style="padding-right: 10px" id="delete-record" href="/@ViewBag.Controller/Delete/@row["ID"]">
                            <i class="ace-icon fa fa-trash-o bigger-130"></i>
                        </a>
                    }


                    if (userPermission.PageActions.Any(x => x.Name.Equals("PRINT") && x.IsAllow))
                    {
                        <a class="blue print-record" style="padding-right: 10px" id="print-record" onclick="print(@row["id"]) " href="">
                            <i class="ace-icon fa fa-print bigger-130"></i>
                        </a>
                    }
                }
                @*if (!CurrentUser.IsAdmin)
                {
                    <a class="blue" style="padding-right: 3px" id="edit-record" href="/@ViewBag.Controller/AddOrEdit/@( row["ID"])">
                        <i class="ace-icon fa fa-pencil bigger-130"></i>
                    </a>

                    <a class="red padding-5" style="padding-right: 10px" id="delete-record" href="/@ViewBag.Controller/Delete/@row["ID"]">
                        <i class="ace-icon fa fa-trash-o bigger-130"></i>
                    </a>
                }*@
            }



        </div>


    </td>
}

<table id="dynamic-table" class="table table-striped table-bordered table-hover">

    @*<thead>
            <tr></tr>
        </thead>*@
    <thead>
    <tr>
        @*    <th class="center">
                    <label class="pos-rel">
                        <input type="checkbox" class="ace" />
                        <span class="lbl"></span>
                    </label>
                </th>*@


        @foreach (DataColumn col in Model.Columns)
        {
            if (!col.Caption.Equals("ID"))
            {
                <th>@col.Caption</th>
            }
        }


        <th style="text-align: right;"></th>
    </tr>
    </thead>

    <tbody>

    @{
        if (((DataTable) Model).Rows.Count == 0)
        {
            <tr class="align-center">
                <h3>لا يوجد سجلات </h3>
            </tr>
        }
    }

    @foreach (DataRow row in Model.Rows)
    {
        <tr data-id="@row["ID"]">

            @*       <td class="center">
                        <label class="pos-rel">
                            <input type="checkbox" class="ace" />
                            <span class="lbl"></span>
                        </label>
                    </td>*@

            @for (var i = 0; i < row.ItemArray.Length; i++)
            {
                if (i != 0)
                {
                    <td style="text-align: right;">@row.ItemArray[i].ToString()</td>
                }
            }

            @*@foreach (var cell in row.ItemArray)
                    {
                        <td class="center">
                            @cell.ToString()
                        </td>

                    }*@


            @GetRecordEDR(row)

        </tr>
    }

    </tbody>
</table>
<div class="clearfix">
    <div class="pull-right tableTools-container">

    </div>
</div>
<script type="text/javascript">

    $('.print-record').hide();

    function print(id) {

        var data = { id: id };
        var title = $("#DocName").val();
        var controller = $("#Controller").val();
        var url = "/" + controller + "/" + "Print";
        try {

            $.ajax({
                //   url:  '@Url.Action("Print", "CashOut")',
                url: url,
                data: data,
                success: function(response) {
                    var result = response;

                    if (result != null) {

                        localStorage['REPORT_title'] = title;
                        localStorage['REPORT'] = result;
                        newWin = window.open("/Print/PrintDialog");
                    }
                }
            });

        } catch (err) {
            alert(err);
        }


    }

</script>