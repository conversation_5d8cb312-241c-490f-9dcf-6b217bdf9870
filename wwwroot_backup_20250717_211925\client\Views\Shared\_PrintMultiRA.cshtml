﻿<div class="inline pos-rel">
    <button class="btn btn-link btn-primary dropdown-toggle" data-toggle="dropdown" data-position="auto">
        <i class="ace-icon fa fa-print icon-only bigger-110"></i>
    </button>
    <ul class="dropdown-menu dropdown-only-icon dropdown-yellow dropdown-menu-right dropdown-caret dropdown-close">
        <li>
            <a class="btn btn-link fa fa-print " style="padding-right: 10px" onclick="print(@Model.ID)">
                قيد بسيط
            </a>
        </li>

        <li>
            <a class="btn btn-link fa fa-print " style="padding-right: 10px" onclick="printPath(@Model.ID,'PrintCreditor')">
                اشعار دائن
            </a>
        </li>

        <li>
            <a class="btn btn-link fa fa-print " style="padding-right: 10px" onclick="printPath(@Model.ID,'PrintDebitor')">
                اشعار مدين
            </a>
        </li>

    </ul>
    
    
    @Html.ActionButton((string)ViewBag.PageName, "Depend", "", "check green", "depend-record", "أعتماد")

    @Html.Partial("_RecordAction")
</div>

