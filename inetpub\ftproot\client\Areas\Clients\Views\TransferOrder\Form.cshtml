﻿@model AppTech.MSMS.Domain.Models.TransferOrder

@{
    Layout = "~/Views/Shared/_MultiLayout.cshtml";
}

<input type="hidden" name="AccountName" value="@AppTech.MSMS.Web.Security.CurrentUser.CurrentSession.Party.Name" />
<input type="hidden" name="Device" value="Web"/>
<div class="form-group">
    @Html.LabelFor(model => model.ServiceID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ServiceID, (SelectList) ViewBag.Services, new {id = "ServiceID", onchange = "onServiceChanged(this)"})
        @Html.ValidationMessageFor(model => model.ServiceID)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.ExchangerID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ExchangerID, (SelectList) ViewBag.Exchangers, new {})
        @Html.ValidationMessageFor(model => model.ExchangerID)
    </div>
</div>


<div class="extra-info">


    <div class="form-group">
        @Html.LabelFor(model => model.TransferNumber, new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EditorFor(model => model.TransferNumber)
            @Html.ValidationMessageFor(model => model.TransferNumber)
        </div>
    </div>

</div>


<div class="form-group form-inline">
    @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Amount, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>

<div class="form-group">
    <label class="control-label col-md-2">العملة</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.CurrencyID, new[]
        {
            new SelectListItem {Text = "يمني", Value = "1"},
            new SelectListItem {Text = "دولار", Value = "2"},
            new SelectListItem {Text = "سعودي", Value = "3"}
        })

    </div>
    @Html.ValidationMessageFor(model => model.CurrencyID)
</div>


<div class="form-group">
    @Html.LabelFor(model => model.SenderName, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderName)
        @Html.ValidationMessageFor(model => model.SenderName)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SenderMobile, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderMobile)
        @Html.ValidationMessageFor(model => model.SenderMobile)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.ReceiverName, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.ReceiverName)
        @Html.ValidationMessageFor(model => model.ReceiverName)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ReceiverMobile, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.ReceiverMobile)
        @Html.ValidationMessageFor(model => model.ReceiverMobile)
    </div>
</div>
<div class="extra-info">




    <div class="form-group">
        <label class="col-sm-2 control-label">صوره البطاقة 1</label>
        <div style="position: relative;">
            <input type="file" name="ImageData" size="40" onchange="showImg(this)">
        </div>
        <img class="img-thumbnail" width="150" height="150" id="preview" />
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">صوره البطاقة 2</label>
        <div style="position: relative;">
            <input type="file" name="ImageData2" size="40" onchange="showImg2(this)">
        </div>
        <img class="img-thumbnail" width="150" height="150" id="preview2" />
    </div>
</div>
@*
    <div id="extra-info">


        <div class="form-group">
            @Html.LabelFor(model => model.TransferNumber, new {@class = "control-label col-md-2"})
            <div class="col-md-10">
                @Html.EditorFor(model => model.TransferNumber)
                @Html.ValidationMessageFor(model => model.TransferNumber)
            </div>
        </div>

    <div class="form-group">
            <label class="col-sm-2 control-label">نوع البطاقة</label>

            <div class="col-sm-10">
                @Html.DropDownListFor(model => model.ReceiverCardType, new[]
                {
                    new SelectListItem {Text = "شخصية", Value = "شخصية"},
                    new SelectListItem {Text = "جواز سفر", Value = "جواز سفر"},
                    new SelectListItem {Text = "عائلية", Value = "عائلية"},
                    new SelectListItem {Text = "عسكرية", Value = "عسكرية"},
                    new SelectListItem {Text = "أخرى", Value = "أخرى"}
                })


            </div>
            @Html.ValidationMessageFor(model => model.ReceiverCardType)
        </div>


        <div class="form-group">
            @Html.LabelFor(model => model.ReceiverCardNo, new {@class = "control-label col-md-2"})
            <div class="col-md-10">
                @Html.EditorFor(model => model.ReceiverCardNo)
                @Html.ValidationMessageFor(model => model.ReceiverCardNo)
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.ReceiverCardIssuerPlace, new {@class = "control-label col-md-2"})
            <div class="col-md-10">
                @Html.EditorFor(model => model.ReceiverCardIssuerPlace)
                @Html.ValidationMessageFor(model => model.ReceiverCardIssuerPlace)
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.ReceiverCardIssuerDate, new {@class = "control-label col-md-2"})
            <div class="col-md-10">
                @Html.EditorFor(model => model.ReceiverCardIssuerDate)
                @Html.ValidationMessageFor(model => model.ReceiverCardIssuerDate)
            </div>
        </div>*@

@*<div class="form-group">
            <label class="col-sm-2 control-label">صوره البطاقة</label>
            <div class="col-sm-10">
                <input type="file" name="ImageData" title="search image" id="ImageData" onchange="show(this)"/>
                <div>
                    <img id="user_img" height="100" width="90" style="border: solid"/>
                </div>
            </div>
        </div
    </div>
    >*@

<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note)
        @Html.ValidationMessageFor(model => model.Note)
    </div>
</div>


<script>
    $(".extra-info").hide();

    function onServiceChanged(self) {

        var service = self.options[self.selectedIndex].value;
        if (service == 10011) {
            {
                $(".extra-info").show();
                $('#ReceiverName').val($('#AccountName').val());
            }
        } else {
            $(".extra-info").hide();
        }

    }
</script>
@*

    <div class="form-group">
                @Html.LabelFor(model => model.ImageName, new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.ImageName)
                    @Html.ValidationMessageFor(model => model.ImageName)
                </div>
            </div>

*@