﻿@model AppTech.MSMS.Domain.Models.CardFaction
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-horizontal">

    @Html.ValidationSummary(true)

    <div class="form-group">
        @Html.LabelFor(model => model.Number , new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Number)
            @Html.ValidationMessageFor(model => model.Number)
        </div>
    </div>  
    <div class="form-group">
        @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Name)
            @Html.ValidationMessageFor(model => model.Name)
        </div>
    </div> 
    <div class="form-group">
        @Html.LabelFor(model => model.CardTypeID, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.CardTypeID, (SelectList)ViewBag.CardTypes, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.CardTypeID)
        </div>
    </div>
    <div class="form-group">
        @Html.Label("سعر التكلفة", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.CostPrice)
            @Html.ValidationMessageFor(model => model.CostPrice)
        </div>
    </div>
    <div class="form-group">
        @Html.Label("سعر البيع", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.SelePrice)
            @Html.ValidationMessageFor(model => model.SelePrice)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Note)
            @Html.ValidationMessageFor(model => model.Note)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Active, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Active)
            @Html.ValidationMessageFor(model => model.Active)
        </div>
    </div>
</div>
