﻿@model AppTech.MSMS.Domain.Reports.Models.FundReportModel
@using Obout.Mvc.ComboBox
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}
@{
    Html.RenderPartial("_DateControl");
}

<span class="lbl">اسم الصندوق </span>
<div class="form-group">
    <div class="col-md-10">
        @Html.Obout(new ComboBox("AccountID")
        {
            Width = 230,
            FilterType = ComboBoxFilterType.Contains
        })
        @Html.ValidationMessageFor(model => model.AccountID)
    </div>
</div>
<div class="hr hr-dotted hr-24"></div>
<div class="space-6"></div>
<span class="lbl"> العملة</span>
@Html.DropDownListFor(m => m.CurrencyID, (SelectList)ViewBag.Currencies)
<div class="hr hr-dotted hr-24"></div>

<script>

    $("#export-pd").hide();
</script>