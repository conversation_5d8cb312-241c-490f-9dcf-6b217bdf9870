﻿@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.RemittanceIn
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<div class="form-group">
    @Html.LabelFor(model => model.TargetPointID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @*<select id="TargetPointID" name="TargetPointID" class="form-control"></select>*@
        @Html.DropDownListFor(model => model.TargetPointID, (SelectList)ViewBag.Targets, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.TargetPointID, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group form-inline">
    @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Amount, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.CurrencyID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CurrencyID, (SelectList)ViewBag.Currencies, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.CurrencyID, "", new { @class = "text-danger" })
    </div>
</div>
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">العمولة</h3>
    </div>
    <div class="panel-body">


        <div class="form-group">
            @Html.Label("العمولة", new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                <input type="text" id="CommissionAmount" name="CommissionAmount" readonly="readonly" />
            </div>
        </div>


        <div class="form-group">
            @Html.Label("عملة العمولة", new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                <input type="text" id="currencyname" name="currencyname" readonly="readonly" />
            </div>
        </div>

    </div>

    <div class="panel-footer">
        <input type="button" class="btn btn-white" value="احتساب العمولة" onclick="calcCommission();" />
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.DebitorAccountID, new { @class = "control-label col-md-2" })
    <div class="col-sm-10">
        @Html.Obout(new ComboBox("DebitorAccountID")
        {
            Width = 300,
            SelectedValue = Model.DebitorAccountID == 0 ? null : Model.DebitorAccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains
        })
        @Html.ValidationMessageFor(model => model.DebitorAccountID)
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.BeneficiaryName, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.BeneficiaryName, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.BeneficiaryName, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.BeneficiaryPhone, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.BeneficiaryPhone, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.BeneficiaryPhone, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SenderName, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderName, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.SenderName, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SenderPhone, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderPhone, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.SenderPhone, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Date, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Date, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.Date, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Purpose, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Purpose, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Purpose, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>


<script>

    function calcCommission() {

        var accountId = 0;
        try {
            accountId = $("#DebitorAccountID").val();
        }
        catch (e) { }

        var data = {
            CurrencyID: $("#CurrencyID").val(),
            Amount: $("#Amount").val(),
            ExchangerID: $("#TargetPointID").val(),
            AccountID: accountId,
            RemittanceType: 0
        };
        AjaxCall('/Remittance/RemittanceIn/CalcCommission', JSON.stringify(data), 'POST').done(function (response) {

            $("#CommissionAmount").val(response.Amount);
            $("#currencyname").val(response.currencyName);


        }).fail(function (xhr, textStatus, errorThrown) {
            parseAndShowError(xhr, textStatus, errorThrown);
        });
    }

    $(function () {
        try {
            //formHelper.onSuccess = function (data) {
            //    log('remittance-formHelper.onSuccess');
            //    hideLoading();
            //    i('data> ' + data);
            //    resetButton();
            //    $('#crudform')[0].reset();
            //    ar('تم أرسال الحوالة بنجاح');
            //    if (confirm("هل تريد طباعة سند أرسال حوالة")) {
            //        PrintReceipt('أرسال حوالة', data);
            //    }
            //}

            formHelper.onBegin = function () {

                var msg = "سوف يتم أرسال حوالة بمبلغ  " +
                    $("#Amount").val() +
                    ' ' +
                    $("#CurrencyID option:selected").text() +
                    ' هل انت متأكد';
                if (!confirm(msg)) {
                    i('not confirmed');
                    return false;
                } else {

                    i('confirmed');
                    //  showLoading();
                    return true;

                }

            }

        } catch (e) {

        }
    });
</script>

<script>

    function fillList(response, element, title) {
        if (response.length > 0) {
            $('#' + element).html('');
            var options = '';
            options += '<option value="Select">' + title + '  </option>';
            for (var i = 0; i < response.length; i++) {
                options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
            }
            $('#' + element).append(options);
        }
    }

    $(function () {

        //$('#Province').on("change",
        //    function() {
        //        i('change');
        //        var province = $('#Province').val();
        //        i('pro>' + province);
        //        var obj = { province: province };
        //        AjaxCall('/Remittance/RemittanceIn/GetRegions', JSON.stringify(obj), 'POST').done(function(response) {
        //            fillList(response, 'TargetRegionID', 'اختر المنطقة');

        //        }).fail(function(error) {
        //            alert(error.StatusText);
        //        });
        //    });

        //$('#TargetRegionID').on("change",
        //    function () {
        //        i('change region');
        //        var regionId = $('#TargetRegionID').val();
        //        i('id ' + regionId);
        //        if (regionId === "0")
        //            return;

        //        var obj = { region: regionId };
        //        AjaxCall('/Remittance/RemittanceIn/GetRemittancePoints', JSON.stringify(obj), 'POST').done(
        //            function (response) {
        //                if (response.length > 0) {
        //                    $('#TargetPointID').html('');
        //                    fillList(response, 'TargetPointID', 'اختر الجهة');
        //                }
        //            }).fail(function (error) {
        //                alert(error.StatusText);
        //            });
        //    });
    });

</script>