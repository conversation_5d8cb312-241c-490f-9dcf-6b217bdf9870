﻿@using AppTech.Common.Extensions
@using AppTech.MSMS.Web.Security
@model AppTech.MSMS.Domain.Models.Client

<div>
<div class="hr dotted"></div>

<div>
<div id="user-profile-1" class="user-profile row">
<div class="col-xs-12 col-sm-3 center">
    <div>
        <span class="profile-picture">
            <img id="avatar" class="editable img-responsive" alt="Alex's Avatar" src="@Url.Content("~/Photos/profile-pic.jpg")"/>
        </span>

        <div class="space-4"></div>

        <div class="width-80 label label-info label-xlg arrowed-in arrowed-in-right">
            <div class="inline position-relative">
                <a href="" class="user-title-label dropdown-toggle" data-toggle="dropdown">
                    @if (Model.IsActive.AsBool())
                    {
                        <i class="ace-icon fa fa-circle light-green"></i>
                    }
                    else
                    {
                        <i class="ace-icon fa fa-circle red"></i>
                    }


                    &nbsp;
                    <span class="white">@Model.Name</span>
                </a>

                <ul class="align-left dropdown-menu dropdown-caret dropdown-lighter">
                    <li class="dropdown-header"> الحالة </li>

                    @if (Model.IsActive.AsBool())
                    {
                        <li>
                            <a href="">
                                <i class="ace-icon fa fa-circle green"></i>
                                &nbsp;
                                <span class="green">مفعل</span>
                            </a>
                        </li>
                    }
                    else
                    {
                        <li>
                            <a href="">
                                <i class="ace-icon fa fa-circle red"></i>
                                &nbsp;
                                <span class="red">موقف</span>
                            </a>
                        </li>
                    }


                    @*<li>
                                    <a href="">
                                        <i class="ace-icon fa fa-circle grey"></i>
                                        &nbsp;
                                        <span class="grey">Invisible</span>
                                    </a>
                                </li>*@
                </ul>
            </div>
        </div>
    </div>

    <div class="space-6"></div>

    <div class="profile-contact-info">
        <input type="hidden" id="id" value="@Model.ID"/>
        <div class="profile-contact-links align-right">


            <li>
                <a class="btn btn-link " href="/Client/Permissions/@Model.ID">

                    <i class="ace-icon fa fa-user-secret bigger-120 green"></i>
                    عداد الصلاحيات

                </a>
            </li>
            <a href="" class="btn btn-link " id="bootbox-reset-pass">
                <i class="ace-icon fa fa-key bigger-120 green"></i>
                أعادة تعيين كلمة المرور
            </a>


        </div>

        <div class="space-6"></div>
        @*
                    <div class="profile-social-links align-center">
                        <a href="" class="tooltip-info" title="" data-original-title="Visit my Facebook">
                            <i class="middle ace-icon fa fa-facebook-square fa-2x blue"></i>
                        </a>

                        <a href="" class="tooltip-info" title="" data-original-title="Visit my Twitter">
                            <i class="middle ace-icon fa fa-twitter-square fa-2x light-blue"></i>
                        </a>

                        <a href="" class="tooltip-error" title="" data-original-title="Visit my Pinterest">
                            <i class="middle ace-icon fa fa-pinterest-square fa-2x red"></i>
                        </a>
                    </div>*@
    </div>

    <div class="hr hr12 dotted"></div>
    <div class="clearfix">
        <div class="grid2">
            <span class="bigger-175 blue">@ViewBag.CurrentBalance</span>

            <br/>
            الرصيد
        </div>

        <div class="grid2">
            <span class="bigger-175 blue">@ViewBag.SlatingAmount</span>

            <br/>
            التسقيف
        </div>
    </div>

    <div class="hr hr16 dotted"></div>
</div>

<div class="col-xs-12 col-sm-9">
@*<div class="center">
                
                <span class="btn btn-app btn-sm btn-light no-hover">

                        <span class="line-height-1 bigger-170 blue">  </span>

                        <br />
                        <span class="line-height-1 smaller-90"> الرصيد </span>
                    </span>

                <span class="btn btn-app btn-sm btn-yellow no-hover">
                        <span class="line-height-1 bigger-170"> 32 </span>

                        <br />
                        <span class="line-height-1 smaller-90"> السقف </span>
                    </span>

                <span class="btn btn-app btn-sm btn-pink no-hover">
                        <span class="line-height-1 bigger-170"> 4 </span>

                        <br />
                        <span class="line-height-1 smaller-90"> Projects </span>
                    </span>

                <span class="btn btn-app btn-sm btn-grey no-hover">
                        <span class="line-height-1 bigger-170"> 23 </span>

                        <br />
                        <span class="line-height-1 smaller-90"> Reviews </span>
                    </span>

                <span class="btn btn-app btn-sm btn-success no-hover">
                        <span class="line-height-1 bigger-170"> 7 </span>

                        <br />
                        <span class="line-height-1 smaller-90"> Albums </span>
                    </span>

                <span class="btn btn-app btn-sm btn-primary no-hover">
                        <span class="line-height-1 bigger-170"> 55 </span>

                        <br />
                        <span class="line-height-1 smaller-90"> Contacts </span>
                    </span>
            </div>*@

<div class="space-12"></div>


<div class="profile-user-info profile-user-info-striped">

    <div class="profile-info-row">
        <div class="profile-info-name"> رقم العميل </div>

        <div class="profile-info-value">
            <span class="editable" id="age"> @Html.DisplayFor(model => model.Number) </span>
        </div>
    </div>


    <div class="profile-info-row">
        <div class="profile-info-name"> اسم العميل </div>

        <div class="profile-info-value">
            <span class="editable" id="username"> @Html.DisplayFor(model => model.Name)</span>
        </div>
    </div>

    <div class="profile-info-row">
        <div class="profile-info-name"> العنوان </div>

        <div class="profile-info-value">
            <i class="fa fa-map-marker light-orange bigger-110"></i>
            <span class="editable" id="country">اليمن</span>
            <span class="editable" id="city"> @Html.DisplayFor(model => model.Address)</span>
        </div>
    </div>

    <div class="profile-info-row">
        <div class="profile-info-name"> اسم المحل </div>

        <div class="profile-info-value">
            <span class="editable" id="signup"> @Html.DisplayFor(model => model.ShopName)</span>
        </div>
    </div>

    <div class="profile-info-row">
        <div class="profile-info-name"> رقم الهاتف </div>

        <div class="profile-info-value">
            <span class="editable" id="login"> @Html.DisplayFor(model => model.PhoneNumber)</span>
        </div>
    </div>

    <div class="profile-info-row">
        <div class="profile-info-name"> تاريخ التسجيل </div>

        <div class="profile-info-value">
            <span class="editable" id="about"> @Html.DisplayFor(model => model.CreatedTime)</span>
        </div>
    </div>


    <div class="profile-info-row">
        <div class="profile-info-name"> نوع البطاقة </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.CardType)</span>
        </div>
    </div>

    <div class="profile-info-row">
        <div class="profile-info-name"> رقم البطاقة </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.CardNumber)</span>
        </div>
    </div>

    <div class="profile-info-row">
        <div class="profile-info-name"> مكان الأصدار </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.CardIssuePlace)</span>
        </div>
    </div>


    <div class="profile-info-row">
        <div class="profile-info-name"> تاريخ الأصدار </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.CardIssueDate)</span>
        </div>
    </div>


    <div class="profile-info-row">
        <div class="profile-info-name"> صورة البطاقة </div>

        <div class="profile-info-value">
            @if (!string.IsNullOrEmpty(Model.ImageName))
            {
                <img id="avatar" class="editable img-responsive" alt="Alex's Avatar" src="@Url.Content(Model.ImageName)"/>
            }

        </div>
    </div>


</div>

<div class="space-20"></div>

@*<div class="widget-box transparent">
                    <div class="widget-header widget-header-small">
                        <h4 class="widget-title blue smaller">
                            <i class="ace-icon fa fa-rss orange"></i>
                            Recent Activities
                        </h4>

                        <div class="widget-toolbar action-buttons">
                            <a href="" data-action="reload">
                                <i class="ace-icon fa fa-refresh blue"></i>
                            </a>
                            &nbsp;
                            <a href="" class="pink">
                                <i class="ace-icon fa fa-trash-o"></i>
                            </a>
                        </div>
                    </div>

                    <div class="widget-body">
                        <div class="widget-main padding-8">
                            <div id="profile-feed-1" class="profile-feed">
                                <div class="profile-activity clearfix">
                                    <div>
                                        <img class="pull-left" alt="Alex Doe's avatar" src="@Url.Content("~/Content/images/yemen_mobile.jpg")" />
                                        <a class="user" href=""> Alex Doe </a>
                                        changed his profile photo.
                                        <a href="">Take a look</a>

                                        <div class="time">
                                            <i class="ace-icon fa fa-clock-o bigger-110"></i>
                                            an hour ago
                                        </div>
                                    </div>

                                    <div class="tools action-buttons">
                                        <a href="" class="blue">
                                            <i class="ace-icon fa fa-pencil bigger-125"></i>
                                        </a>

                                        <a href="" class="red">
                                            <i class="ace-icon fa fa-times bigger-125"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="profile-activity clearfix">
                                    <div>
                                        <img class="pull-left" alt="Susan Smith's avatar" src="assets/images/avatars/avatar1.png" />
                                        <a class="user" href=""> Susan Smith </a>

                                        is now friends with Alex Doe.
                                        <div class="time">
                                            <i class="ace-icon fa fa-clock-o bigger-110"></i>
                                            2 hours ago
                                        </div>
                                    </div>

                                    <div class="tools action-buttons">
                                        <a href="" class="blue">
                                            <i class="ace-icon fa fa-pencil bigger-125"></i>
                                        </a>

                                        <a href="" class="red">
                                            <i class="ace-icon fa fa-times bigger-125"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="profile-activity clearfix">
                                    <div>
                                        <i class="pull-left thumbicon fa fa-check btn-success no-hover"></i>
                                        <a class="user" href=""> Alex Doe </a>
                                        joined
                                        <a href="">Country Music</a>

                                        group.
                                        <div class="time">
                                            <i class="ace-icon fa fa-clock-o bigger-110"></i>
                                            5 hours ago
                                        </div>
                                    </div>

                                    <div class="tools action-buttons">
                                        <a href="" class="blue">
                                            <i class="ace-icon fa fa-pencil bigger-125"></i>
                                        </a>

                                        <a href="" class="red">
                                            <i class="ace-icon fa fa-times bigger-125"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="profile-activity clearfix">
                                    <div>
                                        <i class="pull-left thumbicon fa fa-picture-o btn-info no-hover"></i>
                                        <a class="user" href=""> Alex Doe </a>
                                        uploaded a new photo.
                                        <a href="">Take a look</a>

                                        <div class="time">
                                            <i class="ace-icon fa fa-clock-o bigger-110"></i>
                                            5 hours ago
                                        </div>
                                    </div>

                                    <div class="tools action-buttons">
                                        <a href="" class="blue">
                                            <i class="ace-icon fa fa-pencil bigger-125"></i>
                                        </a>

                                        <a href="" class="red">
                                            <i class="ace-icon fa fa-times bigger-125"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="profile-activity clearfix">
                                    <div>
                                        <img class="pull-left" alt="David Palms's avatar" src="assets/images/avatars/avatar4.png" />
                                        <a class="user" href=""> David Palms </a>

                                        left a comment on Alex's wall.
                                        <div class="time">
                                            <i class="ace-icon fa fa-clock-o bigger-110"></i>
                                            8 hours ago
                                        </div>
                                    </div>

                                    <div class="tools action-buttons">
                                        <a href="" class="blue">
                                            <i class="ace-icon fa fa-pencil bigger-125"></i>
                                        </a>

                                        <a href="" class="red">
                                            <i class="ace-icon fa fa-times bigger-125"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="profile-activity clearfix">
                                    <div>
                                        <i class="pull-left thumbicon fa fa-pencil-square-o btn-pink no-hover"></i>
                                        <a class="user" href=""> Alex Doe </a>
                                        published a new blog post.
                                        <a href="">Read now</a>

                                        <div class="time">
                                            <i class="ace-icon fa fa-clock-o bigger-110"></i>
                                            11 hours ago
                                        </div>
                                    </div>

                                    <div class="tools action-buttons">
                                        <a href="" class="blue">
                                            <i class="ace-icon fa fa-pencil bigger-125"></i>
                                        </a>

                                        <a href="" class="red">
                                            <i class="ace-icon fa fa-times bigger-125"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="profile-activity clearfix">
                                    <div>
                                        <img class="pull-left" alt="Alex Doe's avatar" src="assets/images/avatars/avatar5.png" />
                                        <a class="user" href=""> Alex Doe </a>

                                        upgraded his skills.
                                        <div class="time">
                                            <i class="ace-icon fa fa-clock-o bigger-110"></i>
                                            12 hours ago
                                        </div>
                                    </div>

                                    <div class="tools action-buttons">
                                        <a href="" class="blue">
                                            <i class="ace-icon fa fa-pencil bigger-125"></i>
                                        </a>

                                        <a href="" class="red">
                                            <i class="ace-icon fa fa-times bigger-125"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="profile-activity clearfix">
                                    <div>
                                        <i class="pull-left thumbicon fa fa-key btn-info no-hover"></i>
                                        <a class="user" href=""> Alex Doe </a>

                                        logged in.
                                        <div class="time">
                                            <i class="ace-icon fa fa-clock-o bigger-110"></i>
                                            12 hours ago
                                        </div>
                                    </div>

                                    <div class="tools action-buttons">
                                        <a href="" class="blue">
                                            <i class="ace-icon fa fa-pencil bigger-125"></i>
                                        </a>

                                        <a href="" class="red">
                                            <i class="ace-icon fa fa-times bigger-125"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="profile-activity clearfix">
                                    <div>
                                        <i class="pull-left thumbicon fa fa-power-off btn-inverse no-hover"></i>
                                        <a class="user" href=""> Alex Doe </a>

                                        logged out.
                                        <div class="time">
                                            <i class="ace-icon fa fa-clock-o bigger-110"></i>
                                            16 hours ago
                                        </div>
                                    </div>

                                    <div class="tools action-buttons">
                                        <a href="" class="blue">
                                            <i class="ace-icon fa fa-pencil bigger-125"></i>
                                        </a>

                                        <a href="" class="red">
                                            <i class="ace-icon fa fa-times bigger-125"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="profile-activity clearfix">
                                    <div>
                                        <i class="pull-left thumbicon fa fa-key btn-info no-hover"></i>
                                        <a class="user" href=""> Alex Doe </a>

                                        logged in.
                                        <div class="time">
                                            <i class="ace-icon fa fa-clock-o bigger-110"></i>
                                            16 hours ago
                                        </div>
                                    </div>

                                    <div class="tools action-buttons">
                                        <a href="" class="blue">
                                            <i class="ace-icon fa fa-pencil bigger-125"></i>
                                        </a>

                                        <a href="" class="red">
                                            <i class="ace-icon fa fa-times bigger-125"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>*@

<div class="hr hr2 hr-double"></div>

<div class="space-6"></div>
<div class="center">
    <a href="/Client/Index" class="btn btn-sm btn-primary btn-white btn-round">
        <i class="ace-icon fa fa-rss bigger-150 middle orange2"></i>
        <span class="bigger-110">الرجوع الى الخلف</span>

        <i class="icon-on-right ace-icon fa fa-arrow-right"></i>
    </a>
</div>
</div>
</div>
</div>


</div>

<script src="~/Scripts/bootbox.js"></script>
<script src="~/Scripts/jquery.gritter.min.js"></script>
<script src="~/Scripts/spin.js"></script>
<script>

    $("#bootbox-reject").on('click',
        function() {
            try {

                var id = $("#mr_id").val();
                var orderType = $("#orderType");
                bootbox.prompt("الرجاء قم بأدخال سبب الرفض",
                    function(result) {
                        alert(result);
                    });
            } catch (e) {
                alert(e);
            }
        });

    jQuery(function($) {

        $("#bootbox-reset-pass").on(ace.click_event,
            function() {
                bootbox.confirm("سوف يتم اعداد كلمة المرور لهذا المستخدم , هل انت متأكد?",
                    function(result) {
                        if (result) {
                            try {
                                var id = $("#id").val();

                                $.ajax({
                                    url: '@Url.Action("ResetPassword", "Client")',
                                    data: { id: id },
                                    success: function(data) {
                                        alert(data);
                                    },
                                    error: function(xhr, ajaxOptions, thrownError) {
                                        alert(xhr.responseText);

                                    }
                                });
                            } catch (e) {
                                alert(e);
                            }

                        }
                    });
            });

    });
</script>