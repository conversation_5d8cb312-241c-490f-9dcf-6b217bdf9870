﻿@model AppTech.MSMS.Domain.Models.RemittanceRegion
@{
    Layout = "/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    @Html.LabelFor(model => model.Name, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Name, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ProvinceID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.ProvinceID, (SelectList) ViewBag.Provinces, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.ProvinceID, "", new {@class = "text-danger"})
    </div>
</div>