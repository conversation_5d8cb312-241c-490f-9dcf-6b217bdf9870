#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 15:23:14
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 15:23:14 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 200 0 0 109
2025-07-17 15:23:14 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 200 0 0 0
2025-07-17 15:23:39 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+<PERSON>64;+x64)+AppleWebKit/537.36+(KHT<PERSON>,+like+Gecko)+Chrome/*********+Safari/537.36 - 200 0 0 1
2025-07-17 15:23:39 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 200 0 0 0
2025-07-17 15:23:48 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 200 0 0 0
2025-07-17 15:23:48 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 200 0 0 0
2025-07-17 15:23:48 ::1 GET /parent_injection.js.map - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 2
2025-07-17 15:23:48 ::1 GET /parent_injection.js.map - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
2025-07-17 15:24:52 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 200 0 0 1
2025-07-17 15:24:52 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 200 0 0 0
2025-07-17 15:24:55 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 200 0 0 1
2025-07-17 15:24:55 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 200 0 0 0
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 15:26:22
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 15:26:22 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 403 14 0 116
2025-07-17 15:26:22 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 403 14 0 1
2025-07-17 15:26:25 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 403 14 0 0
2025-07-17 15:26:25 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 403 14 0 1
2025-07-17 15:26:25 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 403 14 0 0
2025-07-17 15:26:25 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 403 14 0 1
2025-07-17 15:27:30 ::1 GET /public/script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
2025-07-17 15:27:30 ::1 GET /public/script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 15:27:41
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 15:27:41 ::1 GET /public/script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 116
2025-07-17 15:27:41 ::1 GET /public/script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
2025-07-17 15:28:15 ::1 GET /public/script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 0
2025-07-17 15:28:15 ::1 GET /public/script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
2025-07-17 15:28:18 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 403 14 0 1
2025-07-17 15:28:18 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 403 14 0 2
2025-07-17 15:28:24 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 403 14 0 1
2025-07-17 15:28:24 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 403 14 0 1
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 15:28:40
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 15:28:40 ::1 GET /script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 84
2025-07-17 15:28:40 ::1 GET /script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
2025-07-17 15:32:21 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 403 14 0 1
2025-07-17 15:32:21 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 403 14 0 2
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 15:33:59
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 15:33:59 ::1 GET /script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 108
2025-07-17 15:34:02 ::1 GET /script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 0
2025-07-17 15:34:02 ::1 GET /script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
2025-07-17 15:34:49 ::1 GET /script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
2025-07-17 15:34:49 ::1 GET /script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 16:14:10
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 16:14:10 ::1 GET /public/script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 106
2025-07-17 16:14:10 ::1 GET /public/script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 3
2025-07-17 16:14:17 ::1 GET /public/script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 0
2025-07-17 16:14:17 ::1 GET /public/script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
2025-07-17 16:14:25 ::1 GET /script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 0
2025-07-17 16:14:25 ::1 GET /script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
2025-07-17 16:14:29 ::1 GET /script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 0
2025-07-17 16:14:29 ::1 GET /script.js - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
2025-07-17 16:15:20 ::1 GET /public - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 0
2025-07-17 16:15:20 ::1 GET /public - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
2025-07-17 16:16:37 ::1 GET /public - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 0
2025-07-17 16:16:37 ::1 GET /public - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 2
2025-07-17 16:16:42 ::1 GET /public - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 0
2025-07-17 16:16:42 ::1 GET /public - 80 - ::1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/*********+Safari/537.36 - 404 0 2 1
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 16:57:33
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 16:57:33 ::1 GET / - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 403 14 0 86
2025-07-17 16:57:40 127.0.0.1 GET /collections_system - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 404 0 2 0
2025-07-17 16:57:40 127.0.0.1 GET /favicon.ico - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/collections_system 404 0 2 1
2025-07-17 16:59:34 127.0.0.1 GET /collections_system/reports.php - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 404 0 2 0
2025-07-17 17:08:39 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 0 2 1
2025-07-17 17:08:39 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 0 2 0
2025-07-17 17:08:39 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 0 2 0
2025-07-17 17:08:41 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 0 2 1
2025-07-17 17:09:36 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 4
2025-07-17 17:09:36 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 6
2025-07-17 17:09:36 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 6
2025-07-17 17:09:36 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 17:09:53 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 500 19 33 1
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 17:34:33
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 17:34:33 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 120
2025-07-17 17:34:33 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 4
2025-07-17 17:34:43 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 500 19 33 2
2025-07-17 17:34:43 127.0.0.1 GET /favicon.ico - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/portal 404 0 64 5
2025-07-17 17:35:44 ::1 GET /collections_system - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 0 2 1
2025-07-17 17:36:54 127.0.0.1 GET /collections_system - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 404 0 2 0
2025-07-17 17:44:50 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 17:44:50 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 1
2025-07-17 17:44:50 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 6
2025-07-17 17:44:58 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 500 19 33 3
2025-07-17 17:44:58 127.0.0.1 GET /favicon.ico - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/portal 404 0 2 0
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 17:47:15
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 17:47:15 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 86
2025-07-17 17:47:15 ::1 GET /portal/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 1
2025-07-17 17:47:15 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 3
2025-07-17 17:47:15 ::1 GET /api/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 0
2025-07-17 17:47:15 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 2
2025-07-17 17:47:15 ::1 GET /client/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 0
2025-07-17 17:47:15 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 1
2025-07-17 17:47:15 ::1 GET /apinewAN/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 0
2025-07-17 17:47:15 ::1 GET /collections_system - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 0 2 2
2025-07-17 17:47:34 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 301 0 0 0
2025-07-17 17:47:34 127.0.0.1 GET /portal/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 200 0 0 0
2025-07-17 17:47:40 127.0.0.1 GET /api - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 301 0 0 0
2025-07-17 17:47:40 127.0.0.1 GET /api/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 200 0 0 0
2025-07-17 17:47:43 127.0.0.1 GET /collections_system - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/api/ 404 0 2 0
2025-07-17 17:47:46 127.0.0.1 GET /api - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/api/ 301 0 0 1
2025-07-17 17:47:46 127.0.0.1 GET /api/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/api/ 304 0 0 0
2025-07-17 17:47:48 127.0.0.1 GET /client - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/api/ 301 0 0 0
2025-07-17 17:47:48 127.0.0.1 GET /client/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/api/ 200 0 0 0
2025-07-17 17:48:00 127.0.0.1 GET /collections_system - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/client/ 404 0 2 0
2025-07-17 17:48:03 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/client/ 301 0 0 0
2025-07-17 17:48:03 127.0.0.1 GET /portal/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/client/ 304 0 0 0
2025-07-17 17:48:06 127.0.0.1 GET /api - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/portal/ 301 0 0 0
2025-07-17 17:48:06 127.0.0.1 GET /api/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/portal/ 304 0 0 0
2025-07-17 17:48:07 127.0.0.1 GET /client - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/api/ 301 0 0 0
2025-07-17 17:48:07 127.0.0.1 GET /client/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/api/ 304 0 0 0
2025-07-17 17:48:08 127.0.0.1 GET /collections_system - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/client/ 404 0 2 0
2025-07-17 17:52:25 ::1 GET /collections_system - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 13 2
2025-07-17 17:52:25 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 0
2025-07-17 17:52:25 ::1 GET /portal/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 0
2025-07-17 17:52:25 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 0
2025-07-17 17:52:25 ::1 GET /api/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 0
2025-07-17 17:52:25 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 0
2025-07-17 17:52:25 ::1 GET /client/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 0
2025-07-17 17:52:25 ::1 GET /collections_system - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 13 1
2025-07-17 17:53:44 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 0
2025-07-17 17:53:44 ::1 GET /portal/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 0
2025-07-17 17:53:44 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 0
2025-07-17 17:53:44 ::1 GET /api/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 0
2025-07-17 17:53:44 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 0
2025-07-17 17:53:44 ::1 GET /client/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 0
2025-07-17 17:53:44 ::1 GET /collections_system - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 1
2025-07-17 17:53:44 ::1 GET /collections_system/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 41
2025-07-17 17:54:09 127.0.0.1 GET /collections_system - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 301 0 0 0
2025-07-17 17:54:09 127.0.0.1 GET /collections_system/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 200 0 0 0
2025-07-17 17:54:29 127.0.0.1 GET /collections_system/manage_agents.php - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/collections_system/ 404 0 2 1
2025-07-17 17:54:34 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/collections_system/ 301 0 0 0
2025-07-17 17:54:34 127.0.0.1 GET /portal/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/collections_system/ 304 0 0 0
2025-07-17 17:54:38 127.0.0.1 GET /client - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/collections_system/ 301 0 0 0
2025-07-17 17:54:38 127.0.0.1 GET /client/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/collections_system/ 304 0 0 0
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 18:05:35
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 18:05:35 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 79
2025-07-17 18:05:35 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 4
2025-07-17 18:05:35 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 6
2025-07-17 18:05:35 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 18:05:50 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 500 19 33 1
2025-07-17 18:05:56 127.0.0.1 GET /api - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 500 19 33 1
2025-07-17 18:07:58 127.0.0.1 GET /api - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 500 19 33 3
2025-07-17 18:08:39 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 18:08:39 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 18:08:39 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 18:08:39 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 2
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 18:11:18
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 18:11:18 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 98
2025-07-17 18:11:18 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 18:11:18 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 18:11:18 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 18:13:04
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 18:13:04 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 93
2025-07-17 18:13:04 ::1 GET /portal/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 57
2025-07-17 18:13:04 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 2
2025-07-17 18:13:04 ::1 GET /api/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 37
2025-07-17 18:13:04 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 1
2025-07-17 18:13:04 ::1 GET /client/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 36
2025-07-17 18:13:04 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 1
2025-07-17 18:13:04 ::1 GET /apinewAN/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 34
2025-07-17 18:13:04 ::1 GET /collections_system - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 2
2025-07-17 18:13:04 ::1 GET /collections_system/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 35
2025-07-17 18:13:20 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 301 0 0 0
2025-07-17 18:13:20 127.0.0.1 GET /portal/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 200 0 0 0
2025-07-17 18:13:26 127.0.0.1 GET /api - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 301 0 0 0
2025-07-17 18:13:26 127.0.0.1 GET /api/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 200 0 0 0
2025-07-17 18:13:33 127.0.0.1 GET /client - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 301 0 0 0
2025-07-17 18:13:33 127.0.0.1 GET /client/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 200 0 0 0
2025-07-17 18:13:38 127.0.0.1 GET /client/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 200 0 0 7
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 18:20:38
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 18:20:38 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 79
2025-07-17 18:20:38 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 18:20:38 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 4
2025-07-17 18:20:38 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 6
2025-07-17 18:20:38 ::1 GET /apiold - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 0 2 3
2025-07-17 18:20:38 ::1 GET /apiTEST - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 0 2 0
2025-07-17 18:20:38 ::1 GET /TopupInspector - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 0 2 0
2025-07-17 18:20:48 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 500 19 33 1
2025-07-17 18:20:51 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 500 19 33 2
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 18:23:36
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 18:23:36 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 84
2025-07-17 18:23:36 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 1
2025-07-17 18:23:36 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 18:23:36 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 1
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 18:25:19
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 18:25:19 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 87
2025-07-17 18:25:19 ::1 GET /portal/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 3 50 3
2025-07-17 18:25:19 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 2
2025-07-17 18:25:19 ::1 GET /api/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 3 50 1
2025-07-17 18:25:19 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 1
2025-07-17 18:25:19 ::1 GET /client/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 3 50 0
2025-07-17 18:25:19 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 2
2025-07-17 18:25:19 ::1 GET /apinewAN/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 3 50 4
2025-07-17 18:25:19 ::1 GET /collections_system - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 1
2025-07-17 18:25:19 ::1 GET /collections_system/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 26
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 18:27:04
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 18:27:04 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 90
2025-07-17 18:27:04 ::1 GET /portal/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 3 50 2
2025-07-17 18:27:04 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 2
2025-07-17 18:27:04 ::1 GET /api/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 3 50 1
2025-07-17 18:27:04 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 1
2025-07-17 18:27:04 ::1 GET /client/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 3 50 1
2025-07-17 18:27:04 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 1
2025-07-17 18:27:04 ::1 GET /apinewAN/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 404 3 50 1
2025-07-17 18:27:04 ::1 GET /collections_system - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 2
2025-07-17 18:27:04 ::1 GET /collections_system/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 28
2025-07-17 18:27:15 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 301 0 0 0
2025-07-17 18:27:15 127.0.0.1 GET /portal/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 404 3 50 1
2025-07-17 18:27:15 127.0.0.1 GET /favicon.ico - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/portal/ 404 0 64 1
2025-07-17 18:27:21 127.0.0.1 GET /api - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 301 0 0 0
2025-07-17 18:27:21 127.0.0.1 GET /api/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 404 3 50 0
2025-07-17 18:27:27 127.0.0.1 GET /client - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 301 0 0 0
2025-07-17 18:27:27 127.0.0.1 GET /client/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 404 3 50 1
2025-07-17 18:27:42 127.0.0.1 GET /client/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 404 3 50 0
2025-07-17 18:28:05 127.0.0.1 GET /client/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 404 3 50 0
2025-07-17 18:28:14 127.0.0.1 GET / - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 403 14 0 2
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 18:30:18
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 18:30:18 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 97
2025-07-17 18:30:18 ::1 GET /client/index.html - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 1
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 18:37:02
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 18:37:02 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 97
2025-07-17 18:37:02 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 18:37:02 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 4
2025-07-17 18:37:02 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 18:37:02 ::1 GET /collections_system - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 4
2025-07-17 18:37:02 ::1 GET /collections_system/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 29
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 18:43:15
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 18:43:15 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 183 98
2025-07-17 18:43:15 ::1 GET /api - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 183 3
2025-07-17 18:43:15 ::1 GET /client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 183 2
2025-07-17 18:43:15 ::1 GET /apinewAN - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 183 2
2025-07-17 18:43:15 ::1 GET /collections_system - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 2
2025-07-17 18:43:15 ::1 GET /collections_system/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 27
2025-07-17 18:43:22 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 500 19 183 1
2025-07-17 18:43:29 127.0.0.1 GET /collections_system - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 301 0 0 0
2025-07-17 18:43:29 127.0.0.1 GET /collections_system/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 200 0 0 0
2025-07-17 18:43:39 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/collections_system/ 500 19 183 1
2025-07-17 18:44:10 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 500 19 183 1
2025-07-17 18:44:45 127.0.0.1 GET /client - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/collections_system/ 500 19 183 1
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 18:49:03
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 18:49:03 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 107
2025-07-17 18:49:03 ::1 GET /portal/Home/Dashboard - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 2
2025-07-17 18:49:03 ::1 GET /portal/Home/Agent - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 2
2025-07-17 18:49:03 ::1 GET /portal/Home/Client - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 3
2025-07-17 18:49:03 ::1 GET /portal/Home/Merchant - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 500 19 33 2
#Software: Microsoft Internet Information Services 10.0
#Version: 1.0
#Date: 2025-07-17 18:53:27
#Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status time-taken
2025-07-17 18:53:27 ::1 GET /portal - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 301 0 0 94
2025-07-17 18:53:27 ::1 GET /portal/ - 80 - ::1 Mozilla/5.0+(Windows+NT;+Windows+NT+10.0;+en-US)+WindowsPowerShell/5.1.17763.7309 - 200 0 0 62
2025-07-17 18:53:42 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 301 0 0 0
2025-07-17 18:53:42 127.0.0.1 GET /portal/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 200 0 0 1
2025-07-17 18:54:05 127.0.0.1 GET /api - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/portal/ 500 19 183 4
2025-07-17 18:54:08 127.0.0.1 GET /portal - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/portal/ 301 0 0 0
2025-07-17 18:54:08 127.0.0.1 GET /portal/ - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 http://localhost/portal/ 304 0 0 0
2025-07-17 18:54:47 127.0.0.1 GET / - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 403 14 0 1
2025-07-17 18:55:32 127.0.0.1 GET /client - 80 - 127.0.0.1 Mozilla/5.0+(Windows+NT+10.0;+Win64;+x64;+rv:140.0)+Gecko/20100101+Firefox/140.0 - 500 19 183 3
