﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.AccountNotification
@{

    Layout = "~/Views/Shared/_Form.cshtml";
}

    <div class="form-group">
        @Html.LabelFor(model => model.AccountState, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.AccountState, new[]
            {
                new SelectListItem {Text = "كافة الحسابات", Value = "1"},
                new SelectListItem {Text = "مجموعة", Value = "2"},
                new SelectListItem {Text = "حساب محدد", Value = "3"}
            })
            @Html.ValidationMessageFor(model => model.AccountState, "", new { @class = "text-danger" })
        </div>
    </div>

<div class="form-group" id="specifc">
    <div class="col-md-12">
        @Html.Label("اسم الحساب", new { @class = "control-label col-md-2" })
        @Html.Obout(new ComboBox("AccountID")
        {
            Width = 300,
            SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains
        })
        @Html.ValidationMessageFor(model => model.AccountID)
    </div>
</div>

<div class="form-group" id="group">
    @Html.LabelFor(model => model.AccountGroupID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.AccountGroupID, (SelectList)ViewBag.Groups, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.AccountGroupID, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Title, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Title, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Title, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Message, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Message, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Message, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.RealTime, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.RealTime)
        @Html.ValidationMessageFor(model => model.RealTime, "", new { @class = "text-danger" })
    </div>
</div>



<script>

    function setState() {

        var num = Number($("#AccountState").children("option:selected").val());
        i('selected state: ' + num);
        if (num === 1) {
            $('#specifc').hide();
            $('#group').hide();

        } else if (num === 3) {
            $('#specifc').show();
            $('#group').hide();

        } else if (num === 2) {
            $('#specifc').hide();
            $('#group').show();
        }
    }

    $(function () {
        $("select#AccountState").val('3');
        setState();
        $('#AccountState').on('change',
            function () {
                setState();
            });
    })
</script>