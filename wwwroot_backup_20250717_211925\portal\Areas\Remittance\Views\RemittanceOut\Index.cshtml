﻿@model AppTech.MSMS.Domain.Models.RemittanceIn
@{
    Layout = "/Views/Shared/_TableLayout.cshtml";
}

<div class="">
    <div class="search-area well well-sm">
        <div class="search-filter-header bg-primary">
            <h5 class="smaller no-margin-bottom">
                <i class="ace-icon fa fa-sliders light-green bigger-130"></i>&nbsp; بحث عن الحوالة
            </h5>
        </div>
        <div class="space-10"></div>
        <div class="hr hr-dotted"></div>
        @using (Ajax.BeginForm("SearchRemittancesIn",
            new AjaxOptions
            {
                LoadingElementId = "loader",
                //  OnSuccess = "onSuccess",
                OnFailure = "OnFailed",
                UpdateTargetId = "list",
                InsertionMode = InsertionMode.Replace
            }
            ))
        {
            @Html.AntiForgeryToken()
            @Html.ValidationSummary(true, "", new {@class = "text-danger"})

            <div class="form-group">
                @Html.LabelFor(model => model.RemittanceNumber, new {@class = "col-md-2"})
                <div class="col-md-10">
                    @Html.EditorFor(model => model.RemittanceNumber, new {htmlAttributes = new {@class = "form-control"}})
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.BeneficiaryName, new {@class = "control-label col-md-2"})
                <div class="col-md-10">
                    @Html.EditorFor(model => model.BeneficiaryName, new {htmlAttributes = new {@class = "form-control"}})
                </div>
            </div>


            @*<div class="form-group">
                @Html.LabelFor(model => model.TargetPointID, new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DropDownListFor(model => model.TargetPointID,(SelectList)ViewBag.Targets, new { htmlAttributes = new { @class = "form-control" } })
                </div>
            </div>*@

            @*<div class="form-group">
                @Html.LabelFor(model => model.BeneficiaryPhone, new {@class = "control-label col-md-2"})
                <div class="col-md-10">
                    @Html.EditorFor(model => model.BeneficiaryPhone, new {htmlAttributes = new {@class = "form-control"}})
                    @Html.ValidationMessageFor(model => model.BeneficiaryPhone, "", new {@class = "text-danger"})
                </div>
            </div>*@


            <div class="space-6"></div>
            <div class="space-6"></div>
            <div class="hr hr-dotted"></div>
            <div class="text-center">

                <button type="submit" width="200" class="btn btn-default btn-round btn-white">
                    <i class="ace-icon fa fa-search blue "></i>
                    &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; البحث &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                </button>
            </div>
        }
        <div class="space-4"></div>
    </div>
</div>

<div class="">
    <div id="list">

    </div>
    <span class="alert"></span>
</div>


<script>
    hideLoading();
    function OnFailed(xhr, textStatus, errorThrown) {
        i('OnFailure ');
        hideLoading();
        i('textStatus ' + textStatus);
        i('errorThrown ' + errorThrown);

        var msg = parseXhr(xhr);
        log('onCrudFailure xhr msg:' + msg);
        alert(msg);

    }

    function handleError(ajaxContext) {
        var response = ajaxContext.get_response();
        var statusCode = response.get_statusCode();
        alert("Sorry, the request failed with status code " + statusCode);
    }
</script>