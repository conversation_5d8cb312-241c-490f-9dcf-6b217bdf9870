﻿@model AppTech.MSMS.Web.Areas.Admin.Models.OrdersModel
@using AppTech.MSMS.Domain;
@{
    Layout = "~/Views/Shared/_CrudLayout.cshtml";
}
<div class="space-6"></div>
<span class="lbl"> الحــالة</span>
@Html.EnumDropDownListFor(m => m.Status)



<script>
    $(function () {
        $('.loading').on('click',
            function () {
                var $this = $(this);
                $this.button('loading');
            });
    });
</script>
<script>



    $(function () {

        $('#relay-commission').on('click',
            function () {
                i('on order status changer')
                showLoading();
                AjaxCall('/Admin/Order/RelayCommissions?id=1').done(function (response) {
                    resetButton();
                    hideLoading();
                    ar(response);
                }).fail(function (xhr, textStatus, errorThrown) {
                    hideLoading();
                    resetButton();
                    parseAndShowError(xhr, textStatus, errorThrown);

                });
            });
        $('#Status').on('change',
            function () {
                i('on order status changer')
                fetchOrders();
            });

        $("#add-record").hide();
        $("#print-grid").hide();
        $('#modal').on('hidden.bs.modal',
            function () {
                i('on order modal hide')

                fetchOrders();
            });
    });

    function fetchOrders() {

        i(' try fetchOrders');
        var controller = $("#Controller").val();
        i(' controller: ' + controller);
        if (!controller.includes("Admin/Order")) {
            return;
        }
        i(' Call fetchOrders');

        i('fetchOrders');
        i('onclick Status find');
        var id = $('#Status').val();
        i('Status ' + id);
        var data = { id: id };
        showLoading();
        AjaxCall('/Admin/Order/GetByStatus', data).done(function (response) {
            hideLoading();
            if (response.length > 0) {
                $("#list").replaceWith(response);
                var pager = Patterns.Art.Pager;
                pager.activateList();
            }
        }).fail(function (xhr, textStatus, errorThrown) {
            hideLoading();
            parseAndShowError(xhr, textStatus, errorThrown);

        });
    };



</script>