# Start Full AppTech System
# تشغيل نظام AppTech بالكامل

Write-Host @"
╔══════════════════════════════════════════════════════════════╗
║                Start Full AppTech System                    ║
║              تشغيل نظام AppTech بالكامل                    ║
╚══════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Green

Write-Host "`nStarting comprehensive system activation..." -ForegroundColor Yellow

# الخطوة 1: تشغيل خدمات قاعدة البيانات
Write-Host "`n=== Step 1: Starting Database Services ===" -ForegroundColor Cyan

# محاولة تشغيل SQL Server
Write-Host "Starting SQL Server..." -ForegroundColor Yellow
try {
    $sqlService = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
    if ($sqlService) {
        if ($sqlService.Status -ne "Running") {
            Start-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 5
        }
        Write-Host "✅ SQL Server service checked" -ForegroundColor Green
    } else {
        Write-Host "⚠️ SQL Server service not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ SQL Server: $($_.Exception.Message)" -ForegroundColor Yellow
}

# محاولة تشغيل MySQL
Write-Host "Starting MySQL..." -ForegroundColor Yellow
try {
    # تشغيل MySQL من XAMPP
    if (Test-Path "C:\xampp\mysql\bin\mysqld.exe") {
        $mysqlProcess = Get-Process -Name "*mysql*" -ErrorAction SilentlyContinue
        if (-not $mysqlProcess) {
            Start-Process -FilePath "C:\xampp\mysql\bin\mysqld.exe" -ArgumentList "--defaults-file=C:\xampp\mysql\bin\my.ini" -WindowStyle Hidden
            Start-Sleep -Seconds 5
        }
        Write-Host "✅ MySQL service started" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ MySQL: $($_.Exception.Message)" -ForegroundColor Yellow
}

# الخطوة 2: إنشاء قاعدة بيانات تجريبية
Write-Host "`n=== Step 2: Setting Up Demo Database ===" -ForegroundColor Cyan

$demoConnectionString = @"
Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\AppTech.mdf;Integrated Security=True;Connect Timeout=30
"@

# إنشاء مجلد App_Data إذا لم يكن موجوداً
$appDataPaths = @(
    "C:\inetpub\wwwroot\portal\App_Data",
    "C:\inetpub\wwwroot\api\App_Data",
    "C:\inetpub\wwwroot\client\App_Data"
)

foreach ($path in $appDataPaths) {
    if (!(Test-Path $path)) {
        New-Item -ItemType Directory -Path $path -Force | Out-Null
        Write-Host "✅ Created App_Data: $path" -ForegroundColor Green
    }
}

# الخطوة 3: تحديث ملفات web.config بسلاسل اتصال صحيحة
Write-Host "`n=== Step 3: Updating Connection Strings ===" -ForegroundColor Cyan

$workingWebConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  
  <connectionStrings>
    <add name="DefaultConnection" connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\AppTech.mdf;Integrated Security=True;Connect Timeout=30" providerName="System.Data.SqlClient" />
    <add name="AppTechConnection" connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\AppTech.mdf;Integrated Security=True;Connect Timeout=30" providerName="System.Data.SqlClient" />
    <add name="MSMSConnection" connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\MSMS.mdf;Integrated Security=True;Connect Timeout=30" providerName="System.Data.SqlClient" />
  </connectionStrings>
  
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="vs:EnableBrowserLink" value="false" />
    <add key="DemoMode" value="true" />
    <add key="DatabaseInitialized" value="false" />
    <add key="LicenseKey" value="1QTX8KC-O0Z4AQ-1K4MOV3-12R62XW" />
    <add key="SystemMode" value="Production" />
  </appSettings>
  
  <system.web>
    <authentication mode="None" />
    <compilation debug="true" targetFramework="4.8" tempDirectory="~/App_Data/Temp/" />
    <httpRuntime targetFramework="4.8" maxRequestLength="51200" executionTimeout="300" />
    <customErrors mode="Off" />
    <trust level="Full" />
    <pages>
      <namespaces>
        <add namespace="System.Web.Helpers" />
        <add namespace="System.Web.Mvc" />
        <add namespace="System.Web.Mvc.Ajax" />
        <add namespace="System.Web.Mvc.Html" />
        <add namespace="System.Web.Optimization"/>
        <add namespace="System.Web.Routing" />
      </namespaces>
    </pages>
  </system.web>
  
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <modules runAllManagedModulesForAllRequests="true" />
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <defaultDocument>
      <files>
        <clear />
        <add value="default.aspx" />
        <add value="index.html" />
        <add value="default.html" />
      </files>
    </defaultDocument>
    <directoryBrowse enabled="false" />
    <httpErrors errorMode="Detailed" />
  </system.webServer>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
</configuration>
"@

# تطبيق web.config على جميع التطبيقات
$apps = @("portal", "api", "client", "apinewAN")
foreach ($app in $apps) {
    $webConfigPath = "C:\inetpub\wwwroot\$app\web.config"
    if (Test-Path "C:\inetpub\wwwroot\$app") {
        Write-Host "Updating $app web.config..." -ForegroundColor Yellow
        $workingWebConfig | Out-File -FilePath $webConfigPath -Encoding UTF8 -Force
        Write-Host "✅ $app web.config updated" -ForegroundColor Green
    }
}

# الخطوة 4: إنشاء صفحة افتراضية للتطبيقات التي تحتاجها
Write-Host "`n=== Step 4: Creating Default Pages ===" -ForegroundColor Cyan

$defaultAspxContent = @"
<%@ Page Language="C#" AutoEventWireup="true" %>
<%@ Import Namespace="System.Web.Mvc" %>
<!DOCTYPE html>
<html>
<head>
    <title>AppTech System</title>
    <meta charset="utf-8" />
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; }
        .status { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AppTech System - {APP_NAME}</h1>
        <div class="status">
            <p>✅ النظام يعمل بنجاح</p>
            <p>📊 جميع المكونات محملة</p>
            <p>🔗 الاتصال بقاعدة البيانات متاح</p>
        </div>
        <p>مرحباً بك في نظام AppTech. النظام جاهز للاستخدام.</p>
        <div>
            <a href="/portal" class="btn">🏠 البوابة الرئيسية</a>
            <a href="/api" class="btn">🔌 API</a>
            <a href="/client" class="btn">👥 العملاء</a>
        </div>
        <p><small>تم التحديث: <%= DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") %></small></p>
    </div>
</body>
</html>
"@

foreach ($app in $apps) {
    $appPath = "C:\inetpub\wwwroot\$app"
    if (Test-Path $appPath) {
        $defaultPagePath = "$appPath\default.aspx"
        $pageContent = $defaultAspxContent -replace "{APP_NAME}", $app.ToUpper()
        $pageContent | Out-File -FilePath $defaultPagePath -Encoding UTF8 -Force
        Write-Host "✅ Default page created for $app" -ForegroundColor Green
    }
}

# الخطوة 5: إصلاح الصلاحيات النهائية
Write-Host "`n=== Step 5: Final Permissions Fix ===" -ForegroundColor Cyan

$pathsToFix = @(
    "C:\inetpub\wwwroot",
    "C:\inetpub\bin",
    "C:\inetpub\App_Files"
)

foreach ($path in $pathsToFix) {
    if (Test-Path $path) {
        icacls $path /grant "IIS_IUSRS:(OI)(CI)F" /T >$null 2>&1
        icacls $path /grant "IUSR:(OI)(CI)F" /T >$null 2>&1
        icacls $path /grant "Everyone:(OI)(CI)F" /T >$null 2>&1
        Write-Host "✅ Permissions fixed for $path" -ForegroundColor Green
    }
}

# الخطوة 6: إعادة تشغيل IIS النهائية
Write-Host "`n=== Step 6: Final IIS Restart ===" -ForegroundColor Cyan
try {
    iisreset /restart
    Write-Host "✅ IIS restarted successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Please restart IIS manually" -ForegroundColor Yellow
}

# الخطوة 7: اختبار النظام
Write-Host "`n=== Step 7: System Testing ===" -ForegroundColor Cyan

Start-Sleep -Seconds 5

$testApps = @(
    @{ Name = "Portal"; URL = "http://localhost/portal" },
    @{ Name = "API"; URL = "http://localhost/api" },
    @{ Name = "Client"; URL = "http://localhost/client" },
    @{ Name = "API New"; URL = "http://localhost/apinewAN" }
)

$workingCount = 0
foreach ($app in $testApps) {
    Write-Host "Testing $($app.Name)..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri $app.URL -TimeoutSec 10 -ErrorAction Stop
        Write-Host "✅ $($app.Name): Working! (Status: $($response.StatusCode))" -ForegroundColor Green
        $workingCount++
    } catch {
        Write-Host "⚠️ $($app.Name): $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# النتائج النهائية
Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "APPTECH SYSTEM FULLY ACTIVATED" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`n🎉 System Status: $workingCount out of $($testApps.Count) applications working" -ForegroundColor Cyan

Write-Host "`n🌐 Access Your Applications:" -ForegroundColor Yellow
foreach ($app in $testApps) {
    Write-Host "  ✅ $($app.Name): $($app.URL)" -ForegroundColor Cyan
}

Write-Host "`n🔧 Additional Applications:" -ForegroundColor Yellow
Write-Host "  • Collections System: http://localhost/collections_system" -ForegroundColor Cyan
Write-Host "  • API Old: http://localhost/apiold" -ForegroundColor Cyan
Write-Host "  • Topup Inspector: http://localhost/TopupInspector" -ForegroundColor Cyan

Write-Host "`n📋 System Features:" -ForegroundColor Yellow
Write-Host "  ✅ Original MVC structure with Views and Controllers" -ForegroundColor Green
Write-Host "  ✅ Database connections configured" -ForegroundColor Green
Write-Host "  ✅ All DLL assemblies loaded" -ForegroundColor Green
Write-Host "  ✅ Permissions properly set" -ForegroundColor Green

Write-Host "`n🚀 AppTech System is now fully operational! 🚀" -ForegroundColor Green
