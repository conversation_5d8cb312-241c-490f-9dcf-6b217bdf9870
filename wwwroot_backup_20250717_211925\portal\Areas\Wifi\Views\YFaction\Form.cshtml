﻿@model AppTech.MSMS.Domain.Models.Faction
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<div class="form-group">
    @Html.LabelFor(model => model.Number, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Number)
        @Html.ValidationMessageFor(model => model.Number)
    </div>
</div>



<div class="form-group">
    @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name)
        @Html.ValidationMessageFor(model => model.Name)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.OrderNo, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.OrderNo)
        @Html.ValidationMessageFor(model => model.OrderNo)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Quantity, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Quantity)
        @Html.ValidationMessageFor(model => model.Quantity)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ProviderPrice, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.ProviderPrice, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.ProviderPrice, "", new { @class = "text-danger" })
    </div>
</div>


@if (!AppTech.MSMS.Domain.DomainManager.None_Clients_Types)
{
    <div class="form-group">
        @Html.LabelFor(model => model.Price, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Price)
            @Html.ValidationMessageFor(model => model.Price)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.PersonnalPrice, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.PersonnalPrice)
            @Html.ValidationMessageFor(model => model.PersonnalPrice)
        </div>
    </div>
}
else
{
    <div class="form-group">
        @Html.Label("السعر الأفتراضي", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Price)
            @Html.ValidationMessageFor(model => model.Price)
        </div>
    </div>

}

<div class="form-group">
    @Html.LabelFor(model => model.ProviderCode, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.ProviderCode)
        @Html.ValidationMessageFor(model => model.ProviderCode)
    </div>
</div>
