﻿@model AppTech.MSMS.Domain.Models.CurrencyRateAccount
@using Obout.Mvc.ComboBox
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<div class="form-group">
    @Html.Label("العملة", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.SourceCurrencyID, (SelectList)ViewBag.Currencies)
        @Html.ValidationMessageFor(model => model.SourceCurrencyID)
    </div>
</div>



<div id="Account-State-View">
    <div class="form-group">
        @Html.LabelFor(model => model.AccountState, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.AccountState, new[]
            {
                new SelectListItem {Text = "مجموعة", Value = "2"},
                new SelectListItem {Text = "حساب محدد", Value = "3"}
            })
            @Html.ValidationMessageFor(model => model.AccountState, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group" id="specifc">
        <div class="col-md-12">
            @Html.Label("اسم الحساب", new { @class = "control-label col-md-2" })
            @Html.Obout(new ComboBox("AccountID")
            {
                Width = 300,
                SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
                FilterType = ComboBoxFilterType.Contains,
                LoadingText = "Loading"
            })

            @Html.ValidationMessageFor(model => model.AccountID)
        </div>
    </div>

    <div class="form-group" id="group">
        @Html.LabelFor(model => model.AccountGroupID, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.AccountGroupID, (SelectList)ViewBag.Groups, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.AccountGroupID, "", new { @class = "text-danger" })
        </div>
    </div>
</div>



<div class="form-group">
    @Html.Label("سعر البيع", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.SellPrice)
        @Html.ValidationMessageFor(model => model.SellPrice)
    </div>
</div>


<div class="form-group">
    @Html.Label("سعر الشراء", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.BuyPrice)
        @Html.ValidationMessageFor(model => model.BuyPrice)
    </div>
</div>


<div class="form-group">
    @Html.Label("ملاحظات", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note)
        @Html.ValidationMessageFor(model => model.Note)
    </div>
</div>

<script>

    function setAccountState() {

        var num = Number($("#AccountState").children("option:selected").val());
        i('selected state: ' + num);
        if (num === 1) {
            $('#specifc').hide();
            $('#group').hide();

        }
        else if (num === 2) {
            $('#specifc').hide();
            $('#group').show();
        }
        else if (num === 3) {
            $('#specifc').show();
            $('#group').hide();

        }
    }

    $(function () {
        setAccountState();
        $('#AccountState').on('change',
            function () {
                setAccountState();
            });
    })
</script>