﻿<style type="text/css">
    
</style>
<div class="row page-header" id="crud-header">
    <div class="col-xs-12">
        <div class="pull-right">
        

            <button class="btn btn-primary btn-info btn-bold btn-round btn-no-border" id="add-record" title="أضافة سجل جديد">
                <i class="ace-icon fa fa-plus bigger-110"></i>
                أضافة جديد
            </button>

            &nbsp;
            <button class="btn btn-white btn-info btn-round " id="search" title="  بحث متقدم">
                <i class="ace-icon fa fa-search bigger-110"></i>
                بحث
            </button>

            <button class="btn btn-white btn-info  btn-round loading" id="refresh" title="تحديث" data-loading-text="<i class='fa fa-spinner fa-spin '></i> جاري تحديث">
                <i class="ace-icon fa fa-refresh bigger-110"></i>
                تحديث
            </button>
            @Html.Button((string)ViewBag.PageName, "PRINT", "طباعة", "print bigger-150", "btn-white btn-info btn-bold btn-round", "طباعة",true,"print-grid")
        

            <div class="btn-group" role="group" aria-label="Button group with nested dropdown">
                <div class="btn-group" role="group">
                    <button id="btnGroupDrop1" type="button" class="btn  btn-white btn-info btn-round dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="تصدير الى اكسل">
                        <i class="ace-icon fa fa-file-excel-o bigger-125"></i>
                        اكسل
                    </button>
                    <div class="dropdown-menu" aria-labelledby="btnGroupDrop1">
                        <button id="export-excel" class="dropdown-item btn btn-link">صفحة واحدة</button>
                        <button class="btn btn-link dropdown-item"  id="export-data"> جميع الصفحات</button>
                    </div>
                </div>
            </div>

        </div>
    </div>
    @Html.Partial("_SearchBox")

  
</div>
<script>
    $(function() {

        $('.loading').on('click',
            function() {
                var $this = $(this);
                $this.button('loading');
            });

        var title = $("#Title").val();
        $("#export-excel").on('click',
            function() {
                $("#simple-table").table2excel({
                    exclude: ".none-print-element",
                    exclude_inputs: true,
                    name: title,
                    filename: title + new Date().toISOString().replace(/[\-\:\.]/g, ""),
                }); 

            });

   
        $("#export-data").on('click',
            function () {
                i('exp');
                var controller = $("#Controller").val();
                var condition = $("#QryCondition").val();
                i('con' + controller + ' condition' + condition );
                window.location = controller + '/ExportExcel?condition=' + condition;
            });


    });
    
    function OnRefreshed() {
        var pager = Patterns.Art.Pager;
        pager.start();
    }
</script>