﻿@model AppTech.MSMS.Domain.Models.Person

@{
    Layout = "~/Views/Shared/_MultiForm.cshtml";
}
<div>
    <div class="form-group">
        @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.FrontImage, new { @class = "control-label col-md-2" })
        <div style="position: relative;">
            <input type="file" name="ImageData" size="40" onchange="showImg(this)">
        </div>
        <img class="img-thumbnail" width="150" height="150" id="preview"
             src="@Url.Action("GetImage", "ProductImage",new {Model.ID})" />
    </div>
</div>



<script>

    $(function () {
        i('on load');
        $("#SyncAccountID").on('change',
            function () {
                i('on change sync account select');
                var name = $("#SyncAccountID option:selected").text();
                $("#OwnerName").val(name);
            });
    });
</script>