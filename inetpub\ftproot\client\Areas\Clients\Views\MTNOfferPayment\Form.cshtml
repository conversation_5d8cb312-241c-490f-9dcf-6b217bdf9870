﻿@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.BagatPayment

@{
    Layout = "~/Views/Shared/_FormAsync.cshtml";
}

<input type="hidden" name="Device" value="Web"/>
<input type="hidden" name="ServiceID" value="@ViewBag.ServiceID"/>


@if (CurrentUser.Type == UserType.Admin)
{
    <div class="form-group">
        @Html.Label("اسم الحساب", new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.Obout(new ComboBox("AccountID")
            {
                Width = 300,
                SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
                FilterType = ComboBoxFilterType.Contains,
                LoadingText = "Loading"
            })
            @Html.ValidationMessageFor(model => model.AccountID, "", new {@class = "text-danger"})
        </div>
    </div>
}
<div class="form-group">
    @Html.LabelFor(model => model.SubscriberNumber, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SubscriberNumber)
        @Html.ValidationMessageFor(model => model.SubscriberNumber)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.LineType, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.LineType, new[]
        {
            new SelectListItem {Text = "دفع مسبق", Value = "دفع مسبق"},
            new SelectListItem {Text = "فوترة", Value = "فوترة"}
        })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.OfferID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.OfferID, (SelectList) ViewBag.Offers)
        @Html.ValidationMessageFor(model => model.OfferID)
    </div>
</div>

<script>
    $(function() {
        $("#cancel-button").hide();
        $("#submit-button").text('تفعيل الباقة');

            formHelper.onBegin = function() {
                var msg = " سوف يتم تفعيل باقة " +
                    $("#OfferID option:selected").text() +
                  ' لرقم '+  $("#SubscriberNumber").val() +
                    ' هل انت متأكد';
                if (!confirm(msg)) {
                    i('not confirmed');
                    return false;
                } else {
                    i('confirmed');
                    return true;
                }
            }
    });
</script>