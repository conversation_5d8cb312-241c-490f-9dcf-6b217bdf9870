﻿
@model AppTech.MSMS.Domain.Reports.Models.TopupModel
@using Obout.Mvc.ComboBox
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Domain
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


<div class="form-horizontal">
    @{
        Html.RenderPartial("_DateControl");
    }

    @*<select id="AccountID" name="AccountID" class="select2" placeholder="كافة الحسابات"></select>
        <div class="space-6"></div>*@
    <div class="form-group">
        @Html.Label("الحساب", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.Obout(new ComboBox("AccountID")
            {
                FilterType = ComboBoxFilterType.Contains,
                Width=200
            })
        </div>
    </div>
    <div class="space-6"></div>
   

    <span class="lbl">اسم الخدمة</span>
    @Html.DropDownListFor(model => model.ServiceID, (SelectList)ViewBag.Services)
    <div class="space-6"></div>

    <span class="lbl">الحالة</span>
    @Html.EnumDropDownListFor(model => model.Status)
    <div class="space-6"></div>

    <span class="lbl">التجميع بالنقاط </span>
    @Html.EditorFor(model => model.GroupByAccounts)
    <div class="space-6"></div>

    @*<span class="lbl"> نوع التقرير</span>
        @Html.EnumDropDownListFor(m => m.Type)*@

</div>
<script>
    $("select#Status").prop('selectedIndex', 1);  
</script>
@*<script>
        $(function () {
            console.log('topupreport load');
            AjaxCall('/Print/GetParties').done(function (response) {
                console.log('get parties');
                if (response.length > 0) {
                    $('#AccountID').html('');
                    var options = '<option value="0">كافة الحسابات</option>';
                    for (var i = 0; i < response.length; i++) {
                        options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
                    }
                    $('#AccountID').append(options);

                }
            }).fail(function (xhr, textStatus, errorThrown) {
                parseAndShowError(xhr, textStatus, errorThrown);
            });

            //select2
            $('.select2').css('width', '200px').select2({ allowClear: true });
            $('#select2-multiple-style .btn').on('click',
                function (e) {
                    var target = $(this).find('input[type=radio]');
                    var which = parseInt(target.val());
                    if (which == 2) $('.select2').addClass('tag-input-style');
                    else $('.select2').removeClass('tag-input-style');
                });
        });

    </script>*@