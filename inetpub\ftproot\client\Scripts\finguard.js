﻿/* ----------------------------------------
// call finguard() in your app // output will be something like this -->  "0821orak0821539ds999.6933.0.76"
 ---------------------------------------- */
function finguard() { function r(r, e) { return (e + 9845 + window.screen.width + r + window.screen.width).split("").reverse().join("") } var e = {}; (o = (a = navigator.userAgent.toLowerCase()).match(/msie ([\d.]+)/)) ? e.ie = o[1] : (o = a.match(/firefox\/([\d.]+)/)) ? e.firefox = o[1] : (o = a.match(/chrome\/([\d.]+)/)) ? e.chrome = o[1] : (o = a.match(/opera.([\d.]+)/)) ? e.opera = o[1] : (o = a.match(/version\/([\d.]+).*safari/)) && (e.safari = o[1]); var a, o; e = {}; return (o = (a = navigator.userAgent.toLowerCase()).match(/msie ([\d.]+)/)) ? e.ie = o[1] : (o = a.match(/firefox\/([\d.]+)/)) ? e.firefox = o[1] : (o = a.match(/chrome\/([\d.]+)/)) ? e.chrome = o[1] : (o = a.match(/opera.([\d.]+)/)) ? e.opera = o[1] : (o = a.match(/version\/([\d.]+).*safari/)) && (e.safari = o[1]), e.ie ? r("itne", e.ie) : e.firefox ? r("fyfo", e.firefox) : e.chrome ? r("karo", e.chrome) : e.opera ? r("orpr", e.opera) : e.safari ? r("sfri", e.safari) : "zefo43.454.43" }