﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.TransferOrder

@{
    Layout = "~/Views/Shared/_MultiLayout.cshtml";
}

<input type="hidden" name="Device" value="Web"/>

<div class="form-group" id="specifc">
    <div class="col-md-12">
        @Html.Label("اسم الحساب", new { @class = "control-label col-md-2" })
        @Html.Obout(new ComboBox("AccountID")
        {
            Width = 300,
            SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains,
            LoadingText = "Loading"
        })

        @Html.ValidationMessageFor(model => model.AccountID)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ExchangerID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ExchangerID, (SelectList) ViewBag.Exchangers, new {})
        @Html.ValidationMessageFor(model => model.ExchangerID)
    </div>
</div>


<div class="extra-info">


    <div class="form-group">
        @Html.LabelFor(model => model.TransferNumber, new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EditorFor(model => model.TransferNumber)
            @Html.ValidationMessageFor(model => model.TransferNumber)
        </div>
    </div>

</div>


<div class="form-group form-inline">
    @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Amount, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>

<div class="form-group">
    <label class="control-label col-md-2">العملة</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.CurrencyID, new[]
        {
            new SelectListItem {Text = "يمني", Value = "1"},
            new SelectListItem {Text = "دولار", Value = "2"},
            new SelectListItem {Text = "سعودي", Value = "3"}
        })

    </div>
    @Html.ValidationMessageFor(model => model.CurrencyID)
</div>


<div class="form-group">
    @Html.LabelFor(model => model.SenderName, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderName)
        @Html.ValidationMessageFor(model => model.SenderName)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.ReceiverName, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.ReceiverName)
        @Html.ValidationMessageFor(model => model.ReceiverName)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ReceiverMobile, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.ReceiverMobile)
        @Html.ValidationMessageFor(model => model.ReceiverMobile)
    </div>
</div>
<div class="extra-info">




    <div class="form-group">
        <label class="col-sm-2 control-label">صوره البطاقة 1</label>
        <div style="position: relative;">
            <input type="file" name="ImageData" size="40" onchange="showImg(this)">
        </div>
        <img class="img-thumbnail" width="150" height="150" id="preview" />
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">صوره البطاقة 2</label>
        <div style="position: relative;">
            <input type="file" name="ImageData2" size="40" onchange="showImg2(this)">
        </div>
        <img class="img-thumbnail" width="150" height="150" id="preview2" />
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note)
        @Html.ValidationMessageFor(model => model.Note)
    </div>
</div>


<script>


    $("#AccountID").on('change',
        function() {
            $('#SenderName').val($('#AccountID').find(":selected").text());
        });

</script>
@*

    <div class="form-group">
                @Html.LabelFor(model => model.ImageName, new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.ImageName)
                    @Html.ValidationMessageFor(model => model.ImageName)
                </div>
            </div>

*@