﻿<%@ Page Language="C#" AutoEventWireup="true" %>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppTech Portal - ط§ظ„ط¨ظˆط§ط¨ط© ط§ظ„ط±ط¦ظٹط³ظٹط©</title>
    <link href="Content/bootstrap.min.css" rel="stylesheet" />
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .main-header { background: #2c3e50; color: white; padding: 20px 0; }
        .nav-menu { background: #34495e; padding: 15px 0; }
        .nav-menu a { color: white; text-decoration: none; padding: 10px 15px; margin: 0 5px; border-radius: 4px; }
        .nav-menu a:hover { background: #2c3e50; }
        .dashboard-card { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin: 15px 0; padding: 20px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        .btn-success { background: #28a745; }
        .btn-info { background: #17a2b8; }
        .btn-warning { background: #ffc107; color: #212529; }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <h1>ًںڈ  AppTech Portal - ط§ظ„ط¨ظˆط§ط¨ط© ط§ظ„ط±ط¦ظٹط³ظٹط©</h1>
            <p>ظ†ط¸ط§ظ… ط¥ط¯ط§ط±ط© ط§ظ„ط£ط¹ظ…ط§ظ„ ط§ظ„ظ…طھظƒط§ظ…ظ„ - ط§ظ„ط¥طµط¯ط§ط± ط§ظ„ط£طµظ„ظٹ</p>
        </div>
    </div>
    
    <div class="nav-menu">
        <div class="container">
            <a href="/portal">ط§ظ„ط±ط¦ظٹط³ظٹط©</a>
            <a href="/portal/Home/Dashboard">ظ„ظˆط­ط© ط§ظ„طھط­ظƒظ…</a>
            <a href="/portal/Home/Agent">ط§ظ„ظˆظƒظ„ط§ط،</a>
            <a href="/portal/Home/Client">ط§ظ„ط¹ظ…ظ„ط§ط،</a>
            <a href="/portal/Home/Merchant">ط§ظ„طھط¬ط§ط±</a>
            <a href="/api">ظˆط§ط¬ظ‡ط© API</a>
            <a href="/collections_system">ظ†ط¸ط§ظ… ط§ظ„طھط­طµظٹظ„ط§طھ</a>
        </div>
    </div>
    
    <div class="container" style="margin-top: 20px;">
        <div class="row">
            <div class="col-md-3">
                <div class="dashboard-card">
                    <h4>ًں“ٹ ظ„ظˆط­ط© ط§ظ„طھط­ظƒظ…</h4>
                    <p>ط¹ط±ط¶ ط´ط§ظ…ظ„ ظ„ظ„ظ†ط¸ط§ظ… ظˆط§ظ„ط¥ط­طµط§ط¦ظٹط§طھ ط§ظ„ط±ط¦ظٹط³ظٹط©</p>
                    <a href="/portal/Home/Dashboard" class="btn">ط¯ط®ظˆظ„ ظ„ظˆط­ط© ط§ظ„طھط­ظƒظ…</a>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card">
                    <h4>ًں‘¥ ط¥ط¯ط§ط±ط© ط§ظ„ظˆظƒظ„ط§ط،</h4>
                    <p>ط¥ط¶ط§ظپط© ظˆط¥ط¯ط§ط±ط© ط­ط³ط§ط¨ط§طھ ط§ظ„ظˆظƒظ„ط§ط، ظˆط§ظ„ظ…ظ†ط¯ظˆط¨ظٹظ†</p>
                    <a href="/portal/Home/Agent" class="btn btn-success">ط¥ط¯ط§ط±ط© ط§ظ„ظˆظƒظ„ط§ط،</a>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card">
                    <h4>ًںڈ¢ ط¥ط¯ط§ط±ط© ط§ظ„ط¹ظ…ظ„ط§ط،</h4>
                    <p>ط¥ط¯ط§ط±ط© ط­ط³ط§ط¨ط§طھ ط§ظ„ط¹ظ…ظ„ط§ط، ظˆط§ظ„ط®ط¯ظ…ط§طھ ط§ظ„ظ…ظ‚ط¯ظ…ط©</p>
                    <a href="/portal/Home/Client" class="btn btn-info">ط¥ط¯ط§ط±ط© ط§ظ„ط¹ظ…ظ„ط§ط،</a>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card">
                    <h4>ًںڈھ ط¥ط¯ط§ط±ط© ط§ظ„طھط¬ط§ط±</h4>
                    <p>ط¥ط¯ط§ط±ط© ط­ط³ط§ط¨ط§طھ ط§ظ„طھط¬ط§ط± ظˆط§ظ„ظ…ط¹ط§ظ…ظ„ط§طھ ط§ظ„طھط¬ط§ط±ظٹط©</p>
                    <a href="/portal/Home/Merchant" class="btn btn-warning">ط¥ط¯ط§ط±ط© ط§ظ„طھط¬ط§ط±</a>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="dashboard-card">
                    <h4>ًں“ˆ ط§ظ„طھظ‚ط§ط±ظٹط± ظˆط§ظ„ط¥ط­طµط§ط¦ظٹط§طھ</h4>
                    <p>طھظ‚ط§ط±ظٹط± ظ…ظپطµظ„ط© ظˆطھط­ظ„ظٹظ„ط§طھ ط´ط§ظ…ظ„ط© ظ„ط¬ظ…ظٹط¹ ط§ظ„ط¹ظ…ظ„ظٹط§طھ</p>
                    <ul>
                        <li>طھظ‚ط§ط±ظٹط± ط§ظ„ظ…ط¨ظٹط¹ط§طھ ط§ظ„ظٹظˆظ…ظٹط©</li>
                        <li>ط¥ط­طµط§ط¦ظٹط§طھ ط§ظ„ظˆظƒظ„ط§ط،</li>
                        <li>طھط­ظ„ظٹظ„ ط§ظ„ط£ط±ط¨ط§ط­</li>
                        <li>طھظ‚ط§ط±ظٹط± ط§ظ„ط¹ظ…ظ„ط§ط،</li>
                    </ul>
                    <a href="#" class="btn">ط¹ط±ط¶ ط§ظ„طھظ‚ط§ط±ظٹط±</a>
                </div>
            </div>
            <div class="col-md-6">
                <div class="dashboard-card">
                    <h4>âڑ™ï¸ڈ ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ظ†ط¸ط§ظ…</h4>
                    <p>طھط®طµظٹطµ ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ظ†ط¸ط§ظ… ظˆط§ظ„طھظپط¶ظٹظ„ط§طھ ط§ظ„ط¹ط§ظ…ط©</p>
                    <ul>
                        <li>ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ظ…ط³طھط®ط¯ظ…ظٹظ†</li>
                        <li>طµظ„ط§ط­ظٹط§طھ ط§ظ„ظˆطµظˆظ„</li>
                        <li>ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ط£ظ…ط§ظ†</li>
                        <li>ط§ظ„ظ†ط³ط® ط§ظ„ط§ط­طھظٹط§ط·ظٹط©</li>
                    </ul>
                    <a href="#" class="btn">ط§ظ„ط¥ط¹ط¯ط§ط¯ط§طھ</a>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info" style="margin-top: 20px; padding: 15px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px;">
            <h5>ًں“‹ ط­ط§ظ„ط© ط§ظ„ظ†ط¸ط§ظ… ط§ظ„ط­ط§ظ„ظٹط©</h5>
            <p><strong>ط§ظ„ط­ط§ظ„ط©:</strong> ط§ظ„ظ†ط¸ط§ظ… ظٹط¹ظ…ظ„ ظپظٹ ظˆط¶ط¹ ط§ظ„ط¹ط±ط¶ ط§ظ„طھظˆط¶ظٹط­ظٹ</p>
            <p><strong>ط§ظ„ظˆط¶ط¹:</strong> ط¨ط¯ظˆظ† ظ‚ط§ط¹ط¯ط© ط¨ظٹط§ظ†ط§طھ (Demo Mode)</p>
            <p><strong>ط§ظ„ظˆطµظˆظ„:</strong> ط¬ظ…ظٹط¹ ط§ظ„ظˆط¸ط§ط¦ظپ ظ…طھط§ط­ط© ظ„ظ„ط§ط³طھط¹ط±ط§ط¶</p>
            <p><strong>ط¢ط®ط± طھط­ط¯ظٹط«:</strong> <%= DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") %></p>
        </div>
    </div>
    
    <script src="Scripts/jquery-3.4.1.min.js"></script>
    <script src="Scripts/bootstrap.min.js"></script>
</body>
</html>
