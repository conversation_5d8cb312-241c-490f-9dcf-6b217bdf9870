﻿@model AppTech.MSMS.Domain.Models.SimItem

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="form-group sim-row">
    @Html.LabelFor(model => model.Number, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Number, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Number, "", new { @class = "text-danger" })
    </div>
</div>
<div class="form-group sim-row">
    @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.NetworkID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.NetworkID, new[]
            {
                new SelectListItem {Text = "يمن موبايل", Value = "1"},
                new SelectListItem {Text = "MTN", Value = "2"},
                new SelectListItem {Text = "سبأفون", Value = "3"},
                new SelectListItem {Text = "واي", Value = "4"}
            })
        </div>
        @Html.ValidationMessageFor(model => model.NetworkID, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ActionType, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.ActionType, new[]
            {
                 new SelectListItem {Text = "شريحة", Value = "1"},
                new SelectListItem {Text = "برمجة", Value = "2"}
            })
        </div>
        @Html.ValidationMessageFor(model => model.ActionType, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SimType, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.SimType, new[]
            {
                new SelectListItem {Text = "جديد", Value = "1"},
                new SelectListItem {Text = "بدل فاقد", Value = "2"}
            })
        </div>
        @Html.ValidationMessageFor(model => model.SimType, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.LineType, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.LineType, new[]
            {
                new SelectListItem {Text = "دفع مسبق", Value = "1"},
                new SelectListItem {Text = "فوترة", Value = "2"}
            })
        </div>
        @Html.ValidationMessageFor(model => model.LineType, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Type, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.Type, new[]
            {
                new SelectListItem {Text = "عادي", Value = "1"},
                new SelectListItem {Text = "برونزي", Value = "2"},
                new SelectListItem {Text = "ذهبي", Value = "3"},
                new SelectListItem {Text = "فضي", Value = "4"}
            })
        </div>
        @Html.ValidationMessageFor(model => model.Type, "", new { @class = "text-danger" })
    </div>
</div>
<div class="form-group form-inline sim-row">
    @Html.LabelFor(model => model.Price, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Price, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Price)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.IsSpecial, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.CheckBoxFor(model => model.IsSpecial)
        @Html.ValidationMessageFor(model => model.IsSpecial, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>