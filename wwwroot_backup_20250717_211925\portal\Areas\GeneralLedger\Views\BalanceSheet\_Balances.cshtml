﻿@using System.Data
@using AppTech.Common.Extensions
<div id="list">
    <div class="align-center table-header">
    </div>
    <span class="alert"></span>
    <table id="basic-table" class="table  table-bordered table-hover table-responsive"
           data-toggle="table">

        <thead class="thin-border-bottom">
        <tr>
            <th>
                <i class="ace-icon fa fa-caret-right blue"></i>اسم العميل
            </th>

            <th>
                <i class="ace-icon fa fa-caret-right blue"></i>الرصيد
            </th>

            <th class="hidden-480">
                <i class="ace-icon fa fa-caret-right blue"></i>الحالة
            </th>
        </tr>
        </thead>

        <tbody>
        @{

            foreach (DataRow row in Model.Result.Rows)
            {
                <tr>
                    <td>@row["AccountName"].ToString()</td>

                    <td>

                        <b class="green">@row["Balance"].ToString()</b>
                    </td>

                    @if (row["Balance"].ToDecimal() > 0)
                    {
                        <td class="hidden-480">
                            <span class="label label-success arrowed-in arrowed-in-right">له</span>
                        </td>
                    }

                    else
                    {
                        <td class="hidden-480">
                            <span class="label label-danger arrowed">علية</span>
                        </td>
                    }

                </tr>
            }
        }

        </tbody>
    </table>
</div>