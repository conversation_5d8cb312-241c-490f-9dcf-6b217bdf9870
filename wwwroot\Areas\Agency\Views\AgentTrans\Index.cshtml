﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Reports.Models.TransactionModel

@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


<div class="form-horizontal">
    @{
        Html.RenderPartial("_DateControl");
    }
    <span class="lbl">اسم الوكيل </span>
    <div class="form-group">
        <div class="col-md-10">
            @Html.Obout(new ComboBox("AccountID")
            {
                Width = 200
            })

        </div>
    </div>
    <div class="hr hr-dotted hr-24"></div>
    <div class="space-6"></div>
    <span class="lbl"> الخدمة</span>
    @Html.DropDownListFor(model => model.ServiceID, (SelectList)ViewBag.Services)

    <div class="hr hr-dotted hr-24"></div>
    <div class="space-6"></div>

    <span class="lbl"> نوع التقرير</span>
    @Html.EnumDropDownListFor(model => model.Type)
    @*@Html.EnumRadioButton(m => m.Type)*@

</div>