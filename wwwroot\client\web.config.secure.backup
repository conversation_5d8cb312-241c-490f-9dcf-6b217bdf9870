﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
  </appSettings>
  
  <system.web>
    <compilation debug="true" targetFramework="4.8" />
    <httpRuntime targetFramework="4.8" />
    <customErrors mode="Off" />
    <trust level="Full" />
    <authentication mode="None" />
  </system.web>
  
  <system.webServer>
    <defaultDocument>
      <files>
        <clear />
        <add value="index.html" />
        <add value="default.aspx" />
        <add value="default.html" />
        <add value="index.aspx" />
      </files>
    </defaultDocument>
    <directoryBrowse enabled="false" />
    <httpErrors errorMode="Detailed" />
    <modules runAllManagedModulesForAllRequests="true" />
  </system.webServer>
</configuration>
