﻿@model AppTech.MSMS.Domain.Models.DbBackup
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div>

    <div class="form-group">
        @Html.LabelFor(model => model.Name, new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EditorFor(model => model.Name)
            @Html.ValidationMessageFor(model => model.Name)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control", id = "form-field-note"}})
            @Html.ValidationMessageFor(model => model.Note)
        </div>
    </div>

</div>