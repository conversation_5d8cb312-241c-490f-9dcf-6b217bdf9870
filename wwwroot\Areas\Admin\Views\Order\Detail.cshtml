﻿@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Web.Code.Caching
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Web.Models.OrderDetailModel

<div id="user-profile-1" class="user-profile row">

    <div class="col-xs-12 col-sm-12">
        <div class="space-12"></div>
        <h3 class="header smaller lighter purple">
            طلب @Model.Parent.ServiceInfo.Name &nbsp;
            <small></small>
        </h3>

        <div class="profile-user-info profile-user-info-striped">

            <div class="profile-info-row">
                <div class="profile-info-name"> رقم الطلب </div>

                <div class="profile-info-value">
                    <span class="editable" id="age"> @Model.Parent.Number </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name"> تاريخ الطلب </div>

                <div class="profile-info-value">
                    <span class="editable"> @Html.DisplayFor(model => model.Parent.CreatedTime)</span>
                </div>
            </div>
            

          @if (Model.ShowAccountInfo)
          {
              <div class="profile-info-row">
                  <div class="profile-info-name"> رقم الحساب </div>

                  <div class="profile-info-value">
                      <span class="editable"> @Html.DisplayFor(model => model.AccountNumber)</span>
                      <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.AccountNumber)">
                          <i class="ace-icon fa fa-copy"></i> نسخ
                      </button>

                  </div>
              </div>

              <div class="profile-info-row">
                  <div class="profile-info-name"> اسم الحساب </div>

                  <div class="profile-info-value">
                      <span class="editable"> @Html.DisplayFor(model => model.Parent.Account.Name)</span>
                      <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.Parent.Account.Name)">
                          <i class="ace-icon fa fa-copy"></i> نسخ
                      </button>

                  </div>
              </div>

              <div class="profile-info-row">
                  <div class="profile-info-name"> نوع الحساب </div>

                  <div class="profile-info-value">
                      <span class="editable"> @Html.DisplayFor(model => model.AccountType)</span>

                  </div>
              </div>
          }
        
            
            
            
            
            <div class="profile-info-row">
                <div class="profile-info-name">  مبلغ وقدرة </div>

                <div class="profile-info-value">
                    <span class="editable" id="AmountInText"> @Html.DisplayFor(model => model.AmountInText)</span>
                    <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.AmountInText)">
                        <i class="ace-icon fa fa-copy"></i> نسخ
                    </button>
                </div>


            </div>

            <div class="profile-info-row">
                <div class="profile-info-name"> الحالة </div>

                <div class="profile-info-value">
                    <span class="editable" id="about"> @Model.Parent.State </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name"> ملاحظات </div>

                <div class="profile-info-value">
                    <span class="editable" id="about"> @Model.Parent.Note | @Model.Parent.Description</span>
                </div>
            </div>

            @if ( Model.EnableExchangeAccount && Model.Parent.Status == (byte)OrderStatus.Pending)
            {
                <div class="profile-info-row">
                    <div class="profile-info-name"> الحساب  المقابل </div>

                    <div class="profile-info-value">
                        <span class="editable" id="about">
                            @Html.Obout(new ComboBox("ExchangerAccountID")
    {
    Width = 300,
    SelectedValue = Model.Parent.ExchangerAccountID == 0 ? null : Model.Parent.ExchangerAccountID.ToString(),
    FilterType = ComboBoxFilterType.Contains,
    })
                        </span>
                    </div>
                </div>
            }



            @if (Model.Parent.Status == (byte)OrderStatus.Debited)
            {
                <div class="profile-info-row">
                    <div class="profile-info-name"> الترحيل بواسطة </div>

                    <div class="profile-info-value">
                        <span class="editable" id="about"> @Model.Parent.UserInfo1.UserName </span>
                    </div>
                </div>
            }

            @if (Model.Parent.Status == (byte)OrderStatus.Rejected)
            {
                <div class="profile-info-row">
                    <div class="profile-info-name"> سبب الرفض </div>

                    <div class="profile-info-value">
                        <span class="editable" id="about"> @Model.Parent.RejectReason </span>
                    </div>
                </div>
            }

        </div>

        <div class="center">
            <div class="order-actions">

                @if (Model.Parent.Status == (byte)OrderStatus.Pending)

                {
                    var userPermission = UserPermissionCache.Get(CurrentUser.Id, CurrentUser.Type).UserPermissions.SingleOrDefault(x => x.Page.PageName.Equals(ViewBag.PageName));
                    if (userPermission != null)
                    {
                        if (userPermission.PageActions.Any(x => x.Name.Equals("RELAY") && x.IsAllow))
                        {
                            <button class="btn btn-success loading" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار" id="bootbox-relay">
                                <i class="ace-icon fa fa-cloud-upload"></i>
                                ترحيل
                            </button>

                          
                        }


                        if (userPermission.PageActions.Any(x => x.Name.Equals("REJECT") && x.IsAllow))
                        {
                            if (Model.Parent.Status != (byte)OrderStatus.Rejected)
                            {
                                <button class="btn  btn-danger" id="bootbox-reject">
                                    <i class="ace-icon fa fa-bolt bigger-125"></i>
                                    رفض الطلب
                                </button>
                            }
                        }
                        if (userPermission.PageActions.Any(x => x.Name.Equals("ModifyAmount") && x.IsAllow))
                        {
                            <button class="btn" id="bootbox-modify-amount">
                                <i class="ace-icon fa fa-pencil align-top bigger-125"></i>
                                تعديل المبلغ
                            </button>
                        }




                    }

                    <button class="btn btn-warning loading" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار" id="bootbox-cancel-process" onclick="cancel()">
                        <i class="ace-icon fa fa-sign-out align-top bigger-125"></i>
                       خروج وإالغاء الربط الطلب
                    </button>
                }
                else if (Model.Parent.Status == (byte)OrderStatus.Debited)
                {

                    <button class="btn btn-warning loading" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار" id="bootbox-cancel-relay" onclick="cancelOrder()">
                        <i class="ace-icon fa fa-flask align-top bigger-125"></i>
                        إلغاء التجهيز
                    </button>
                }
            </div>
        </div>
        <div class="space-12"></div>
        <div class="profile-user-info profile-user-info-striped">

            <input type="hidden" id="mr_id" value="@Model.Parent.ID">
            <input type="hidden" id="orderType" value="@Model.Parent.OrderType">

            @{
                Html.RenderPartial(Model.Target + "OrderDetail", Model.Detail);
            }
        </div>

        <div class="space-6"></div>

    </div>

</div>

<script src="~/Scripts/clipboard.min.js"></script>

<script>
            function changerExchanger() {

        i('changerExchanger');
        var id = $("#mr_id").val();
        var num = $("#ExchangerID").children("option:selected").val();
        $.ajax({
            url: '@Url.Action("ChangeExchanger", "Order")',
            data: { id: id, exchnagerId: num },
            success: function(data) {

                if (data.Success) {
                    alert("تم تعديل الشركة بنجاح");
                    //  $('.trans-num').val(num);
                } else {
                    alert(data.Message);
                }
                resetButton();
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(xhr.responseText);
            }
        });
    }


           function saveTransNumber() {

        var id = $("#mr_id").val();
        var num = $("#TransferNumber").val();
        $.ajax({
            url: '@Url.Action("SaveTransNumber", "Order")',
            data: { id: id, num: num },
            success: function(data) {

                if (data.Success) {
                    alert("تم حفظ رقم الحوالة بنجاح");
                    $('.trans-num').val(num);
                } else {
                    alert(data.Message);
                }
                resetButton();
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(xhr.responseText);
            }
        });
    }

    function changeCommission() {
        i('changerExchanger');
        var id = $("#mr_id").val();
        var commission = $("#Commission").val();
        var num = $("#CommissionCurrencyID").children("option:selected").val();
        $.ajax({
            url: '@Url.Action("ChangeCommission", "Order")',
            data: { id: id, curId: num, amount: commission },
            success: function(data) {

                if (data.Success) {
                    alert("تم تعديل العمولة بنجاح");
                } else {
                    alert(data.Message);
                }
                resetButton();
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(xhr.responseText);
            }
        });
    }

</script>


<script type="text/javascript">

    function getText(id) {
        i('copy');
        var copyText = document.getElementById(id);
        copyText.select();
        copyText.setSelectionRange(0, 99999)
        document.execCommand("copy");
        i('aftr copy');
        alert("Copied the text: " + copyText.value);
    }


    function cancel() {
        i('bootbox-Cancel');
        var id = $("#mr_id").val();
        $.ajax({
            url: '@Url.Action("CancelProccessing", "Order")',
            data: { id: id },
            success: function(data) {
                $("#modal").modal('hide');
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(xhr.responseText);
            }
        });
    }


    function cancelOrder() {
        i('bootbox-Cancel');
        var id = $("#mr_id").val();
        $.ajax({
            url: '@Url.Action("Cancel", "Order")',
            data: { id: id },
            success: function(data) {
                var result = data;
                if (result.Success) {
                    alert(result.Message);
                    $("#modal").modal('hide');
                } else {
                    alert(result.Message);
                    resetButton();
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(xhr.responseText);
            }
        });
    }

    jQuery(function($) {

        $('.loading').on('click',
            function() {
                var $this = $(this);
                $this.button('loading');
            });

        $("#bootbox-relay").on(ace.click_event,
            function() {
                i('on relay clicked');
                var id = $("#mr_id").val();
                var exchId = $("#ExchangerAccountID").val();

                if (Number(exchId) === 0) {
                    ar('الرجاء قم بأختيار الحساب المقابل');
                    resetButton();
                    return;

                }
                bootbox.confirm("سوف يتم ترحيل هذا الطلب , هل انت متأكد?",
                    function(result) {
                        if (result) {
                            try {

                                $.ajax({
                                    url: '@Url.Action("Relay", "Order")',
                                    data: { id: id, orderType:exchId },
                                    success: function(data) {
                                        var result = data;
                                        if (result.Success) {
                                            alert(result.Message);
                                            $(".order-actions").hide();
                                            $("#modal").modal('hide');
                                        } else {
                                            alert(result.Message);
                                            resetButton();
                                        }
                                    },
                                    error: function(xhr, ajaxOptions, thrownError) {
                                        alert(xhr.responseText);

                                    }
                                });
                            } catch (e) {
                                alert(e);
                            }

                        } else {
                            resetButton();
                        }
                    }
                );
            });

        @*$("#bootbox-depend").on(ace.click_event,
            function() {
                i('on depend clicked');
                bootbox.confirm("سوف يتم تجهيز هذا الطلب , هل انت متأكد?",
                    function(result) {
                        if (result) {
                            try {
                                var id = $("#mr_id").val();
                                $.ajax({
                                    url: '@Url.Action("Depend", "Order")',
                                    data: { id: id, rejectreason: result, orderType: "" },
                                    success: function(data) {
                                        var result = data;
                                        if (result.Success) {
                                            alert(result.Message);
                                            $(".order-actions").hide();
                                            $("#modal").modal('hide');
                                        } else {
                                            alert(result.Message);
                                            resetButton();
                                        }
                                    },
                                    error: function(xhr, ajaxOptions, thrownError) {
                                        alert(xhr.responseText);

                                    }
                                });
                            } catch (e) {
                                alert(e);
                            }

                        } else {
                            resetButton();
                        }
                    }
                );
            });*@


        $("#bootbox-reject").on('click',
            function() {
                try {


                    bootbox.prompt("الرجاء قم بأدخال سبب الرفض",
                        function(result) {

                            if (result) {
                                var id = $("#mr_id").val();
                                $.ajax({
                                    url: '@Url.Action("Reject", "Order")',
                                    data: { id: id, rejectreason: result, orderType: "" },
                                    success: function(data) {
                                        var result = data;
                                        if (result.Success) {
                                            alert(result.Message);
                                            //   $("#bootbox-reject").hide();
                                            $(".order-actions").hide();
                                            $("#modal").modal('hide');
                                            //   window.location.href = '/Order/OrderDetail/' + id;
                                        } else {
                                            alert(result.Message);
                                        }
                                    },
                                    error: function(xhr, ajaxOptions, thrownError) {
                                        alert(xhr.responseText);

                                    }
                                });
                            }
                        });
                } catch (e) {
                    alert(e);
                }
            });

        $("#bootbox-modify-amount").on('click',
            function() {
                try {
                    var id = $("#mr_id").val();
                    bootbox.prompt("الرجاء قم بأدخال المبلغ",
                        function(result) {

                            if (result) {

                                $.ajax({
                                    url: '@Url.Action("ModifyAmount", "Order")',
                                    data: { id: id, amount: result, orderType: "" },
                                    success: function(data) {

                                        if (data.Success) {
                                            alert('تم تعديل المبلغ بنجاح');
                                            $("#AmountInText").text(data.Message);
                                            $("#Amount").text(result);

                                            //   window.location.href = '/Order/OrderDetail/' + id;
                                        } else {
                                            alert(data.Message);
                                        }
                                    },
                                    error: function(xhr, ajaxOptions, thrownError) {
                                        alert(xhr.responseText);

                                    }
                                });
                            }
                        });
                } catch (e) {
                    alert(e);
                }
            });

        @*$("#bootbox-extra-amount").on('click',
            function() {
                try {
                    var id = $("#mr_id").val();
                    bootbox.prompt("  الرجاء قم بأدخال المبلغ الأضافي",
                        function(result) {
                            if (result) {
                                $.ajax({
                                    url: '@Url.Action("ExtraAmount", "Order")',
                                    data: { id: id, amount: result, orderType: "" },
                                    success: function(data) {
                                        var result = data;
                                        if (result.Success) {
                                            alert(result.Message);
                                            //  window.location.href = '/Order/OrderDetail/' + id;
                                        } else {
                                            alert(result.Message);
                                        }
                                    },
                                    error: function(xhr, ajaxOptions, thrownError) {
                                        alert(xhr.responseText);

                                    }
                                });
                            }

                        });
                } catch (e) {
                    alert(e);
                }
            });


        $("#bootbox-delete").on(ace.click_event,
            function() {
                bootbox.confirm("سوف يتم حذف هذا الطلب , هل انت متأكد?",
                    function(result) {
                        if (result) {
                            try {
                                var id = $("#mr_id").val();

                                $.ajax({
                                    url: '@Url.Action("Delete", "Order")',
                                    data: { id: id },
                                    success: function(data) {
                                        var result = data;
                                        if (result.Success) {
                                            alert(result.Message);
                                            window.location.href = '/Order/OrderDetail/' + id;

                                        } else {
                                            alert(result.Message);
                                        }
                                    },
                                    error: function(xhr, ajaxOptions, thrownError) {
                                        alert(xhr.responseText);

                                    }
                                });
                            } catch (e) {
                                alert(e);
                            }

                        }
                    });
            });


        $("#bootbox-options").on('click',
            function() {
                bootbox.dialog({
                    message: "<span class='bigger-110'>I am a custom dialog with smaller buttons</span>",
                    buttons:
                    {
                        "success":
                        {
                            "label": "<i class='ace-icon fa fa-check'></i> Success!",
                            "className": "btn-sm btn-success",
                            "callback": function() {
                                ar("great success");
                            }
                        },
                        "danger":
                        {
                            "label": "Danger!",
                            "className": "btn-sm btn-danger",
                            "callback": function() {
                                //Example.show("uh oh, look out!");
                            }
                        },
                        "click":
                        {
                            "label": "Click ME!",
                            "className": "btn-sm btn-primary",
                            "callback": function() {
                                //Example.show("Primary button");
                            }
                        },
                        "button":
                        {
                            "label": "Just a button...",
                            "className": "btn-sm"
                        }
                    }
                });
            });*@


    });

</script>