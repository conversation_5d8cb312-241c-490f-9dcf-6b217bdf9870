# Test Script for AppTech Deobfuscation Tools
# سكريبت اختبار أدوات فك التشفير

Write-Host "=== Testing AppTech Deobfuscation Tools ===" -ForegroundColor Green

# اختبار وجود الأدوات المطلوبة
Write-Host "`nChecking required tools..." -ForegroundColor Yellow

$tools = @{
    "de4dot" = "C:\inetpub\de4dot\de4dot.exe"
    "dnSpy" = "C:\inetpub\DeobfuscationTools\dnSpy\dnSpy.exe"
    "dnSpy Console" = "C:\inetpub\DeobfuscationTools\dnSpy\dnSpy.Console.exe"
}

$allToolsAvailable = $true

foreach ($tool in $tools.GetEnumerator()) {
    if (Test-Path $tool.Value) {
        Write-Host "✅ $($tool.Key): Available" -ForegroundColor Green
    } else {
        Write-Host "❌ $($tool.Key): Missing" -ForegroundColor Red
        $allToolsAvailable = $false
    }
}

if (!$allToolsAvailable) {
    Write-Host "`n⚠️ Some tools are missing. Please ensure all tools are properly installed." -ForegroundColor Red
    exit 1
}

# اختبار صلاحيات النظام
Write-Host "`nChecking system permissions..." -ForegroundColor Yellow

try {
    $testFile = "C:\inetpub\DeobfuscationTools\permission_test.tmp"
    "test" | Out-File -FilePath $testFile -Force
    Remove-Item $testFile -Force
    Write-Host "✅ Write permissions: Available" -ForegroundColor Green
} catch {
    Write-Host "❌ Write permissions: Insufficient" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator" -ForegroundColor Yellow
    exit 1
}

# اختبار de4dot
Write-Host "`nTesting de4dot..." -ForegroundColor Yellow

try {
    $de4dotTest = & "C:\inetpub\de4dot\de4dot.exe" --help 2>&1
    if ($LASTEXITCODE -eq 0 -or $de4dotTest -match "de4dot") {
        Write-Host "✅ de4dot: Working" -ForegroundColor Green
    } else {
        Write-Host "❌ de4dot: Not responding properly" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ de4dot: Error - $($_.Exception.Message)" -ForegroundColor Red
}

# البحث عن ملفات AppTech للاختبار
Write-Host "`nSearching for AppTech files..." -ForegroundColor Yellow

$testDlls = Get-ChildItem -Path "C:\inetpub" -Filter "AppTech*.dll" -Recurse | Select-Object -First 3
$testLicenses = Get-ChildItem -Path "C:\inetpub" -Filter "*.lic" -Recurse | Select-Object -First 2
$testConfigs = Get-ChildItem -Path "C:\inetpub" -Filter "web.config" -Recurse | Select-Object -First 2

Write-Host "Found files for testing:" -ForegroundColor Cyan
Write-Host "  - DLL files: $($testDlls.Count)" -ForegroundColor White
Write-Host "  - License files: $($testLicenses.Count)" -ForegroundColor White
Write-Host "  - Config files: $($testConfigs.Count)" -ForegroundColor White

if ($testDlls.Count -eq 0 -and $testLicenses.Count -eq 0 -and $testConfigs.Count -eq 0) {
    Write-Host "❌ No AppTech files found for testing" -ForegroundColor Red
    exit 1
}

# اختبار سريع لفك تشفير ترخيص واحد
if ($testLicenses.Count -gt 0) {
    Write-Host "`nTesting license decryption..." -ForegroundColor Yellow
    
    try {
        $licenseFile = $testLicenses[0]
        $content = Get-Content $licenseFile.FullName -Raw
        
        if ($content -match "^[A-Za-z0-9+/=]+$") {
            $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($content))
            if ($decoded -match "<LicenseEntity") {
                Write-Host "✅ License decryption: Working" -ForegroundColor Green
            } else {
                Write-Host "⚠️ License decryption: Unexpected format" -ForegroundColor Yellow
            }
        } else {
            Write-Host "⚠️ License file: Not Base64 encoded" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ License decryption: Failed - $($_.Exception.Message)" -ForegroundColor Red
    }
}

# اختبار سريع لتحليل ملف تكوين
if ($testConfigs.Count -gt 0) {
    Write-Host "`nTesting config analysis..." -ForegroundColor Yellow
    
    try {
        $configFile = $testConfigs[0]
        $content = Get-Content $configFile.FullName -Raw
        [xml]$xmlContent = $content
        
        if ($xmlContent.configuration) {
            Write-Host "✅ Config analysis: Working" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Config analysis: Invalid XML format" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ Config analysis: Failed - $($_.Exception.Message)" -ForegroundColor Red
    }
}

# اختبار سريع لـ de4dot على ملف DLL
if ($testDlls.Count -gt 0) {
    Write-Host "`nTesting de4dot on sample DLL..." -ForegroundColor Yellow
    
    try {
        $testDll = $testDlls[0]
        $outputTest = "C:\inetpub\DeobfuscationTools\test_output.dll"
        
        $de4dotArgs = @(
            "`"$($testDll.FullName)`"",
            "--output", "`"$outputTest`"",
            "--detect-obfuscators"
        )
        
        $process = Start-Process -FilePath "C:\inetpub\de4dot\de4dot.exe" -ArgumentList $de4dotArgs -Wait -PassThru -NoNewWindow -RedirectStandardOutput "C:\inetpub\DeobfuscationTools\test_output.log" -RedirectStandardError "C:\inetpub\DeobfuscationTools\test_error.log"
        
        if ($process.ExitCode -eq 0 -and (Test-Path $outputTest)) {
            Write-Host "✅ de4dot processing: Working" -ForegroundColor Green
            Remove-Item $outputTest -Force -ErrorAction SilentlyContinue
        } else {
            Write-Host "⚠️ de4dot processing: Completed with warnings (Exit Code: $($process.ExitCode))" -ForegroundColor Yellow
        }
        
        # تنظيف ملفات الاختبار
        Remove-Item "C:\inetpub\DeobfuscationTools\test_*.log" -Force -ErrorAction SilentlyContinue
        
    } catch {
        Write-Host "❌ de4dot processing: Failed - $($_.Exception.Message)" -ForegroundColor Red
    }
}

# النتيجة النهائية
Write-Host "`n=== Test Results Summary ===" -ForegroundColor Green
Write-Host "All basic tests completed. The tools appear to be ready for use." -ForegroundColor Cyan

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Run the master decryption script:" -ForegroundColor White
Write-Host "   .\MasterDecryption.ps1" -ForegroundColor Cyan
Write-Host "2. Or run individual scripts as needed" -ForegroundColor White
Write-Host "3. Check the README.md for detailed instructions" -ForegroundColor White

Write-Host "`n✨ Ready to decrypt AppTech files! ✨" -ForegroundColor Green
