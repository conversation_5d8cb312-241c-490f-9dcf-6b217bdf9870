﻿@*@model AppTech.MSMS.Domain.Models.SatellitePayment

<div class="space-6"></div>
<span class="label label-info"> تفاصيل الطلب</span>
<div class="space-6"></div>


<div class="profile-info-row">
    <div class="profile-info-name"> القناة </div>
    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.ProviderID)</span>
    </div>
</div>
<div class="profile-info-row">
    <div class="profile-info-name"> الفئة</div>
    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.FactionID)</span>
    </div>
</div>
<div class="profile-info-row">
    <div class="profile-info-name"> مدة الاشتراك</div>
    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.SubscriptionTerm)</span>
    </div>
</div>
<div class="profile-info-row">
    <div class="profile-info-name"> المبلغ</div>
    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Amount)</span>
    </div>
</div>
<div class="profile-info-row">
    <div class="profile-info-name"> رقم الاشتراك</div>
    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Note)</span>
    </div>
</div>*@

@model AppTech.MSMS.Domain.Models.SatellitePayment

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
    <div>
        <h3 class="" style="text-align: center;color: #e00a00;">بيانات الطلب</h3>
        <div class="form-group">
            @Html.LabelFor(model => model.ProviderID, new { @class = "contol-label col-md-2 align-left" })
            <div class="col-md-10 form-inline">
                @Html.DisplayFor(model => model.ProviderID, new { @class = "col-md-8" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.FactionID, new { @class = "contol-label col-md-2 align-left" })
            <div class="col-md-10 form-inline">
                @Html.DisplayFor(model => model.FactionID, new { @class = "col-md-8" })
            </div>
        </div>
        <div class="form-group form-inline">
            @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
            <div class="col-md-10 form-inline">
                @Html.DisplayFor(model => model.Amount, new { @class = "col-md-8" })
            </div>
        </div>
          <div class="form-group form-inline">
            @Html.LabelFor(model => model.SubscriptionTerm, new { @class = "control-label col-md-2" })
            <div class="col-md-10 form-inline">
                @Html.DisplayFor(model => model.SubscriptionTerm, new { @class = "col-md-8" })
            </div>
        </div>
        <div class="form-group form-inline">
            @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
            <div class="col-md-10 form-inline">
                @Html.DisplayFor(model => model.Note, new { @class = "col-md-8" })
            </div>
        </div>
        <hr />
        <h3 style="text-align: center;color: #058c15;">معالجة الطلب</h3>
        <div class="form-group">
            @Html.LabelFor(model => model.Status, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownListFor(model => model.Status, new[]
                  {

                      new SelectListItem {Text = "المعالجة", Value = ""},
                      new SelectListItem {Text = "قبول الطلب", Value = "1"},
                      new SelectListItem {Text = "رفض الطلب", Value = "2"}
                  })
                @Html.ValidationMessageFor(model => model.Status)
            </div>
        </div>
    </div>

