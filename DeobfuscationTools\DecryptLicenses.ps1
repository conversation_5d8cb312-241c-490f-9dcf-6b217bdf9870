# AppTech License Decryption Script
# سكريبت فك تشفير ملفات التراخيص

param(
    [string]$SourcePath = "C:\inetpub",
    [string]$OutputPath = "C:\inetpub\DecryptedLicenses"
)

Write-Host "=== AppTech License Decryption Tool ===" -ForegroundColor Green

# إنشاء مجلد الإخراج
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
}

# البحث عن ملفات التراخيص
Write-Host "`nSearching for license files..." -ForegroundColor Cyan
$LicenseFiles = Get-ChildItem -Path $SourcePath -Filter "*.lic" -Recurse

Write-Host "Found $($LicenseFiles.Count) license files:" -ForegroundColor Yellow
foreach ($lic in $LicenseFiles) {
    Write-Host "  - $($lic.FullName)" -ForegroundColor White
}

# دالة لفك تشفير Base64
function Decrypt-Base64License {
    param(
        [string]$InputFile,
        [string]$OutputFile
    )
    
    try {
        Write-Host "`nDecrypting: $InputFile" -ForegroundColor Cyan
        
        # قراءة المحتوى
        $base64Content = Get-Content $InputFile -Raw
        
        # فك تشفير Base64
        $decodedBytes = [System.Convert]::FromBase64String($base64Content)
        $decodedText = [System.Text.Encoding]::UTF8.GetString($decodedBytes)
        
        # حفظ النتيجة
        $decodedText | Out-File -FilePath $OutputFile -Encoding UTF8
        
        Write-Host "Decrypted license saved to: $OutputFile" -ForegroundColor Green
        
        # تحليل محتوى XML
        try {
            [xml]$xmlContent = $decodedText
            Write-Host "`nLicense Information:" -ForegroundColor Yellow
            
            if ($xmlContent.LicenseEntity) {
                $license = $xmlContent.LicenseEntity
                Write-Host "  - App Name: $($license.AppName)" -ForegroundColor White
                Write-Host "  - UID: $($license.UID)" -ForegroundColor White
                Write-Host "  - Type: $($license.Type)" -ForegroundColor White
                Write-Host "  - Version Type: $($license.VersionType)" -ForegroundColor White
                Write-Host "  - Expiry Date: $($license.ExpiryDate)" -ForegroundColor White
                Write-Host "  - Create Date: $($license.CreateDateTime)" -ForegroundColor White
                Write-Host "  - System Level: $($license.SystemLevel)" -ForegroundColor White
                Write-Host "  - Max Users: $($license.MaxUsers)" -ForegroundColor White
                Write-Host "  - Max Branches: $($license.MaxBranches)" -ForegroundColor White
                Write-Host "  - Customer Number: $($license.CustomerNumber)" -ForegroundColor White
                
                if ($license.Signature) {
                    Write-Host "  - Digital Signature: Present" -ForegroundColor Red
                }
            }
        } catch {
            Write-Host "Could not parse as XML: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        return $true
    } catch {
        Write-Host "Error decrypting license: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# معالجة ملفات التراخيص
Write-Host "`n=== Starting License Decryption ===" -ForegroundColor Green

$successCount = 0
$failCount = 0

foreach ($licFile in $LicenseFiles) {
    $relativePath = $licFile.FullName.Substring($SourcePath.Length + 1)
    $outputFile = Join-Path $OutputPath ($relativePath -replace "\.lic$", "_decrypted.xml")
    $outputDir = Split-Path $outputFile -Parent
    
    # إنشاء مجلد الإخراج
    if (!(Test-Path $outputDir)) {
        New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    }
    
    if (Decrypt-Base64License -InputFile $licFile.FullName -OutputFile $outputFile) {
        $successCount++
    } else {
        $failCount++
    }
}

Write-Host "`n=== License Decryption Complete ===" -ForegroundColor Green
Write-Host "Successfully decrypted: $successCount files" -ForegroundColor Green
Write-Host "Failed to decrypt: $failCount files" -ForegroundColor Red
Write-Host "Output directory: $OutputPath" -ForegroundColor Yellow
