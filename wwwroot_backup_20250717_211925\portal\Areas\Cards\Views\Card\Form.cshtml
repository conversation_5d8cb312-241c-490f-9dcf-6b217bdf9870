﻿@model AppTech.MSMS.Domain.Models.Card
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-horizontal">

    @Html.ValidationSummary(true)

    <div class="form-group">
        @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Name)
            @Html.ValidationMessageFor(model => model.Name)
        </div>
    </div> 
    <div class="form-group">
        @Html.LabelFor(model => model.Password, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Password)
            @Html.ValidationMessageFor(model => model.Password)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.CardTypeID, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            <select id="CardTypeID" name="CardTypeID" class="select2" ></select>
            @Html.ValidationMessageFor(model => model.CardTypeID)
        </div>
    </div> 
    <div class="form-group">
        @Html.LabelFor(model => model.CardFactionID, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            <select id="CardFactionID" name="CardFactionID" class="select2" style="width: 110px;" placeholder="اختر فئه" required></select>
            @Html.ValidationMessageFor(model => model.CardFactionID)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Note)
            @Html.ValidationMessageFor(model => model.Note)
        </div>
    </div>
</div>

<script>
    $("#CardTypeID").on("change", function () {
        var id =$("#CardTypeID").val();
        loadFactionList(id);
    });
    function loadFirstList() {
        i('loadDataList');
        fillDataList('CardTypeID', '/Cards/Card/GetCardTypes');
    }
    function loadFactionList(id) {
        i('loadDataList');
        fillDataList('CardFactionID', '/Cards/Card/GetCardFactions?id=' + id,);
    }
    loadFirstList();
    loadFactionList(1);
</script>
