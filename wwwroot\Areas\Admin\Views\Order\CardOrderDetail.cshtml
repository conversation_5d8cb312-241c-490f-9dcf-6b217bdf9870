﻿@model AppTech.MSMS.Domain.Models.CardOrder

<div class="space-6"></div>
<span class="label label-info"> تفاصيل الطلب</span>
<div class="space-6"></div>


<div class="profile-info-row">
    <div class="profile-info-name"> المبلغ </div>

    <div class="profile-info-value">
        <span class="editable" id="signup"> @Html.DisplayFor(model => model.Amount)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name">  النوع </div>
    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.CardType.Name)</span>
    </div>
</div>
<div class="profile-info-row">
    <div class="profile-info-name">  الفئة </div>
    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.CardFaction.Name)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> ID </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.UserID)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name">اسم المستخدم  </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Username)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> كلمة المرور </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Password)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> الإيميل </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Email)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> رقم الهاتف </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Phone)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> الملاحظات </div>

    <div class="profile-info-value">
        <span class="editable" id="about"> @Html.DisplayFor(model => model.Note)</span>
    </div>
</div>
