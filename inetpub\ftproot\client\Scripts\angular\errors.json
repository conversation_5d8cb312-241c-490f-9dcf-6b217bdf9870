{"id": "ng", "generated": "Tue Mar 12 2019 09:40:19 GMT+0000 (UTC)", "errors": {"ng": {"areq": "Argument '{0}' is {1}", "cpta": "Can't copy! TypedArray destination cannot be mutated.", "test": "no injector found for element argument to getTestability", "cpws": "Can't copy! Making copies of Window or Scope instances is not supported.", "aobj": "Argument '{0}' must be an object", "btstrpd": "App already bootstrapped with this element '{0}'", "cpi": "Can't copy! Source and destination are identical.", "badname": "hasOwnProperty is not a valid {0} name"}, "$http": {"baddata": "Data must be a valid JSON object. Received: \"{0}\". Parse error: \"{1}\"", "badreq": "Http request configuration url must be a string or a $sce trusted object.  Received: {0}", "badjsonp": "Illegal use of callback param, \"{0}\", in url, \"{1}\""}, "ngPattern": {"noregexp": "Expected {0} to be a RegExp but was {1}. Element: {2}"}, "$parse": {"ueoe": "Unexpected end of expression: {0}", "lexerr": "<PERSON>er Error: {0} at column{1} in expression [{2}].", "esc": "IMPOSSIBLE", "lval": "Trying to assign a value to a non l-value", "syntax": "Syntax Error: Token '{0}' {1} at column {2} of the expression [{3}] starting at [{4}]."}, "orderBy": {"notarray": "Expected array but received: {0}"}, "$q": {"norslvr": "Expected resolverFn, got '{0}'", "qcycle": "Expected promise to be resolved with value other than itself '{0}'"}, "$injector": {"pget": "Provider '{0}' must define $get factory method.", "cdep": "Circular dependency found: {0}", "nomod": "Module '{0}' is not available! You either misspelled the module name or forgot to load it. If registering a module ensure that you specify the dependencies as the second argument.", "strictdi": "{0} is not using explicit annotation and cannot be invoked in strict mode", "modulerr": "Failed to instantiate module {0} due to:\n{1}", "undef": "Provider '{0}' must return a value from $get factory method.", "unpr": "Unknown provider: {0}", "itkn": "Incorrect injection token! Expected service name as string, got {0}"}, "ngTransclude": {"orphan": "Illegal use of ngTransclude directive in the template! No parent directive that requires a transclusion found. Element: {0}"}, "ngModel": {"nopromise": "Expected asynchronous validator to return a promise but got '{0}' instead.", "nonassign": "Expression '{0}' is non-assignable. Element: {1}", "datefmt": "Expected `{0}` to be a date", "constexpr": "Expected constant expression for `{0}`, but saw `{1}`.", "numfmt": "Expected `{0}` to be a number"}, "ngOptions": {"iexp": "Expected expression in form of '_select_ (as _label_)? for (_key_,)?_value_ in _collection_' but got '{0}'. Element: {1}"}, "$rootScope": {"inprog": "{0} already in progress", "infdig": "{0} $digest() iterations reached. Aborting!\nWatchers fired in the last 5 iterations: {1}"}, "$compile": {"selmulti": "Binding to the 'multiple' attribute is not supported. Element: {0}", "ctreq": "Controller '{0}', required by directive '{1}', can't be found!", "tplrt": "Template for directive '{0}' must have exactly one root element. {1}", "iscp": "Invalid {3} for directive '{0}'. Definition: {... {1}: '{2}' ...}", "baddir": "Directive/Component name '{0}' is invalid. The name should not contain leading or trailing whitespaces", "multilink": "This element has already been linked.", "noctrl": "Cannot bind to controller without directive '{0}'s controller.", "srcset": "Can't pass trusted values to `{0}`: \"{1}\"", "multidir": "Multiple directives [{0}{1}, {2}{3}] asking for {4} on: {5}", "badrestrict": "Restrict property '{0}' of directive '{1}' is invalid", "uterdir": "Unterminated attribute, found '{0}' but no matching '{1}' found.", "ctxoverride": "Property context '{0}.{1}' already set to '{2}', cannot override to '{3}'.", "infchng": "{0} $onChanges() iterations reached. Aborting!\n", "reqslot": "Required transclusion slot `{0}` was not filled.", "nodomevents": "Interpolations for HTML DOM event attributes are disallowed", "nonassign": "Expression '{0}' in attribute '{1}' used with directive '{2}' is non-assignable!", "missingattr": "Attribute '{0}' of '{1}' is non-optional and must be set!", "noslot": "No parent directive that requires a transclusion with slot name \"{0}\". Element: {1}"}, "ngRepeat": {"badident": "alias '{0}' is invalid --- must be a valid JS identifier which is not a reserved name.", "iexp": "Expected expression in form of '_item_ in _collection_[ track by _id_]' but got '{0}'.", "dupes": "Duplicates in a repeater are not allowed. Use 'track by' expression to specify unique keys. Repeater: {0}, Duplicate key: {1}, Duplicate value: {2}", "iidexp": "'_item_' in '_item_ in _collection_' should be an identifier or '(_key_, _value_)' expression, but got '{0}'."}, "$sce": {"imatcher": "Matchers may only be \"self\", string patterns or RegExp objects", "icontext": "Attempted to trust a value in invalid context. Context: {0}; Value: {1}", "iwcard": "Illegal sequence *** in string matcher.  String: {0}", "insecurl": "Blocked loading resource from url not allowed by $sceDelegate policy.  URL: {0}", "iequirks": "Strict Contextual Escaping does not support Internet Explorer version < 11 in quirks mode.  You can fix this by adding the text <!doctype html> to the top of your HTML document.  See http://docs.angularjs.org/api/ng.$sce for more information.", "unsafe": "Attempting to use an unsafe value in a safe context.", "itype": "Attempted to trust a non-string value in a content requiring a string: Context: {0}"}, "$timeout": {"badprom": "`$timeout.cancel()` called with a promise that was not generated by `$timeout()`."}, "ngRef": {"nonassign": "Expression in ngRef=\"{0}\" is non-assignable!", "noctrl": "The controller for ngRefRead=\"{0}\" could not be found on ngRef=\"{1}\""}, "$controller": {"ctrlfmt": "Badly formed controller string '{0}'. Must match `__name__ as __id__` or `__name__`.", "ctrlreg": "The controller with the name '{0}' is not registered.", "noscp": "Cannot export controller '{0}' as '{1}'! No $scope object provided via `locals`."}, "jqLite": {"offargs": "jqLite#off() does not support the `selector` argument", "onargs": "jqLite#on() does not support the `selector` or `eventData` parameters", "nosel": "Looking up elements via selectors is not supported by jqLite! See: http://docs.angularjs.org/api/angular.element"}, "$animate": {"notcsel": "Expecting class selector starting with '.' got '{0}'.", "nongcls": "$animateProvider.classNameFilter(regex) prohibits accepting a regex value which matches/contains the \"{0}\" CSS class."}, "$templateRequest": {"tpload": "Failed to load template: {0} (HTTP status: {1} {2})"}, "filter": {"notarray": "Expected array but received: {0}"}, "$interval": {"badprom": "`$interval.cancel()` called with a promise that was not generated by `$interval()`."}, "$location": {"nostate": "History API state support is available only in HTML5 mode and only in browsers supporting HTML5 History API", "badpath": "Invalid url \"{0}\".", "ipthprfx": "Invalid url \"{0}\", missing path prefix \"{1}\".", "isrcharg": "The first argument of the `$location#search()` call must be a string or an object.", "nobase": "$location in HTML5 mode requires a <base> tag to be present!"}, "$cacheFactory": {"iid": "CacheId '{0}' is already taken!"}, "$interpolate": {"noconcat": "Error while interpolating: {0}\nStrict Contextual Escaping disallows interpolations that concatenate multiple expressions when a trusted value is required.  See http://docs.angularjs.org/api/ng.$sce", "interr": "Can't interpolate: {0}\n{1}", "nochgmustache": "angular-message-format.js currently does not allow you to use custom start and end symbols for interpolation.", "reqcomma": "Expected a comma after the keyword “{0}” at line {1}, column {2} of text “{3}”", "untermstr": "The string beginning at line {0}, column {1} is unterminated in text “{2}”", "badexpr": "Unexpected operator “{0}” at line {1}, column {2} in text. Was expecting “{3}”. Text: “{4}”", "dupvalue": "The choice “{0}” is specified more than once. Duplicate key is at line {1}, column {2} in text “{3}”", "unsafe": "Use of select/plural MessageFormat syntax is currently disallowed in a secure context ({0}).  At line {1}, column {2} of text “{3}”", "reqother": "“other” is a required option.", "reqendinterp": "Expecting end of interpolation symbol, “{0}”, at line {1}, column {2} in text “{3}”", "reqarg": "Expected one of “plural” or “select” at line {0}, column {1} of text “{2}”", "wantstring": "Expected the beginning of a string at line {0}, column {1} in text “{2}”", "logicbug": "The messageformat parser has encountered an internal error.  Please file a github issue against the AngularJS project and provide this message text that triggers the bug.  Text: “{0}”", "reqopenbrace": "The plural choice “{0}” must be followed by a message in braces at line {1}, column {2} in text “{3}”", "unknarg": "Unsupported keyword “{0}” at line {0}, column {1}. Only “plural” and “select” are currently supported.  Text: “{3}”", "reqendbrace": "The plural/select choice “{0}” message starting at line {1}, column {2} does not have an ending closing brace. Text “{3}”"}, "$resource": {"badargs": "Expected up to 4 arguments [params, data, success, error], got {0} arguments", "badmember": "Dotted member path \"@{0}\" is invalid.", "badname": "hasOwnProperty is not a valid parameter name.", "badcfg": "Error in resource configuration for action `{0}`. Expected response to contain an {1} but got an {2} (Request: {3} {4})"}, "$route": {"norout": "Tried updating route with no current route"}, "linky": {"notstring": "Expected string but received: {0}"}, "$sanitize": {"noinert": "Can't create an inert html document", "uinput": "Failed to sanitize html because the input is unstable", "elclob": "Failed to sanitize html because the element is clobbered: {0}"}}}