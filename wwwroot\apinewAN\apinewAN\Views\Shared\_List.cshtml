﻿@using System.Data
@using AppTech.Common.Extensions
@using AppTech.MSMS.Web.Models
@model AppTech.MSMS.Web.Code.Paginate.PagedList
@{
    var condtion = Model.Condition;
}
<style>
    .table-wrapper-scroll-y {
        display: block;
        max-height: 500px;
        overflow-y: auto;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }

    .table > thead > tr:first-child > td {
        border: none;
    }

    .table-bordered td {
        border: none !important;
        border-right: solid 0px #ccc !important;
    }


    th {
        background-color: white;
        color: steelblue;
        border-right: none !important;
        border-left: none !important;
    }





    /*.wrapper1, .wrapper2 {
        width: 100%;
        overflow-x: scroll;
        overflow-y: hidden;
    }

    .wrapper1 {
        height: 20px;
    }

    .div1 {
        height: 20px;
        overflow: none;
    }

    .div2 {
        overflow: auto;
    }*/
</style>


<div id="list">

    <input type="hidden" id="QryCondition" value="@Model.Condition" />

    <div class="table-responsive double-scroll">
        <table id="simple-table" class="table table-hover table-bordered table-striped table-condensed crud-table table-sm" width="100%" border="0" cellspacing="0" cellpadding="0">
            <thead class="">
                <tr height="40" class="text-center">
                    @foreach (DataColumn col in Model.Items.Columns)
                    {
                        if (!col.Caption.Equals("ID"))
                        {
                            <th data-sortable="true">@col.Caption</th>
                        }
                    }
                    <th style="text-align: right;" class="none-print-element"></th>
                </tr>
            </thead>
            <tbody id="table-body">
                @foreach (DataRow row in Model.Items.Rows)
                {
                <tr height="" data-id="@row["ID"]">
                    @for (var i = 0; i < row.ItemArray.Length; i++)
                    {
                        if (i != 0)
                        {
                            if (row.ItemArray[i] is decimal)
                            {
                                <td style="text-align: right;">@row.ItemArray[i].ToDecimal().ToString("#.##")</td>
                            }
                            else if (row.ItemArray[i].ToString().StartsWith("~/Photos/"))
                            {
                                <td style="text-align: right;"><img src="@row.ItemArray[i].ToString().TrimStart('~')" style="width:200px;height:auto" /></td>
                            }
                            else if (row.ItemArray[i].ToString().Equals("True"))
                            {
                                <td style="text-align: right;">نعم</td>
                            }
                            else if (row.ItemArray[i].ToString().Equals("False"))
                            {
                                <td style="text-align: right;">لا</td>
                            }
                            else
                            {
                                <td class="record-row" style="text-align: right;">@row.ItemArray[i].ToString()</td>
                            }
                        }
                    }
                    <td style="text-align: right;" class="none-print-element record-actions noExl">

                        <div class="btn-group form-inline">
                            @{
                                if (!string.IsNullOrEmpty(Model.RecordAction))
                                {
                                    @Html.Partial(Model.RecordAction, new ActionModel { ID = row["ID"].ToLong(), Row = row })
                                }
                                else
                                {
                                    @Html.Partial("_RecordAction", new ActionModel { ID = row["ID"].ToLong() })
                                }
                            }
                        </div>
                    </td>
                </tr>
                }
            </tbody>

            @*<tfoot class="none-print-element">
                    <tr height="40" class="text-center">
                        @foreach (DataColumn col in Model.Items.Columns)
                        {
                            if (!col.Caption.Equals("ID"))
                            {
                                <th data-sortable="true">@col.Caption</th>
                            }
                        }

                    </tr>
                </tfoot>*@
        </table>
    </div>
    <div class="center none-print-element">
        @Html.Pager(Model)
    </div>


</div>

<script type="text/javascript">
    $(document).ready(function () {


    });
</script>



<script>
  


    //jQuery(document).ready(function ($) {
    //   // var table = $("#very-wide-element1").DataTable();
    //    try {
    //        var table = $("#simple-table").DataTable({
    //            //   "dom": '<"toolbar">frtip',
    //            //initComplete: function () {
    //            //    this.api().columns().every(function () {
    //            //        return;
    //            //        var column = this;
    //            //        var select = $('<select><option value=""><< الكل >></option></select>')
    //            //            .appendTo($(column.footer()).empty())
    //            //            .on('change', function () {
    //            //                var val = $.fn.dataTable.util.escapeRegex(
    //            //                    $(this).val()
    //            //                );

    //            //                column
    //            //                    .search(val ? '^' + val + '$' : '', true, false)
    //            //                    .draw();
    //            //            });

    //            //        column.data().unique().sort().each(function (d, j) {
    //            //            select.append('<option value="' + d + '">' + d + '</option>')
    //            //        });
    //            //    });
    //            //},

    //              "scrollX": true,
    //              "scrollY": 400,
    //            "scrollCollapse": false,
    //            "paging": false,
    //            //     "order": [[0, "desc"]],
    //            "lengthChange": true,
    //            "searching": true,
    //            "ordering": false,
    //            "info": true,
    //            "autoWidth": false,
    //            //dom: "Bfrtip",
    //            //buttons: [
    //            //    "copy", "csv", "excel", "pdf", "print"
    //            //],
    //            "language": {
    //                "decimal": ",",
    //                "thousands": ".",
    //                "sProcessing": "جارٍ التحميل...",
    //                "sLengthMenu": "أظهر _MENU_ مدخلات",
    //                "sZeroRecords": "لم يعثر على أية سجلات",
    //                "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
    //                "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
    //                "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
    //                "sInfoPostFix": "",
    //                "sSearch": "البحث:",
    //                "sUrl": "",
    //                "oPaginate": {
    //                    "sFirst": "الأول",
    //                    "sPrevious": "السابق",
    //                    "sNext": "التالي",
    //                    "sLast": "الأخير"
    //                }
    //            }
    //        });

    //    } catch (e) {
    //        i("data error: " + e);
    //    }
    //    $('.dataTables_length').addClass('bs-select');
    //    //    $("div.toolbar").html('<div><select id="pageSize">    <option>10</option>  <option>30</option> </select></div>');
    //});
</script>
