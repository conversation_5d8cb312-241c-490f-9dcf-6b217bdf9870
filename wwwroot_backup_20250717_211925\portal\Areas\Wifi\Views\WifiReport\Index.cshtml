﻿@model AppTech.MSMS.Domain.Reports.Models.WifiModel
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Domain
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


    <div class="form-horizontal">
        @{
            Html.RenderPartial("_DateControl");
        }
        <span class="lbl"> الشبكة</span>
        @Html.DropDownListFor(model => model.ProviderID, (SelectList)ViewBag.Providers)
        <div class="space-6"></div>

        <span class="lbl">الفئة</span>
        @Html.DropDownListFor(model => model.FactionID, (SelectList)ViewBag.Factions)
        <div class="space-6"></div>

        <span class="lbl"> اسم الحساب</span>
        @Html.DropDownListFor(model => model.AccountID, (SelectList)ViewBag.Accounts)
        <div class="space-6"></div>


        <span class="lbl">الحالة</span>
        @Html.DropDownListFor(model => model.State, new[]
           {
               new SelectListItem {Text = "لم يتم البيع", Value = "0"},
               new SelectListItem {Text = "تم البيع ", Value = "1"}
           })
        <div class="space-6"></div>

    </div>

