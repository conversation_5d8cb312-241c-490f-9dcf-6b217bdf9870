﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
        <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
        <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </configSections>
    <system.serviceModel>
        <bindings>
            <basicHttpBinding>
                <binding name="BasicHttpBinding_ICSDServiceLinkLib" />
                <binding name="BasicHttpBinding_Ialbayanmtnclientservice" />
                <binding name="BasicHttpBinding_ICSDServiceLinkLib1" />
                <binding name="BasicHttpBinding_ICSDServiceLinkLib2" />
                <binding name="BasicHttpBinding_ICSDServiceLinkLib3" />
                <binding name="BasicHttpBinding_ICSDServiceLinkLib4" />
                <binding name="BasicHttpBinding_ICSDServiceLinkLib5" />
                <binding name="billpaymentwsSoapBinding" />
                <binding name="BasicHttpBinding_ICSDServiceLinkLib6" />
                <binding name="BasicHttpBinding_Ialbayanmtnclientservice1" />
                <binding name="BasicHttpBinding_ICSDServiceLinkLib7" />
                <binding name="BasicHttpBinding_IService" />
            </basicHttpBinding>
        </bindings>
        <client>
            <endpoint address="http://*************:8444/ForMeFast/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib" contract="ForMeServiceReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib" />
            <endpoint address="http://***************:9999/albayanmtnclientservice/services/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_Ialbayanmtnclientservice" contract="ServiceReference1.Ialbayanmtnclientservice" name="BasicHttpBinding_Ialbayanmtnclientservice" />
            <endpoint address="http://*************:8444/DerhimApiWebService/MainService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib1" contract="DerhimApiReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib1" />
            <endpoint address="http://alatheirvas.com:7443/AtheerWebService/MainService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib2" contract="AtheerServiceReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib2" />
            <endpoint address="http://etsalatie.com:7433/SareeaOLWebService/MainService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib3" contract="AlSareeaOnLineServiceReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib3" />
            <endpoint address="http://alatheirvas.com:6443/AtheerWebService/MainService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib4" contract="AtheerServiceReference2.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib4" />
            <endpoint address="http://qulaidi.dyndns.org:8443/QulaidiWebService/MainService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib5" contract="QulaidiServiceReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib5" />
            <endpoint address="http://***************:8081/YemenPostPayment/services/billpaymentws" binding="basicHttpBinding" bindingConfiguration="billpaymentwsSoapBinding" contract="YemenPostReference.billpaymentws" name="billpaymentws" />
            <endpoint address="http://qulaidi.dyndns.org:8443/QulaidiWebService/MainService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib6" contract="QulaidiServiceReference2.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib6" />
            <endpoint address="http://**************:5525/albayanmtnclientservice/services/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_Ialbayanmtnclientservice1" contract="AlbayanReference.Ialbayanmtnclientservice" name="BasicHttpBinding_Ialbayanmtnclientservice1" />
            <endpoint address="http://*************:8448/Juzaifa/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib7" contract="JuzaifaServiceReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib7" />
            <endpoint address="http://*************:8080/AgentsService.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IService" contract="BaAmerReference.IService" name="BasicHttpBinding_IService" />
        </client>
    </system.serviceModel>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Google.Apis" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Google.Apis.Auth" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IdentityModel.Tokens.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Principal.Windows" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <entityFramework>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
</configuration>