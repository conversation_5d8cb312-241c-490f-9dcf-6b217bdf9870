﻿<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppTech API NEW - ظ†ط¸ط§ظ… ط¥ط¯ط§ط±ط© ط§ظ„ط£ط¹ظ…ط§ظ„</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container { 
            text-align: center; 
            background: rgba(255,255,255,0.1); 
            padding: 40px; 
            border-radius: 20px; 
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            max-width: 800px;
            width: 90%;
        }
        h1 { 
            font-size: 3.5em; 
            margin: 0 0 20px 0; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .status { 
            font-size: 1.3em; 
            margin: 30px 0; 
            padding: 20px;
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        .feature:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.2);
        }
        .btn { 
            display: inline-block; 
            padding: 15px 30px; 
            margin: 10px; 
            background: rgba(255,255,255,0.2); 
            color: white; 
            text-decoration: none; 
            border-radius: 10px; 
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .btn:hover { 
            background: rgba(255,255,255,0.3); 
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .info { 
            margin: 30px 0; 
            font-size: 0.9em; 
            opacity: 0.9; 
            background: rgba(0,0,0,0.2);
            padding: 15px;
            border-radius: 8px;
        }
        .success-badge {
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ًںڑ€ AppTech API NEW</h1>
        
        <div class="status">
            <div class="success-badge">âœ… ط§ظ„ظ†ط¸ط§ظ… ظٹط¹ظ…ظ„</div>
            <div class="success-badge">ًں”§ طھظ… ط§ظ„ط¥طµظ„ط§ط­</div>
            <div class="success-badge">ًںژ¯ ط¬ط§ظ‡ط² ظ„ظ„ط§ط³طھط®ط¯ط§ظ…</div>
            <p style="margin-top: 15px;">طھظ… ط¥طµظ„ط§ط­ ط¬ظ…ظٹط¹ ط§ظ„ظ…ط´ط§ظƒظ„ ظˆطھط­ط¯ظٹط« ط§ظ„ظ†ط¸ط§ظ… ط¨ظ†ط¬ط§ط­</p>
        </div>

        <div class="features">
            <div class="feature">
                <h3>ًںڈ  ط§ظ„ط¨ظˆط§ط¨ط© ط§ظ„ط±ط¦ظٹط³ظٹط©</h3>
                <p>ط§ظ„ظˆط§ط¬ظ‡ط© ط§ظ„ط±ط¦ظٹط³ظٹط© ظ„ظ„ظ†ط¸ط§ظ…</p>
            </div>
            <div class="feature">
                <h3>ًں”Œ ظˆط§ط¬ظ‡ط© API</h3>
                <p>ط®ط¯ظ…ط§طھ ط§ظ„ط¨ط±ظ…ط¬ط© ظˆط§ظ„طھظƒط§ظ…ظ„</p>
            </div>
            <div class="feature">
                <h3>ًں‘¥ ط¨ظˆط§ط¨ط© ط§ظ„ط¹ظ…ظ„ط§ط،</h3>
                <p>ط¥ط¯ط§ط±ط© ط­ط³ط§ط¨ط§طھ ط§ظ„ط¹ظ…ظ„ط§ط،</p>
            </div>
            <div class="feature">
                <h3>ًں’¼ ط¥ط¯ط§ط±ط© ط§ظ„ط£ط¹ظ…ط§ظ„</h3>
                <p>ط£ط¯ظˆط§طھ ط¥ط¯ط§ط±ط© ط´ط§ظ…ظ„ط©</p>
            </div>
        </div>

        <div>
            <a href="/portal" class="btn">ًںڈ  ط§ظ„ط¨ظˆط§ط¨ط© ط§ظ„ط±ط¦ظٹط³ظٹط©</a>
            <a href="/api" class="btn">ًں”Œ API</a>
            <a href="/client" class="btn">ًں‘¥ ط§ظ„ط¹ظ…ظ„ط§ط،</a>
            <a href="/apinewAN" class="btn">ًں†• API ط§ظ„ط¬ط¯ظٹط¯</a>
        </div>

        <div>
            <a href="/collections_system" class="btn">ًں’° ظ†ط¸ط§ظ… ط§ظ„طھط­طµظٹظ„ط§طھ</a>
            <a href="javascript:location.reload()" class="btn">ًں”„ طھط­ط¯ظٹط« ط§ظ„طµظپط­ط©</a>
        </div>

        <div class="info">
            <h4>ًں“ٹ ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„ظ†ط¸ط§ظ…:</h4>
            <p><strong>ط§ظ„ط¥طµط¯ط§ط±:</strong> AppTech 2024 - Professional Edition</p>
            <p><strong>ط§ظ„ط­ط§ظ„ط©:</strong> ظ†ط´ط· ظˆظٹط¹ظ…ظ„ ط¨ظƒط§ظ…ظ„ ط§ظ„ظˆط¸ط§ط¦ظپ</p>
            <p><strong>ط¢ط®ط± طھط­ط¯ظٹط«:</strong> <span id="currentTime"></span></p>
            <p><strong>ط§ظ„ط®ط§ط¯ظ…:</strong> IIS + .NET Framework</p>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.3);">
            <p style="font-size: 0.9em; opacity: 0.8;">
                ًں”§ طھظ… ط¥طµظ„ط§ط­ ط¬ظ…ظٹط¹ ط§ظ„ظ…ط´ط§ظƒظ„ ط§ظ„طھظ‚ظ†ظٹط© ظˆطھط­ط¯ظٹط« ظ…ظ„ظپط§طھ ط§ظ„ظ†ط¸ط§ظ…
            </p>
        </div>
    </div>

    <script>
        // ط¹ط±ط¶ ط§ظ„ظˆظ‚طھ ط§ظ„ط­ط§ظ„ظٹ
        document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        
        // طھط­ط¯ظٹط« ط§ظ„ظˆظ‚طھ ظƒظ„ ط«ط§ظ†ظٹط©
        setInterval(function() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        }, 1000);

        // ط¥ط¶ط§ظپط© طھط£ط«ظٹط±ط§طھ طھظپط§ط¹ظ„ظٹط©
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                setTimeout(() => {
                    feature.style.opacity = '0';
                    feature.style.transform = 'translateY(20px)';
                    feature.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        feature.style.opacity = '1';
                        feature.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
