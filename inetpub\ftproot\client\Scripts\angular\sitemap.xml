<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://docs.angularjs.org/api</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$animate/nongcls</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$animate/notcsel</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$cacheFactory/iid</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/baddir</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/badrestrict</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/ctreq</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/ctxoverride</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/infchng</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/iscp</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/missingattr</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/multidir</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/multilink</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/noctrl</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/nodomevents</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/nonassign</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/noslot</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/reqslot</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/selmulti</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/srcset</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/tplrt</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile/uterdir</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$controller/ctrlfmt</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$controller/ctrlreg</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$controller/noscp</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$http/baddata</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$http/badjsonp</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$http/badreq</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$injector/cdep</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$injector/itkn</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$injector/modulerr</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$injector/nomod</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$injector/pget</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$injector/strictdi</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$injector/undef</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$injector/unpr</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/badexpr</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/dupvalue</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/interr</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/logicbug</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/nochgmustache</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/noconcat</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/reqarg</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/reqcomma</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/reqendbrace</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/reqendinterp</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/reqopenbrace</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/reqother</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/unknarg</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/unsafe</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/untermstr</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate/wantstring</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interval/badprom</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$location/badpath</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$location/ipthprfx</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$location/isrcharg</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$location/nobase</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$location/nostate</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$parse/esc</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$parse/lexerr</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$parse/lval</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$parse/syntax</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$parse/ueoe</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$q/norslvr</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$q/qcycle</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$resource/badargs</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$resource/badcfg</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$resource/badmember</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$resource/badname</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$rootScope/infdig</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$rootScope/inprog</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$route/norout</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$sanitize/elclob</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$sanitize/noinert</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$sanitize/uinput</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$sce/icontext</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$sce/iequirks</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$sce/imatcher</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$sce/insecurl</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$sce/itype</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$sce/iwcard</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$sce/unsafe</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$templateRequest/tpload</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$timeout/badprom</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/filter/notarray</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/jqLite/nosel</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/jqLite/offargs</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/jqLite/onargs</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/linky/notstring</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ng/aobj</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ng/areq</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ng/badname</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ng/btstrpd</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ng/cpi</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ng/cpta</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ng/cpws</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ng/test</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngModel/constexpr</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngModel/datefmt</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngModel/nonassign</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngModel/nopromise</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngModel/numfmt</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngOptions/iexp</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngPattern/noregexp</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngRef/noctrl</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngRef/nonassign</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngRepeat/badident</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngRepeat/dupes</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngRepeat/iexp</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngRepeat/iidexp</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngTransclude/orphan</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/orderBy/notarray</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/$location</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/accessibility</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/animations</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/bootstrap</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/compiler</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/component-router</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/component</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/concepts</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/controller</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/css-styling</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/databinding</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/decorators</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/di</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/directive</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/e2e-testing</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/expression</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/external-resources</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/filter</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/forms</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/i18n</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/ie</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/interpolation</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/introduction</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/migration</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/module</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/production</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/providers</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/scope</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/security</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/services</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/templates</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/guide/unit-testing</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/misc/contribute</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/misc/downloading</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/misc/faq</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/misc</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/misc/started</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/misc/version-support-status</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_00</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_01</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_02</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_03</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_04</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_05</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_06</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_07</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_08</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_09</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_10</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_11</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_12</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_13</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/step_14</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/tutorial/the_end</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.forEach</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.extend</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.merge</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.noop</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.identity</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.isUndefined</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.isDefined</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.isObject</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.isString</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.isNumber</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.isDate</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.isArray</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.isFunction</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.isElement</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.copy</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.equals</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngJq</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.bind</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.toJson</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.fromJson</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngApp</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.bootstrap</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.reloadWithDebugInfo</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/object/angular.version</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.injector</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/auto</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/auto/service/$injector</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/auto/service/$provide</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.element</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/type/angular.Module</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.module</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function/angular.errorHandlingConfig</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$anchorScrollProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$anchorScroll</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$animateProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$animate</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$animateCss</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$cacheFactory</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/type/$cacheFactory.Cache</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$templateCache</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$compile</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngProp</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngOn</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$compileProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/type/$compile.directive.Attributes</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$controllerProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$controller</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/a</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngHref</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngSrc</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngSrcset</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngDisabled</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngChecked</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngReadonly</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngSelected</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngOpen</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/type/form.FormController</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngForm</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/form</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input/input[text]</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input/input[date]</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input/input[datetime-local]</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input/input[time]</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input/input[week]</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input/input[month]</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input/input[number]</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input/input[url]</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input/input[email]</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input/input[radio]</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input/input[range]</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input/input[checkbox]</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/textarea</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/input</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngValue</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngBind</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngBindTemplate</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngBindHtml</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngChange</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngClass</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngClassOdd</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngClassEven</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngCloak</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngController</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngCsp</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngClick</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngDblclick</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngMousedown</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngMouseup</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngMouseover</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngMouseenter</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngMouseleave</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngMousemove</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngKeydown</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngKeyup</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngKeypress</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngSubmit</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngFocus</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngBlur</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngCopy</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngCut</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngPaste</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngIf</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngInclude</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngInit</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngList</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/type/ngModel.NgModelController</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngModel</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/type/ModelOptions</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngModelOptions</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngNonBindable</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngOptions</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngPluralize</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngRef</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngRepeat</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngShow</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngHide</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngStyle</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngSwitch</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngTransclude</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/script</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/type/select.SelectController</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/select</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngRequired</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngPattern</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngMaxlength</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive/ngMinlength</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$document</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$exceptionHandler</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$filterProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$filter</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/filter/filter</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/filter/currency</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/filter/number</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/filter/date</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/filter/json</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/filter/lowercase</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/filter/uppercase</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/filter/limitTo</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/filter/orderBy</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$httpParamSerializer</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$httpParamSerializerJQLike</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$httpProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$http</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$xhrFactory</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$httpBackend</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$interpolateProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$interpolate</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$interval</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$jsonpCallbacks</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$locale</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$location</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$locationProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$log</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$logProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$parse</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$parseProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$q</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$qProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$rootElement</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$rootScopeProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$rootScope</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/type/$rootScope.Scope</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$sceDelegate</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$sceDelegateProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$sceProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$sce</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider/$templateRequestProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$templateRequest</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$timeout</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service/$window</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngAnimate/directive/ngAnimateChildren</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngAnimate/service/$animateCss</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngAnimate</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngAnimate/service/$animate</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngAnimate/directive/ngAnimateSwap</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngAria</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngAria/provider/$ariaProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngAria/service/$aria</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter/type/Router</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter/type/ChildRouter</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter/type/RootRouter</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter/type/ComponentInstruction</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter/type/RouteDefinition</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter/type/RouteParams</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter/directive/ngOutlet</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter/service/$rootRouter</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter/service/$routerRootComponent</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngCookies</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngCookies/provider/$cookiesProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngCookies/service/$cookies</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMessageFormat</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMessages</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMessages/directive/ngMessages</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMessages/directive/ngMessagesInclude</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMessages/directive/ngMessage</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMessages/directive/ngMessageExp</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMessages/directive/ngMessageDefault</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/object/angular.mock</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/service/$flushPendingTasks</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/service/$verifyNoPendingTasks</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/provider/$exceptionHandlerProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/service/$exceptionHandler</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/service/$log</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/service/$interval</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/type/angular.mock.TzDate</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/service/$animate</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/function/angular.mock.dump</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/service/$httpBackend</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/service/$timeout</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/service/$controller</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/service/$componentController</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMockE2E</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMockE2E/service/$httpBackend</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/type/$rootScope.Scope</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/function/angular.mock.module</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/function/angular.mock.module.sharedInjector</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/function/angular.mock.inject</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/function/browserTrigger</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngParseExt</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngResource</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngResource/provider/$resourceProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngResource/service/$resource</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngRoute/directive/ngView</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngRoute</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngRoute/provider/$routeProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngRoute/service/$route</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngRoute/service/$routeParams</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngSanitize/filter/linky</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngSanitize</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngSanitize/service/$sanitize</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngSanitize/provider/$sanitizeProvider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngTouch/directive/ngSwipeLeft</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngTouch/directive/ngSwipeRight</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngTouch/service/$swipe</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngTouch</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$animate</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$cacheFactory</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$compile</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$controller</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$http</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$injector</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interpolate</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$interval</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$location</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$parse</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$q</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$resource</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$rootScope</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$route</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$sanitize</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$sce</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$templateRequest</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/$timeout</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/filter</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/jqLite</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/linky</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ng</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngModel</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngOptions</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngPattern</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngRef</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngRepeat</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/ngTransclude</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/error/orderBy</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/function</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/directive</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/object</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/type</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/provider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/service</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/input</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ng/filter</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/auto/service</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngAnimate/directive</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngAnimate/service</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngAria/provider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngAria/service</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter/type</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter/directive</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngComponentRouter/service</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngCookies/provider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngCookies/service</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMessages/directive</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/object</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/service</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/provider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/type</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMock/function</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngMockE2E/service</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngResource/provider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngResource/service</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngRoute/directive</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngRoute/provider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngRoute/service</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngSanitize/filter</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngSanitize/service</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngSanitize/provider</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngTouch/directive</loc>
  </url>
  <url>
    <loc>https://docs.angularjs.org/api/ngTouch/service</loc>
  </url>
</urlset>