﻿@{
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}
<div class="row search-page" id="search-page-1">
    <div class="col-xs-12">
        <div class="row">
            <div class="col-xs-12 col-sm-3">
                <div id="search-form-container" class="search-area well well-sm">
           
                    @using (Ajax.BeginForm(
                      ajaxOptions: new AjaxOptions
                      {
                          LoadingElementId = "loader",
                          OnFailure = "onFailure",
                          OnSuccess ="OnSuccess", 
                          UpdateTargetId = "list",
                          InsertionMode = InsertionMode.Replace
                      }
                        ))
                    {

                        <div class="search-filter-header bg-primary">
                            <button type="submit" name="submitButton" id="onlybagat" value="data" width="200" class="btn btn-default btn-round btn-white loading" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار">
                                <i class="ace-icon fa fa-search blue "></i>
                                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; عرض &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            </button>
                            
                            @if (ViewBag.CanPrint == true)
                            {
                                <button type="button" id="print_grid" onclick="printGrid() " class="btn btn-default btn-round btn-white">
                                    <i class="ace-icon fa fa-print blue "></i>
                                </button>
                            
                                <a  class="btn btn-white btn-info btn-bold btn-round" id="export_to_excel">
                                    <i class="ace-icon fa fa-file-excel-o bigger-110"></i>
                                </a>
                            }
                          
                        </div>
                        <div class="space-6"></div>
                        @Html.AntiForgeryToken()
                        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                        <section>
                            <div>
                                @RenderBody()
                            </div>
                        </section>


                  
                    }
                  
                </div>
            </div>
            <div class="col-xs-12 col-sm-9">
                <span class="alert"></span>
               
                <div id="list">
                  
                    @{
                        @Html.Partial("_ReportList")
                    }
                </div>
             
            </div>
        </div>
    </div>
</div>
@section Scripts {

    <script>

        $(function() {
          
            try {
                var report = Patterns.Art.Report;
                report.start();

            } catch (e) {
                alert(e);
            }
            $('.loading').on('click',
                function() {
                    var $this = $(this);
                    $this.button('loading');
                });

            $("#loader").hide();

            $("#excel").click(function() {
                jsExportExcel();
            });

            $("#export_to_excel").click(function() {
                try {
                    var controller = $('#Controller').val();
                    i('controller:' + controller);
                    var url = controller + '/ExportExcel';
                    downloan(url, $('#search-form-container').children('form').serialize());
                } catch (e) {
                    i(e);
                    jsExportExcel();
                }
            });

        });

        function jsExportExcel() {
            $("#simple-table").table2excel({
                exclude_inputs: true,
                name: title,
                filename: title + new Date().toISOString().replace(/[\-\:\.]/g, ""),
            });
        }

        function OnSuccess(response) {
            resetButton();
        }

        function onFailure(xhr, textStatus, errorThrown) {
            resetButton();
            parseAndShowError(xhr, textStatus, errorThrown);
        }
    </script>
}