﻿@using System.Data
@using AppTech.Common.Extensions
<div id="list">
    <div class="align-center table-header">
    </div>
    <span class="alert"></span>
    <table id="simple-table" class="table  table-bordered table-hover table-responsive"
           data-toggle="table">

        <thead class="thin-border-bottom">
        <tr>
            <th>
                اسم العميل
            </th>

            <th>
                العملة
            </th>

            <th>
                الرصيد
            </th>

            <th class="hidden-480">
                الحالة
            </th>
        </tr>
        </thead>

        <tbody>
        @{

            foreach (DataRow row in Model.Result.Rows)
            {
                <tr>
                    <td>@row["AccountName"].ToString()</td>
                    <td>@row["CurrencyName"].ToString()</td>


                    @if (row["Balance"].ToDecimal() > 0)
                    {
                        <td>

                            <b class="green">@row["Balance"].ToString()</b>
                        </td>

                        <td class="hidden-480">
                            <span class="label label-success arrowed-in arrowed-in-right">له</span>
                        </td>
                    }

                    else if (row["Balance"].ToDecimal() < 0)
                    {
                        <td>

                            <b class="red">@row["Balance"].ToString()</b>
                        </td>
                        <td class="hidden-480">
                            <span class="label label-danger arrowed">علية</span>
                        </td>
                    }
                    else
                    {
                        <td>

                            <b class="blue">@row["Balance"].ToString()</b>
                        </td>
                        <td class="hidden-480">
                            <span class="label label-info arrowed">----</span>
                        </td>
                    }

                </tr>
            }
        }

        </tbody>
    </table>
</div>