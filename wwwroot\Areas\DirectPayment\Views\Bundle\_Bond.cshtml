﻿@model IEnumerable<AppTech.MSMS.Domain.Models.Bundle>
    <div id="clist">
        <table class="table table-hover">
            <thead>
                <tr>

                    <th>
                        رقم المزود
                    </th>

                    <th>
                      كود الربط
                    </th>
                    <th>سعر التكلفة</th>
                    <th></th>
                </tr>
            </thead>
            @foreach (var item in Model)
            {
                <tbody>
                    <tr>

                        <td>
                            @Html.DisplayFor(modelItem => item.ProviderNumber)
                        </td>


                        <td>
                            @Html.DisplayFor(modelItem => item.Code)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.CostPrice)
                        </td>
                        <td>
                            <Button class="btn btn-link" onclick="unbind('@item.ID')">
                                <i class="ace-icon fa fa-trash-o bigger-110"></i>
                                حذف
                            </Button>

                        </td>
                    </tr>
                </tbody>
            }

        </table>

    </div>
<script>
    //function openEditModal(id) {
    //    i('open modal id' + id);
    //    openViewAsModal('DirectPayment/Bundle/AddOrEditBond?id=' + id);
    //}

    function unbind(id) {
        i('remove bundle id' + id);
        var fId =@ViewBag.FactionID;
         
        i('remove bundle fId' + fId);
        var url = 'DirectPayment/Bundle/Remove?id=' + id+ '&factionId=' + fId;

        AjaxCall(url)
            .done(function (response) {
              //  ar(response.Message);
                $("#clist").replaceWith(response);
            }).fail(function (xhr, textStatus, errorThrown) {
                    parseAndShowError(xhr, textStatus, errorThrown);
                });
    }
</script>