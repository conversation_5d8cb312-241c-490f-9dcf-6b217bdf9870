﻿# Additional Deobfuscation Tools
## ط£ط¯ظˆط§طھ ط¥ط¶ط§ظپظٹط© ظ„ظپظƒ ط§ظ„طھط´ظپظٹط±

### ط§ظ„ط£ط¯ظˆط§طھ ط§ظ„ظ…ط­ظ…ظ„ط©:
1. **Reflexil** - ظ…ط­ط±ط± ط§ظ„طھط¬ظ…ظٹط¹ط§طھ ظ„ظ€ .NET
2. **NET-Deobfuscator** - ظ…ط¬ظ…ظˆط¹ط© ط£ط¯ظˆط§طھ ظپظƒ ط§ظ„طھط´ظˆظٹط´

### ط§ظ„ط³ظƒط±ظٹط¨طھط§طھ ط§ظ„ظ…ط³ط§ط¹ط¯ط©:
1. **AnalyzeSignature.ps1** - طھط­ظ„ظٹظ„ ط§ظ„طھظˆظ‚ظٹط¹ط§طھ ط§ظ„ط±ظ‚ظ…ظٹط©
2. **ExtractResources.ps1** - ط§ط³طھط®ط±ط§ط¬ ط§ظ„ظ…ظˆط§ط±ط¯ ظ…ظ† ط§ظ„طھط¬ظ…ظٹط¹ط§طھ
3. **AnalyzeDependencies.ps1** - طھط­ظ„ظٹظ„ ط§ط¹طھظ…ط§ط¯ظٹط§طھ ط§ظ„طھط¬ظ…ظٹط¹ط§طھ

### ط§ظ„ط§ط³طھط®ط¯ط§ظ…:
`powershell
# طھط­ظ„ظٹظ„ ط§ظ„طھظˆظ‚ظٹط¹ ط§ظ„ط±ظ‚ظ…ظٹ
.\AnalyzeSignature.ps1 -FilePath "path\to\assembly.dll"

# ط§ط³طھط®ط±ط§ط¬ ط§ظ„ظ…ظˆط§ط±ط¯
.\ExtractResources.ps1 -AssemblyPath "path\to\assembly.dll" -OutputDir ".\ExtractedResources"

# طھط­ظ„ظٹظ„ ط§ظ„ط§ط¹طھظ…ط§ط¯ظٹط§طھ
.\AnalyzeDependencies.ps1 -AssemblyPath "path\to\assembly.dll"
`

### ظ…ظ„ط§ط­ط¸ط§طھ:
- طھط£ظƒط¯ ظ…ظ† طھط´ط؛ظٹظ„ PowerShell ظƒظ…ط¯ظٹط±
- ط¨ط¹ط¶ ط§ظ„ط£ط¯ظˆط§طھ ظ‚ط¯ طھط­طھط§ط¬ ط¥ظ„ظ‰ ط§ط³طھط®ط±ط§ط¬ ظٹط¯ظˆظٹ
- ط§ط³طھط®ط¯ظ… ظ‡ط°ظ‡ ط§ظ„ط£ط¯ظˆط§طھ ط¨ظ…ط³ط¤ظˆظ„ظٹط© ظˆظپظ‚ط§ظ‹ ظ„ظ„ظ‚ظˆط§ظ†ظٹظ† ط§ظ„ظ…ط­ظ„ظٹط©
