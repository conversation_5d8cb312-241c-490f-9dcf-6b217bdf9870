﻿@*@model IEnumerable<AppTech.MSMS.Domain.Models.JournalEntry>*@
@model AppTech.MSMS.Domain.Models.Journal

@Styles.Render("~/Content/print")
<p>

</p>
<h2 class="center">   تفاصيل القيد المزدوج</h2>
<div class="table-bordered" style="padding: 20px 20px;">
    <div class=" " style="margin-bottom: 25px;">
        <div class="table-responsive">
            <table class="table no-border table-condensed table-sm">
                <tr>
                    <td class="col-sm-12">
                        <label class="bolder">رقم السند: </label>
                        @Html.DisplayFor(m => m.Number)
                    </td>
                    <td class="col-sm-12">
                        <label class="bolder">العملة: </label>
                        @if (Model.CurrencyID == 1)
                        {
                            <label class="bolder">يمني</label>
                        }
                        else if (Model.CurrencyID == 2)
                        {
                            <label class="bolder">دولار</label>
                        }
                        else if (Model.CurrencyID == 3)
                        {
                            <label class="bolder">سعودي</label>
                        }
                    </td>
                    <td class="col-sm-12">
                        <label class="bolder">التاريخ: </label>
                        @Html.DisplayFor(m => m.Date)
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-sm">
            <tr>
                <th>
                    @Html.DisplayName("اسم الحساب")
                </th>
                <th>
                    @Html.DisplayName("المبلغ")
                </th>
                <th>
                    @Html.DisplayName("العملة")
                </th>
                <th>
                    @Html.DisplayName("نوع القيد")
                </th>
                <th>
                    @Html.DisplayName("البيان")
                </th>
            </tr>

            @foreach (var item in Model.JournalEntries)
            {
                <tr>

                    <td>
                        @Html.DisplayFor(modelItem => item.Account.Name)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Amount)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Currency.Name)
                    </td>

                    <td>
                        @if (item.CostCenterID == 0)
                        {
                            <span>من حــ/</span>
                        }
                        else
                        {
                            <span>الى حــ/</span>
                        }
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Note)
                    </td>

                </tr>
            }

        </table>
    </div>
</div>


