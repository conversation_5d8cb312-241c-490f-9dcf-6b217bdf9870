<?xml version="1.0"?>
<doc>
    <assembly>
        <name>dnSpy.Debugger.DotNet.Metadata</name>
    </assembly>
    <members>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain">
            <summary>
            A .NET AppDomain
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.YouCantDeriveFromThisClass">
            <summary>
            Dummy abstract method to make sure no-one outside this assembly can create their own <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.Runtime">
            <summary>
            Gets the runtime
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.Id">
            <summary>
            Gets the unique AppDomain id
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateAssembly(System.Func{dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytes},System.Boolean,System.Boolean,System.String,System.String)">
            <summary>
            Creates an assembly and adds it to the AppDomain. The first created assembly must be the corlib (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CorLib"/>)
            </summary>
            <param name="getMetadata">Called to provide the metadata</param>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <param name="assemblyLocation">Location of the assembly or an empty string (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Location"/>)</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateSyntheticAssembly(System.Func{dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytes},System.Boolean,System.Boolean,System.String,System.String,System.String)">
            <summary>
            Creates a synthetic assembly but does not add it to the AppDomain
            </summary>
            <param name="getMetadata">Called to provide the metadata</param>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <param name="assemblyLocation">Location of the assembly or an empty string (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Location"/>)</param>
            <param name="assemblySimpleName">The assembly's simple name or null if it's unknown</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateAssembly(System.Func{dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytes},System.Boolean,System.Boolean,System.String,System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Creates an assembly and optionally adds it to the AppDomain. The first created assembly must be the corlib (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CorLib"/>)
            </summary>
            <param name="getMetadata">Called to provide the metadata</param>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <param name="assemblyLocation">Location of the assembly or an empty string (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Location"/>)</param>
            <param name="assemblySimpleName">The assembly's simple name or null if it's unknown</param>
            <param name="isSynthetic">true if it's a synthetic assembly; it's not loaded in the debugged process</param>
            <param name="addAssembly">true if the assembly should be added to the AppDomain</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateAssembly(System.Func{dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytes},dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyInfo)">
            <summary>
            Creates an assembly and optionally adds it to the AppDomain. The first created assembly must be the corlib (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CorLib"/>)
            </summary>
            <param name="getMetadata">Called to provide the metadata</param>
            <param name="assemblyInfo">Assembly info</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateAssembly(System.String,System.Boolean,System.Boolean,System.Boolean,System.String,System.String)">
            <summary>
            Creates an assembly. The first created assembly must be the corlib (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CorLib"/>)
            </summary>
            <param name="filename">Filename</param>
            <param name="isFileLayout">true if file layout, false if memory layout</param>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <param name="assemblyLocation">Location of the assembly or an empty string (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Location"/>)</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateAssembly(System.IntPtr,System.UInt32,System.Boolean,System.Boolean,System.Boolean,System.String,System.String)">
            <summary>
            Creates an assembly. The first created assembly must be the corlib (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CorLib"/>)
            </summary>
            <param name="address">Address of PE file</param>
            <param name="size">Size of PE file</param>
            <param name="isFileLayout">true if file layout, false if memory layout</param>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <param name="assemblyLocation">Location of the assembly or an empty string (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Location"/>)</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateAssembly(System.Byte[],System.Boolean,System.Boolean,System.Boolean,System.String,System.String)">
            <summary>
            Creates an assembly. The first created assembly must be the corlib (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CorLib"/>)
            </summary>
            <param name="assemblyBytes">Raw PE file bytes</param>
            <param name="isFileLayout">true if file layout, false if memory layout</param>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <param name="assemblyLocation">Location of the assembly or an empty string (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Location"/>)</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateAssembly(System.Object,dnSpy.Debugger.DotNet.Metadata.DmdDynamicModuleHelper,dnSpy.Debugger.DotNet.Metadata.DmdDispatcher,System.Boolean,System.Boolean,System.String,System.String)">
            <summary>
            Creates an assembly. The first created assembly must be the corlib (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CorLib"/>)
            </summary>
            <param name="comMetadata">COM <c>IMetaDataImport</c> instance</param>
            <param name="dynamicModuleHelper">Helper class</param>
            <param name="dispatcher">Dispatcher to use when accessing <paramref name="comMetadata"/></param>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <param name="assemblyLocation">Location of the assembly or an empty string (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Location"/>)</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateModule(dnSpy.Debugger.DotNet.Metadata.DmdAssembly,System.Func{dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytes},System.Boolean,System.Boolean,System.String)">
            <summary>
            Adds a module to an existing assembly
            </summary>
            <param name="assembly">Assembly</param>
            <param name="getMetadata">Called to provide the metadata</param>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateModule(dnSpy.Debugger.DotNet.Metadata.DmdAssembly,System.String,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            Adds a module to an existing assembly
            </summary>
            <param name="assembly">Assembly</param>
            <param name="filename">Filename</param>
            <param name="isFileLayout">true if file layout, false if memory layout</param>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateModule(dnSpy.Debugger.DotNet.Metadata.DmdAssembly,System.IntPtr,System.UInt32,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            Adds a module to an existing assembly
            </summary>
            <param name="assembly">Assembly</param>
            <param name="address">Address of the PE file</param>
            <param name="size">Size of the PE file</param>
            <param name="isFileLayout">true if file layout, false if memory layout</param>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateModule(dnSpy.Debugger.DotNet.Metadata.DmdAssembly,System.Byte[],System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            Adds a module to an existing assembly
            </summary>
            <param name="assembly">Assembly</param>
            <param name="moduleBytes">Raw PE file bytes</param>
            <param name="isFileLayout">true if file layout, false if memory layout</param>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateModule(dnSpy.Debugger.DotNet.Metadata.DmdAssembly,System.Object,dnSpy.Debugger.DotNet.Metadata.DmdDynamicModuleHelper,dnSpy.Debugger.DotNet.Metadata.DmdDispatcher,System.Boolean,System.Boolean,System.String)">
            <summary>
            Adds a module to an existing assembly
            </summary>
            <param name="assembly">Assembly</param>
            <param name="comMetadata">COM <c>IMetaDataImport</c> instance</param>
            <param name="dynamicModuleHelper">Helper class</param>
            <param name="dispatcher">Dispatcher to use when accessing <paramref name="comMetadata"/></param>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.Add(dnSpy.Debugger.DotNet.Metadata.DmdAssembly)">
            <summary>
            Adds an assembly
            </summary>
            <param name="assembly">Assembly to add</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.Remove(dnSpy.Debugger.DotNet.Metadata.DmdAssembly)">
            <summary>
            Removes an assembly
            </summary>
            <param name="assembly">Assembly to remove</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.AddTemporaryAssembly(dnSpy.Debugger.DotNet.Metadata.DmdAssembly)">
            <summary>
            Adds an assembly. It gets removed when the return value's <see cref="M:System.IDisposable.Dispose"/> method gets called
            </summary>
            <param name="assembly">Assembly to add</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.TemporaryAssemblyDisposable">
            <summary>
            Adds and removes an assembly
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.TemporaryAssemblyDisposable.Dispose">
            <summary>
            Dispose()
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.GetAssemblies">
            <summary>
            Gets all assemblies
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.GetAssemblies(System.Boolean)">
            <summary>
            Gets all assemblies
            </summary>
            <param name="includeSyntheticAssemblies">true to include synthetic assemblies</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.GetAssembly(System.String)">
            <summary>
            Gets an assembly or returns null if there's no such assembly
            </summary>
            <param name="simpleName">Simple name of the assembly, eg. "System"</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.GetAssembly(dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName)">
            <summary>
            Gets an assembly or returns null if there's no such assembly
            </summary>
            <param name="name">Assembly name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.Load(System.Object,System.String)">
            <summary>
            Loads an assembly
            </summary>
            <param name="context">Evaluation context</param>
            <param name="assemblyName">Full assembly name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.Load(System.Object,dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName)">
            <summary>
            Loads an assembly
            </summary>
            <param name="context">Evaluation context</param>
            <param name="name">Assembly name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.LoadFrom(System.Object,System.String)">
            <summary>
            Loads an assembly. Will fail on .NET Core 1.x (but not on .NET Core 2.x or later)
            </summary>
            <param name="context">Evaluation context</param>
            <param name="assemblyFile">Assembly name or path to assembly</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.LoadFile(System.Object,System.String)">
            <summary>
            Loads an assembly. Will fail on .NET Core 1.x (but not on .NET Core 2.x or later)
            </summary>
            <param name="context">Evaluation context</param>
            <param name="path">Path to assembly</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CorLib">
            <summary>
            Gets the core library (eg. mscorlib if it's .NET Framework)
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.HasWellKnownType(dnSpy.Debugger.DotNet.Metadata.DmdWellKnownType)">
            <summary>
            Checks if a well known type exists in one of the loaded assemblies
            </summary>
            <param name="wellKnownType">Well known type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.GetWellKnownType(dnSpy.Debugger.DotNet.Metadata.DmdWellKnownType)">
            <summary>
            Gets a well known type
            </summary>
            <param name="wellKnownType">Well known type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.GetWellKnownType(dnSpy.Debugger.DotNet.Metadata.DmdWellKnownType,System.Boolean)">
            <summary>
            Gets a well known type
            </summary>
            <param name="wellKnownType">Well known type</param>
            <param name="isOptional">Used if the type couldn't be found. If true, null is returned, and if false, an exception is thrown</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.Intern(dnSpy.Debugger.DotNet.Metadata.DmdType,dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions)">
            <summary>
            Returns a cached type if present else the input type
            </summary>
            <param name="type">Type</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.MakePointerType(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier},dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions)">
            <summary>
            Makes a pointer type
            </summary>
            <param name="elementType">Element type</param>
            <param name="customModifiers">Custom modifiers or null</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.MakeByRefType(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier},dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions)">
            <summary>
            Makes a by-ref type
            </summary>
            <param name="elementType">Element type</param>
            <param name="customModifiers">Custom modifiers or null</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.MakeArrayType(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier},dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions)">
            <summary>
            Makes a SZ array type
            </summary>
            <param name="elementType">Element type</param>
            <param name="customModifiers">Custom modifiers or null</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.MakeArrayType(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Int32,System.Collections.Generic.IList{System.Int32},System.Collections.Generic.IList{System.Int32},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier},dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions)">
            <summary>
            Makes a multi-dimensional array type
            </summary>
            <param name="elementType">Element type</param>
            <param name="rank">Number of dimensions</param>
            <param name="sizes">Sizes</param>
            <param name="lowerBounds">Lower bounds</param>
            <param name="customModifiers">Custom modifiers or null</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.MakeGenericType(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier},dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions)">
            <summary>
            Makes a generic type
            </summary>
            <param name="genericTypeDefinition">Generic type definition</param>
            <param name="typeArguments">Generic arguments</param>
            <param name="customModifiers">Custom modifiers or null</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.MakeGenericMethod(dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions)">
            <summary>
            Makes a generic method
            </summary>
            <param name="genericMethodDefinition">Generic method definition</param>
            <param name="typeArguments">Generic arguments</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.MakeFunctionPointerType(dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier},dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions)">
            <summary>
            Makes a function pointer type
            </summary>
            <param name="methodSignature">Method signature</param>
            <param name="customModifiers">Custom modifiers or null</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.MakeFunctionPointerType(dnSpy.Debugger.DotNet.Metadata.DmdSignatureCallingConvention,System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdType,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier},dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions)">
            <summary>
            Makes a function pointer type
            </summary>
            <param name="flags">Flags</param>
            <param name="genericParameterCount">Number of generic parameters</param>
            <param name="returnType">Return type</param>
            <param name="parameterTypes">Parameter types</param>
            <param name="varArgsParameterTypes">VarArgs parameter types</param>
            <param name="customModifiers">Custom modifiers or null</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.MakeGenericTypeParameter(System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Makes a generic type parameter
            </summary>
            <param name="position">Position</param>
            <param name="declaringType">Declaring type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.MakeGenericTypeParameter(System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdType,System.String,dnSpy.Debugger.DotNet.Metadata.DmdGenericParameterAttributes,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier},dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions)">
            <summary>
            Makes a generic type parameter
            </summary>
            <param name="position">Position</param>
            <param name="declaringType">Declaring type</param>
            <param name="name">Name</param>
            <param name="attributes">Attributes</param>
            <param name="customModifiers">Custom modifiers or null</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.MakeGenericMethodParameter(System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdMethodBase)">
            <summary>
            Makes a generic method parameter
            </summary>
            <param name="position">Position</param>
            <param name="declaringMethod">Declaring method</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.MakeGenericMethodParameter(System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdMethodBase,System.String,dnSpy.Debugger.DotNet.Metadata.DmdGenericParameterAttributes,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier},dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions)">
            <summary>
            Makes a generic method parameter
            </summary>
            <param name="position">Position</param>
            <param name="declaringMethod">Declaring method</param>
            <param name="name">Name</param>
            <param name="attributes">Attributes</param>
            <param name="customModifiers">Custom modifiers or null</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.GetType(System.Type)">
            <summary>
            Gets a type
            </summary>
            <param name="type">Type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.GetTypeThrow(System.Type)">
            <summary>
            Gets a type and throws if it couldn't be found
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.GetType(System.Type,dnSpy.Debugger.DotNet.Metadata.DmdGetTypeOptions)">
            <summary>
            Gets a type
            </summary>
            <param name="type">Type</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.GetType(System.String,dnSpy.Debugger.DotNet.Metadata.DmdGetTypeOptions)">
            <summary>
            Gets a type
            </summary>
            <param name="typeName">Full name of the type (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdType.FullName"/>) or the assembly qualified name (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdType.AssemblyQualifiedName"/>).
            Version, public key token and culture are optional.</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.GetType(System.String)">
            <summary>
            Gets a type
            </summary>
            <param name="typeName">Full name of the type (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdType.FullName"/>) or the assembly qualified name (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdType.AssemblyQualifiedName"/>).
            Version, public key token and culture are optional.</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.GetTypeThrow(System.String)">
            <summary>
            Gets a type and throws if it couldn't be found
            </summary>
            <param name="typeName">Full name of the type (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdType.FullName"/>) or the assembly qualified name (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdType.AssemblyQualifiedName"/>).
            Version, public key token and culture are optional.</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.CreateInstance(System.Object,dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo,System.Object[])">
            <summary>
            Creates a new instance of a type
            </summary>
            <param name="context">Evaluation context</param>
            <param name="ctor">Constructor</param>
            <param name="parameters">Parameters passed to the method</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.Invoke(System.Object,dnSpy.Debugger.DotNet.Metadata.DmdMethodBase,System.Object,System.Object[])">
            <summary>
            Executes a method
            </summary>
            <param name="context">Evaluation context</param>
            <param name="method">Method to call</param>
            <param name="obj">Instance object or null if it's a static method</param>
            <param name="parameters">Parameters passed to the method</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.LoadField(System.Object,dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo,System.Object)">
            <summary>
            Loads a field
            </summary>
            <param name="context">Evaluation context</param>
            <param name="field">Field</param>
            <param name="obj">Instance object or null if it's a static field</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAppDomain.StoreField(System.Object,dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo,System.Object,System.Object)">
            <summary>
            Stores a value in a field
            </summary>
            <param name="context">Evaluation context</param>
            <param name="field">Field</param>
            <param name="obj">Instance object or null if it's a static field</param>
            <param name="value">Value to store in the field</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions">
            <summary>
            Options used when creating types
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions.None">
            <summary>
            No bit is set
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdMakeTypeOptions.NoResolve">
            <summary>
            Don't try to resolve a reference
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdGetTypeOptions">
            <summary>
            Options used when finding a type
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdGetTypeOptions.None">
            <summary>
            No bit is set
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdGetTypeOptions.ThrowOnError">
            <summary>
            Throw if the type couldn't be found
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdGetTypeOptions.IgnoreCase">
            <summary>
            Ignore case
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyOptions">
            <summary>
            Create-assembly-options
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyOptions.None">
            <summary>
            No bit is set
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyOptions.InMemory">
            <summary>
            Set if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyOptions.Dynamic">
            <summary>
            Set if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyOptions.Synthetic">
            <summary>
            Synthetic assembly, eg. created by the expression compiler
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyOptions.DontAddAssembly">
            <summary>
            Don't add the assembly to the AppDomain
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyOptions.IsEXE">
            <summary>
            It's an exe file. If it's not set, it's either a DLL or it's unknown
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyOptions.IsDLL">
            <summary>
            It's a dll file. If it's not set, it's either an EXE or it's unknown
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyInfo">
            <summary>
            Info needed when creating an assembly
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyInfo.Options">
            <summary>
            Gets the options
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyInfo.FullyQualifiedName">
            <summary>
            The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyInfo.AssemblyLocation">
            <summary>
            Location of the assembly or an empty string (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Location"/>)
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyInfo.AssemblySimpleName">
            <summary>
            Gets the assembly's simple name or null if it's unknown
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyInfo.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyOptions,System.String,System.String,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="options">Options</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <param name="assemblyLocation">Location of the assembly or an empty string (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Location"/>)</param>
            <param name="assemblySimpleName">The assembly's simple name or null if it's unknown</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCreateAssemblyInfo.#ctor(System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.String,System.String,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="isInMemory">true if the module is in memory (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory"/>)</param>
            <param name="isDynamic">true if it's a dynamic module (types can be added at runtime) (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic"/>)</param>
            <param name="isSynthetic">true if it's a synthetic assembly, eg. created by the expression compiler</param>
            <param name="addAssembly">true if the assembly should be added to the AppDomain</param>
            <param name="fullyQualifiedName">The fully qualified name of the module (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName"/>). See <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)"/></param>
            <param name="assemblyLocation">Location of the assembly or an empty string (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Location"/>)</param>
            <param name="assemblySimpleName">The assembly's simple name or null if it's unknown</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdAssembly">
            <summary>
            A .NET assembly
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.YouCantDeriveFromThisClass">
            <summary>
            Dummy abstract method to make sure no-one outside this assembly can create their own <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdAssembly"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.AppDomain">
            <summary>
            Gets the AppDomain
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.IsCorLib">
            <summary>
            true if this is the corlib assembly
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.CreateQualifiedName(System.String,System.String)">
            <summary>
            Creates a qualified type name
            </summary>
            <param name="assemblyName">Full assembly name of the type</param>
            <param name="typeName">Full type name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetName">
            <summary>
            Gets the assembly name
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.FullName">
            <summary>
            Gets the full name of the assembly
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Location">
            <summary>
            Gets the assembly location or an empty string
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.ImageRuntimeVersion">
            <summary>
            Gets the runtime version found in the metadata
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.IsDynamic">
            <summary>
            true if it's a dynamic assembly (types can be added at runtime)
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.IsInMemory">
            <summary>
            true if it's an in-memory assembly (eg. loaded from a <see cref="T:System.Byte"/> array)
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.IsSynthetic">
            <summary>
            true if it's a synthetic assembly; it's not loaded in the debugged process
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.IsLoaded">
            <summary>
            true if the assembly has been added to its AppDomain
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.EntryPoint">
            <summary>
            Gets the entry point or null
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.ManifestModule">
            <summary>
            Gets the first module of this assembly
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetType(System.Type)">
            <summary>
            Gets a type
            </summary>
            <param name="type">Type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetTypeThrow(System.Type)">
            <summary>
            Gets a type and throws if it couldn't be found
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetType(System.Type,dnSpy.Debugger.DotNet.Metadata.DmdGetTypeOptions)">
            <summary>
            Gets a type
            </summary>
            <param name="type">Type</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetType(System.String)">
            <summary>
            Gets a type in this assembly or null if it doesn't exist
            </summary>
            <param name="name">Name of type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetTypeThrow(System.String)">
            <summary>
            Gets a type and throws if it couldn't be found
            </summary>
            <param name="name">Name of type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetType(System.String,System.Boolean)">
            <summary>
            Gets a type in this assembly
            </summary>
            <param name="name">Name of type</param>
            <param name="throwOnError">true to throw if the type doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetType(System.String,System.Boolean,System.Boolean)">
            <summary>
            Gets a type in this assembly
            </summary>
            <param name="name">Name of type</param>
            <param name="throwOnError">true to throw if the type doesn't exist</param>
            <param name="ignoreCase">true if case insensitive comparisons</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetType(System.String,dnSpy.Debugger.DotNet.Metadata.DmdGetTypeOptions)">
            <summary>
            Gets a type
            </summary>
            <param name="typeName">Full name of the type (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdType.FullName"/>)</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.ExportedTypes">
            <summary>
            Gets all public types in this assembly
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetExportedTypes">
            <summary>
            Gets all public types in this assembly
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetForwardedTypes">
            <summary>
            Gets all forwarded types (types that now exist in another assembly)
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetTypes">
            <summary>
            Gets all types in this assembly
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.SecurityAttributes">
            <summary>
            Gets the security attributes
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetSecurityAttributesData">
            <summary>
            Gets the security attributes
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.CustomAttributes">
            <summary>
            Gets the custom attributes
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetCustomAttributesData">
            <summary>
            Gets the custom attributes
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.IsDefined(System.String,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.IsDefined(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.IsDefined(System.Type,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.FindCustomAttribute(System.String,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.FindCustomAttribute(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.FindCustomAttribute(System.Type,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.CreateInstance(System.Object,System.String)">
            <summary>
            Creates an instance of a type
            </summary>
            <param name="context">Evaluation context</param>
            <param name="typeName">Fully qualified name of type to create</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.CreateInstance(System.Object,System.String,System.Boolean)">
            <summary>
            Creates an instance of a type
            </summary>
            <param name="context">Evaluation context</param>
            <param name="typeName">Fully qualified name of type to create</param>
            <param name="ignoreCase">true to ignore case</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.CreateInstance(System.Object,System.String,System.Boolean,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,System.Object[])">
            <summary>
            Creates an instance of a type
            </summary>
            <param name="context">Evaluation context</param>
            <param name="typeName">Fully qualified name of type to create</param>
            <param name="ignoreCase">true to ignore case</param>
            <param name="bindingAttr">Binding attributes</param>
            <param name="args">Constructor arguments or null</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.CreateInstance(System.Object,System.String,System.Boolean,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,System.Object[],System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Creates an instance of a type
            </summary>
            <param name="context">Evaluation context</param>
            <param name="typeName">Fully qualified name of type to create</param>
            <param name="ignoreCase">true to ignore case</param>
            <param name="bindingAttr">Binding attributes</param>
            <param name="args">Constructor arguments or null</param>
            <param name="argTypes">Constructor parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.CreateInstance(System.Object,System.String,System.Boolean,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,System.Object[],System.Collections.Generic.IList{System.Type})">
            <summary>
            Creates an instance of a type
            </summary>
            <param name="context">Evaluation context</param>
            <param name="typeName">Fully qualified name of type to create</param>
            <param name="ignoreCase">true to ignore case</param>
            <param name="bindingAttr">Binding attributes</param>
            <param name="args">Constructor arguments or null</param>
            <param name="argTypes">Constructor parameter types or null</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Modules">
            <summary>
            Gets all loaded modules
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetLoadedModules">
            <summary>
            Gets all loaded modules
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetModules">
            <summary>
            Gets all modules
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetModule(System.String)">
            <summary>
            Gets a module
            </summary>
            <param name="name">Name of module</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.GetReferencedAssemblies">
            <summary>
            Gets all referenced assemblies
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.Remove(dnSpy.Debugger.DotNet.Metadata.DmdModule)">
            <summary>
            Removes a module from the assembly
            </summary>
            <param name="module">Module to remove</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssembly.ToString">
            <summary>
            Gets the full name
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyContentType">
            <summary>
            Assembly content type
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyContentType.Default">
            <summary>
            Default content type
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyContentType.WindowsRuntime">
            <summary>
            Windows runtime content type
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyHashAlgorithm">
            <summary>
            Assembly hash algorithm
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName">
            <summary>
            Assembly name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.Name">
            <summary>
            Gets/sets the simple name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.Version">
            <summary>
            Gets/sets the version
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.CultureName">
            <summary>
            Gets/sets the culture name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.RawFlags">
            <summary>
            Gets/sets the flags
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.Flags">
            <summary>
            Gets/sets the flags. The content type and processor architecture bits are ignored, use <see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.RawFlags"/> instead
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.ProcessorArchitecture">
            <summary>
            Gets/sets the processor architecture
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.ContentType">
            <summary>
            Gets/sets the content type
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.GetPublicKey">
            <summary>
            Gets the public key
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.SetPublicKey(System.Byte[])">
            <summary>
            Sets the public key
            </summary>
            <param name="publicKey">Public key or null</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.GetPublicKeyToken">
            <summary>
            Gets the public key token
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.SetPublicKeyToken(System.Byte[])">
            <summary>
            Sets the public key token
            </summary>
            <param name="publicKeyToken">Public key token</param>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.HashAlgorithm">
            <summary>
            Gets/sets the hash algorithm
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.FullName">
            <summary>
            Gets the full assembly name
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.ToString">
            <summary>
            Gets the full assembly name
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="assemblyName">Assembly name</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.#ctor(dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName)">
            <summary>
            Constructor
            </summary>
            <param name="name">Assembly name</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.AsReadOnly">
            <summary>
            Creates a read only assembly name
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyName.Clone">
            <summary>
            Clones this instance
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdAssemblyNameFlags">
            <summary>
            Assembly name flags
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags">
            <summary>
            Member binding flags
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdCallingConventions">
            <summary>
            Calling convention flags
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo">
            <summary>
            A .NET constructor
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.MemberType">
            <summary>
            Gets the member type
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.ResolveMember(System.Boolean)">
            <summary>
            Resolves a constructor reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.ResolveMethodBase(System.Boolean)">
            <summary>
            Resolves a constructor reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.Resolve">
            <summary>
            Resolves a constructor reference and throws if it doesn't exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.ResolveNoThrow">
            <summary>
            Resolves a constructor reference and returns null if it doesn't exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.Resolve(System.Boolean)">
            <summary>
            Resolves a constructor reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.ContainsGenericParameters">
            <summary>
            true if it contains generic parameters
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.Invoke(System.Object,System.Object[])">
            <summary>
            Calls the method
            </summary>
            <param name="context">Evaluation context</param>
            <param name="parameters">Parameters</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.Invoke(System.Object,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,System.Object[])">
            <summary>
            Calls the method
            </summary>
            <param name="context">Evaluation context</param>
            <param name="invokeAttr">Binding flags</param>
            <param name="parameters">Parameters</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.Equals(dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.ConstructorName">
            <summary>
            Gets the name of instance constructors
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo.TypeConstructorName">
            <summary>
            Gets the name of type initializers
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeData">
            <summary>
            Custom attribute data
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeData.AttributeType">
            <summary>
            Gets the custom attribute type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeData.Constructor">
            <summary>
            Gets the custom attribute constructor
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeData.ConstructorArguments">
            <summary>
            Gets the constructor arguments
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeData.NamedArguments">
            <summary>
            Gets all named arguments (properties and fields)
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeData.IsPseudoCustomAttribute">
            <summary>
            true if this custom attribute was not part of the #Blob but created from some other info
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeData.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeTypedArgument},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeNamedArgument},System.Boolean)">
            <summary>
            Constructor
            </summary>
            <param name="constructor">Custom attribute constructor</param>
            <param name="constructorArguments">Constructor arguments or null</param>
            <param name="namedArguments">Custom attribute named arguments (fields and properties) or null</param>
            <param name="isPseudoCustomAttribute">true if this custom attribute was not part of the #Blob but created from some other info</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeData.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeTypedArgument">
            <summary>
            Custom attribute typed argument
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeTypedArgument.ArgumentType">
            <summary>
            Gets the argument type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeTypedArgument.Value">
            <summary>
            Gets the argument value
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeTypedArgument.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Object)">
            <summary>
            Constructor
            </summary>
            <param name="argumentType">Argument type</param>
            <param name="value">Argument value</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeTypedArgument.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeTypedArgument.Equals(dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeTypedArgument)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeTypedArgument.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeTypedArgument.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeNamedArgument">
            <summary>
            Custom attribute named argument
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeNamedArgument.MemberInfo">
            <summary>
            Gets the member (a property or a field)
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeNamedArgument.TypedValue">
            <summary>
            Gets the value
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeNamedArgument.MemberName">
            <summary>
            Gets the member name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeNamedArgument.IsField">
            <summary>
            true if it's a field, false if it's a property
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeNamedArgument.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo,dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeTypedArgument)">
            <summary>
            Constructor
            </summary>
            <param name="memberInfo">A property or a field</param>
            <param name="typedArgument"></param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeNamedArgument.Equals(dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeNamedArgument)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeNamedArgument.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeNamedArgument.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomAttributeNamedArgument.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier">
            <summary>
            A required or optional custom modifier
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier.IsRequired">
            <summary>
            true if it's a required C modifier
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier.IsOptional">
            <summary>
            true if it's an optional C modifier
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier.Type">
            <summary>
            Gets the type
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Constructor
            </summary>
            <param name="type">Type</param>
            <param name="isRequired">true if it's a required C modifier, false if it's an optional C modifier</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier.Equals(dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdDataStream">
            <summary>
            Data stream
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.Position">
            <summary>
            Gets/sets the position
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.Length">
            <summary>
            Gets the stream length
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadByte">
            <summary>
            Reads a <see cref="T:System.Byte"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadUInt16">
            <summary>
            Reads a <see cref="T:System.UInt16"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadUInt32">
            <summary>
            Reads a <see cref="T:System.UInt32"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadUInt64">
            <summary>
            Reads a <see cref="T:System.UInt64"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadSingle">
            <summary>
            Reads a <see cref="T:System.Single"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadDouble">
            <summary>
            Reads a <see cref="T:System.Double"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadBytes(System.Int32)">
            <summary>
            Reads bytes
            </summary>
            <param name="length">Number of bytes to read</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadSByte">
            <summary>
            Reads a <see cref="T:System.SByte"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadInt16">
            <summary>
            Reads a <see cref="T:System.Int16"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadInt32">
            <summary>
            Reads a <see cref="T:System.Int32"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadInt64">
            <summary>
            Reads a <see cref="T:System.Int64"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadCompressedUInt32">
            <summary>
            Reads a compressed <see cref="T:System.UInt32"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.ReadCompressedInt32">
            <summary>
            Reads a compressed <see cref="T:System.Int32"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDataStream.Dispose">
            <summary>
            Disposes this instance
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdDispatcher">
            <summary>
            Invokes code on another thread.
            It's used if the underlying .NET metadata reader is a COM interface.
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDispatcher.CheckAccess">
            <summary>
            Checks whether the current thread is the dispatcher thread
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDispatcher.VerifyAccess">
            <summary>
            Throws if the current thread isn't the dispatcher thread
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDispatcher.Invoke(System.Action)">
            <summary>
            Switches to the dispatcher thread and executes <paramref name="callback"/>
            </summary>
            <param name="callback">Code to execute</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDispatcher.Invoke``1(System.Func{``0})">
            <summary>
            Switches to the dispatcher thread and executes <paramref name="callback"/>
            </summary>
            <typeparam name="T">Type of return data</typeparam>
            <param name="callback">Code to execute</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdDynamicModuleHelper">
            <summary>
            Returns info to the COM MetaDataImport reader code that isn't made available by the COM MetaDataImport API
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdDynamicModuleHelper.TryGetMethodBody(dnSpy.Debugger.DotNet.Metadata.DmdModule,System.Int32,System.UInt32)">
            <summary>
            Called to get the method body stream or null if there's no method body.
            
            This method should use the CLR debugger API to get the address of the method body.
            
            This method is only called on the COM thread.
            </summary>
            <param name="module">Module</param>
            <param name="metadataToken">Metadata token of the method</param>
            <param name="rva">RVA of method body</param>
            <returns></returns>
        </member>
        <member name="E:dnSpy.Debugger.DotNet.Metadata.DmdDynamicModuleHelper.TypeLoaded">
            <summary>
            Raised when a new type in this module is loaded. It must be raised on the COM thread.
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdTypeLoadedEventArgs">
            <summary>
            Class loaded event args
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdTypeLoadedEventArgs.MetadataToken">
            <summary>
            Gets the metadata token of the type that got loaded
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdTypeLoadedEventArgs.#ctor(System.Int32)">
            <summary>
            Constructor
            </summary>
            <param name="metadataToken">Metadata token of the type that got loaded</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdEvaluator">
            <summary>
            Executes methods and loads/stores fields
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEvaluator.CreateInstance(System.Object,dnSpy.Debugger.DotNet.Metadata.DmdConstructorInfo,System.Object[])">
            <summary>
            Creates a new instance of a type
            </summary>
            <param name="context">Evaluation context</param>
            <param name="ctor">Constructor</param>
            <param name="arguments">Arguments passed to the constructor</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEvaluator.Invoke(System.Object,dnSpy.Debugger.DotNet.Metadata.DmdMethodBase,System.Object,System.Object[])">
            <summary>
            Executes a method
            </summary>
            <param name="context">Evaluation context</param>
            <param name="method">Method to call</param>
            <param name="obj">Instance object or null if it's a constructor or a static method</param>
            <param name="arguments">Arguments passed to the method</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEvaluator.LoadField(System.Object,dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo,System.Object)">
            <summary>
            Loads a field
            </summary>
            <param name="context">Evaluation context</param>
            <param name="field">Field</param>
            <param name="obj">Instance object or null if it's a static field</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEvaluator.StoreField(System.Object,dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo,System.Object,System.Object)">
            <summary>
            Stores a value in a field
            </summary>
            <param name="context">Evaluation context</param>
            <param name="field">Field</param>
            <param name="obj">Instance object or null if it's a static field</param>
            <param name="value">Value to store in the field</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdEventAttributes">
            <summary>
            Event attributes
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo">
            <summary>
            A .NET event
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.AppDomain">
            <summary>
            Gets the AppDomain
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.MemberType">
            <summary>
            Gets the member type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.Attributes">
            <summary>
            Gets the event attributes
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.EventHandlerType">
            <summary>
            Gets the event handler type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.IsMulticast">
            <summary>
            true if it's a multi-cast delegate
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.ResolveMember(System.Boolean)">
            <summary>
            Resolves a member reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.IsMetadataReference">
            <summary>
            Returns false since there are no event references
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.AddMethod">
            <summary>
            Gets the add method
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.RemoveMethod">
            <summary>
            Gets the remove method
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.RaiseMethod">
            <summary>
            Gets the raise method
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetOtherMethods">
            <summary>
            Gets all public 'other' methods
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetAddMethod">
            <summary>
            Gets the public add method
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetRemoveMethod">
            <summary>
            Gets the public remove method
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetRaiseMethod">
            <summary>
            Gets the public raise method
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetOtherMethods(System.Boolean)">
            <summary>
            Gets 'other' methods
            </summary>
            <param name="nonPublic">true to include all methods, false to only include public methods</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetOtherMethods(dnSpy.Debugger.DotNet.Metadata.DmdGetAccessorOptions)">
            <summary>
            Gets 'other' methods
            </summary>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetAddMethod(System.Boolean)">
            <summary>
            Gets the add method
            </summary>
            <param name="nonPublic">true to return any method, false to only return a public method</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetAddMethod(dnSpy.Debugger.DotNet.Metadata.DmdGetAccessorOptions)">
            <summary>
            Gets the add method
            </summary>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetRemoveMethod(System.Boolean)">
            <summary>
            Gets the remove method
            </summary>
            <param name="nonPublic">true to return any method, false to only return a public method</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetRemoveMethod(dnSpy.Debugger.DotNet.Metadata.DmdGetAccessorOptions)">
            <summary>
            Gets the remove method
            </summary>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetRaiseMethod(System.Boolean)">
            <summary>
            Gets the raise method
            </summary>
            <param name="nonPublic">true to return any method, false to only return a public method</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetRaiseMethod(dnSpy.Debugger.DotNet.Metadata.DmdGetAccessorOptions)">
            <summary>
            Gets the raise method
            </summary>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.Equals(dnSpy.Debugger.DotNet.Metadata.DmdEventInfo)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdEventInfo.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdFieldAttributes">
            <summary>
            Field attributes
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo">
            <summary>
            A .NET field
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.AppDomain">
            <summary>
            Gets the AppDomain
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.MemberType">
            <summary>
            Gets the member type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.FieldType">
            <summary>
            Gets the field type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.Attributes">
            <summary>
            Gets the field attributes
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.FieldRVA">
            <summary>
            Gets the RVA of the data if <see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.HasFieldRVA"/> is true
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.ResolveMember(System.Boolean)">
            <summary>
            Resolves a field reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.Resolve">
            <summary>
            Resolves a field reference and throws if it doesn't exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.ResolveNoThrow">
            <summary>
            Resolves a field reference and returns null if it doesn't exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.Resolve(System.Boolean)">
            <summary>
            Resolves a field reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.GetRequiredCustomModifiers">
            <summary>
            Gets all required custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.GetOptionalCustomModifiers">
            <summary>
            Gets all optional custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.GetCustomModifiers">
            <summary>
            Gets all custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.GetRawConstantValue">
            <summary>
            Gets the constant value stored in metadata if any exists
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.GetValue(System.Object,System.Object)">
            <summary>
            Gets the current value
            </summary>
            <param name="context">Evaluation context</param>
            <param name="obj">Instance or null if it's a static field</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.SetValue(System.Object,System.Object,System.Object,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Sets a new value
            </summary>
            <param name="context">Evaluation context</param>
            <param name="obj">Instance or null if it's a static field</param>
            <param name="value">New value</param>
            <param name="invokeAttr">Binding attributes</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.SetValue(System.Object,System.Object,System.Object)">
            <summary>
            Sets a new value
            </summary>
            <param name="context">Evaluation context</param>
            <param name="obj">Instance or null if it's a static field</param>
            <param name="value">New value</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.Equals(dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdGenericParameterAttributes">
            <summary>
            Generic parameter attributes
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdGetAccessorOptions">
            <summary>
            Get property/event accessor options
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdGetAccessorOptions.None">
            <summary>
            No bit is set
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdGetAccessorOptions.NonPublic">
            <summary>
            Return non-public accessors (doesn't include private accessors in base classes)
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdGetAccessorOptions.All">
            <summary>
            Return all accessors, even if they're in a base class and private
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdImageFileMachine">
            <summary>
            Machine
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytes">
            <summary>
            Base class of all classes that contain metadata
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesArray">
            <summary>
            Metadata in a <see cref="T:System.Byte"/> array
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesArray.Bytes">
            <summary>
            Gets the raw PE file bytes
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesArray.IsFileLayout">
            <summary>
            true if file layout, false if memory layout
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesArray.#ctor(System.Byte[],System.Boolean)">
            <summary>
            Constructor
            </summary>
            <param name="bytes">Raw PE file bytes</param>
            <param name="isFileLayout">true if file layout, false if memory layout</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesFile">
            <summary>
            Metadata in a file
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesFile.Filename">
            <summary>
            Gets the filename
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesFile.IsFileLayout">
            <summary>
            true if file layout, false if memory layout
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesFile.#ctor(System.String,System.Boolean)">
            <summary>
            Constructor
            </summary>
            <param name="filename">Filename</param>
            <param name="isFileLayout">true if file layout, false if memory layout</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesPtr">
            <summary>
            Metadata in memory
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesPtr.Address">
            <summary>
            Gets the address of the PE file
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesPtr.Size">
            <summary>
            Gets the size of the PE file
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesPtr.IsFileLayout">
            <summary>
            true if file layout, false if memory layout
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesPtr.#ctor(System.IntPtr,System.UInt32,System.Boolean)">
            <summary>
            Constructor
            </summary>
            <param name="address">Address of the PE file</param>
            <param name="size">Size of the PE file</param>
            <param name="isFileLayout">true if file layout, false if memory layout</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesCom">
            <summary>
            COM <c>IMetaDataImport</c> metadata
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesCom.ComMetadata">
            <summary>
            Gets the COM <c>IMetaDataImport</c> instance
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesCom.Dispatcher">
            <summary>
            Gets the dispatcher to use when accessing <see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesCom.ComMetadata"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesCom.DynamicModuleHelper">
            <summary>
            Gets the helper class
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdLazyMetadataBytesCom.#ctor(System.Object,dnSpy.Debugger.DotNet.Metadata.DmdDynamicModuleHelper,dnSpy.Debugger.DotNet.Metadata.DmdDispatcher)">
            <summary>
            Constructor
            </summary>
            <param name="comMetadata">COM <c>IMetaDataImport</c> instance</param>
            <param name="dynamicModuleHelper">Helper class</param>
            <param name="dispatcher">Dispatcher to use when accessing <paramref name="comMetadata"/></param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo">
            <summary>
            Base class of all types, fields, methods, constructors, properties, events
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.YouCantDeriveFromThisClass">
            <summary>
            Dummy abstract method to make sure no-one outside this assembly can create their own <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo"/>
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.ResolveMember">
            <summary>
            Resolves a member reference and throws if it doesn't exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.ResolveMemberNoThrow">
            <summary>
            Resolves a member reference and returns null if it doesn't exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.ResolveMember(System.Boolean)">
            <summary>
            Resolves a member reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.AppDomain">
            <summary>
            Gets the AppDomain
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.MemberType">
            <summary>
            Gets the member type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.Name">
            <summary>
            Gets the member name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.DeclaringType">
            <summary>
            Gets the declaring type. This is the type that declares the member, see also <see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.ReflectedType"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.ReflectedType">
            <summary>
            Gets the reflected type. This is the type that owns this member, see also <see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.DeclaringType"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.MetadataToken">
            <summary>
            Gets the metadata token
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.Module">
            <summary>
            Gets the module
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.IsMetadataReference">
            <summary>
            true if it's a reference to another type or member, eg. a TypeRef, MemberRef
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.HasSameMetadataDefinitionAs(dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo)">
            <summary>
            Checks if this instance and <paramref name="other"/> have the same metadata definition
            </summary>
            <param name="other">Other member</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.SecurityAttributes">
            <summary>
            Gets the security attributes
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.GetSecurityAttributesData">
            <summary>
            Gets the security attributes
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.CustomAttributes">
            <summary>
            Gets the custom attributes
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.GetCustomAttributesData">
            <summary>
            Gets the custom attributes
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.IsDefined(System.String,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.IsDefined(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.IsDefined(System.Type,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.FindCustomAttribute(System.String,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.FindCustomAttribute(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.FindCustomAttribute(System.Type,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.Equals(dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfoEqualityComparer">
            <summary>
            Compares types, members, parameters
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfoEqualityComparer.DefaultType">
            <summary>
            Should be used when comparing types that aren't part of a member signature. Custom modifiers and
            MD arrays' lower bounds and sizes are ignored.
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfoEqualityComparer.DefaultMember">
            <summary>
            Should be used when comparing member signatures or when comparing types in member signatures.
            Custom modifiers are compared and types are checked for equivalence.
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfoEqualityComparer.DefaultParameter">
            <summary>
            Should be used when comparing parameters
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfoEqualityComparer.DefaultCustomModifier">
            <summary>
            Should be used when comparing custom modifiers
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfoEqualityComparer.DefaultOther">
            <summary>
            Should be used when comparing any other supported class, eg. <see cref="T:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName"/>s
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfoEqualityComparer.DefaultTypeOptions">
            <summary>
            Gets the default options used by <see cref="F:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfoEqualityComparer.DefaultType"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfoEqualityComparer.Options">
            <summary>
            Gets the options
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfoEqualityComparer.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions)">
            <summary>
            Constructor
            </summary>
            <param name="options">Options</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdMemberTypes">
            <summary>
            Member types
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdMethodAttributes">
            <summary>
            Method attributes
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase">
            <summary>
            Base class of .NET methods
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.SpecialMethodKind">
            <summary>
            Gets the method kind
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.AppDomain">
            <summary>
            Gets the AppDomain
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.MethodImplementationFlags">
            <summary>
            Gets the method impl flags
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.Attributes">
            <summary>
            Gets the method attributes
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.CallingConvention">
            <summary>
            Gets the calling convention flags
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.IsGenericMethodDefinition">
            <summary>
            true if it's a generic method definition
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.IsGenericMethod">
            <summary>
            true if it's a generic method
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.IsConstructedGenericMethod">
            <summary>
            true if it's a constructed generic method
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.ContainsGenericParameters">
            <summary>
            true if it contains generic parameters
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.RVA">
            <summary>
            Gets the RVA of the method body or native code or 0 if none
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.IsConstructor">
            <summary>
            true if this is an instance constructor
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.ResolveMethodBase">
            <summary>
            Resolves a method reference and throws if it doesn't exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.ResolveMethodBaseNoThrow">
            <summary>
            Resolves a method reference and returns null if it doesn't exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.ResolveMethodBase(System.Boolean)">
            <summary>
            Resolves a method reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.GetParameters">
            <summary>
            Gets all parameters
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.GetGenericArguments">
            <summary>
            Gets all generic arguments if it's a generic method
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.GetMethodBody">
            <summary>
            Gets the method body
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.GetMethodSignature">
            <summary>
            Gets the method signature
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.GetMethodSignature(System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Gets the method signature
            </summary>
            <param name="genericMethodArguments">Generic method arguments</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.GetMethodSignature(System.Collections.Generic.IList{System.Type})">
            <summary>
            Gets the method signature
            </summary>
            <param name="genericMethodArguments">Generic method arguments</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.GetSecurityAttributesData">
            <summary>
            Gets the security attributes
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.Invoke(System.Object,System.Object,System.Object[])">
            <summary>
            Calls the method
            </summary>
            <param name="context">Evaluation context</param>
            <param name="obj">Instance or null if it's a static method</param>
            <param name="parameters">Parameters</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.Invoke(System.Object,System.Object,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,System.Object[])">
            <summary>
            Calls the method
            </summary>
            <param name="context">Evaluation context</param>
            <param name="obj">Instance or null if it's a static method</param>
            <param name="invokeAttr">Binding flags</param>
            <param name="parameters">Parameters</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.Equals(dnSpy.Debugger.DotNet.Metadata.DmdMethodBase)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBase.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdSpecialMethodKind">
            <summary>
            Special methods created by the CLR
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSpecialMethodKind.Metadata">
            <summary>
            It was read from metadata
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSpecialMethodKind.Array_Set">
            <summary>
            SZArray/MDArray Set method: void Set(int, ..., ElementType)
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSpecialMethodKind.Array_Address">
            <summary>
            SZArray/MDArray Address method: ElementType&amp; Address(int, ...)
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSpecialMethodKind.Array_Get">
            <summary>
            SZArray/MDArray Get method: ElementType Get(int, ...)
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSpecialMethodKind.Array_Constructor1">
            <summary>
            SZArray/MDArray constructor that takes <see cref="T:System.Int32"/> args specifying the sizes of all dimensions.
            Lower bound is assumed to be zero.
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSpecialMethodKind.Array_Constructor2">
            <summary>
            MDArray constructor that takes <see cref="T:System.Int32"/> args in pairs, one per dimension. The first
            <see cref="T:System.Int32"/> is the lower bound for the dimension and the following <see cref="T:System.Int32"/> is
            the size.
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdMethodBody">
            <summary>
            .NET method body
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBody.LocalSignatureMetadataToken">
            <summary>
            Gets the token of the locals signature
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBody.LocalVariables">
            <summary>
            Gets all locals
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBody.MaxStackSize">
            <summary>
            Gets max stack size
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBody.InitLocals">
            <summary>
            true if locals are automatically initialized
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodBody.GetILAsByteArray">
            <summary>
            Gets the IL bytes
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBody.ExceptionHandlingClauses">
            <summary>
            Gets the exception clauses
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBody.GenericTypeArguments">
            <summary>
            Gets the generic type arguments that were used to create this method body
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodBody.GenericMethodArguments">
            <summary>
            Gets the generic method arguments that were used to create this method body
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdLocalVariableInfo">
            <summary>
            Local variable info
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLocalVariableInfo.LocalType">
            <summary>
            Gets the type of the local
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLocalVariableInfo.IsPinned">
            <summary>
            true if it's a pinned local
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdLocalVariableInfo.LocalIndex">
            <summary>
            Index of the local in the locals signature
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdLocalVariableInfo.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Int32,System.Boolean)">
            <summary>
            Constructor
            </summary>
            <param name="localType">Type of the local</param>
            <param name="localIndex">Index of local</param>
            <param name="isPinned">True if it's a pinned local</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdLocalVariableInfo.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdExceptionHandlingClauseOptions">
            <summary>
            Exception clause kind
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdExceptionHandlingClause">
            <summary>
            Exception clause
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdExceptionHandlingClause.Flags">
            <summary>
            Gets the clause kind
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdExceptionHandlingClause.TryOffset">
            <summary>
            Try offset
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdExceptionHandlingClause.TryLength">
            <summary>
            Try length
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdExceptionHandlingClause.HandlerOffset">
            <summary>
            Handler offset
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdExceptionHandlingClause.HandlerLength">
            <summary>
            Handler length
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdExceptionHandlingClause.FilterOffset">
            <summary>
            Filter offset
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdExceptionHandlingClause.CatchType">
            <summary>
            Catch type
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdExceptionHandlingClause.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdExceptionHandlingClauseOptions,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Constructor
            </summary>
            <param name="flags">Flags</param>
            <param name="tryOffset">Try offset</param>
            <param name="tryLength">Try length</param>
            <param name="handlerOffset">Handler offset</param>
            <param name="handlerLength">Handler length</param>
            <param name="filterOffset">Filter offset</param>
            <param name="catchType">Catch type</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdExceptionHandlingClause.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdMethodImplAttributes">
            <summary>
            Method implementation attributes
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo">
            <summary>
            A .NET method
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.MemberType">
            <summary>
            Gets the member type
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.ResolveMember(System.Boolean)">
            <summary>
            Resolves a member reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.ResolveMethodBase(System.Boolean)">
            <summary>
            Resolves a method reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.Resolve">
            <summary>
            Resolves a method reference and throws if it doesn't exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.ResolveNoThrow">
            <summary>
            Resolves a method reference and returns null if it doesn't exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.Resolve(System.Boolean)">
            <summary>
            Resolves a method reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.ReturnType">
            <summary>
            Gets the return type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.ReturnParameter">
            <summary>
            Gets the return parameter
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.ReturnTypeCustomAttributes">
            <summary>
            Gets the return type's custom attributes
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.ContainsGenericParameters">
            <summary>
            true if it contains generic parameters
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.GetBaseDefinition">
            <summary>
            Gets the base method definition or itself if it doesn't override a method
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.GetParentDefinition">
            <summary>
            Gets the parent method
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.GetGenericArguments">
            <summary>
            Gets all generic arguments if it's a generic method
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.GetGenericMethodDefinition">
            <summary>
            Gets the generic method definition
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.MakeGenericMethod(dnSpy.Debugger.DotNet.Metadata.DmdType[])">
            <summary>
            Creates a generic method
            </summary>
            <param name="typeArguments">Generic arguments</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.MakeGenericMethod(System.Type[])">
            <summary>
            Creates a generic method
            </summary>
            <param name="typeArguments">Generic arguments</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.MakeGenericMethod(System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Creates a generic method
            </summary>
            <param name="typeArguments">Generic arguments</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.MakeGenericMethod(System.Collections.Generic.IList{System.Type})">
            <summary>
            Creates a generic method
            </summary>
            <param name="typeArguments">Generic arguments</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.IsDefined(System.String,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.IsDefined(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.FindCustomAttribute(System.String,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.FindCustomAttribute(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.Equals(dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodInfo.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature">
            <summary>
            .NET method signature
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.Flags">
            <summary>
            Gets the flags
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.IsGeneric">
            <summary>
            true if it's a generic method signature
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.HasThis">
            <summary>
            true if 'this' is a hidden parameter
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.ExplicitThis">
            <summary>
            true if 'this' is an explicit parameter
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.GenericParameterCount">
            <summary>
            Generic parameter count
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.ReturnType">
            <summary>
            Gets the return type
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdSignatureCallingConvention,System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdType,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Constructor
            </summary>
            <param name="flags">Flags</param>
            <param name="genericParameterCount">Number of generic parameters</param>
            <param name="returnType">Return type</param>
            <param name="parameterTypes">Parameter types or null</param>
            <param name="varArgsParameterTypes">Var args parameter types or null</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.GetParameterTypes">
            <summary>
            Gets the parameter types, see also <see cref="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.GetVarArgsParameterTypes"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.GetVarArgsParameterTypes">
            <summary>
            Gets the var args parameter types
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.Equals(dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdModule">
            <summary>
            A .NET module
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.YouCantDeriveFromThisClass">
            <summary>
            Dummy abstract method to make sure no-one outside this assembly can create their own <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdModule"/>
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFullyQualifiedName(System.Boolean,System.Boolean,System.String)">
            <summary>
            Returns the fully qualified name
            </summary>
            <param name="isInMemory">true if the module is in memory</param>
            <param name="isDynamic">true if it's a dynamic module</param>
            <param name="fullyQualifiedName">Module filename or null</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.AppDomain">
            <summary>
            Gets the AppDomain
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.FullyQualifiedName">
            <summary>
            Gets the fully qualified name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsCorLib">
            <summary>
            true if this is the corlib module
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetTypes">
            <summary>
            Gets all types in this module
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetExportedTypes">
            <summary>
            Gets all types that exist in the ExportedType table. This includes types that have been
            forwarded to other assemblies.
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.ModuleVersionId">
            <summary>
            Gets the module version ID
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.MetadataToken">
            <summary>
            Gets the metadata token
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.GlobalType">
            <summary>
            Gets the global type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.MDStreamVersion">
            <summary>
            Gets the metadata stream version
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.ScopeName">
            <summary>
            Gets the metadata name of the module
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.DynamicModuleVersion">
            <summary>
            Gets a dynamic module's version number. It gets incremented each time a new type gets added to the dynamic module.
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.Name">
            <summary>
            Gets the module name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.Assembly">
            <summary>
            Gets the assembly
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDynamic">
            <summary>
            true if it's a dynamic module (types can be added at runtime)
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsInMemory">
            <summary>
            true if it's an in-memory module (eg. loaded from a <see cref="T:System.Byte"/> array)
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsSynthetic">
            <summary>
            true if it's a synthetic module; it's not loaded in the debugged process
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.CustomAttributes">
            <summary>
            Gets the custom attributes
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetCustomAttributesData">
            <summary>
            Gets the custom attributes
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDefined(System.String,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDefined(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.IsDefined(System.Type,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.FindCustomAttribute(System.String,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.FindCustomAttribute(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.FindCustomAttribute(System.Type,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMethod(System.Int32)">
            <summary>
            Resolves a method
            </summary>
            <param name="metadataToken">Token</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMethod(System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a method
            </summary>
            <param name="metadataToken">Token</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMethod(System.Int32,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Resolves a method
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMethod(System.Int32,System.Collections.Generic.IList{System.Type},System.Collections.Generic.IList{System.Type})">
            <summary>
            Resolves a method
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMethod(System.Int32,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a method
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMethod(System.Int32,System.Collections.Generic.IList{System.Type},System.Collections.Generic.IList{System.Type},dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a method
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveField(System.Int32)">
            <summary>
            Resolves a field
            </summary>
            <param name="metadataToken">Token</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveField(System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a field
            </summary>
            <param name="metadataToken">Token</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveField(System.Int32,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Resolves a field
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveField(System.Int32,System.Collections.Generic.IList{System.Type},System.Collections.Generic.IList{System.Type})">
            <summary>
            Resolves a field
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveField(System.Int32,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a field
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveField(System.Int32,System.Collections.Generic.IList{System.Type},System.Collections.Generic.IList{System.Type},dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a field
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveType(System.Int32)">
            <summary>
            Resolves a type
            </summary>
            <param name="metadataToken">Token</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveType(System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a type
            </summary>
            <param name="metadataToken">Token</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveType(System.Int32,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Resolves a type
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveType(System.Int32,System.Collections.Generic.IList{System.Type},System.Collections.Generic.IList{System.Type})">
            <summary>
            Resolves a type
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveType(System.Int32,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a type
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveType(System.Int32,System.Collections.Generic.IList{System.Type},System.Collections.Generic.IList{System.Type},dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a type
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMember(System.Int32)">
            <summary>
            Resolves a member
            </summary>
            <param name="metadataToken">Token</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMember(System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a member
            </summary>
            <param name="metadataToken">Token</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMember(System.Int32,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Resolves a member
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMember(System.Int32,System.Collections.Generic.IList{System.Type},System.Collections.Generic.IList{System.Type})">
            <summary>
            Resolves a member
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMember(System.Int32,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a member
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMember(System.Int32,System.Collections.Generic.IList{System.Type},System.Collections.Generic.IList{System.Type},dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a member
            </summary>
            <param name="metadataToken">Token</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMethodSignature(System.Int32,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Resolves a method signature
            </summary>
            <param name="metadataToken">StandaloneSig token from a method body</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMethodSignature(System.Int32,System.Collections.Generic.IList{System.Type},System.Collections.Generic.IList{System.Type})">
            <summary>
            Resolves a method signature
            </summary>
            <param name="metadataToken">StandaloneSig token from a method body</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMethodSignature(System.Int32,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a method signature
            </summary>
            <param name="metadataToken">StandaloneSig token from a method body</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveMethodSignature(System.Int32,System.Collections.Generic.IList{System.Type},System.Collections.Generic.IList{System.Type},dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions)">
            <summary>
            Resolves a method signature
            </summary>
            <param name="metadataToken">StandaloneSig token from a method body</param>
            <param name="genericTypeArguments">Generic type arguments or null</param>
            <param name="genericMethodArguments">Generic method arguments or null</param>
            <param name="options">Resolve options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveSignature(System.Int32)">
            <summary>
            Resolves a signature
            </summary>
            <param name="metadataToken">Token</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ResolveString(System.Int32)">
            <summary>
            Resolves a string
            </summary>
            <param name="metadataToken">String token (<c>0x70xxxxxx</c>)</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetPEKind(dnSpy.Debugger.DotNet.Metadata.DmdPortableExecutableKinds@,dnSpy.Debugger.DotNet.Metadata.DmdImageFileMachine@)">
            <summary>
            Gets PE information
            </summary>
            <param name="peKind">PE Kind</param>
            <param name="machine">Machine</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetType(System.Type)">
            <summary>
            Gets a type
            </summary>
            <param name="type">Type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetTypeThrow(System.Type)">
            <summary>
            Gets a type and throws if it couldn't be found
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetType(System.Type,dnSpy.Debugger.DotNet.Metadata.DmdGetTypeOptions)">
            <summary>
            Gets a type
            </summary>
            <param name="type">Type</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetType(System.String,System.Boolean)">
            <summary>
            Gets a type
            </summary>
            <param name="className">Name of type</param>
            <param name="ignoreCase">true to ignore case</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetType(System.String)">
            <summary>
            Gets a type
            </summary>
            <param name="className">Name of type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetTypeThrow(System.String)">
            <summary>
            Gets a type and throws if it couldn't be found
            </summary>
            <param name="className">Name of type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetType(System.String,System.Boolean,System.Boolean)">
            <summary>
            Gets a type
            </summary>
            <param name="className">Name of type</param>
            <param name="throwOnError">true to throw if the type couldn't be found</param>
            <param name="ignoreCase">true to ignore case</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetType(System.String,dnSpy.Debugger.DotNet.Metadata.DmdGetTypeOptions)">
            <summary>
            Gets a type
            </summary>
            <param name="typeName">Full name of the type (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdType.FullName"/>)</param>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFields">
            <summary>
            Gets all global public static and instance fields
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetFields(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets all global fields
            </summary>
            <param name="bindingFlags">Binding attributes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetField(System.String)">
            <summary>
            Gets a global public static or instance field
            </summary>
            <param name="name">Field name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetField(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets a global field
            </summary>
            <param name="name">Field name</param>
            <param name="bindingAttr">Binding attributes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetMethods">
            <summary>
            Gets all global public static or instance methods
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetMethods(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets global methods
            </summary>
            <param name="bindingFlags">Binding attributes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetMethod(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,dnSpy.Debugger.DotNet.Metadata.DmdCallingConventions,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Gets a global method
            </summary>
            <param name="name">Method name</param>
            <param name="bindingAttr">Binding attributes</param>
            <param name="callConvention">Calling convention</param>
            <param name="types">Method parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetMethod(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,dnSpy.Debugger.DotNet.Metadata.DmdCallingConventions,System.Collections.Generic.IList{System.Type})">
            <summary>
            Gets a global method
            </summary>
            <param name="name">Method name</param>
            <param name="bindingAttr">Binding attributes</param>
            <param name="callConvention">Calling convention</param>
            <param name="types">Method parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetMethod(System.String,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Gets a global public static or instance method
            </summary>
            <param name="name">Method name</param>
            <param name="types">Method parameter types</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetMethod(System.String,System.Collections.Generic.IList{System.Type})">
            <summary>
            Gets a global public static or instance method
            </summary>
            <param name="name">Method name</param>
            <param name="types">Method parameter types</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetMethod(System.String)">
            <summary>
            Gets a global public static or instance method
            </summary>
            <param name="name">Method name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.GetReferencedAssemblies">
            <summary>
            Gets all referenced assemblies
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ReadMemory(System.UInt32,System.Void*,System.Int32)">
            <summary>
            Reads memory. Returns false if data couldn't be read.
            </summary>
            <param name="rva">RVA of data</param>
            <param name="destination">Destination address</param>
            <param name="size">Number of bytes to read</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ReadMemory(System.UInt32,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads memory. Returns false if data couldn't be read.
            </summary>
            <param name="rva">RVA of data</param>
            <param name="destination">Destination buffer</param>
            <param name="destinationIndex">Destination index</param>
            <param name="size">Number of bytes to read</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ReadMemory(System.UInt32,System.Int32)">
            <summary>
            Reads memory. Returns null if data couldn't be read.
            </summary>
            <param name="rva">RVA of data</param>
            <param name="size">Number of bytes to read</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdModule.ToString">
            <summary>
            Returns the metadata name (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdModule.ScopeName"/>)
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions">
            <summary>
            Type/member resolve options
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions.None">
            <summary>
            No bit is set
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions.ThrowOnError">
            <summary>
            Throw if the type or member couldn't be resolved
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdResolveOptions.NoTryResolveRefs">
            <summary>
            Don't try to resolve type refs, field refs, method refs
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdObject">
            <summary>
            Base class of types, members, assemblies, modules that allows you to attach data to instances
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdObject.LockObject">
            <summary>
            Gets the lock object used by this instance
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdObject.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdObject.HasData``1">
            <summary>
            Checks if the data exists or is null
            </summary>
            <typeparam name="T">Type of data</typeparam>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdObject.GetOrCreateData``1">
            <summary>
            Gets or creates data
            </summary>
            <typeparam name="T">Type of data</typeparam>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdObject.TryGetData``1(``0@)">
            <summary>
            Gets data
            </summary>
            <typeparam name="T">Type of data</typeparam>
            <param name="value">Result</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdObject.GetData``1">
            <summary>
            Gets existing data or throws if the data doesn't exist
            </summary>
            <typeparam name="T">Type of data</typeparam>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdObject.GetOrCreateData``1(System.Func{``0})">
            <summary>
            Gets or creates data
            </summary>
            <typeparam name="T">Type of data</typeparam>
            <param name="create">Creates the data if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdParameterAttributes">
            <summary>
            Parameter attributes
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo">
            <summary>
            A .NET method parameter
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.YouCantDeriveFromThisClass">
            <summary>
            Dummy abstract method to make sure no-one outside this assembly can create their own <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.ParameterType">
            <summary>
            Gets the parameter type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.Name">
            <summary>
            Gets the parameter name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.HasDefaultValue">
            <summary>
            true if <see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.RawDefaultValue"/> is valid
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.RawDefaultValue">
            <summary>
            Gets the default value, see also <see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.HasDefaultValue"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.Position">
            <summary>
            Gets the parameter index
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.Attributes">
            <summary>
            Gets the parameter attributes
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.Member">
            <summary>
            Gets the owner method or property
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.IsMetadataReference">
            <summary>
            true if this is not the real method parameter since the declaring method is just a reference.
            Resolve the method to get the real method parameters.
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.MetadataToken">
            <summary>
            Gets the metadata token
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.GetRequiredCustomModifiers">
            <summary>
            Gets all required custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.GetOptionalCustomModifiers">
            <summary>
            Gets all optional custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.GetCustomModifiers">
            <summary>
            Gets all custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.CustomAttributes">
            <summary>
            Gets the custom attributes
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.GetCustomAttributesData">
            <summary>
            Gets the custom attributes
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.IsDefined(System.String,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.IsDefined(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.IsDefined(System.Type,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.FindCustomAttribute(System.String,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.FindCustomAttribute(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.FindCustomAttribute(System.Type,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.Equals(dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdPortableExecutableKinds">
            <summary>
            PE flags
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdProcessorArchitecture">
            <summary>
            Processor architecture
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdPropertyAttributes">
            <summary>
            Property attributes
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo">
            <summary>
            A .NET property
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.AppDomain">
            <summary>
            Gets the AppDomain
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.MemberType">
            <summary>
            Gets the member type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.PropertyType">
            <summary>
            Gets the property type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.Attributes">
            <summary>
            Gets the property attributes
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.CanRead">
            <summary>
            true if the property can be read
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.CanWrite">
            <summary>
            true if the property can be written to
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.ResolveMember(System.Boolean)">
            <summary>
            Resolves a property reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.IsMetadataReference">
            <summary>
            Returns false since there are no property references
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetRawConstantValue">
            <summary>
            Gets the constant stored in metadata
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetAccessors(System.Boolean)">
            <summary>
            Gets all accessors
            </summary>
            <param name="nonPublic">true to include all accessors, false to only include public accessors</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetAccessors(dnSpy.Debugger.DotNet.Metadata.DmdGetAccessorOptions)">
            <summary>
            Gets all accessors
            </summary>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetGetMethod(System.Boolean)">
            <summary>
            Gets the get method
            </summary>
            <param name="nonPublic">true to return any get method, false to only return a public get method</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetGetMethod(dnSpy.Debugger.DotNet.Metadata.DmdGetAccessorOptions)">
            <summary>
            Gets the get method
            </summary>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetSetMethod(System.Boolean)">
            <summary>
            Gets the set method
            </summary>
            <param name="nonPublic">true to return any set method, false to only return a public set method</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetSetMethod(dnSpy.Debugger.DotNet.Metadata.DmdGetAccessorOptions)">
            <summary>
            Gets the set method
            </summary>
            <param name="options">Options</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetIndexParameters">
            <summary>
            Gets the index parameters
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetRequiredCustomModifiers">
            <summary>
            Gets all required custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetOptionalCustomModifiers">
            <summary>
            Gets all optional custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetCustomModifiers">
            <summary>
            Gets all custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetAccessors">
            <summary>
            Gets all public accessors
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetMethod">
            <summary>
            Gets the get method
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.SetMethod">
            <summary>
            Gets the set method
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetGetMethod">
            <summary>
            Gets the public get method
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetSetMethod">
            <summary>
            Gets the public set method
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetMethodSignature">
            <summary>
            Gets the method signature
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetValue(System.Object,System.Object)">
            <summary>
            Gets the property value
            </summary>
            <param name="context">Evaluation context</param>
            <param name="obj">Instance or null if it's a static property</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetValue(System.Object,System.Object,System.Object[])">
            <summary>
            Gets the property value
            </summary>
            <param name="context">Evaluation context</param>
            <param name="obj">Instance or null if it's a static property</param>
            <param name="index">Property indexes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetValue(System.Object,System.Object,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,System.Object[])">
            <summary>
            Gets the property value
            </summary>
            <param name="context">Evaluation context</param>
            <param name="obj">Instance or null if it's a static property</param>
            <param name="invokeAttr">Binding flags</param>
            <param name="index">Property indexes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.SetValue(System.Object,System.Object,System.Object)">
            <summary>
            Writes a new property value
            </summary>
            <param name="context">Evaluation context</param>
            <param name="obj">Instance or null if it's a static property</param>
            <param name="value">New value</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.SetValue(System.Object,System.Object,System.Object,System.Object[])">
            <summary>
            Writes a new property value
            </summary>
            <param name="context">Evaluation context</param>
            <param name="obj">Instance or null if it's a static property</param>
            <param name="value">New value</param>
            <param name="index">Property indexes</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.SetValue(System.Object,System.Object,System.Object,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,System.Object[])">
            <summary>
            Writes a new property value
            </summary>
            <param name="context">Evaluation context</param>
            <param name="obj">Instance or null if it's a static property</param>
            <param name="value">New value</param>
            <param name="invokeAttr">Binding flags</param>
            <param name="index">Property indexes</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.Equals(dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName">
            <summary>
            A read only assembly name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.Name">
            <summary>
            Gets the simple name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.Version">
            <summary>
            Gets the version
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.CultureName">
            <summary>
            Gets the culture name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.RawFlags">
            <summary>
            Gets the flags
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.Flags">
            <summary>
            Gets the flags. The content type and processor architecture bits are ignored, use <see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.RawFlags"/> instead
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.ProcessorArchitecture">
            <summary>
            Gets the processor architecture
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.ContentType">
            <summary>
            Gets the content type
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.GetPublicKey">
            <summary>
            Gets the public key
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.GetPublicKeyToken">
            <summary>
            Gets the public key token
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.HashAlgorithm">
            <summary>
            Gets the hash algorithm
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.FullName">
            <summary>
            Gets the full assembly name
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="assemblyName">Assembly name</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.#ctor(System.String,System.Version,System.String,dnSpy.Debugger.DotNet.Metadata.DmdAssemblyNameFlags,System.Byte[],System.Byte[],dnSpy.Debugger.DotNet.Metadata.DmdAssemblyHashAlgorithm)">
            <summary>
            Constructor
            </summary>
            <param name="name">Simple name</param>
            <param name="version">Version</param>
            <param name="cultureName">Culture or null</param>
            <param name="flags">Flags</param>
            <param name="publicKey">Public key or null</param>
            <param name="publicKeyToken">Public key token or null</param>
            <param name="hashAlgorithm">Hash algorithm</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.#ctor(System.String,System.Version,System.String,dnSpy.Debugger.DotNet.Metadata.DmdAssemblyNameFlags,System.Byte[],dnSpy.Debugger.DotNet.Metadata.DmdAssemblyHashAlgorithm)">
            <summary>
            Constructor
            </summary>
            <param name="name">Simple name</param>
            <param name="version">Version</param>
            <param name="cultureName">Culture or null</param>
            <param name="flags">Flags</param>
            <param name="publicKeyOrToken">Public key or public key token or null</param>
            <param name="hashAlgorithm">Hash algorithm</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.#ctor(dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName)">
            <summary>
            Constructor
            </summary>
            <param name="name">Assembly name</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.AsMutable">
            <summary>
            Converts it to a mutable assembly name
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.AsReadOnly">
            <summary>
            Creates a read only assembly name
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdReadOnlyAssemblyName.ToString">
            <summary>
            Gets the full assembly name
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdRuntime">
            <summary>
            A .NET runtime
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdRuntime.YouCantDeriveFromThisClass">
            <summary>
            Dummy abstract method to make sure no-one outside this assembly can create their own <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdRuntime"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdRuntime.PointerSize">
            <summary>
            Gets the size of a pointer in bytes
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdRuntime.Machine">
            <summary>
            Gets the machine
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdRuntime.GetAppDomains">
            <summary>
            Gets all AppDomains
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdRuntime.GetAppDomain(System.Int32)">
            <summary>
            Returns an AppDomain or null if it doesn't exist
            </summary>
            <param name="id">AppDomain id</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdRuntime.CreateAppDomain(System.Int32)">
            <summary>
            Creates an AppDomain
            </summary>
            <param name="id">AppDomain id, must be a unique identifier</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdRuntime.Remove(dnSpy.Debugger.DotNet.Metadata.DmdAppDomain)">
            <summary>
            Removes an AppDomain
            </summary>
            <param name="appDomain">AppDomain to remove</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdRuntimeFactory">
            <summary>
            Creates runtimes
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdRuntimeFactory.CreateRuntime(dnSpy.Debugger.DotNet.Metadata.DmdEvaluator,dnSpy.Debugger.DotNet.Metadata.DmdImageFileMachine)">
            <summary>
            Creates a runtime
            </summary>
            <param name="evaluator">Evaluator</param>
            <param name="machine">Machine</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions">
            <summary>
            Type and member comparer options
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions.None">
            <summary>
            No bit is set
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions.DontCompareTypeScope">
            <summary>
            Don't compare type scope (assembly / module)
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions.CompareDeclaringType">
            <summary>
            Compare declaring type. It's ignored if it's a nested type and only used if it's a field, constructor, method, property, event, parameter
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions.DontCompareReturnType">
            <summary>
            Don't compare return types
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions.CaseInsensitiveMemberNames">
            <summary>
            Case insensitive member names
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions.ProjectWinMDReferences">
            <summary>
            Project WinMD references
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions.CheckTypeEquivalence">
            <summary>
            Check type equivalence
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions.CompareCustomModifiers">
            <summary>
            Compare optional and required C modifiers
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions.CompareGenericParameterDeclaringMember">
            <summary>
            Compare generic type/method parameter's declaring member
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions.IgnoreMultiDimensionalArrayLowerBoundsAndSizes">
            <summary>
            When comparing types, don't compare a multi-dimensional array's lower bounds and sizes
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer">
            <summary>
            Compares types and members
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdSigComparerOptions)">
            <summary>
            Constructor
            </summary>
            <param name="options">Options</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.Equals(dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo,dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo)">
            <summary>
            Compares two members
            </summary>
            <param name="a">First member</param>
            <param name="b">Second member</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.Equals(dnSpy.Debugger.DotNet.Metadata.DmdType,dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Compares two types
            </summary>
            <param name="a">First type</param>
            <param name="b">Second type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.Equals(dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo,dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo)">
            <summary>
            Compares two fields
            </summary>
            <param name="a">First field</param>
            <param name="b">Second field</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.Equals(dnSpy.Debugger.DotNet.Metadata.DmdMethodBase,dnSpy.Debugger.DotNet.Metadata.DmdMethodBase)">
            <summary>
            Compares two methods or constructors
            </summary>
            <param name="a">First method or constructor</param>
            <param name="b">Second method or constructor</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.Equals(dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo,dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo)">
            <summary>
            Compares two properties
            </summary>
            <param name="a">First property</param>
            <param name="b">Second property</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.Equals(dnSpy.Debugger.DotNet.Metadata.DmdEventInfo,dnSpy.Debugger.DotNet.Metadata.DmdEventInfo)">
            <summary>
            Compares two events
            </summary>
            <param name="a">First event</param>
            <param name="b">Second event</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.Equals(dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo,dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo)">
            <summary>
            Compares two method parameters
            </summary>
            <param name="a">First method parameter</param>
            <param name="b">Second method parameter</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.Equals(dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName,dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName)">
            <summary>
            Compares two assembly names
            </summary>
            <param name="a">First assembly name</param>
            <param name="b">Second assembly name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.Equals(dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature,dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature)">
            <summary>
            Compares two method signatures
            </summary>
            <param name="a">First method signature</param>
            <param name="b">Second method signature</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.Equals(dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier,dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier)">
            <summary>
            Compares two custom modifiers
            </summary>
            <param name="a">First custom modifier</param>
            <param name="b">Second custom modifier</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.GetHashCode(dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo)">
            <summary>
            Gets the hash code of a member
            </summary>
            <param name="a">Member</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.GetHashCode(dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Gets the hash code of a type
            </summary>
            <param name="a">Type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.GetHashCode(dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo)">
            <summary>
            Gets the hash code of a field
            </summary>
            <param name="a">Field</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.GetHashCode(dnSpy.Debugger.DotNet.Metadata.DmdMethodBase)">
            <summary>
            Gets the hash code of a method or constructor
            </summary>
            <param name="a">Method or constructor</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.GetHashCode(dnSpy.Debugger.DotNet.Metadata.DmdPropertyInfo)">
            <summary>
            Gets the hash code of a property
            </summary>
            <param name="a">Property</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.GetHashCode(dnSpy.Debugger.DotNet.Metadata.DmdEventInfo)">
            <summary>
            Gets the hash code of an event
            </summary>
            <param name="a">Event</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.GetHashCode(dnSpy.Debugger.DotNet.Metadata.DmdParameterInfo)">
            <summary>
            Gets the hash code of a method parameter
            </summary>
            <param name="a">Method parameter</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.GetHashCode(dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName)">
            <summary>
            Gets the hash code of an assembly name
            </summary>
            <param name="a">Assembly name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.GetHashCode(dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature)">
            <summary>
            Gets the hash code of a method signature
            </summary>
            <param name="a">Method signature</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer.GetHashCode(dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier)">
            <summary>
            Gets the hash code of a custom modifier
            </summary>
            <param name="a">Custom modifier</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdSignatureCallingConvention">
            <summary>
            Signature calling convention flags
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdType">
            <summary>
            A .NET type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.AppDomain">
            <summary>
            Gets the AppDomain
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.MemberType">
            <summary>
            Gets the member type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.TypeSignatureKind">
            <summary>
            Gets the type signature kind
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.TypeScope">
            <summary>
            Gets the type scope. This property is only valid if it's a TypeDef or TypeRef (i.e., not an array, generic instance, etc)
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.ReflectedType">
            <summary>
            Gets the reflected type. This is the type that owns this member, see also <see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.DeclaringType"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.DeclaringMethod">
            <summary>
            Gets the declaring method or null
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.Module">
            <summary>
            Gets the module
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.Assembly">
            <summary>
            Gets the assembly
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.FullName">
            <summary>
            Gets the full name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.Namespace">
            <summary>
            Gets the namespace or null
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.Name">
            <summary>
            Gets the name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.MetadataNamespace">
            <summary>
            Gets the namespace or null. This is the namespace stored in the metadata. <see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdType.Namespace"/>
            is the namespace of the non-declaring type.
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.MetadataName">
            <summary>
            Gets the name stored in the metadata. It's not escaped like <see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdMemberInfo.Name"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.AssemblyQualifiedName">
            <summary>
            Gets the assembly qualified name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.BaseType">
            <summary>
            Gets the base type or null if none
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.StructLayoutAttribute">
            <summary>
            Gets the struct layout attribute
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsNested">
            <summary>
            true if it's a nested type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.GenericParameterAttributes">
            <summary>
            Gets the generic parameter attributes
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsVisible">
            <summary>
            true if this is a public type and all its declaring types are public
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.Attributes">
            <summary>
            Gets the type attributes
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsNullable">
            <summary>
            true if this type is <see cref="T:System.Nullable`1"/>
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetNullableElementType">
            <summary>
            Gets the nullable value type, eg. <see cref="T:System.Int32"/> if it's a nullable <see cref="T:System.Int32"/>
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsSerializable">
            <summary>
            true if it's a serializable type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.HasTypeEquivalence">
            <summary>
            Only used by CanCastTo(). true if it or any of the types it depends on (base type, generic args,
            element type, interfaces) has the TypeIdentifierAttribute or similar attributes that enable type
            equivalency checks.
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.ResolveMember(System.Boolean)">
            <summary>
            Resolves a member reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.Resolve">
            <summary>
            Resolves a type reference and throws if it doesn't exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.ResolveNoThrow">
            <summary>
            Resolves a type reference and returns null if it doesn't exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.Resolve(System.Boolean)">
            <summary>
            Resolves a type reference
            </summary>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.MakePointerType">
            <summary>
            Makes a pointer type
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.MakeByRefType">
            <summary>
            Makes a by-ref type
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.MakeArrayType">
            <summary>
            Makes a SZ array type
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.MakeArrayType(System.Int32)">
            <summary>
            Makes a multi-dimensional array type
            </summary>
            <param name="rank">Number of dimensions</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.MakeArrayType(System.Int32,System.Collections.Generic.IList{System.Int32},System.Collections.Generic.IList{System.Int32})">
            <summary>
            Makes a multi-dimensional array type
            </summary>
            <param name="rank">Number of dimensions</param>
            <param name="sizes">Sizes</param>
            <param name="lowerBounds">Lower bounds</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.MakeGenericType(dnSpy.Debugger.DotNet.Metadata.DmdType[])">
            <summary>
            Makes a generic type
            </summary>
            <param name="typeArguments">Generic arguments</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.MakeGenericType(System.Type[])">
            <summary>
            Makes a generic type
            </summary>
            <param name="typeArguments">Generic arguments</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.MakeGenericType(System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Makes a generic type
            </summary>
            <param name="typeArguments">Generic arguments</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.MakeGenericType(System.Collections.Generic.IList{System.Type})">
            <summary>
            Makes a generic type
            </summary>
            <param name="typeArguments">Generic arguments</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetTypeCode(dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Gets the type code
            </summary>
            <param name="type">Type or null</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.Fields">
            <summary>
            Gets all fields
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.Methods">
            <summary>
            Gets all methods and constructors
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.Properties">
            <summary>
            Gets all properties
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.Events">
            <summary>
            Gets all events
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.DeclaredFields">
            <summary>
            Gets all declared fields
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.DeclaredMethods">
            <summary>
            Gets all declared methods and constructors
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.DeclaredProperties">
            <summary>
            Gets all declared properties
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.DeclaredEvents">
            <summary>
            Gets all declared events
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.NestedTypes">
            <summary>
            Gets all nested types
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(dnSpy.Debugger.DotNet.Metadata.DmdModule,System.Int32)">
            <summary>
            Gets a method or returns null if it doesn't exist
            </summary>
            <param name="module">Module</param>
            <param name="metadataToken">Metadata token</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(dnSpy.Debugger.DotNet.Metadata.DmdModule,System.Int32,System.Boolean)">
            <summary>
            Gets a method
            </summary>
            <param name="module">Module</param>
            <param name="metadataToken">Metadata token</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetField(dnSpy.Debugger.DotNet.Metadata.DmdModule,System.Int32)">
            <summary>
            Gets a field or returns null if it doesn't exist
            </summary>
            <param name="module">Module</param>
            <param name="metadataToken">Metadata token</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetField(dnSpy.Debugger.DotNet.Metadata.DmdModule,System.Int32,System.Boolean)">
            <summary>
            Gets a field
            </summary>
            <param name="module">Module</param>
            <param name="metadataToken">Metadata token</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(dnSpy.Debugger.DotNet.Metadata.DmdModule,System.Int32)">
            <summary>
            Gets a property or returns null if it doesn't exist
            </summary>
            <param name="module">Module</param>
            <param name="metadataToken">Metadata token</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(dnSpy.Debugger.DotNet.Metadata.DmdModule,System.Int32,System.Boolean)">
            <summary>
            Gets a property
            </summary>
            <param name="module">Module</param>
            <param name="metadataToken">Metadata token</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetEvent(dnSpy.Debugger.DotNet.Metadata.DmdModule,System.Int32)">
            <summary>
            Gets an event or returns null if it doesn't exist
            </summary>
            <param name="module">Module</param>
            <param name="metadataToken">Metadata token</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetEvent(dnSpy.Debugger.DotNet.Metadata.DmdModule,System.Int32,System.Boolean)">
            <summary>
            Gets an event
            </summary>
            <param name="module">Module</param>
            <param name="metadataToken">Metadata token</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String,dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature,System.Boolean)">
            <summary>
            Gets a method
            </summary>
            <param name="name">Method name</param>
            <param name="methodSignature">Method signature</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String,dnSpy.Debugger.DotNet.Metadata.DmdSignatureCallingConvention,System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdType,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Boolean)">
            <summary>
            Gets a method
            </summary>
            <param name="name">Method name</param>
            <param name="flags">Method signature flags</param>
            <param name="genericParameterCount">Generic parameter count</param>
            <param name="returnType">Return type or null to ignore it</param>
            <param name="parameterTypes">Parameter types</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String,dnSpy.Debugger.DotNet.Metadata.DmdSignatureCallingConvention,System.Int32,System.Type,System.Collections.Generic.IList{System.Type},System.Boolean)">
            <summary>
            Gets a method
            </summary>
            <param name="name">Method name</param>
            <param name="flags">Method signature flags</param>
            <param name="genericParameterCount">Generic parameter count</param>
            <param name="returnType">Return type or null to ignore it</param>
            <param name="parameterTypes">Parameter types</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String,dnSpy.Debugger.DotNet.Metadata.DmdType,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Boolean)">
            <summary>
            Gets a method
            </summary>
            <param name="name">Method name</param>
            <param name="returnType">Return type or null to ignore it</param>
            <param name="parameterTypes">Parameter types</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String,System.Type,System.Collections.Generic.IList{System.Type},System.Boolean)">
            <summary>
            Gets a method
            </summary>
            <param name="name">Method name</param>
            <param name="returnType">Return type or null to ignore it</param>
            <param name="parameterTypes">Parameter types</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetField(System.String,dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Gets a field
            </summary>
            <param name="name">Name</param>
            <param name="fieldType">Field type</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetField(System.String,System.Type,System.Boolean)">
            <summary>
            Gets a field
            </summary>
            <param name="name">Name</param>
            <param name="fieldType">Field type</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String,dnSpy.Debugger.DotNet.Metadata.DmdMethodSignature,System.Boolean)">
            <summary>
            Gets a property
            </summary>
            <param name="name">Name</param>
            <param name="methodSignature">Method signature</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String,dnSpy.Debugger.DotNet.Metadata.DmdSignatureCallingConvention,System.Int32,dnSpy.Debugger.DotNet.Metadata.DmdType,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType},System.Boolean)">
            <summary>
            Gets a property
            </summary>
            <param name="name">Property name</param>
            <param name="flags">Property signature flags</param>
            <param name="genericParameterCount">Generic parameter count</param>
            <param name="returnType">Return type or null to ignore it</param>
            <param name="parameterTypes">Parameter types</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String,dnSpy.Debugger.DotNet.Metadata.DmdSignatureCallingConvention,System.Int32,System.Type,System.Collections.Generic.IList{System.Type},System.Boolean)">
            <summary>
            Gets a property
            </summary>
            <param name="name">Property name</param>
            <param name="flags">Property signature flags</param>
            <param name="genericParameterCount">Generic parameter count</param>
            <param name="returnType">Return type or null to ignore it</param>
            <param name="parameterTypes">Parameter types</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetEvent(System.String,dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Gets an event
            </summary>
            <param name="name">Name</param>
            <param name="eventHandlerType">Event handler type</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetEvent(System.String,System.Type,System.Boolean)">
            <summary>
            Gets an event
            </summary>
            <param name="name">Name</param>
            <param name="eventHandlerType">Event handler type</param>
            <param name="throwOnError">true to throw if it doesn't exist, false to return null if it doesn't exist</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetConstructor(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,dnSpy.Debugger.DotNet.Metadata.DmdCallingConventions,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Gets a constructor
            </summary>
            <param name="bindingAttr">Binding flags</param>
            <param name="callConvention">Calling convention</param>
            <param name="types">Parameter types</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetConstructor(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,dnSpy.Debugger.DotNet.Metadata.DmdCallingConventions,System.Collections.Generic.IList{System.Type})">
            <summary>
            Gets a constructor
            </summary>
            <param name="bindingAttr">Binding flags</param>
            <param name="callConvention">Calling convention</param>
            <param name="types">Parameter types</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetConstructor(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Gets a constructor
            </summary>
            <param name="bindingAttr">Binding flags</param>
            <param name="types">Parameter types</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetConstructor(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,System.Collections.Generic.IList{System.Type})">
            <summary>
            Gets a constructor
            </summary>
            <param name="bindingAttr">Binding flags</param>
            <param name="types">Parameter types</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetConstructor(System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Gets a public constructor
            </summary>
            <param name="types">Parameter types</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetConstructor(System.Collections.Generic.IList{System.Type})">
            <summary>
            Gets a public constructor
            </summary>
            <param name="types">Parameter types</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetConstructors">
            <summary>
            Gets all public constructors
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetConstructors(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets constructors
            </summary>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.TypeInitializer">
            <summary>
            Gets the type initializer
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,dnSpy.Debugger.DotNet.Metadata.DmdCallingConventions,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Gets a method
            </summary>
            <param name="name">Name</param>
            <param name="bindingAttr">Binding flags</param>
            <param name="callConvention">Calling convention</param>
            <param name="types">Parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,dnSpy.Debugger.DotNet.Metadata.DmdCallingConventions,System.Collections.Generic.IList{System.Type})">
            <summary>
            Gets a method
            </summary>
            <param name="name">Name</param>
            <param name="bindingAttr">Binding flags</param>
            <param name="callConvention">Calling convention</param>
            <param name="types">Parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Gets a method
            </summary>
            <param name="name">Name</param>
            <param name="bindingAttr">Binding flags</param>
            <param name="types">Parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,System.Collections.Generic.IList{System.Type})">
            <summary>
            Gets a method
            </summary>
            <param name="name">Name</param>
            <param name="bindingAttr">Binding flags</param>
            <param name="types">Parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Gets a public static or instance method
            </summary>
            <param name="name">Name</param>
            <param name="types">Parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String,System.Collections.Generic.IList{System.Type})">
            <summary>
            Gets a public static or instance method
            </summary>
            <param name="name">Name</param>
            <param name="types">Parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets a method
            </summary>
            <param name="name">Name</param>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethod(System.String)">
            <summary>
            Gets a public static or instance method
            </summary>
            <param name="name">Name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethods">
            <summary>
            Gets all public static or instance methods
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMethods(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets all methods
            </summary>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetField(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets a field
            </summary>
            <param name="name">Name</param>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetField(System.String)">
            <summary>
            Gets a public static or instance field
            </summary>
            <param name="name">Name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetFields">
            <summary>
            Gets all public static or instance fields
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetFields(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets all fields
            </summary>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetInterface(System.String)">
            <summary>
            Gets an interface
            </summary>
            <param name="name">Name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetInterface(System.String,System.Boolean)">
            <summary>
            Gets an interface
            </summary>
            <param name="name">Name</param>
            <param name="ignoreCase">true if ignore case</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetInterfaces">
            <summary>
            Gets all interfaces
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetEvent(System.String)">
            <summary>
            Gets a public static or instance event
            </summary>
            <param name="name">Name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetEvent(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets an event
            </summary>
            <param name="name">Name</param>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetEvents">
            <summary>
            Gets all public static or instance events
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetEvents(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets all events
            </summary>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,dnSpy.Debugger.DotNet.Metadata.DmdType,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Gets a property
            </summary>
            <param name="name">Name</param>
            <param name="bindingAttr">Binding flags</param>
            <param name="returnType">Return type or null</param>
            <param name="types">Parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags,System.Type,System.Collections.Generic.IList{System.Type})">
            <summary>
            Gets a property
            </summary>
            <param name="name">Name</param>
            <param name="bindingAttr">Binding flags</param>
            <param name="returnType">Return type</param>
            <param name="types">Parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets a property
            </summary>
            <param name="name">Name</param>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String,dnSpy.Debugger.DotNet.Metadata.DmdType,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Gets a public static or instance property
            </summary>
            <param name="name">Name</param>
            <param name="returnType">Return type or null</param>
            <param name="types">Parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String,System.Type,System.Collections.Generic.IList{System.Type})">
            <summary>
            Gets a public static or instance property
            </summary>
            <param name="name">Name</param>
            <param name="returnType">Return type or null</param>
            <param name="types">Parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String,System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdType})">
            <summary>
            Gets a public static or instance property
            </summary>
            <param name="name">Name</param>
            <param name="types">Parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String,System.Collections.Generic.IList{System.Type})">
            <summary>
            Gets a public static or instance property
            </summary>
            <param name="name">Name</param>
            <param name="types">Parameter types or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String,dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Gets a public static or instance property
            </summary>
            <param name="name">Name</param>
            <param name="returnType">Return type or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String,System.Type)">
            <summary>
            Gets a public static or instance property
            </summary>
            <param name="name">Name</param>
            <param name="returnType">Return type or null</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperty(System.String)">
            <summary>
            Gets a public static or instance property
            </summary>
            <param name="name">Name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperties(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets all properties
            </summary>
            <param name="bindingAttr">Bindig flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetProperties">
            <summary>
            Gets all public static or instance properties
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetNestedTypes">
            <summary>
            Gets all public nested types
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetNestedTypes(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets all nested types
            </summary>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetNestedType(System.String)">
            <summary>
            Gets a public nested type
            </summary>
            <param name="name">Name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetNestedType(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets a nested type
            </summary>
            <param name="name">Name</param>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMember(System.String)">
            <summary>
            Gets a public static or instance member
            </summary>
            <param name="name">Name</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMember(System.String,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets a public static or instance member
            </summary>
            <param name="name">Name</param>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMember(System.String,dnSpy.Debugger.DotNet.Metadata.DmdMemberTypes,dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets members
            </summary>
            <param name="name">Name</param>
            <param name="type">Member type</param>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMembers">
            <summary>
            Gets all public static or instance members
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetMembers(dnSpy.Debugger.DotNet.Metadata.DmdBindingFlags)">
            <summary>
            Gets all members
            </summary>
            <param name="bindingAttr">Binding flags</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetDefaultMembers">
            <summary>
            Gets all default members
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetArrayRank">
            <summary>
            Gets the number of dimensions if this is an array
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetArraySizes">
            <summary>
            Gets the array sizes of each dimension of an array. The returned list could
            have less elements than the rank of the array.
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetArrayLowerBounds">
            <summary>
            Gets the lower bounds of each dimension of an array. The returned list could
            have less elements than the rank of the array.
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsArray">
            <summary>
            true if it's an array (SZ array or MD array)
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsSZArray">
            <summary>
            true if it's an SZ array
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsVariableBoundArray">
            <summary>
            true if it's a multi-dimensional array
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsGenericType">
            <summary>
            true if it's a generic type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsGenericTypeDefinition">
            <summary>
            true if it's a generic type definition
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsConstructedGenericType">
            <summary>
            true if it's a constructed generic type. These types can be instantiated.
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsGenericParameter">
            <summary>
            true if it's a generic type or method parameter
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.GenericParameterPosition">
            <summary>
            Gets the generic parameter position if this is a generic parameter
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsTypeDefinition">
            <summary>
            true if it's a non constructed type with a TypeDef token
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsGenericTypeParameter">
            <summary>
            true if it's a generic type parameter
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsGenericMethodParameter">
            <summary>
            true if it's a generic method parameter
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.ContainsGenericParameters">
            <summary>
            true if this type contains generic parameters
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetGenericParameterConstraints">
            <summary>
            Gets all generic parameter constraints
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsByRef">
            <summary>
            true if this is a by-ref type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsPointer">
            <summary>
            true if this is a pointer type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsFunctionPointer">
            <summary>
            true if this is a function pointer type
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetFunctionPointerMethodSignature">
            <summary>
            Gets the method signature if this is a function pointer type
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsPrimitive">
            <summary>
            true if this is a primitive type (<see cref="T:System.Boolean"/>, <see cref="T:System.Char"/>, <see cref="T:System.SByte"/>,
            <see cref="T:System.Byte"/>, <see cref="T:System.Int16"/>, <see cref="T:System.UInt16"/>, <see cref="T:System.Int32"/>, <see cref="T:System.UInt32"/>,
            <see cref="T:System.Int64"/>, <see cref="T:System.UInt64"/>, <see cref="T:System.Single"/>, <see cref="T:System.Double"/>, <see cref="T:System.IntPtr"/>,
            <see cref="T:System.UIntPtr"/>)
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsCOMObject">
            <summary>
            true if it's a COM object
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.HasElementType">
            <summary>
            true if it has an element type, i.e., it's an array, a by-ref or a pointer type
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsContextful">
            <summary>
            true if it's a <see cref="T:System.ContextBoundObject"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsMarshalByRef">
            <summary>
            true if it's a <see cref="T:System.MarshalByRefObject"/>
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetWellKnownType">
            <summary>
            Gets the <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdWellKnownType"/> value or <see cref="F:dnSpy.Debugger.DotNet.Metadata.DmdWellKnownType.None"/> if it's not a well known type
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetElementType">
            <summary>
            Gets the element type if it's an array, a by-ref or a pointer type
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetGenericArguments">
            <summary>
            Gets the generic arguments
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.GenericTypeArguments">
            <summary>
            Gets all generic arguments if it's a constructed generic type (<see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsConstructedGenericType"/>)
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetGenericTypeDefinition">
            <summary>
            Gets the generic type definition if it's a generic type
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetRequiredCustomModifiers">
            <summary>
            Gets all required custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetOptionalCustomModifiers">
            <summary>
            Gets all optional custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetCustomModifiers">
            <summary>
            Gets all custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.WithCustomModifiers(System.Collections.Generic.IList{dnSpy.Debugger.DotNet.Metadata.DmdCustomModifier})">
            <summary>
            Returns a type with the specified custom modifiers
            </summary>
            <param name="customModifiers">New custom modifiers</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.WithoutCustomModifiers">
            <summary>
            Returns a type without custom modifiers
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetEnumNames">
            <summary>
            Returns the names of the members of the enum type
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetEnumUnderlyingType">
            <summary>
            Gets the underlying type of an enum (a primitive type)
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.IsSubclassOf(dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Returns true if this instance derives from <paramref name="type"/>. Also returns
            true if this type is an interface and <paramref name="type"/> is <see cref="T:System.Object"/>.
            </summary>
            <param name="type">Other type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.IsSubclassOf(System.Type)">
            <summary>
            Returns true if this instance derives from <paramref name="type"/>. Also returns
            true if this type is an interface and <paramref name="type"/> is <see cref="T:System.Object"/>.
            </summary>
            <param name="type">Other type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.IsAssignableFrom(dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Returns true if an instance of <paramref name="c"/> can be assigned to an instance of this type
            </summary>
            <param name="c"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.IsAssignableFrom(System.Type)">
            <summary>
            Returns true if an instance of <paramref name="c"/> can be assigned to an instance of this type
            </summary>
            <param name="c"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.CanCastTo(dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Returns true if it's possible to cast this type to <paramref name="target"/>
            </summary>
            <param name="target">Target type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.CanCastTo(System.Type)">
            <summary>
            Returns true if it's possible to cast this type to <paramref name="target"/>
            </summary>
            <param name="target">Target type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.IsEquivalentTo(dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Returns true if this type is equivalent to <paramref name="other"/>
            </summary>
            <param name="other">Other types</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.IsEquivalentTo(System.Type)">
            <summary>
            Returns true if this type is equivalent to <paramref name="other"/>
            </summary>
            <param name="other">Other types</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdType.IsByRefLike">
            <summary>
            true if this is a by-ref like value type
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.IsDefined(System.String,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.IsDefined(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.FindCustomAttribute(System.String,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.FindCustomAttribute(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.Equals(dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Equals()
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.Equals(System.Object)">
            <summary>
            Equals()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.GetHashCode">
            <summary>
            GetHashCode()
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdType.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdTypeAttributes">
            <summary>
            Type attributes
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeName.Namespace">
            <summary>
            Namespace or null
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeName.Name">
            <summary>
            Name
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeName.Extra">
            <summary>
            Nested type names, separated with '+'
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdTypeName.#ctor(System.String,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="namespace">Namespace or null</param>
            <param name="name">Name</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdTypeName.#ctor(System.String,System.String,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="namespace">Namespace or null</param>
            <param name="name">Name</param>
            <param name="extra">Nested type names, separated with '+'</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdTypeName.Create(dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Creates a <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdTypeName"/>
            </summary>
            <param name="type">Type</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdTypeName.ToString">
            <summary>
            Gets the type name
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdTypeNameEqualityComparer">
            <summary>
            <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdTypeName"/> equality comparer
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeNameEqualityComparer.Instance">
            <summary>
            Gets the single instance
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdTypeNameEqualityComparer.Equals(dnSpy.Debugger.DotNet.Metadata.DmdTypeName,dnSpy.Debugger.DotNet.Metadata.DmdTypeName)">
            <summary>
            Equals()
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdTypeNameEqualityComparer.GetHashCode(dnSpy.Debugger.DotNet.Metadata.DmdTypeName)">
            <summary>
            GetHashCode()
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdTypeScopeKind">
            <summary>
            Type scope kind
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeScopeKind.Invalid">
            <summary>
            It's not a TypeDef or TypeRef so it doesn't have a type scope
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeScopeKind.Module">
            <summary>
            Same module as the reference
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeScopeKind.ModuleRef">
            <summary>
            A reference to another module in the same assembly
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeScopeKind.AssemblyRef">
            <summary>
            A reference to another assembly
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdTypeScope">
            <summary>
            A <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdType"/> scope
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeScope.Invalid">
            <summary>
            An instance whose <see cref="P:dnSpy.Debugger.DotNet.Metadata.DmdTypeScope.Kind"/> equals <see cref="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeScopeKind.Invalid"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdTypeScope.Kind">
            <summary>
            Gets the kind
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdTypeScope.Data">
            <summary>
            Gets the data: <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdModule"/>, <see cref="T:System.String"/> (<see cref="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeScopeKind.ModuleRef"/>), <see cref="T:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName"/>
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdTypeScope.Data2">
            <summary>
            Used if it's a module reference. This is the assembly name (<see cref="T:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName"/>)
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdTypeScope.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdModule)">
            <summary>
            Constructor
            </summary>
            <param name="module">Module</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdTypeScope.#ctor(dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="assembly">Assembly</param>
            <param name="moduleName">Module name</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdTypeScope.#ctor(dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName)">
            <summary>
            Constructor
            </summary>
            <param name="assemblyRef">Assembly reference</param>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdTypeScope.ToString">
            <summary>
            ToString()
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdTypeSignatureKind">
            <summary>
            Type signature code
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeSignatureKind.Type">
            <summary>
            A normal type (TypeDef or TypeRef)
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeSignatureKind.Pointer">
            <summary>
            Pointer type
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeSignatureKind.ByRef">
            <summary>
            By-ref type
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeSignatureKind.TypeGenericParameter">
            <summary>
            Generic parameter (type)
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeSignatureKind.MethodGenericParameter">
            <summary>
            Generic parameter (method)
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeSignatureKind.SZArray">
            <summary>
            SZ array type
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeSignatureKind.MDArray">
            <summary>
            Multi-dimensional array type
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeSignatureKind.GenericInstance">
            <summary>
            Generic instance type
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.DmdTypeSignatureKind.FunctionPointer">
            <summary>
            Function pointer type
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdWellKnownType">
            <summary>
            Well known types
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.DmdWellKnownTypeUtils">
            <summary>
            <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdWellKnownType"/> utils
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdWellKnownTypeUtils.TryGetWellKnownType(dnSpy.Debugger.DotNet.Metadata.DmdTypeName@,dnSpy.Debugger.DotNet.Metadata.DmdWellKnownType@)">
            <summary>
            Gets the well known type
            </summary>
            <param name="name">Name</param>
            <param name="wellKnownType">Updated with well known type if successful</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.DmdWellKnownTypeUtils.GetTypeName(dnSpy.Debugger.DotNet.Metadata.DmdWellKnownType)">
            <summary>
            Gets the name
            </summary>
            <param name="wellKnownType">Well known type</param>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.DmdWellKnownTypeUtils.WellKnownTypesCount">
            <summary>
            Gets the number of well known types
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName">
            <summary>
            A read only assembly name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.Name">
            <summary>
            Gets the simple name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.Version">
            <summary>
            Gets the version
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.CultureName">
            <summary>
            Gets the culture name
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.RawFlags">
            <summary>
            Gets the flags
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.Flags">
            <summary>
            Gets the flags. The content type and processor architecture bits are ignored, use <see cref="P:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.RawFlags"/> instead
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.ProcessorArchitecture">
            <summary>
            Gets the processor architecture
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.ContentType">
            <summary>
            Gets the content type
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.GetPublicKey">
            <summary>
            Gets the public key
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.GetPublicKeyToken">
            <summary>
            Gets the public key token
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.HashAlgorithm">
            <summary>
            Gets the hash algorithm
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.FullName">
            <summary>
            Gets the full assembly name
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.IDmdAssemblyName.AsReadOnly">
            <summary>
            Creates a read only assembly name
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.IDmdCustomAttributeProvider">
            <summary>
            Implemented by classes that can have custom attributes, eg. types, members, parameters, assemblies, modules
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.IDmdCustomAttributeProvider.CustomAttributes">
            <summary>
            Gets the custom attributes
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.IDmdCustomAttributeProvider.GetCustomAttributesData">
            <summary>
            Gets the custom attributes
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.IDmdCustomAttributeProvider.IsDefined(System.String,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.IDmdCustomAttributeProvider.IsDefined(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.IDmdCustomAttributeProvider.IsDefined(System.Type,System.Boolean)">
            <summary>
            Checks if a custom attribute is present
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.IDmdCustomAttributeProvider.FindCustomAttribute(System.String,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeTypeFullName">Full name of the custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.IDmdCustomAttributeProvider.FindCustomAttribute(dnSpy.Debugger.DotNet.Metadata.DmdType,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.IDmdCustomAttributeProvider.FindCustomAttribute(System.Type,System.Boolean)">
            <summary>
            Finds a custom attribute
            </summary>
            <param name="attributeType">Custom attribute type</param>
            <param name="inherit">true to check custom attributes in all base classes</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.IDmdSecurityAttributeProvider">
            <summary>
            Implemented by classes that can have security attributes, eg. assemblies, types, constructors, methods
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.IDmdSecurityAttributeProvider.SecurityAttributes">
            <summary>
            Gets the security attributes
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.IDmdSecurityAttributeProvider.GetSecurityAttributesData">
            <summary>
            Gets the security attributes
            </summary>
            <returns></returns>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.Impl.DmdTypesUpdatedEventArgs.Tokens">
            <summary>
            Tokens of updated types
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.Impl.DmdTypeBase.IsFullyResolved">
            <summary>
            true if there are no metadata references. This instance and any other <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdType"/> that it
            references directly or indirectly (element type, generic arguments) are all resolved types
            (TypeDefs, and not TypeRefs).
            
            Even if this property is true, it could still have metadata references:
            optional/required modifiers, base type, types in custom attributes, etc could
            contain one or more TypeRefs.
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.Impl.DmdTypeBase.FullResolve">
            <summary>
            Resolves a type whose <see cref="P:dnSpy.Debugger.DotNet.Metadata.Impl.DmdTypeBase.IsFullyResolved"/> property is true or returns null if the resolve failed
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.Impl.DmdTypeBase.DynamicType_InvalidateCachedNestedTypes">
            <summary>
            Invalidates all cached nested types.
            
            Used by dynamic modules when the debugger sends a LoadClass event.
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.Impl.DmdTypeBase.DynamicType_InvalidateCachedMembers">
            <summary>
            Invalidates all cached collections of types and members.
            
            Used by dynamic modules when the debugger sends a LoadClass event.
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.Impl.DmdTypeBase.MemberInfoKind.Visible">
            <summary>
            Normal visible member
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.Impl.DmdTypeBase.MemberInfoKind.Overridden">
            <summary>
            Overridden by a derived class
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.Impl.DmdTypeBase.MemberInfoKind.Inaccessible">
            <summary>
            Method is private in base class and thus not accessible by the current type, so it's hidden
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.Impl.DmdTypeBase.MemberInfoKind.BaseConstructor">
            <summary>
            Constructor in base class
            </summary>
        </member>
        <member name="F:dnSpy.Debugger.DotNet.Metadata.Impl.DmdTypeBase.MemberInfoKind.VtblGap">
            <summary>
            VtblGap method
            </summary>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.MemberNotFoundException">
            <summary>
            Thrown when a type or a member couldn't be found
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.MemberNotFoundException.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="message">Message</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.TypeNotFoundException">
            <summary>
            Thrown when a type couldn't be found
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.TypeNotFoundException.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="typeName">Type that couldn't be found</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.FieldNotFoundException">
            <summary>
            Thrown when a field couldn't be found
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.FieldNotFoundException.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="fieldName">Field that couldn't be found</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.MethodNotFoundException">
            <summary>
            Thrown when a method couldn't be found
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.MethodNotFoundException.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="methodName">Method that couldn't be found</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.PropertyNotFoundException">
            <summary>
            Thrown when a property couldn't be found
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.PropertyNotFoundException.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="propertyName">Property that couldn't be found</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.EventNotFoundException">
            <summary>
            Thrown when an event couldn't be found
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.EventNotFoundException.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="eventName">Event that couldn't be found</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.ResolveException">
            <summary>
            Thrown when a type or member couldn't be resolved
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.ResolveException.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="message">Message</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.TypeResolveException">
            <summary>
            Thrown when a type couldn't be resolved
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.TypeResolveException.Type">
            <summary>
            Gets the type that couldn't be resolved
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.TypeResolveException.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Constructor
            </summary>
            <param name="type">Type that couldn't be resolved</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.FieldResolveException">
            <summary>
            Thrown when a field couldn't be resolved
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.FieldResolveException.Field">
            <summary>
            Gets the field that couldn't be resolved
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.FieldResolveException.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdFieldInfo)">
            <summary>
            Constructor
            </summary>
            <param name="field">Field that couldn't be resolved</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.MethodResolveException">
            <summary>
            Thrown when a method couldn't be resolved
            </summary>
        </member>
        <member name="P:dnSpy.Debugger.DotNet.Metadata.MethodResolveException.Method">
            <summary>
            Gets the method that couldn't be resolved
            </summary>
        </member>
        <member name="M:dnSpy.Debugger.DotNet.Metadata.MethodResolveException.#ctor(dnSpy.Debugger.DotNet.Metadata.DmdMethodBase)">
            <summary>
            Constructor
            </summary>
            <param name="method">Method that couldn't be resolved</param>
        </member>
        <member name="T:dnSpy.Debugger.DotNet.Metadata.TIAHelper">
            <summary>
            <c>System.Runtime.InteropServices.TypeIdentifierAttribute</c> helper code used by <see cref="T:dnSpy.Debugger.DotNet.Metadata.DmdSigComparer"/>
            </summary>
        </member>
    </members>
</doc>
