﻿@using System.Data
@using AppTech.MSMS.Web.Security
@Styles.Render("~/Content/print")
<body class="no-skin rtl ">
<div class="main-content">
    <div class="main-content-inner">
        <div class="page-content">

            <div id="page-wrap">


                <div id="calisha" style="margin-top: 20px" class="align-center">


                    <img id="receipt_logo" class="align-center" src="@Url.Content("~/Photos/calisha.jpeg")" alt="logo" style="margin-left: 90px"/>


                </div>

                <div id="report_title" class="reportheader col-sm-6 col-xs-6" style="text-align: center">
                    @ViewBag.Title
                </div>
                <div id="divReport" style="margin-left: 5px; margin-right: 5px;">
                    <div>
                        <hr/>

                        <table id="dynamic-table" class="table table-striped table-bordered table-hover">

                            <thead>
                            <tr>

                                @foreach (DataColumn col in Model.Columns)
                                {
                                    <th>@col.Caption</th>
                                }


                            </tr>
                            </thead>

                            <tbody>

                            @foreach (DataRow row in Model.Rows)
                            {
                                <tr>

                                    @foreach (var cell in row.ItemArray)
                                    {
                                        <td>@cell.ToString()</td>
                                    }
                                </tr>
                            }

                            </tbody>
                        </table>
                    </div>
                </div>


                @*   <hr />*@
                <div id="terms">
                    @* <h5></h5>*@
                    <textarea>المستخدم: @CurrentUser.CurrentSession.User.Name</textarea>
                </div>

            </div>
        </div>
    </div>
</div>
</body>