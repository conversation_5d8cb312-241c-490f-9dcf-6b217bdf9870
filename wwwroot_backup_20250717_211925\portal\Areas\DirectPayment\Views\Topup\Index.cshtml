﻿@{
    ViewBag.Title = "التحصيلات";
    Layout = "~/Views/Shared/_CrudLayout.cshtml";
}
@*<div class="btn-group" style="margin-top:20px;">
        <a class="btn btn-primary btn-info btn-bold btn-block" href="/#!/route/clients/Charging">تسديد مباشر</a>
    </div>*@

<div class="search-area well well-sm" style="margin-top:10px">
    <div class="row form-group" id="searchLine" style="display:block">
        <div class="col-md-3 ">
            <select id="Status" name="Status" style="width:100%;" class="input-small">
                <option value="10">كافة الحالات</option>
                <option value="1">مرحلة</option>
                <option value="2">معلقة</option>
                <option value="0">ملغية</option>
                <option value="6">معكوسة</option>
                <option value="3">قيد التنفيذ</option>
            </select>
        </div>
        <div class="col-md-3 ">
            <select id="Type" name="Type" style="width:100%;" class="input-small"></select>
        </div>
        <div class="col-md-3 ">
            <select id="Provider" name="Provider" style="width:100%;" class="input-small"></select>
        </div>
        <div class="col-md-3 ">
            <input type="number" name="Phone" id="Phone" min="0" placeholder="رقم المشترك" style="width:68%;" class="input-small"/>
        </div>
    </div>
  
    
    <div class="row form-group" id="searchLine" style="display: block">
       
        <div class="col-md-3 ">
            <label>التاريخ</label>
            <input type="date" name="Date"  id="Date" placeholder="التاريخ" style="width:70%;" class="input-small"/>
        </div>
          
        <div class="col-md-3 ">
            <label>الى </label>
            <input type="date" name="ToDate" id="ToDate" placeholder="التاريخ" style="width:70%;" class="input-small"/>
        </div>

        <div class="col-md-3 ">
            <button class="btn btn-white btn-info btn-bold btn-round" id="search1" style="width: 30%;">بحث</button>
        </div>
        <div class="col-md-3 ">
        </div>
    </div>
</div>
    <script>
        $(function () {
            loadDataTypeList();
            function loadDataTypeList() {
                fillDataList('Type', '/DirectPayment/Topup/GetTypes', false, "كافة الاصناف")
            }
        });
        $(function () {
            loadDataCurrencyList();
            function loadDataCurrencyList() {
                fillDataList('Provider', '/DirectPayment/Topup/GetProviders', false, "كافة المزودين")
            }
        });

        $(function () {
            $("#search1").on("click",
                function (e) {
                    //   e.p
                    var Status = $("#Status").val();
                    var Type = $("#Type").val();
                    var Provider = $("#Provider").val();
                    var Phone = $("#Phone").val();
                    var Date = $("#Date").val();
                    var ToDate = $("#ToDate").val();
                    var pageSize = $("#pageSize").val();

                    i(' Status :' + Status);

                    var data = {
                        pageSize: pageSize,
                        Status: Status,
                        Type: Type,
                        Provider: Provider,
                        Date: Date,
                        ToDate: ToDate,
                        Phone: Phone
                    };
                    showLoading();
                    AjaxCall('DirectPayment/Topup/Searchs', data).done(function (response) {
                        //    resetButton();
                        //    i('search response' +response)
                        hideLoading();
                        $("#list").replaceWith(response);
                        var pager = Patterns.Art.Pager;
                        pager.activateList();
                        //  ar(response);
                    }).fail(function (xhr, textStatus, errorThrown) {
                        hideLoading();
                        //   resetButton();
                        parseAndShowError(xhr, textStatus, errorThrown);

                    });
                    //$.ajax({
                    //    method: "POST",
                    //    url: 'DirectPayment/Topup/Searchs',
                    //    data:data,
                    //    success: {},
                    //    error: {
                    //    }
                    //});


                });

        });
    </script>
