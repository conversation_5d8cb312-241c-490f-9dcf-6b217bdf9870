﻿@model AppTech.MSMS.Domain.Models.Journal
@{

}
<form id="form">
    <div class="form-horizontal">
        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })





        <div class="form-group">
            <label class="col-sm-2 control-label no-padding-right" for="HeaderDate"> رقم القيد</label>
            <div class="col-md-3">
                <input type="number" name="Number" value="@Model.Number" id="HeaderNumber" />
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label no-padding-right" for="HeaderDate"> التاريخ</label>
            <div class="col-md-3">
                <input type="text" class="date-picker" name="Date" value="@Model.Date.ToShortDateString()" id="HeaderDate" />
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label no-padding-right" for="HeaderDate"> العملة</label>
            <div class="col-md-3">
                @Html.DropDownListFor(m => m.CurrencyID, (SelectList)ViewBag.Currencies)
                @*<select style="width: 160px;" id="CurrencyID" name="CurrencyID"></select>*@
            </div>
        </div>

        @*<div class="form-group">
                <label class="col-sm-2 control-label no-padding-right" for="HeaderDate"> ملاحظات</label>
                <div class="col-md-3">
                    <input type="text" name="HeaderNote" placeholder="ملاحظة" id="HeaderNote" />
                </div>
            </div>
            <br /><br />*@

        <button type='button' class="btn btn-white btn-round" id='add' onclick='myfunction()'>
            <i class="ace-icon fa fa-plus bigger-110"></i>
            اضافة سجل
        </button>
        <button type='button' class="btn btn-white btn-round" id='setSelectedAccount'>
            <i class="ace-icon fa fa-plus bigger-110"></i>
            setSelectedAccount
        </button>
        <table class="table table-responsive table-hover" id="detailsTable">
            <thead>
            <tr style="background:gray">

                <th>

                </th>
                <th>
                    الحساب
                </th>
                <th>
                    نوع القيد
                </th>
                <th>
                    المبلغ
                </th>

                <th>
                    ملاحظات
                </th>
            </tr>
            </thead>
            <tbody>
            @if (Model.ID == 0)
            {
                <tr>
                    <td>
                        <a class="fa fa-trash-o remove-row"></a>
                    </td>
                    <td>
                        <select id="AccountID" name="AccountID" class="select2" style="width: 110px;" placeholder="choose aacounts" required></select>
                        @*<select style="width: 110px;" id="AccountID" name="AccountID" required></select>*@
                    </td>
                    <td>
                        <select style="width: 65px;" id="Account" name="Account" onchange='countTotal()' required>
                            <option value="0">من حـــ</option>
                            <option value="1">الى حـــ</option>
                        </select>
                    </td>
                    <td>
                        <input type='number' name='Amount' onchange='countTotal()' id='Amount' value='0' min='0' step='0.01' required />
                    </td>
                    <td>
                        <input type='text' name='Note' placeholder='ملاحظة' id='Note' />
                    </td>

                </tr>
            }
            else
            {

                foreach (var item in Model.JournalEntries)
                {

                    <tr>

                        <td>
                            <script>
                                i('accountid attach listener');
                                $('#detailsTable').trigger('rowAddOrRemove');
                            </script>
                            <a class="fa fa-trash-o remove-row"></a>
                        </td>
                        <td>
                            <select id="AccountID" name="AccountID" class="select2" style="width: 110px;" placeholder="choose aacounts" required></select>
                            <input type="hidden" id="AccountIDHolder" value="@item.AccountID" />
                            @*<select style="width: 110px;" id="AccountID" name="AccountID" required></select>*@

                        </td>
                        <td>
                            <select style="width: 65px;" id="Account" name="Account" onchange='countTotal()' required>
                                @if (item.Amount < 0)
                                {
                                    <option value="0" selected>من حـــ</option>
                                    <option value="1">الى حـــ</option>
                                }
                                else
                                {
                                    <option value="0">من حـــ</option>
                                    <option value="1" selected>الى حـــ</option>
                                }
                            </select>
                        </td>

                        <td>
                            <input type='number' value="@Math.Abs(item.Amount)" name='Amount' onchange='countTotal()' id='Amount' min='0' step='0.01' required />
                        </td>

                        <td>
                            <input type='text' name='Note' value="@item.Note" id='Note' />
                        </td>

                    </tr>
                }
            }
            </tbody>
            <tfoot>
            <tr style="background:lightgray">
                <td colspan="5">
                    إجمالي المدين
                    <input style=" width: 150px;" type='number' step='0.01' id='fromTotal' disabled />
                    إجمالي الدائن
                    <input style=" width: 150px;" type='number' step='0.01' id='toTotal' disabled />
                    الرصيد
                    <input style=" width: 200px;" type='number' step='0.01' value="" id='Total' disabled />
                </td>
            </tr>
            </tfoot>
        </table>
        
        <div class="modal-footer">
            <button class="btn btn-primary btn-info btn-bold loading pull-right btn-round" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار" type="submit" id="submit" value="submit">
                <i class="ace-icon fa fa-save bigger-110"></i>
                حفظ
            </button>
            <button type="button" id="cancelModal" class="btn btn-danger btn-round "><i class="glyphicon glyphicon-remove"></i> إلغاء</button>
        </div>
       
    </div>
</form>

<script>

    $(function () {
        $('#setSelectedAccount').hide();
        if(Number(@Model.ID)>0)
        {
            countTotal();
            $.each($("#detailsTable tbody tr"), function () {
                i('each tb');
                var selectAccount = $(this).find("#AccountID");
                var AccountIDHolder = $(this).find("#AccountIDHolder").val();
                    i(' AccountIDHolder'+AccountIDHolder);
                    fillListByElement(selectAccount, AccountIDHolder, '/GeneralLedger/DoubleEntryBond/GetAccounts','', function () {
                        initAutoSearch();
                    });
            });
        }
        else {
            $("#HeaderDate").val(getToday());
            loadFirstList();
        }
        $("#detailsTable").on("click",
            ".remove-row",
            function () {
                    var table = $(this);
                    table.parents("tr").fadeOut(1000,
                        function () {
                            $(this).remove();
                            countTotal();
                        });
            });

        $('#cancelModal').on('click',
            function() {
                closeDialog();
            });
    });
    function loadFirstList() {
        i('loadDataList');
        fillDataList('AccountID', '/GeneralLedger/DoubleEntryBond/GetAccounts');
             initAutoSearch();
    }
    function myfunction() {
        var table = $("#detailsTable tbody");
        var rowCount = $('#detailsTable tr').length-1;
        var innerHTML = "<tr>";
        innerHTML += "<td> <a class='fa fa-trash-o remove-row'></a></td>";
        innerHTML += "<td><select id='AccountID' name='AccountID' class='select2 AccountID' style='width: 110px;'></select></td>";
        innerHTML +='<td><select style="width: 65px;" id="Account" name="Account" onchange="countTotal()" required><option value="0">من حـــ</option><option value="1">الى حـــ</option></select></td>';
        innerHTML += "<td><input type='number' name='Amount' onchange='countTotal()' id='Amount' value='0' step='0.01' min='0' required/></td>";
        innerHTML += "<td><input type='text' name='Note' placeholder='ملاحظة' id='Note'/></td>";
        innerHTML += "</tr>";
        table.append(innerHTML);
        var currentRow = $('#detailsTable').find('tr').eq(rowCount);
        var selectAccount = currentRow.find("#AccountID");
        fillListByElement(selectAccount, 0, '/GeneralLedger/DoubleEntryBond/GetAccounts');
        initAutoSearch();
    };

    function countTotal() {
        i('countTotal');
    var from =  0;
    var to = 0;
    var total = 0;

    $.each($("#detailsTable tbody tr"), function () {
        i('countTotal each row');
        var type = Number($(this).find('td:eq(2) option:selected').val());
        i('countTotal type ' + type);
        if (type === 0) {
            var madeen = parseFloat($(this).find('td:eq(3) input[type="number"]').val());
            i('countTotal madeen ' + madeen);
            from += madeen;
        } else if (type === 1) {

            var dain = parseFloat($(this).find('td:eq(3) input[type="number"]').val());
            i('countTotal dain ' + dain);
            to += dain;
        }
    });

    total = from - to;

    i('from:' + from + ' to:' + to);

    $("#fromTotal").val(from);
        $("#toTotal").val(to);
        $("#Total").val(total);
};

    $(function () {
        i('form load');
      $("#form").submit(function(event) {
            i('  onsubmit');
            event.preventDefault();

                if ($("#Total").val() == 0) {
                var Entry = [];
                Entry.length = 0;
                 $.each($("#detailsTable tbody tr"), function () {
                     Entry.push({
                    AccountID: $(this).find('td:eq(1) option:selected').val(),
                    CostCenterID: $(this).find('td:eq(2) option:selected').val(),
                    Amount: $(this).find('td:eq(3) input[type="number"]').val(),
                    Note: $(this).find('td:eq(4) input[type="text"]').val()
                });
                 });

                 var data = JSON.stringify({
                     ID:@Model.ID,
                    Date: $("#HeaderDate").val(),
                    CurrencyID: $("#CurrencyID").val(),
                    Note: $("#HeaderNote").val(),
                    JournalEntries: Entry
                });

                $.ajax({
                    method: "POST",
                    url: 'GeneralLedger/DoubleEntryBond/AddOrEditEntry',
                   contentType: "application/json",
                    data: data ,
                success: function (data) {
            showSuccess(data.Message);
                    closeDialog();

            fetchData(false);
                },
                error: function (xhr, textStatus, errorThrown) {
                    parseAndShowError(xhr, textStatus, errorThrown)
                }
                });

                } else {
                    alert(" يجب ان يكون القيد متزن");
                }

        });

    });
  //  initAutoSearch();
    function initAutoSearch() {
        i('initAutoSearch');
        $('.select2').css('width', '200px').select2({ allowClear: false });
        $('#select2-multiple-style .btn').on('click',
            function (e) {
                var target = $(this).find('input[type=radio]');
                var which = parseInt(target.val());
                if (which == 2) $('.select2').addClass('tag-input-style');
                else $('.select2').removeClass('tag-input-style');
            });
    }
</script>