﻿@using AppTech.MSMS.Web.Security
<div class="alert alert-block alert-success">
    <button type="button" class="close" data-dismiss="alert">
        <i class="ace-icon fa fa-times"></i>
    </button>
    @*<h4>مرحباً <strong class="green">@CurrentUser.CurrentSession.Party.Name </strong></h4>*@
    <strong class="green">
        رصيدك الحالي
    </strong>,
    @ViewBag.Balance <span> ريال يمني </span>
 

</div>
<div class="hr hr32 hr-dotted"></div>

<div class="row">
    <div class="space-6"></div>
    <div class="profile-user-info profile-user-info-striped">


        <div class="profile-info-row">
            <div class="profile-info-name">رقم الحساب </div>

            <div class="profile-info-value">
                <span> @CurrentUser.CurrentSession.Party.Number</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> اسم الحساب </div>

            <div class="profile-info-value">
                <span> @CurrentUser.CurrentSession.Party.Name </span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> الهاتف </div>

            <div class="profile-info-value">
                <span> @CurrentUser.CurrentSession.Party.PhoneNumber</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> العنوان </div>

            <div class="profile-info-value">
                <span> @CurrentUser.CurrentSession.Party.Address</span>
            </div>
        </div>


    </div>

</div>
@*<div class="hr hr32 hr-dotted"></div>
<div class="row">
    <div class="space-6"></div>
    <div class="col-sm-5">
        <div class="widget-box">
            <div class="widget-header widget-header-flat widget-header-small">
                <h5 class="widget-title">
                    <i class="ace-icon fa fa-signal"></i>
                    الطلبات
                </h5>
            </div>
            <div class="widget-body">
                <div class="widget-main">
                    <div id="piechart-placeholder"></div>

                    <div class="hr hr8 hr-double"></div>

                    <div class="clearfix">
                        <div class="grid3">
                            <span class="grey">
                                &nbsp; طلبات جاهزه
                            </span>
                            <h4 class="bigger pull-right">@ViewBag.RelayCount</h4>
                        </div>

                        <div class="grid3">
                            <span class="grey">
                                &nbsp; طلبات غير جاهزه
                            </span>
                            <h4 class="bigger pull-right">@ViewBag.UnRelayCount</h4>
                        </div>

                        <div class="grid3">
                            <span class="grey">
                                &nbsp; طلبات مرفوضة
                            </span>
                            <h4 class="bigger pull-right">@ViewBag.RejectCount</h4>
                        </div>

                    </div>
                </div><!-- /.widget-main -->
            </div><!-- /.widget-body -->
        </div><!-- /.widget-box -->
    </div><!-- /.col -->
</div>*@