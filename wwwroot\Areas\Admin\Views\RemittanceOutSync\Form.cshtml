﻿@model AppTech.MSMS.Web.Areas.Remittance.Models.RemittanceOutModel

<!-- Remittance Details -->
<div>

    <h3 class="row header smaller lighter blue">
        <span class="col-xs-6"> تفاصيل الحوالة </span>
    </h3>
    <div id="user-profile-1" class="user-profile row">
        <div class="profile-user-info profile-user-info-striped">

            <div class="profile-info-row">
                <div class="profile-info-name">@Html.DisplayNameFor(model => model.RemittanceIn.RemittanceNumber) </div>

                <div class="profile-info-value">
                    <span>@Html.DisplayFor(model => model.RemittanceIn.RemittanceNumber) </span>
                </div>
            </div>

            <div class="profile-info-row">
                <div class="profile-info-name">@Html.DisplayNameFor(model => model.RemittanceIn.Amount)</div>

                <div class="profile-info-value">
                    <span>@Html.DisplayFor(model => model.RemittanceIn.Amount) </span>
                </div>
            </div>

            @*<div class="profile-info-row">
                <div class="profile-info-name">@Html.DisplayNameFor(model => model.RemittanceIn.CurrencyID)</div>

                <div class="profile-info-value">
                    <span>@Html.DisplayFor(model => model.RemittanceIn.Currency.Name) </span>
                </div>
            </div>*@


            <div class="profile-info-row">
                <div class="profile-info-name">@Html.DisplayNameFor(model => model.RemittanceIn.BeneficiaryName)</div>

                <div class="profile-info-value">
                    <span> @Html.DisplayFor(model => model.RemittanceIn.BeneficiaryName)</span>
                </div>
            </div>


            <div class="profile-info-row">
                <div class="profile-info-name">@Html.DisplayNameFor(model => model.RemittanceIn.SenderName)</div>

                <div class="profile-info-value">
                    <span> @Html.DisplayFor(model => model.RemittanceIn.SenderName)</span>
                </div>
            </div>


            <div class="profile-info-row">
                <div class="profile-info-name">@Html.DisplayNameFor(model => model.RemittanceIn.Date)</div>

                <div class="profile-info-value">
                    <span>@Html.DisplayFor(model => model.RemittanceIn.Date) </span>
                </div>
            </div>


            @*<div class="profile-info-row">
                <div class="profile-info-name">@Html.DisplayNameFor(model => model.RemittanceIn.Note)</div>

                <div class="profile-info-value">
                    <span> @Html.DisplayFor(model => model.RemittanceIn.Note)</span>
                </div>
            </div>*@
        </div>

    </div>
</div>

<hr/>

<!-- User Inputs -->
<div class="col-sm-12">

    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">بيانات أضافية</h3>
        </div>
        <div class="panel-body">
            @{ Html.RenderPartial("_Form", Model.RemittanceOut); }
        </div>
        <div class="panel-footer">
        </div>
    </div>

    <div class="space-6"></div>
    <div class="center">
        <button id="Unquery" class="btn btn-sm btn-primary btn-white btn-round">
            <i class="ace-icon fa fa-rss bigger-150 middle orange2"></i>
            <span class="bigger-110">ألغاء عملية السحب</span>
            <i class="icon-on-right ace-icon fa fa-sign-out"></i>
        </button>
    </div>
</div>
<script>

    $(function() {
        setPageTitle('تفاصيل الحوالة');

        $("#Unquery").on('click',
            function() {
                window.location.href = '/#!/route/Admin/RemittanceOutSync';

    });
</script>