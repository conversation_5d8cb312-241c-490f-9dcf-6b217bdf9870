<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" />
  </startup>
  <entityFramework>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <appSettings>
    <!--basic settings-->
    <add key="ApplicationType" value="Desktop" />
    <add key="ConnectionStringSource" value="EncrptedFile" />
  </appSettings>
  
  
  
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
	    <binding name="billpaymentwsSoapBinding" sendTimeout="00:03:00" receiveTimeout="00:03:00" />
        <binding name="BasicHttpBinding_ICSDServiceLinkLib" />
        <binding name="BasicHttpBinding_ICSDServiceLinkLib1" />
        <binding name="BasicHttpBinding_ICSDServiceLinkLib2" />
        <binding name="BasicHttpBinding_IService" />
        <binding name="BasicHttpBinding_Ialbayanmtnclientservice" />
        <binding name="BasicHttpsBinding_IService">
          <security mode="Transport" />
        </binding>
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://*************:8444/ForMeFast/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib" contract="ForMeServiceReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib" />
      <endpoint address="http://*************:8020/CSDWebServiceLinkLibrary/CSDServiceLinkLib/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib" contract="AbsiReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib" />
      <endpoint address="https://mobile.mutarrebvon.com/AgentsService2/AgentsService.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpsBinding_IService" contract="AlmutarebReference.IService" name="BasicHttpsBinding_IService" />
      <endpoint address="http://*************:8444/DerhimApiWebService/MainService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib1" contract="DerhimApiReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib1" />
      <endpoint address="http://tadawul.dyndns.org:8448/Tadawul/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib1" contract="TadawulReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib1" />
      <endpoint address="http://alatheirvas.com:7443/AtheerWebService/MainService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib2" contract="AtheerServiceReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib2" />
      <endpoint address="http://etsalatie.com:7433/SareeaOLWebService/MainService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib" contract="AlSareeaOnLineServiceReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib" />
      <endpoint address="http://alatheirvas.com:6443/AtheerWebService/MainService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib2" contract="AtheerServiceReference2.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib2" />
       <!-- <endpoint address="http://***************:9999/albayanmtnclientservice/services/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_Ialbayanmtnclientservice" contract="ServiceReference1.Ialbayanmtnclientservice" name="BasicHttpBinding_Ialbayanmtnclientservice" /> -->
	  <endpoint address="http://**************:9999/albayanmtnclientservice/services/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_Ialbayanmtnclientservice" contract="ServiceReference1.Ialbayanmtnclientservice" name="BasicHttpBinding_Ialbayanmtnclientservice" />	
    	  <!-- <endpoint address="http://**************:9999/albayanmtnclientservice/services/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_Ialbayanmtnclientservice" contract="ServiceReference1.Ialbayanmtnclientservice" name="BasicHttpBinding_ICSDServiceLinkLib" />	 -->

		<endpoint address="http://***************:8148/YemenPostPayment/services/billpaymentws" binding="basicHttpBinding" bindingConfiguration="billpaymentwsSoapBinding" contract="YemenPostReference.billpaymentws" name="billpaymentws" />
    </client>
  </system.serviceModel>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Google.Apis" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Google.Apis.Auth" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="RestSharp" publicKeyToken="598062e77f915f75" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-106.11.3.0" newVersion="106.11.3.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>