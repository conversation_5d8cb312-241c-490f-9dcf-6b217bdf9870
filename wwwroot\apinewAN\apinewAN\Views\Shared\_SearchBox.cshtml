﻿@using System.Data
<div id="searchbox" class="row">
    <div class="col-xs-12">
        <div class="search-area well " style="margin-top: 8px; margin-left: 20px; margin-right: 10px">
            <form id="search-records-form">
                <input type="hidden" id="pagerSize"  name="pagerSize" value="10"/>
                <select name="field" id="field" class="form-control-select" required="required">
                    <option></option>
                    @if (ViewBag.dtDefinitin != null)
                    {
                        foreach (DataRow row in ViewBag.dtDefinitin.Rows)
                        {
                            <option value="@string.Format("{0},{1}", row[0], row[1])">@row[1].ToString()</option>
                        }
                    }
                </select>
                <select id="operators" name="operators" class="form-control-select" required="required"></select>
                <input type="text" id="values" name="value" title="ادخل القيمة هنا.."/>
                <input type="text" id="value2" name="value2" class="date-picker">
                <button id="search-records" class="btn btn-white btn-info btn-bold">بحث</button>

            </form>
        </div>
    </div>
</div>

