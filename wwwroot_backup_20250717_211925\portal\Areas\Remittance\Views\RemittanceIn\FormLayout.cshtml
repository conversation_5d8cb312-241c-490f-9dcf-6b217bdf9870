﻿@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@model AppTech.MSMS.Domain.Models.RemittanceIn
@{
    Layout = "~/Views/Shared/_FormLayout.cshtml";
}
<div class="page-header">
    <h1 id="page-title">
        قبض حوالة
    </h1>
</div>
@*<h3>قبض حوالة</h3>
    <div class="hr hr-dotted hr-24"></div>
    <div class="space-6"></div>
    <div class="form-group">
            @Html.Label("المحافطة", new {@class = "control-label col-md-2"})
            <div class="col-md-10">
                @Html.DropDownList("Province", (SelectList) ViewBag.Provinces, new {htmlAttributes = new {@class = "form-control"}})
                @Html.ValidationMessageFor(model => model.TargetPointID, "", new {@class = "text-danger"})
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.TargetRegionID, new {@class = "control-label col-md-2"})
            <div class="col-md-10">
                <select id="TargetRegionID" name="TargetRegionID" class="form-control"></select>
    @Html.DropDownListFor(model => model.TargetRegionID, (SelectList)ViewBag.Targets, new { htmlAttributes = new { @class = "form-control" } })
    @Html.ValidationMessageFor(model => model.TargetRegionID, "", new { @class = "text-danger" })
        </div>
    </div>
*@

<div class="form-group">
    @Html.LabelFor(model => model.TargetPointID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @*<select id="TargetPointID" name="TargetPointID" class="form-control"></select>*@
        @Html.DropDownListFor(model => model.TargetPointID, (SelectList) ViewBag.Targets, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.TargetPointID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Amount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Amount, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Amount, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.CurrencyID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CurrencyID, (SelectList) ViewBag.Currencies, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.CurrencyID, "", new {@class = "text-danger"})
    </div>
</div>
@if (CurrentUser.Type == UserType.Admin)
{
    <div class="form-group">
        @Html.LabelFor(model => model.DebitorAccountID, new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.DebitorAccountID, (SelectList) ViewBag.Funds, new {htmlAttributes = new {@class = "form-control"}})
            @Html.ValidationMessageFor(model => model.DebitorAccountID, "", new {@class = "text-danger"})
        </div>
    </div>
}
<div class="form-group">
    @Html.LabelFor(model => model.BeneficiaryName, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.BeneficiaryName, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.BeneficiaryName, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.BeneficiaryPhone, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.BeneficiaryPhone, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.BeneficiaryPhone, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SenderName, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderName, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.SenderName, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SenderPhone, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderPhone, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.SenderPhone, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Date, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Date, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Date, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Purpose, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Purpose, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Purpose, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
    </div>
</div>

<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">العمولة</h3>
    </div>
    <div class="panel-body">

        <div class="row">
            <div class="col-xs-12 col-sm-6">
                <div class="form-group">
                    @Html.LabelFor(model => model.CenterCommission, new {@class = "control-label col-md-2"})
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.CenterCommission, new
                        {
                            htmlAttributes = new {@class = "form-control", @readonly = "readonly"}
                        })
                    </div>
                </div>
            </div>

            <div class="col-xs-12 col-sm-6">
                <div class="form-group">
                    @Html.LabelFor(model => model.PointCommission, new {@class = "control-label col-md-2"})
                    <div class="col-md-10">
                        <input type="text" id="PointCommission" name="PointCommission" readonly="readonly"/>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            @Html.Label("إجمالي المبلغ", new {@class = "control-label col-md-2"})
            <div class="col-md-10">
                <input type="text" id="CommissionAmount" name="CommissionAmount" readonly="readonly"/>
            </div>
        </div>

    </div>


    <div class="panel-footer">
        <a href="" class="btn btn-primary btn-round" onclick="calcCommission();"> احتساب العمولة</a>
    </div>
</div>

<script>

    function calcCommission() {
        var data = {
            CurrencyID: $("#CurrencyID").val(),
            Amount: $("#Amount").val(),
            RemittanceType: 0
        };
        AjaxCall('/Remittance/RemittanceIn/CalcCommission', JSON.stringify(data), 'POST').done(function(response) {
            $("#PointCommission").val(response.PointCommission);
            $("#CenterCommission").val(response.CenterCommission);
            $("#CommissionAmount").val(response.Total);
            //if (response === undefined) {
            //    ar('لم يتم تحديد عمولة بحسب البيانات المدخلة');
            //} else {
            //    $("#PointCommission").val(response.PointCommission);
            //    $("#CenterCommission").val(response.CenterCommission);
            //    $("#CommissionAmount").val(response.Total);
            //}
        }).fail(function (xhr, textStatus, errorThrown) {
            parseAndShowError(xhr, textStatus, errorThrown);
        });
    }

    $(function() {
        try {
            formHelper.onSuccess = function(data) {
                log('remittance-formHelper.onSuccess');
                hideLoading();
                i('data> ' + data);
                resetButton();
                $('#crudform')[0].reset();
                ar('تم أرسال الحوالة بنجاح');
                if (confirm("هل تريد طباعة سند أرسال حوالة")) {
                    PrintReceipt('أرسال حوالة', data);
                }
            }

            formHelper.onBegin = function() {

                var msg = "سوف يتم أرسال حوالة بمبلغ  " +
                    $("#Amount").val() +
                    ' ' +
                    $("#CurrencyID option:selected").text() +
                    ' هل انت متأكد';
                if (!confirm(msg)) {
                    i('not confirmed');
                    return false;
                } else {

                    i('confirmed');
                    showLoading();
                    return true;

                }

            }

        } catch (e) {

        }
    });
</script>

<script>

    function fillList(response, element, title) {
        if (response.length > 0) {
            $('#' + element).html('');
            var options = '';
            options += '<option value="Select">' + title + '  </option>';
            for (var i = 0; i < response.length; i++) {
                options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
            }
            $('#' + element).append(options);
        }
    }

    $(function() {

        //$('#Province').on("change",
        //    function() {
        //        i('change');
        //        var province = $('#Province').val();
        //        i('pro>' + province);
        //        var obj = { province: province };
        //        AjaxCall('/Remittance/RemittanceIn/GetRegions', JSON.stringify(obj), 'POST').done(function(response) {
        //            fillList(response, 'TargetRegionID', 'اختر المنطقة');

        //        }).fail(function(error) {
        //            alert(error.StatusText);
        //        });
        //    });

        //$('#TargetRegionID').on("change",
        //    function () {
        //        i('change region');
        //        var regionId = $('#TargetRegionID').val();
        //        i('id ' + regionId);
        //        if (regionId === "0")
        //            return;

        //        var obj = { region: regionId };
        //        AjaxCall('/Remittance/RemittanceIn/GetRemittancePoints', JSON.stringify(obj), 'POST').done(
        //            function (response) {
        //                if (response.length > 0) {
        //                    $('#TargetPointID').html('');
        //                    fillList(response, 'TargetPointID', 'اختر الجهة');
        //                }
        //            }).fail(function (error) {
        //                alert(error.StatusText);
        //            });
        //    });
    });

</script>