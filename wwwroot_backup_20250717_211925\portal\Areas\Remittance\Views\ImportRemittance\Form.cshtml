﻿@model AppTech.MSMS.Domain.Models.RemittanceIn
@{
    Layout = "~/Views/Shared/_FormLayout.cshtml";
}
<h2>title</h2>
<div class="form-group">
    @Html.LabelFor(model => model.SourcePointID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.SourcePointID, (SelectList) ViewBag.Sources, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.SourcePointID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.TargetPointID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.TargetPointID, (SelectList) ViewBag.Targets, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.TargetPointID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Amount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Amount, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Amount, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.CurrencyID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CurrencyID, (SelectList) ViewBag.Currencies, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.CurrencyID, "", new {@class = "text-danger"})
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.DebitorAccountID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.DebitorAccountID, (SelectList) ViewBag.Funds, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.DebitorAccountID, "", new {@class = "text-danger"})
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.BeneficiaryName, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.BeneficiaryName, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.BeneficiaryName, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.BeneficiaryPhone, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.BeneficiaryPhone, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.BeneficiaryPhone, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SenderName, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderName, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.SenderName, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SenderPhone, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderPhone, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.SenderPhone, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Date, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Date, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Date, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Purpose, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Purpose, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Purpose, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
    </div>
</div>

<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">العمولة</h3>
    </div>
    <div class="panel-body">

        <div class="row">
            <div class="col-xs-12 col-sm-6">
                <div class="form-group">
                    @Html.LabelFor(model => model.CommissionAmount, new {@class = "control-label col-md-2"})
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.CommissionAmount, new {htmlAttributes = new {@class = "form-control"}})
                        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
                    </div>
                </div>
            </div>

            <div class="col-xs-12 col-sm-6">
                <div class="form-group">
                    @Html.LabelFor(model => model.CommissionAmount, new {@class = "control-label col-md-2"})
                    <div class="col-md-10">
                        <input type="text" id="CenterCommission" name="CenterCommission" readonly="readonly"/>
                        @Html.ValidationMessageFor(model => model.CommissionAmount, "", new {@class = "text-danger"})
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="panel-footer">
        <a href="" class="btn btn-primary btn-round" onclick="calcCommission();"> احتساب العمولة</a>
    </div>
</div>

<script>

    function calcCommission() {
        var data = {
            CurrencyID: $("#CurrencyID").val(),
            Amount: $("#Amount").val(),
            RemittanceType: 0
        };
        AjaxCall('/Remittance/RemittanceIn/CalcCommission', JSON.stringify(data), 'POST').done(function(response) {

            if (response === undefined) {
                ar('لم يتم تحديد عمولة بحسب البيانات المدخلة');
            } else {
                $("#CommissionAmount").val(response.PointCommission);
                $("#CenterCommission").val(response.CenterCommission);
            }
        }).fail(function (xhr, textStatus, errorThrown) {
            parseAndShowError(xhr, textStatus, errorThrown);
        });
    }

    $(function() {
        try {
            formHelper.onSuccess = function(data) {
                log('remittance-formHelper.onSuccess');
                hideLoading();
                i('data> ' + data);
                $('#crudform')[0].reset();
                ar('تم أرسال الحوالة بنجاح');
                if (confirm("هل تريد طباعة سند أرسال حوالة")) {
                    PrintReceipt('أرسال حوالة', data);
                }
            }

            formHelper.onBegin = function() {

                var msg = "سوف يتم أرسال حوالة بمبلغ  " + $("#Amount").val() + $("#CurrencyID").val() + ' هل انت متأكد';
                if (!confirm(msg)) {
                    i('not confirmed');
                    return false;
                } else {

                    i('confirmed');
                    showLoading();
                    return true;

                }

            }

        } catch (e) {

        }
    });
</script>