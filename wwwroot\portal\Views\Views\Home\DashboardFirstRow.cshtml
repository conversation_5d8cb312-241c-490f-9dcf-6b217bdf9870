﻿@model AppTech.MSMS.Web.Models.HomeModel

<div class="row">
    <div class="col-lg-3 col-xs-6  ">
        <!-- small box -->
        <a href="/#!/route/admin/order" class="small-box box1">

            <div class="inner">
                <h3>
                    @Model.UnreadyOrders
                </h3>
                <p>
                    طلبات جديدة
                </p>
            </div>
            <div class="icon">
                <i class="fa fa-server"></i>
            </div>
            @*<a href="/#!/route/admin/order" class="small-box-footer">
                التفاصيل <i class="fa fa-arrow-circle-right"></i>
            </a>*@
        </a>
    </div><!-- ./col -->

    <div class="col-lg-3 col-xs-6  ">
        <!-- small box -->
        <a href="/#!/route/Client/Registeration" class="small-box box2">
            <div class="inner">
                <h3>
                    @Model.NewClients
                </h3>
                <p>
                    طلبات فتح حساب
                </p>
            </div>
            <div class="icon">
                <i class="fa fa-user-plus"></i>
            </div>
            @*<div  class="small-box-footer">
                التفاصيل <i class="fa fa-arrow-circle-right"></i>
            </div>*@
        </a>
    </div><!-- ./col -->
    @*
        <div class="col-lg-3 col-xs-6">
            <!-- small box -->
            <div class="small-box bg-green">
                <div class="inner">
                    <h3>
                        @Model.DirectTrans<sup style="font-size: 20px"></sup>
                    </h3>
                    <p>
                        العمليات
                    </p>
                </div>
                <div class="icon">
                    <i class="ion ion-stats-bars"></i>
                </div>
                <a href="/#!/route/client/PaymentSync" class="small-box-footer">
                    التفاصيل <i class="fa fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>*@


    <!-- ./col -->


    <div class="col-lg-3 col-xs-6  ">
        <!-- small box -->
        <a href="/#!/route/client/client" class="small-box box3">
            <div class="inner">
                <h3>
                    @Model.ClinetsCount
                </h3>
                <p>
                    العملاء
                </p>
            </div>
            <div class="icon">
                <i class="fa fa-users"></i>
            </div>
            @*<a href="/#!/route/client/client" class="small-box-footer">
                التفاصيل <i class="fa fa-arrow-circle-right"></i>
            </a>*@
        </a>
    </div><!-- ./col -->

    <div class="col-lg-3 col-xs-6  ">
        <!-- small box -->
        <a href="/#!/route/DirectPayment/Topup" class="small-box box4">
            <div class="inner">
                <h3>
                    @Model.Topups<sup style="font-size: 20px"></sup>
                </h3>
                <p>
                    التحصيلات
                </p>
            </div>
            <div class="icon">
                <i class="fa fa-bar-chart"></i>
            </div>
            @*<a href="/#!/route/DirectPayment/Topup" class="small-box-footer">
                التفاصيل <i class="fa fa-arrow-circle-right"></i>
            </a>*@

            @*<a href="/#!/route/DirectPayment/GomalaTopup" class="small-box-footer">
                    الجملة   @Model.GomalaTopups<i class="fa fa-arrow-circle-right"></i>
                </a>*@
        </a>
    </div>

        <div class="col-lg-3 col-xs-6">
            <!-- small box -->
            <a href="/#!/route/DirectPayment/SuspendTopup" class="small-box box5">
                <div class="inner">
                    <h3>
                        @Model.SuspendTopups<sup style="font-size: 20px"></sup>
                    </h3>
                    <p>
                        التحصيلات المعلقة
                    </p>
                </div>
                <center>

                    <div class="icon">
                        <i class="fa fa-area-chart"></i>
                    </div>
                </center>
                @*<a href="/#!/route/DirectPayment/SuspendTopup" class="small-box-footer">
                        التفاصيل <i class="fa fa-arrow-circle-right"></i>
                    </a>*@
            </a>
        </div>

</div>
<style>
    .small-box {
        border-radius: 15px;
        box-shadow: 13px 12px 41px -28px #000;
        height: 120px;
        color:#fff;
        /*transition :width 2s,height 2s;*/
        /*transition-delay:1s;*/
    }
    .small-box>.inner {
        padding: 25px;
    }
     .small-box:hover {
        /*width: 105%;
        height: 150px;
        background: linear-gradient(80deg, #000,#349abb);*/
        
     }
    .box1{
        background: linear-gradient(80deg, #11e1d7,#022422);
    }
    .box2{
        background: linear-gradient(80deg, #ef00ff,#2e0e0e);
    }
    .box3{
        background: linear-gradient(80deg, #349abb,#07151a);
    }
    .box4{
        /*background: linear-gradient(80deg, #349abb,#ffd800);
        background: linear-gradient(80deg, #4cff00,#349abb);*/
        background: linear-gradient(80deg, #00ff13,#002403);
    }
    .box5{
        /*background: linear-gradient(230deg, #ffc480, #ff763b);
        background: linear-gradient(80deg, #349abb,#020300);
        background: linear-gradient(80deg, #e11111,#6c0808);*/
        background: linear-gradient(80deg, #e11111,#020300);

    }
    .small-box:hover .icon {
        animation-fill-mode: none;
        animation-iteration-count: 0;
        transform:scale(1.1);
    }
    .small-box .icon {
        right: 145px;
        top: 2px;
        bottom:0;
        transition-duration: .5s;
    }
/*.skin-1 .nav-list > li > a,
.skin-1 .nav-list > li,
.rtl .nav-list > li .submenu > li > a {
    border-top-left-radius: 15px !important;
    border-bottom-left-radius: 15px !important;
    background-color: #005170 !important;
}

    .skin-1 .nav-list > li .submenu > li > a {
        border-top-color: #000 !important;
        background-color: #008cc2  !important;
        color: #fff !important;
    }
    .skin-1 ,
    .skin-1 .nav-list > li .submenu {
        background-color: #ffffff !important;
    border-top-color: #ffffff !important;

    }
    .skin-1 .nav-list > li:hover > a ,
     .ace-nav>li.dark>a:hover{
        background-color: #0176a3  !important;
    }
    .skin-1 .nav-list > li.open > a ,
    .skin-1 .sidebar-toggle{
    border-top-left-radius: 15px !important;
    border-bottom-left-radius: 15px !important;
        color: #ffffff !important;
        background-color: #005170  !important;
    border-color: #ffffff;

    }
    .skin-1 .nav-list > li.open > a ,
    .ace-nav>li.dark>a {
        background-color: #013245 !important;
    }
.skin-2 {
    background-color: #005170 !important;
}

.skin-1 .nav-list>li>a {
    color: #ffffff !important;
}

.skin-1 .nav-list>li {
    border-color: #000000 !important;
    border-top-left-radius: 15px !important;
    border-bottom-left-radius: 15px !important;
}
.skin-1 .sidebar-toggle>.ace-icon {
    background-color: #222a2d !important;
    color: #fff !important;
    border-color: #fff !important;
}
.skin-1 .sidebar-toggle {
    background-color: #181e21;
    border-color: #ffffff;
}
.dropdown-toggle .icon {
        font-size: 19px;
}
.dropdown-toggle .menu-text {
        margin-right: 6px;
}*/
</style>
