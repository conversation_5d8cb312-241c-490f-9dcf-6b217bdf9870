﻿@model AppTech.MSMS.Domain.Models.PaymentCommission

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.LabelFor(model => model.ServiceID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ServiceID, (SelectList) ViewBag.Services)
        @Html.ValidationMessageFor(model => model.ServiceID)
    </div>
</div>


<div class="form-group">
    <div class="col-md-12">
        @Html.LabelFor(model => model.CurrencyID, new {@class = "control-label col-md-2"})
        @Html.DropDownListFor(m => m.CurrencyID, (SelectList) ViewBag.Currencies)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.AccountState, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.AccountState, new[]
        {
            new SelectListItem {Text = "كافة الحسابات", Value = "1"},
            new SelectListItem {Text = "مجموعة", Value = "2"},
            new SelectListItem {Text = "حساب محدد", Value = "3"}
        })
        @Html.ValidationMessageFor(model => model.AccountState, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group" id="specifc">
    @Html.LabelFor(model => model.AccountID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.AccountID, (SelectList) ViewBag.Accounts, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.AccountID, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group" id="group">
    @Html.LabelFor(model => model.AccountGroupID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.AccountGroupID, (SelectList) ViewBag.Groups, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.AccountGroupID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group form-inline">
    @Html.LabelFor(model => model.FromAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.FromAmount, new {@class = "col-md-8"})
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.FromAmount)
    </div>
</div>


<div class="form-group form-inline">
    @Html.LabelFor(model => model.ToAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.ToAmount, new {@class = "col-md-8"})
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.ToAmount)
    </div>
</div>

<div class="form-group">
    @Html.Label("نوع العمولة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CommissionType, new[]
        {
            new SelectListItem {Text = "بالمبلغ", Value = "0"},
            new SelectListItem {Text = "بالنسبة", Value = "1"}
        })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.CommissionCurrencyID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CommissionCurrencyID, (SelectList) ViewBag.CommissionCurrencies, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.CommissionCurrencyID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group form-inline">
    @Html.LabelFor(model => model.TraderAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.TraderAmount, new {@class = "col-md-8"})
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.TraderAmount)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.PersonalAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.PersonalAmount, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.PersonalAmount, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group form-inline">
    @Html.LabelFor(model => model.IsAgainst, new {@class = "control-label col-md-2"})
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.IsAgainst, new {@class = "col-md-8"})
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.IsAgainst)
    </div>
</div>

<script>

    function setState() {

        var num = Number($("#AccountState").children("option:selected").val());
        i('selected state: ' + num);
        if (num === 1) {
            $('#specifc').hide();
            $('#group').hide();

        } else if (num === 3) {
            $('#specifc').show();
            $('#group').hide();

        } else if (num === 2) {
            $('#specifc').hide();
            $('#group').show();
        }
    }

    $(function() {
        // $('#specifc').hide();
        //$('#group').hide();
        setState();
        $('#AccountState').on('change',
            function() {

                setState();

            });
    })
</script>