﻿@model AppTech.MSMS.Domain.Models.Topup
@using Obout.Mvc.ComboBox
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    <div class="col-md-12">
        @Html.Label("اسم الحساب", new { @class = "control-label col-md-2" })
        @Html.Obout(new ComboBox("DebitorAccountID")
        {
            Width = 300,
            SelectedValue = Model.DebitorAccountID == 0 ? null : Model.DebitorAccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains,
            LoadingText = "Loading"
        })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ServiceID, htmlAttributes: new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.ServiceID, (SelectList)ViewBag.Services)
        @Html.ValidationMessageFor(model => model.ServiceID, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.SubscriberNumber, htmlAttributes: new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.SubscriberNumber, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.SubscriberNumber, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group form-inline">
    @Html.LabelFor(model => model.Quantity, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Quantity, new { htmlAttributes = new { @class = "form-control" } })
        <input type="button" class="btn btn-white" value="احتساب المبلغ" id="calc"/>
        @Html.ValidationMessageFor(model => model.Quantity, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Amount, htmlAttributes: new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Amount, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Amount, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">

    @Html.Label("التاريخ", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Date, new { htmlAttributes = new { @class = "date-picker" } })
        @Html.ValidationMessageFor(model => model.Date, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
        @Html.ValidationMessageFor(model => model.Note)
    </div>
</div>


 <script>

     $("#calc").on('click', function () {
         calcRemote();
     });
         function calcRemote() {
             var startNo = Number($('#Quantity').val());
             var accountId = Number($('#DebitorAccountID').val());
        var sid = $('#ServiceID').val();
        i('sid' + sid);
        i('sno' + startNo);
        if (startNo <= 0) {
            ar('قم بأخال الكمية ');
            return;
        }
        AjaxCall('/DirectPayment/Topup/CalcAmount?sid=' + sid + '&units=' + startNo + '&accountId='+accountId)
            .done(function (response) {
                i('response ' + response);
              //  var amount = Number(response);
                var amount = response;

                $('#Amount').val(amount);
            }).fail(function (xhr, textStatus, errorThrown) {
                parseAndShowError(xhr, textStatus, errorThrown)
            }); 
    }
 </script>