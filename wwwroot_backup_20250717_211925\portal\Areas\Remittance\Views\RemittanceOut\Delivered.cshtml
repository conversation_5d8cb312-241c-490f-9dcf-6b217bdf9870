﻿<input type="hidden" id="id" value="@ViewBag.ID">
<button class="btn  btn-white btn-bold btn-lg" onclick="printRemittance()">
    <i class="ace-icon fa fa-print bigger-125"></i>
    طباعة سند صرف حوالة
</button>
<br/>
<div class="space-6"></div>
<button class="btn btn-default btn-bold btn-lg" onclick="newRemittance()">
    <i class="ace-icon fa fa-plus"></i>
    صرف حوالة جديدة
</button>

<script>
    function printRemittance() {

        var id = $('#id').val();
        i('id ' + id);
        printReciept(id);
    }

    function newRemittance() {
        window.location.href = '/#!/route/Remittance/RemittanceOut';
    }

    function printReciept(id) {

        //   window.open("/Print/PrintPrintTest");
        var data = { id: id };
        var title = "سند صرف حوالة";
        var controller = "Remittance/RemittanceOut/";
        var url = "/" + controller + "/" + "Print";
        try {

            $.ajax({
                url: url,
                data: data,
                success: function(response) {
                    var result = response;
                    if (result !== undefined) {

                        localStorage["REPORT_title"] = title;
                        localStorage["REPORT"] = result;
                        window.open("/Print/PrintDialog");
                    }
                }
            });

        } catch (err) {
            alert(err);
        }


    }
</script>