﻿@model AppTech.MSMS.Domain.Reports.Models.RemittReportModel
@{
    ViewBag.Title = "تقرير الحوالات";
    Layout = "~/Views/Shared/_Report.cshtml";
}

@{
    Html.RenderPartial("_DateControl");
}
<span class="lbl"> نوع الحوالة</span>
@Html.EnumDropDownListFor(m => m.Type)

<div class="hr hr-dotted hr-24"></div>
<div class="space-6"></div>


<span class="lbl">اسم الحساب </span>
<select id="AccountID" name="AccountID" class="select2" placeholder="كافة الحسابات"></select>
<div class="space-6"></div>


<span class="lbl">اسم الجهة</span>
<select id="TargetID" name="TargetID" class="select2" placeholder="كافة الجهات"></select>
<div class="space-6"></div>

@*<span class="lbl">اسم المصدر </span>
    <select id="SourceID" name="SourceID" class="select2" placeholder="كافة المصادر"></select>
    <div class="space-6"></div>*@

<span class="lbl"> العملة</span>
@Html.DropDownListFor(m => m.CurrencyID, (SelectList)ViewBag.Currencies)
<div class="space-6"></div>



@*<span class="lbl">اسم المستخدم </span>
    <select id="UserID" name="UserID" class="select2" placeholder="كافة المستخدمين"></select>
    <div class="space-6"></div>*@



<script>
    $(function () {
        fillDataList('AccountID', '/Print/GetAccounts', false, 'كافة الحسابات');
        //    fillDataList('UserID', '/Print/GetAccounts', false, 'كافة المستخدمين');
        fillDataList('TargetID', '/Print/GetTargets', false, 'كافة الجهات');
        //   fillDataList('SourceID', '/Print/GetTargets', false, 'كافة المصادر');
    //    $("select#Status").prop('selectedIndex', 2);  

        $("select#Type").prop('selectedIndex', 0);  
        //   AjaxCall('/Print/GetAccounts').done(function (response) {
        //    console.log('get parties');
        //    if (response.length > 0) {
        //        $('#AccountID').html('');
        //        var options = '<option value="0">كافة الحسابات</option>';
        //        for (var i = 0; i < response.length; i++) {
        //            options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
        //        }
        //        $('#AccountID').append(options);

        //    }
        //}).fail(function (xhr, textStatus, errorThrown) {
        //    parseAndShowError(xhr, textStatus, errorThrown);
        //});



        //select2
        $('.select2').css('width', '200px').select2({ allowClear: true });
        $('#select2-multiple-style .btn').on('click',
            function (e) {
                var target = $(this).find('input[type=radio]');
                var which = parseInt(target.val());
                if (which == 2) $('.select2').addClass('tag-input-style');
                else $('.select2').removeClass('tag-input-style');
            });
    });

</script>