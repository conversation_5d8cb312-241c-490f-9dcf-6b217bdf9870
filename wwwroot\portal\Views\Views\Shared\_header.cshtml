﻿@using AppTech.MSMS.Domain
<header>

    <div id="navbar" class="navbar navbar-default skin-2">
        <div class="navbar-container ace-save-state" id="navbar-container">
            <button type="button" class="navbar-toggle menu-toggler pull-left" id="menu-toggler" data-target="#sidebar">
                <span class="sr-only">Toggle sidebar</span>

                <span class="icon-bar"></span>

                <span class="icon-bar"></span>

                <span class="icon-bar"></span>
            </button>

            <div class="navbar-header pull-left">
                <a href="" class="navbar-brand">
                    <small>
                        @ClientLicense.Customer.CompanyInfo.Name
                    </small>
                </a>
            </div>

            <div class="navbar-buttons navbar-header pull-right" role="navigation">

                <div class="navigation">
                    <div class="partialMainContents" data-url="/home/<USER>">
                        @Html.Partial("_Indicator")
                    </div>
                </div>
            </div>
        </div>
    </div>


</header>