﻿@model AppTech.MSMS.Web.Models.ReceiptModel
@Styles.Render("~/Content/print")

<div id="invoice-POS">
    <center id="top">
        <div class="logo">
            <img id="receipt_logo" class="align-center" src="@Url.Content("~/Photos/logo.png")" alt="logo" />
            <p> @Html.DisplayFor(model => model.DeliveryTitle)</p>
        </div>
        <!--End Info-->
    </center><!--End InvoiceTop-->

    <div id="mid">
        <div class="info">
            <div class="align-center"> @Html.DisplayFor(model => model.Type)</div><br />
            <div>  التاريخ:   @Html.DisplayFor(model => model.Date)</div>
            <div>  رقم السند:   @Html.DisplayFor(model => model.Number)</div>
        </div>
        <div class="info">
            السيد :  @Html.DisplayFor(model => model.AccountName)
        </div>
    </div><!--End Invoice Mid-->

    <div id="bot">
        <div id="table">
            <table>
                <tr class="tabletitle align-center">
                    <td class="item"><h2>العملة</h2></td>
                    <td class="item"><h2>المبلغ</h2></td>
                    <td class="item"><h2>سعرالصرف</h2></td>
                    <td class="item"><h2>القيمة</h2></td>
                </tr>

                <tr class="service align-center">
                    <td class="tableitem"><p class="itemtext">@Html.DisplayFor(model => model.CurrencyName)</p></td>
                    <td class="tableitem"><p class="itemtext">@Html.DisplayFor(model => model.Amount)</p></td>
                    <td class="tableitem"><p class="itemtext">@Html.DisplayFor(model => model.Delivery)</p></td>
                    <td class="tableitem"><p class="itemtext">@Html.DisplayFor(model => model.FundName)</p></td>
                </tr>
                <tr class="service">
                    <td class="tableitem"><p class="itemtext"></p></td>
                    <td class="tableitem"><p class="itemtext"></p></td>
                    <td class="tableitem"><p class="itemtext"></p></td>
                    <td class="tableitem"><p class="itemtext"></p></td>
                </tr>
                <tr class="service">
                    <td class="tableitem"><p class="itemtext"></p></td>
                    <td class="tableitem"><p class="itemtext"></p></td>
                    <td class="tableitem"><p class="itemtext"></p></td>
                    <td class="tableitem"><p class="itemtext"></p></td>
                </tr>

                <tr class="tabletitle">
                    <td colspan="3"><h2>@Html.DisplayFor(model => model.AmountInText)</h2></td>
                    <td class="payment"><h2>@Html.DisplayFor(model => model.FundName)</h2></td>
                </tr>

            </table>
        </div><!--End Table-->

        <div id="legalcopy">
            <ul>
                <li>
                    <strong>على العميل </strong> ان يقوم بفحص وعد النقود قبل المغادرة.
                </li>
                <li>
                    <strong>اسعار العملات </strong> تتغير بين لحظة واخرى ولن يتحمل الصراف اي مسؤولية قانونية او أخلاقية ناتجة عن ذلك.
                </li>
            </ul>
        </div>

    </div><!--End InvoiceBot-->
</div><!--End Invoice-->


<style>
    #invoice-POS {
        box-shadow: 0 0 1in -0.25in rgba(0, 0, 0, 0.5);
        padding: 2mm;
        margin: 0 auto;
        width: 44mm;
        background: #fff;
    }

    #legalcopy ul {
        margin-right: 6px;
        font-size: 6px;
    }

    #invoice-POS ::selection {
        background: #f31544;
        color: #fff;
    }

    #invoice-POS ::moz-selection {
        background: #f31544;
        color: #fff;
    }

    #invoice-POS h1 {
        font-size: 1.5em;
        color: #222;
    }

    #invoice-POS h2 {
        font-size: 0.9em;
    }

    #invoice-POS h3 {
        font-size: 1.2em;
        font-weight: 300;
        line-height: 2em;
    }

    #invoice-POS p {
        font-size: 0.7em;
        color: #666;
        line-height: 1.2em;
    }

    #invoice-POS #mid .info, #invoice-POS #mid, #invoice-POS #bot {
        /* Targets all id with 'col-' */
        border-bottom: 1px solid #eee;
    }

    #invoice-POS {
        min-height: 100px;
    }

    #invoice-POS #mid {
        min-height: 30px;
    }

    #invoice-POS #bot {
        min-height: 50px;
    }

    #invoice-POS #top .logo {
        height: 60px;
        width: 60px;
    }

    #invoice-POS .clientlogo {
        float: left;
        height: 60px;
        width: 60px;
        background-size: 60px 60px;
        border-radius: 50px;
    }

    #invoice-POS .info {
        display: block;
        margin-left: 0;
    }

    .info {
        font-size: 8px;
    }

    #invoice-POS .title {
        float: right;
    }

    #invoice-POS .title p {
        text-align: right;
    }

    #invoice-POS table {
        width: 100%;
        border-collapse: collapse;
    }

    #invoice-POS .tabletitle {
        font-size: 0.5em;
        background: #eee;
    }

    #invoice-POS .service {
        border-bottom: 1px solid #eee;
    }

    #invoice-POS .item {
        width: 24mm;
    }

    #invoice-POS .itemtext {
        font-size: 0.5em;
    }

    #invoice-POS #legalcopy {
        margin-top: 5mm;
    }
</style>

<script>
    //$(function () {
    //    $("#calisha").hide();
    //})
 
        document.getElementById("calisha").style.display = "none";
</script>