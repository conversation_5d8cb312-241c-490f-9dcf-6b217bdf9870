﻿@model AppTech.MSMS.Domain.Reports.Models.TransactionModel

@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


<div class="form-horizontal">


    @{

        Html.RenderPartial("_DateControl");
    }


    <span class="lbl"> نوع العملية</span>
    @Html.DropDownListFor(x => x.ServiceID, (SelectList) ViewBag.Services)
    <div class="space-6"></div>

    <span class="lbl"> نوع التقرير</span>
    @Html.EnumDropDownListFor(m => m.Type)
    <div class="space-6"></div>
</div>