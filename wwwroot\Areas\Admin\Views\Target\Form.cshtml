﻿@model AppTech.MSMS.Domain.Models.RemittancePoint
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    @Html.LabelFor(model => model.Number, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Number, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Number, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.Label("أختر الجهة", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.Phone, (SelectList)ViewBag.SyncTargets, new { htmlAttributes = new { @class = "form-control" } })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Type, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.Type, new[]
            {
                new SelectListItem {Text = "شركات", Value = "0"},
                new SelectListItem {Text = "شبكة", Value = "1"}
            })
        </div>
    </div>
</div>



<div class="form-group">
    @Html.LabelFor(model => model.Fax, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Fax, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Fax, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Address, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Address, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Address, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>