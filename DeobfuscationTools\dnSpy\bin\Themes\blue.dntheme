<theme guid="61AA341E-281D-4154-985F-C81E9269034B" name="blue" menu-name="_Blue" order="100" is-light="true" is-high-contrast="false">
	<colors>
		<color name="defaulttext" fg="Black" bg="White" />
		<color name="operator" fg="#000000" />
		<color name="punctuation" fg="#000000" />
		<color name="number" fg="#5B2DA8" />
		<color name="comment" fg="#008000" />
		<color name="keyword" fg="#0000FF" />
		<color name="string" fg="#A31515" />
		<color name="verbatimstring" fg="#800000" />
		<color name="char" fg="#A31515" />
		<color name="namespace" fg="#9E7E00" />
		<color name="type" fg="#2B91AF" />
		<color name="sealedtype" fg="#2B91AF" />
		<color name="statictype" fg="#1B5D70" />
		<color name="delegate" fg="#6666FF" />
		<color name="enum" fg="#336600" />
		<color name="interface" fg="#1E667A" />
		<color name="valuetype" fg="#009933" />
		<color name="module" fg="#1B5D70" />
		<color name="typegenericparameter" fg="#2B91AF" />
		<color name="methodgenericparameter" fg="#2B91AF" />
		<color name="instancemethod" fg="#880000" />
		<color name="staticmethod" fg="#880000" />
		<color name="extensionmethod" fg="#960000" italics="true" />
		<color name="instancefield" fg="#CC3399" />
		<color name="enumfield" fg="#6F008A" />
		<color name="literalfield" fg="#9900FF" />
		<color name="staticfield" fg="#990099" />
		<color name="instanceevent" fg="#990033" />
		<color name="staticevent" fg="#660033" />
		<color name="instanceproperty" fg="#996633" />
		<color name="staticproperty" fg="#7A5229" />
		<color name="local" fg="Black" />
		<color name="parameter" fg="Black" bold="true" />
		<color name="preprocessorkeyword" fg="#FF808080" />
		<color name="preprocessortext" fg="#FF000000" />
		<color name="label" fg="#663300" />
		<color name="opcode" fg="#993366" />
		<color name="ildirective" fg="#009900" />
		<color name="ilmodule" fg="#660033" />
		<color name="excludedcode" fg="#808080" />
		<color name="xmldoccommentattributename" fg="#FF808080" />
		<color name="xmldoccommentattributequotes" fg="#FF808080" />
		<color name="xmldoccommentattributevalue" fg="#FF808080" />
		<color name="xmldoccommentcdatasection" fg="#FF808080" />
		<color name="xmldoccommentcomment" fg="#FF808080" />
		<color name="xmldoccommentdelimiter" fg="#FF808080" />
		<color name="xmldoccommententityreference" fg="#FF008000" />
		<color name="xmldoccommentname" fg="#FF808080" />
		<color name="xmldoccommentprocessinginstruction" fg="#FF808080" />
		<color name="xmldoccommenttext" fg="#FF008000" />
		<color name="xmlliteralattributename" fg="#FFB96464" />
		<color name="xmlliteralattributequotes" fg="#FF555555" />
		<color name="xmlliteralattributevalue" fg="#FF6464B9" />
		<color name="xmlliteralcdatasection" fg="#FFC0C0C0" />
		<color name="xmlliteralcomment" fg="#FF629755" />
		<color name="xmlliteraldelimiter" fg="#FF6464B9" />
		<color name="xmlliteralembeddedexpression" fg="#FF555555" />
		<color name="xmlliteralentityreference" fg="#FFB96464" />
		<color name="xmlliteralname" fg="#FF844646" />
		<color name="xmlliteralprocessinginstruction" fg="#FFC0C0C0" />
		<color name="xmlliteraltext" fg="#FF555555" />
		<color name="xmlattribute" fg="#FFFF0000" />
		<color name="xmlattributequotes" fg="#FF000000" />
		<color name="xmlattributevalue" fg="#FF0000FF" />
		<color name="xmlcdatasection" fg="#FF808080" />
		<color name="xmlcomment" fg="#FF008000" />
		<color name="xmldelimiter" fg="#FF0000FF" />
		<color name="xmlkeyword" fg="#FF0000FF" />
		<color name="xmlname" fg="#FFA31515" />
		<color name="xmlprocessinginstruction" fg="#FF808080" />
		<color name="xmltext" fg="#FF000000" />
		<color name="xamlattribute" fg="#FFFF0000" />
		<color name="xamlattributequotes" fg="#FF000000" />
		<color name="xamlattributevalue" fg="#FF0000FF" />
		<color name="xamlcdatasection" fg="#FF808080" />
		<color name="xamlcomment" fg="#FF008000" />
		<color name="xamldelimiter" fg="#FF0000FF" />
		<color name="xamlkeyword" fg="#FF0000FF" />
		<color name="xamlmarkupextensionclass" fg="#FFA31515" />
		<color name="xamlmarkupextensionparametername" fg="#FFFF0000" />
		<color name="xamlmarkupextensionparametervalue" fg="#FF0000FF" />
		<color name="xamlname" fg="#FFA31515" />
		<color name="xamlprocessinginstruction" fg="#FF808080" />
		<color name="xamltext" fg="#FF000000" />
		<color name="xmldoctooltipheader" italics="true" bold="true" />
		<color name="assembly" fg="#444444" />
		<color name="assemblyexe" fg="#C9801A" />
		<color name="assemblymodule" fg="#682E95" />
		<color name="directorypart" fg="Black" />
		<color name="filenamenoextension" fg="#990000" />
		<color name="fileextension" fg="#3366CC" />
		<color name="error" fg="Red" />
		<color name="tostringeval" fg="#2E387C" />
		<color name="linenumber" fg="#2B91AF" />
		<color name="repllinenumberinput1" fg="#A31515" />
		<color name="repllinenumberinput2" fg="#0000FF" />
		<color name="repllinenumberoutput" fg="#2B91AF" />
		<color name="visiblewhitespace" fg="#FF2B91AF" />
		<color name="selectedtext" bg="#FF0078D7" />
		<color name="inactiveselectedtext" bg="#FFBFCDDB" />
		<color name="highlightedreference" bg="#FFDBE0CC" />
		<color name="highlightedwrittenreference" bg="#FFDBE0CC" />
		<color name="highlighteddefinition" bg="#FFDBE0CC" fg="#FF808080" />
		<color name="currentstatement" fg="#FF000000" />
		<color name="currentstatementmarker" bg="#FFFFEE62" />
		<color name="callreturn" fg="#FF000000" />
		<color name="callreturnmarker" bg="#FFB4E4B4" />
		<color name="activestatementmarker" bg="#FFC0C0C0" />
		<color name="breakpointstatement" fg="#FFFFFFFF" />
		<color name="breakpointstatementmarker" bg="#FF963A46" />
		<color name="selectedbreakpointstatementmarker" bg="#FF963A46" fg="#FF0000FF" />
		<color name="disabledbreakpointstatementmarker" fg="#FF800000" />
		<color name="advancedbreakpointstatement" fg="#FFFFFFFF" />
		<color name="advancedbreakpointstatementmarker" bg="#FF963A46" />
		<color name="selectedadvancedbreakpointstatementmarker" bg="#FF963A46" fg="#FF0000FF" />
		<color name="disabledadvancedbreakpointstatement" fg="#FF800000" />
		<color name="disabledadvancedbreakpointstatementmarker" bg="#FFFFFFFF" />
		<color name="selecteddisabledadvancedbreakpointstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="breakpointwarningstatement" fg="#FFFFFFFF" />
		<color name="breakpointwarningstatementmarker" bg="#FF963A46" />
		<color name="selectedbreakpointwarningstatementmarker" bg="#FF963A46" fg="#FF0000FF" />
		<color name="breakpointerrorstatement" fg="#FFFFDBA3" />
		<color name="breakpointerrorstatementmarker" bg="#FF963A46" />
		<color name="selectedbreakpointerrorstatementmarker" bg="#FF963A46" fg="#FF0000FF" />
		<color name="advancedbreakpointwarningstatement" fg="#FFFFFFFF" />
		<color name="advancedbreakpointwarningstatementmarker" bg="#FF963A46" />
		<color name="selectedadvancedbreakpointwarningstatementmarker" bg="#FF963A46" fg="#FF0000FF" />
		<color name="advancedbreakpointerrorstatement" fg="#FFFFDBA3" />
		<color name="advancedbreakpointerrorstatementmarker" bg="#FF963A46" />
		<color name="selectedadvancedbreakpointerrorstatementmarker" bg="#FF963A46" fg="#FF0000FF" />
		<color name="tracepointstatement" fg="#FF800000" />
		<color name="tracepointstatementmarker" bg="#FFFFFFFF" />
		<color name="selectedtracepointstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="disabledtracepointstatement" fg="#FF800000" />
		<color name="disabledtracepointstatementmarker" bg="#FFFFFFFF" />
		<color name="selecteddisabledtracepointstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="advancedtracepointstatement" fg="#FF800000" />
		<color name="advancedtracepointstatementmarker" bg="#FFFFFFFF" />
		<color name="selectedadvancedtracepointstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="disabledadvancedtracepointstatement" fg="#FF800000" />
		<color name="disabledadvancedtracepointstatementmarker" bg="#FFFFFFFF" />
		<color name="selecteddisabledadvancedtracepointstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="tracepointwarningstatement" fg="#FF800000" />
		<color name="tracepointwarningstatementmarker" bg="#FFFFFFFF" />
		<color name="selectedtracepointwarningstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="tracepointerrorstatement" fg="#FF800000" />
		<color name="tracepointerrorstatementmarker" bg="#FFFFFFFF" />
		<color name="selectedtracepointerrorstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="advancedtracepointwarningstatement" fg="#FF800000" />
		<color name="advancedtracepointwarningstatementmarker" bg="#FFFFFFFF" />
		<color name="selectedadvancedtracepointwarningstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="advancedtracepointerrorstatement" fg="#FF800000" />
		<color name="advancedtracepointerrorstatementmarker" bg="#FFFFFFFF" />
		<color name="selectedadvancedtracepointerrorstatementmarker" bg="#FFFFFFFF" fg="#FF0000FF" />
		<color name="bookmarkname" fg="#A31515" />
		<color name="activebookmarkname" fg="#A31515" bold="true" />
		<color name="currentline" fg="#EAEAF2" />
		<color name="currentlinenofocus" fg="#EAEAF2" />
		<color name="hextext" fg="Black" bg="White" />
		<color name="hexoffset" fg="#FF000000" />
		<color name="hexbyte0" fg="#FF3333CC" />
		<color name="hexbyte1" fg="#FF993366" />
		<color name="hexbyteerror" fg="#FFFF0000" />
		<color name="hexascii" fg="#FF000000" />
		<color name="hexcaret" bg="#66000000" />
		<color name="hexinactivecaret" bg="#FFFF0000" />
		<color name="hexselection" bg="#FF3399FF" />
		<color name="replprompt1" fg="Black" />
		<color name="replprompt2" fg="Black" />
		<color name="reploutputtext" fg="Black" />
		<color name="replscriptoutputtext" fg="Black" />
		<color name="black" fg="#FF000000" />
		<color name="blue" fg="#FF0000FF" />
		<color name="cyan" fg="#FF00C0C0" />
		<color name="darkblue" fg="#FF00007F" />
		<color name="darkcyan" fg="#FF007F7F" />
		<color name="darkgray" fg="#FF7F7F7F" />
		<color name="darkgreen" fg="#FF007F00" />
		<color name="darkmagenta" fg="#FF7F007F" />
		<color name="darkred" fg="#FF7F0000" />
		<color name="darkyellow" fg="#FF7F7F00" />
		<color name="gray" fg="#FFC0C0C0" />
		<color name="green" fg="#FF00C000" />
		<color name="magenta" fg="#FFFF00FF" />
		<color name="red" fg="#FFFF0000" />
		<color name="white" fg="#FF7F7F7F" />
		<color name="yellow" fg="#FFC0C020" />
		<color name="invblack" fg="White" bg="#FF000000" />
		<color name="invblue" fg="White" bg="#FF0000FF" />
		<color name="invcyan" fg="White" bg="#FF00C0C0" />
		<color name="invdarkblue" fg="White" bg="#FF00007F" />
		<color name="invdarkcyan" fg="White" bg="#FF007F7F" />
		<color name="invdarkgray" fg="White" bg="#FF7F7F7F" />
		<color name="invdarkgreen" fg="White" bg="#FF007F00" />
		<color name="invdarkmagenta" fg="White" bg="#FF7F007F" />
		<color name="invdarkred" fg="White" bg="#FF7F0000" />
		<color name="invdarkyellow" fg="White" bg="#FF7F7F00" />
		<color name="invgray" fg="White" bg="#FFC0C0C0" />
		<color name="invgreen" fg="White" bg="#FF00C000" />
		<color name="invmagenta" fg="White" bg="#FFFF00FF" />
		<color name="invred" fg="White" bg="#FFFF0000" />
		<color name="invwhite" fg="White" bg="#FF7F7F7F" />
		<color name="invyellow" fg="White" bg="#FFC0C020" />
		<color name="debuglogexceptionhandled" fg="#FFFF0000" />
		<color name="debuglogexceptionunhandled" fg="#FFFF0000" />
		<color name="debuglogstepfiltering" fg="#FFCC00CC" />
		<color name="debuglogloadmodule" fg="#FF996633" />
		<color name="debuglogunloadmodule" fg="#FF6600FF" />
		<color name="debuglogexitprocess" fg="#FF000000" />
		<color name="debuglogexitthread" fg="#FF006699" />
		<color name="debuglogprogramoutput" fg="#FF487C5F" />
		<color name="debuglogmda" fg="#FFFF0000" />
		<color name="debuglogtimestamp" fg="#FF6600CC" />
		<color name="glyphmargin" bg="#FFE6E7E8" />
		<color name="bracematching" bg="#FFDBE0CC" />
		<color name="lineseparator" fg="#FFA5A5A5" />
		<color name="findmatchhighlightmarker" bg="#FFF4A721" />
		<color name="blockstructurenamespace" fg="#FFB6B6B6" />
		<color name="blockstructuretype" fg="#FF7CCADD" />
		<color name="blockstructuremodule" fg="#FF7CCADD" />
		<color name="blockstructurevaluetype" fg="#FF7CCADD" />
		<color name="blockstructureinterface" fg="#FF7CCADD" />
		<color name="blockstructuremethod" fg="#FF879FFF" />
		<color name="blockstructureaccessor" fg="#FF879FFF" />
		<color name="blockstructureanonymousmethod" fg="#FF879FFF" />
		<color name="blockstructureconstructor" fg="#FF879FFF" />
		<color name="blockstructuredestructor" fg="#FF879FFF" />
		<color name="blockstructureoperator" fg="#FF879FFF" />
		<color name="blockstructureconditional" fg="#FF7CCC87" />
		<color name="blockstructureloop" fg="#FFFF00DC" />
		<color name="blockstructureproperty" fg="#FF879FFF" />
		<color name="blockstructureevent" fg="#FF879FFF" />
		<color name="blockstructuretry" fg="#FFFF776D" />
		<color name="blockstructurecatch" fg="#FFFF776D" />
		<color name="blockstructurefilter" fg="#FFFF776D" />
		<color name="blockstructurefinally" fg="#FFFF776D" />
		<color name="blockstructurefault" fg="#FFFF776D" />
		<color name="blockstructurelock" fg="#FFB6B6B6" />
		<color name="blockstructureusing" fg="#FFB6B6B6" />
		<color name="blockstructurefixed" fg="#FFB6B6B6" />
		<color name="blockstructureswitch" fg="#FF7CCC87" />
		<color name="blockstructurecase" fg="#FF7CCC87" />
		<color name="blockstructurelocalfunction" fg="#FF879FFF" />
		<color name="blockstructureother" fg="#FFB6B6B6" />
		<color name="blockstructurexml" fg="#FFB6B6B6" />
		<color name="blockstructurexaml" fg="#FFB6B6B6" />
		<color name="completionmatchhighlight" bg="#FFF4A721" />
		<color name="completionsuffix" fg="Black" />
		<color name="signaturehelpdocumentation" bold="true" />
		<color name="signaturehelpcurrentparameter" bold="true" bg="#FFF4A721" />
		<color name="signaturehelpparameter" bold="true" italic="true" />
		<color name="signaturehelpparameterdocumentation" italic="true" />
		<color name="url" fg="blue" />
		<color name="hexpedosheader" fg="#004A7F" />
		<color name="hexpefileheader" fg="#004A7F" />
		<color name="hexpeoptionalheader32" fg="#004A7F" />
		<color name="hexpeoptionalheader64" fg="#004A7F" />
		<color name="hexpesection" fg="#004A7F" />
		<color name="hexpesectionname" fg="#A31515" />
		<color name="hexcor20header" fg="#7F3300" />
		<color name="hexstoragesignature" fg="#7F3300" />
		<color name="hexstorageheader" fg="#7F3300" />
		<color name="hexstoragestream" fg="#7F3300" />
		<color name="hexstoragestreamname" fg="#A31515" />
		<color name="hexstoragestreamnameinvalid" fg="#FF0000" />
		<color name="hextablesstream" fg="#7F3300" />
		<color name="hextablename" fg="#A31515" />
		<color name="documentlistmatchhighlight" bg="#FFF4A721" />
		<color name="gacmatchhighlight" bg="#FFF4A721" />
		<color name="appsettingstreeviewnodematchhighlight" bg="#FFF4A721" />
		<color name="appsettingstextmatchhighlight" bg="#FFF4A721" />
		<color name="hexcurrentline" fg="#A0A0A0" />
		<color name="hexcurrentlinenofocus" fg="#A0A0A0" />
		<color name="hexinactiveselectedtext" bg="#FFBFCDDB" />
		<color name="hexcolumnline0" fg="#FF000000" />
		<color name="hexcolumnline1" fg="#FF000000" />
		<color name="hexcolumnlinegroup0" fg="#FF000000" />
		<color name="hexcolumnlinegroup1" fg="#FF000000" />
		<color name="hexhighlightedvaluescolumn" bg="#FFFCFCFC" />
		<color name="hexhighlightedasciicolumn" bg="#FFFCFCFC" />
		<color name="hexglyphmargin" bg="#FFE6E7E8" />
		<color name="hexcurrentvaluecell" bg="#FFDBE0CC" />
		<color name="hexcurrentasciicell" bg="#FFDBE0CC" />
		<color name="outputwindowtext" fg="Black" bg="White" />
		<color name="hexfindmatchhighlightmarker" bg="#FFF4A721" />
		<color name="hextooltipservicefield0" bg="#FFB3B6D3" fg="#FF888AA0" />
		<color name="hextooltipservicefield1" bg="#FFDDCCAF" fg="#FFA09480" />
		<color name="hextooltipservicecurrentfield" bg="#FFB8DB95" fg="#FF74895F" />
		<color name="listfindmatchhighlight" bg="#FFF4A721" />
		<color name="debuglogtrace" fg="#FF0000CC" />
		<color name="debuglogextensionmessage" fg="#FFFF3DE5" />
		<color name="debuggervaluechangedhighlight" bg="#FFF4A721" />
		<color name="debugexceptionname" fg="Black" />
		<color name="debugstowedexceptionname" fg="Black" />
		<color name="debugreturnvaluename" fg="Black" />
		<color name="debugvariablename" fg="Black" />
		<color name="debugobjectidname" fg="Black" />
		<color name="debuggerdisplayattributeeval" fg="#2E387C" />
		<color name="debuggernostringquoteseval" fg="#2E387C" />
		<color name="debugviewpropertyname" fg="#996633" />
		<color name="asmcomment" fg="#008000" />
		<color name="asmdirective" fg="#6F42C1" />
		<color name="asmprefix" fg="#D73A49" />
		<color name="asmmnemonic" fg="#D73A49" />
		<color name="asmkeyword" fg="#6F42C1" />
		<color name="asmoperator" fg="#000000" />
		<color name="asmpunctuation" fg="#000000" />
		<color name="asmnumber" fg="#5B2DA8" />
		<color name="asmregister" fg="#E36209" />
		<color name="asmselectorvalue" fg="#5B2DA8" />
		<color name="asmlabeladdress" fg="#5B2DA8" />
		<color name="asmfunctionaddress" fg="#5B2DA8" />
		<color name="asmlabel" fg="#663300" />
		<color name="asmfunction" fg="#339966" />
		<color name="asmdata" fg="#CC3399" />
		<color name="asmaddress" fg="#5B2DA8" />
		<color name="asmhexbytes" fg="#000000" />

		<color name="systemcolorscontrol" bg="#FFF0F0F0" />
		<color name="systemcolorscontroldark" bg="#FFA0A0A0" />
		<color name="systemcolorscontroldarkdark" bg="#FF696969" />
		<color name="systemcolorscontrollight" bg="#FFE3E3E3" />
		<color name="systemcolorscontrollightlight" bg="#FFFFFFFF" />
		<color name="systemcolorscontroltext" fg="#FF000000" />
		<color name="systemcolorsgraytext" fg="#FF6D6D6D" />
		<color name="systemcolorshighlight" bg="#FF3399FF" />
		<color name="systemcolorshighlighttext" fg="#FFFFFFFF" />
		<color name="systemcolorsinactivecaption" bg="#FFBFCDDB" />
		<color name="systemcolorsinactivecaptiontext" fg="#FF000000" />
		<color name="systemcolorsinactiveselectionhighlight" bg="#FFF0F0F0" />
		<color name="systemcolorsinactiveselectionhighlighttext" fg="#FF000000" />
		<color name="systemcolorsmenutext" fg="#FF000000" />
		<color name="systemcolorswindow" bg="#FFFFFFFF" />
		<color name="systemcolorswindowtext" fg="#FF000000" />
		<color name="pehex" fg="Black" bg="White" />
		<color name="pehexborder" bg="White" />
		<color name="dialogwindow" fg="#FF1E1E1E" bg="#FFD6DBE9" />
		<color name="dialogwindowactivecaption" fg="#FF000000" bg="#FFC9CEDB" />
		<color name="dialogwindowactivedebuggingborder" bg="#FF0D202D" />
		<color name="dialogwindowactivedefaultborder" bg="#FF0D202D" />
		<color name="dialogwindowbuttonhoverinactive" bg="#FFFFFCF4" />
		<color name="dialogwindowbuttonhoverinactiveborder" bg="#FFE5C365" />
		<color name="dialogwindowbuttonhoverinactiveglyph" bg="#FF000000" />
		<color name="dialogwindowbuttoninactiveborder" bg="#00000000" />
		<color name="dialogwindowbuttoninactiveglyph" bg="#FF000000" />
		<color name="dialogwindowinactiveborder" bg="#FF525252" />
		<color name="dialogwindowinactivecaption" fg="#99000000" bg="#FFC9CEDB" />
		<color name="environmentbackgroundbrush" bg="#FF35496A" />
		<color name="environmentbackground" fg="#FF293955" bg="#FF35496A" color3="#FF35496A" color4="#FF293955" />
		<color name="environmentforeground" fg="White" />
		<color name="environmentmainwindowactivecaption" fg="#FF000000" bg="#FFD6DBE9" />
		<color name="environmentmainwindowactivedebuggingborder" bg="#FF0D202D" />
		<color name="environmentmainwindowactivedefaultborder" bg="#FF0D202D" />
		<color name="environmentmainwindowbuttonactiveborder" bg="#00000000" />
		<color name="environmentmainwindowbuttonactiveglyph" bg="#FF000000" />
		<color name="environmentmainwindowbuttondown" bg="#FFFFE8A6" />
		<color name="environmentmainwindowbuttondownborder" bg="#FFE5C365" />
		<color name="environmentmainwindowbuttondownglyph" bg="#FF000000" />
		<color name="environmentmainwindowbuttonhoveractive" bg="#FFFFFCF4" />
		<color name="environmentmainwindowbuttonhoveractiveborder" bg="#FFE5C365" />
		<color name="environmentmainwindowbuttonhoveractiveglyph" bg="#FF000000" />
		<color name="environmentmainwindowbuttonhoverinactive" bg="#FFFFFCF4" />
		<color name="environmentmainwindowbuttonhoverinactiveborder" bg="#FFE5C365" />
		<color name="environmentmainwindowbuttonhoverinactiveglyph" bg="#FF000000" />
		<color name="environmentmainwindowbuttoninactiveborder" bg="#00000000" />
		<color name="environmentmainwindowbuttoninactiveglyph" bg="#FF000000" />
		<color name="environmentmainwindowinactiveborder" bg="#FF525252" />
		<color name="environmentmainwindowinactivecaption" fg="#99000000" bg="#FFD6DBE9" />
		<color name="controlshadow" bg="#71000000" />
		<color name="gridsplitterpreviewfill" bg="#80000000" />
		<color name="groupboxborderbrush" bg="#FFB1B4BC" />
		<color name="toplevelmenuheaderhoverborder" bg="#FFE5C365" />
		<color name="toplevelmenuheaderhover" bg="#FFFDF4BF" />
		<color name="menuitemseparatorfilltop" bg="#BEC3CB" />
		<color name="menuitemseparatorfillbottom" bg="Transparent" />
		<color name="menuitemglyphpanelborderbrush" bg="#E5C365" />
		<color name="menuitemhighlightedinnerborder" bg="#FDF4BF" />
		<color name="menuitemdisabledforeground" fg="#808080" />
		<color name="menuitemdisabledglyphpanelbackground" bg="#FFA2A4A5" />
		<color name="menuitemdisabledglyphfill" bg="#808080" />
		<color name="toolbarbuttonpressed" bg="#B79C51" />
		<color name="toolbarseparatorfill" bg="#BEC3CB" />
		<color name="toolbarbuttonhover" bg="#FDF4BF" />
		<color name="toolbarbuttonhoverborder" bg="#E5C365" />
		<color name="toolbarbuttonpressedborder" bg="#A08847" />
		<color name="toolbarmenuborder" bg="#9BA7B7" />
		<color name="toolbarsubmenubackground" bg="#EAF0FF" />
		<color name="toolbarbuttonchecked" fg="#FF000000" bg="#FFCEB05B" />
		<color name="toolbaropenheaderbackground" fg="#EAF0FF" bg="#EAF0FF" />
		<color name="toolbariconverticalbackground" bg="#F2F4FE" />
		<color name="toolbarverticalbackground" fg="#F2F4FE" bg="#F2F4FE" color3="#F2F4FE" />
		<color name="toolbariconbackground" bg="#D6DBE9" />
		<color name="toolbarhorizontalbackground" fg="#D6DBE9" bg="#D6DBE9" color3="#D6DBE9" />
		<color name="toolbardisabledfill" bg="#FFDADADA" />
		<color name="toolbardisabledborder" bg="#FFDADADA" />
		<color name="environmentcommandbar" fg="#FFCFD6E5" bg="#FFCFD6E5" color3="#FFCFD6E5" />
		<color name="environmentcommandbaricon" bg="#FFCFD6E5" />
		<color name="environmentcommandbarmenumouseoversubmenuglyph" bg="#FF000000" />
		<color name="environmentcommandbarmenuseparator" bg="#FFF0F0F0" />
		<color name="environmentcommandbarcheckbox" bg="#FF000000" />
		<color name="environmentcommandbarselectedicon" bg="#FFE9ECEE" />
		<color name="environmentcommandbarcheckboxmouseover" bg="#FF000000" />
		<color name="environmentcommandbarhoveroverselectedicon" bg="#FFFFFCF4" />
		<color name="environmentcommandbarmenuitemmouseover" fg="#FF1E1E1E" bg="#FFFDF4BF" />
		<color name="commoncontrolsbuttoniconbackground" bg="#FFECECF0" />
		<color name="commoncontrolsbutton" fg="#FF1E1E1E" bg="#FFECECF0" />
		<color name="commoncontrolsbuttonborder" bg="#FFACACAC" />
		<color name="commoncontrolsbuttonborderdefault" bg="#FFE5C365" />
		<color name="commoncontrolsbuttonborderdisabled" bg="#FFCCCEDB" />
		<color name="commoncontrolsbuttonborderfocused" bg="#FFE5C365" />
		<color name="commoncontrolsbuttonborderhover" bg="#FFE5C365" />
		<color name="commoncontrolsbuttonborderpressed" bg="#FFE5C365" />
		<color name="commoncontrolsbuttondefault" fg="#FF1E1E1E" bg="#FFECECF0" />
		<color name="commoncontrolsbuttondisabled" fg="#FFA2A4A5" bg="#FFF5F5F5" />
		<color name="commoncontrolsbuttonfocused" fg="#FF1E1E1E" bg="#FFFDF4BF" />
		<color name="commoncontrolsbuttonhover" fg="#FF1E1E1E" bg="#FFFDF4BF" />
		<color name="commoncontrolsbuttonpressed" fg="#FFFFFFFF" bg="#FFE5C365" />
		<color name="commoncontrolscheckboxbackground" bg="#FFFEFEFE" />
		<color name="commoncontrolscheckboxbackgrounddisabled" bg="#FFF6F6F6" />
		<color name="commoncontrolscheckboxbackgroundfocused" bg="#FFFDF4BF" />
		<color name="commoncontrolscheckboxbackgroundhover" bg="#FFFDF4BF" />
		<color name="commoncontrolscheckboxbackgroundpressed" bg="#FFE5C365" />
		<color name="commoncontrolscheckboxborder" bg="#FF717171" />
		<color name="commoncontrolscheckboxborderdisabled" bg="#FFC6C6C6" />
		<color name="commoncontrolscheckboxborderfocused" bg="#FFE5C365" />
		<color name="commoncontrolscheckboxborderhover" bg="#FFE5C365" />
		<color name="commoncontrolscheckboxborderpressed" bg="#FFE5C365" />
		<color name="commoncontrolscheckboxglyph" bg="#FF1E1E1E" />
		<color name="commoncontrolscheckboxglyphdisabled" bg="#FFA2A4A5" />
		<color name="commoncontrolscheckboxglyphfocused" bg="#FF424242" />
		<color name="commoncontrolscheckboxglyphhover" bg="#FF424242" />
		<color name="commoncontrolscheckboxglyphpressed" bg="#FF1E1E1E" />
		<color name="commoncontrolscheckboxtext" bg="#FF1E1E1E" />
		<color name="commoncontrolscheckboxtextdisabled" bg="#FFA2A4A5" />
		<color name="commoncontrolscheckboxtextfocused" bg="#FF1E1E1E" />
		<color name="commoncontrolscheckboxtexthover" bg="#FF1E1E1E" />
		<color name="commoncontrolscheckboxtextpressed" bg="#FF1E1E1E" />
		<color name="commoncontrolscomboboxbackground" bg="#FFEFEFEF" />
		<color name="commoncontrolscomboboxbackgrounddisabled" bg="#FFDFE7F3" />
		<color name="commoncontrolscomboboxbackgroundfocused" bg="#FFEFEFEF" />
		<color name="commoncontrolscomboboxbackgroundhover" bg="#FFFCFCFC" />
		<color name="commoncontrolscomboboxbackgroundpressed" bg="#FFFCFCFC" />
		<color name="commoncontrolscomboboxborder" bg="#FF8591A2" />
		<color name="commoncontrolscomboboxborderdisabled" bg="#FFA4ADBA" />
		<color name="commoncontrolscomboboxborderfocused" bg="#FFE5C365" />
		<color name="commoncontrolscomboboxborderhover" bg="#FFE5C365" />
		<color name="commoncontrolscomboboxborderpressed" bg="#FFE5C364" />
		<color name="commoncontrolscomboboxglyph" bg="#FF1B293E" />
		<color name="commoncontrolscomboboxglyphbackground" bg="#FFFCFCFC" />
		<color name="commoncontrolscomboboxglyphbackgrounddisabled" bg="#FFD5DCE8" />
		<color name="commoncontrolscomboboxglyphbackgroundfocused" bg="#FFFCFCFC" />
		<color name="commoncontrolscomboboxglyphbackgroundhover" bg="#FFFDF4BF" />
		<color name="commoncontrolscomboboxglyphbackgroundpressed" bg="#FFE5C364" />
		<color name="commoncontrolscomboboxglyphdisabled" bg="#FFA2A4A5" />
		<color name="commoncontrolscomboboxglyphfocused" bg="#FF000000" />
		<color name="commoncontrolscomboboxglyphhover" bg="#FF000000" />
		<color name="commoncontrolscomboboxglyphpressed" bg="#FF000000" />
		<color name="commoncontrolscomboboxlistbackground" bg="#FFEFEFEF" />
		<color name="commoncontrolscomboboxlistborder" bg="#FF9BA7B7" />
		<color name="commoncontrolscomboboxlistitembackgroundhover" bg="#FFFDF4BF" />
		<color name="commoncontrolscomboboxlistitemborderhover" bg="#FFFDF4BF" />
		<color name="commoncontrolscomboboxlistitemtext" bg="#FF000000" />
		<color name="commoncontrolscomboboxlistitemtexthover" bg="#FF000000" />
		<color name="commoncontrolscomboboxseparator" bg="#FF8591A2" />
		<color name="commoncontrolscomboboxseparatorfocused" bg="#FFE5C365" />
		<color name="commoncontrolscomboboxseparatorhover" bg="#FFE5C365" />
		<color name="commoncontrolscomboboxseparatorpressed" bg="#FFE5C365" />
		<color name="commoncontrolscomboboxtext" bg="#FF000000" />
		<color name="commoncontrolscomboboxtextdisabled" bg="#FFA2A4A5" />
		<color name="commoncontrolscomboboxtextfocused" bg="#FF000000" />
		<color name="commoncontrolscomboboxtexthover" bg="#FF000000" />
		<color name="commoncontrolscomboboxtextinputselection" bg="#66007ACC" />
		<color name="commoncontrolscomboboxtextpressed" bg="#FF000000" />
		<color name="commoncontrolsradiobuttonbackground" bg="#FFFEFEFE" />
		<color name="commoncontrolsradiobuttonbackgrounddisabled" bg="#FFF6F6F6" />
		<color name="commoncontrolsradiobuttonbackgroundfocused" bg="#FFFDF4BF" />
		<color name="commoncontrolsradiobuttonbackgroundhover" bg="#FFFDF4BF" />
		<color name="commoncontrolsradiobuttonbackgroundpressed" bg="#FFE5C365" />
		<color name="commoncontrolsradiobuttonborder" bg="#FF717171" />
		<color name="commoncontrolsradiobuttonborderdisabled" bg="#FFC6C6C6" />
		<color name="commoncontrolsradiobuttonborderfocused" bg="#FFE5C365" />
		<color name="commoncontrolsradiobuttonborderhover" bg="#FFE5C365" />
		<color name="commoncontrolsradiobuttonborderpressed" bg="#FFE5C365" />
		<color name="commoncontrolsradiobuttonglyph" bg="#FF1E1E1E" />
		<color name="commoncontrolsradiobuttonglyphdisabled" bg="#FFA2A4A5" />
		<color name="commoncontrolsradiobuttonglyphfocused" bg="#FF424242" />
		<color name="commoncontrolsradiobuttonglyphhover" bg="#FF424242" />
		<color name="commoncontrolsradiobuttonglyphpressed" bg="#FF1E1E1E" />
		<color name="commoncontrolsradiobuttontext" bg="#FF1E1E1E" />
		<color name="commoncontrolsradiobuttontextdisabled" bg="#FFA2A4A5" />
		<color name="commoncontrolsradiobuttontextfocused" bg="#FF1E1E1E" />
		<color name="commoncontrolsradiobuttontexthover" bg="#FF1E1E1E" />
		<color name="commoncontrolsradiobuttontextpressed" bg="#FF1E1E1E" />
		<color name="commoncontrolstextbox" fg="#FF000000" bg="#FFFCFCFC" />
		<color name="commoncontrolstextboxborder" bg="#FF8591A2" />
		<color name="commoncontrolstextboxborderdisabled" bg="#FFA4ADBA" />
		<color name="commoncontrolstextboxbordererror" bg="Red" />
		<color name="commoncontrolstextboxborderfocused" bg="#FFE5C365" />
		<color name="commoncontrolstextboxdisabled" fg="#FFA2A4A5" bg="#FFDFE7F3" />
		<color name="commoncontrolstextboxerror" fg="#FF000000" bg="Pink" />
		<color name="commoncontrolstextboxfocused" fg="#FF000000" bg="#FFFCFCFC" />
		<color name="commoncontrolstextboxmouseoverborder" bg="#FFB79C51" />
		<color name="commoncontrolstextboxselection" bg="#FF3399FF" />
		<color name="commoncontrolsfocusvisual" fg="#FF000000" bg="#FFFFFFFF" />
		<color name="tabitemforeground" bg="#FF000000" />
		<color name="tabitemstaticbackground" bg="#FFF0F0F0" />
		<color name="tabitemstaticborder" bg="#FFACACAC" />
		<color name="tabitemmouseoverbackground" bg="#FFFDF4BF" />
		<color name="tabitemmouseoverborder" bg="#FFE5C365" />
		<color name="tabitemselectedbackground" bg="#FFFFFFFF" />
		<color name="tabitemselectedborder" bg="#FFACACAC" />
		<color name="tabitemdisabledbackground" bg="#FFF0F0F0" />
		<color name="tabitemdisabledborder" bg="#FFD9D9D9" />
		<color name="listboxbackground" bg="White" />
		<color name="listboxborder" bg="Black" />
		<color name="listboxitemmouseoverbackground" bg="#FFF0F0F0" />
		<color name="listboxitemmouseoverborder" bg="#FFE8E8E8" />
		<color name="listboxitemselectedinactivebackground" bg="#FFF0F0F0" />
		<color name="listboxitemselectedinactiveborder" bg="#FFE8E8E8" />
		<color name="listboxitemselectedactivebackground" bg="#FFF0F0F0" />
		<color name="listboxitemselectedactiveborder" bg="#FFE8E8E8" />
		<color name="contextmenubackground" bg="#EAF0FF" />
		<color name="contextmenuborderbrush" bg="#9BA7B7" />
		<color name="contextmenurectanglefill" bg="#F2F4FE" />
		<color name="expanderstaticcirclestroke" bg="#FF333333" />
		<color name="expanderstaticcirclefill" bg="#FFFFFFFF" />
		<color name="expanderstaticarrowstroke" bg="#FF333333" />
		<color name="expandermouseovercirclestroke" bg="#FF5593FF" />
		<color name="expandermouseovercirclefill" bg="#FFF3F9FF" />
		<color name="expandermouseoverarrowstroke" bg="#FF000000" />
		<color name="expanderpressedcirclestroke" bg="#FF3C77DD" />
		<color name="expanderpressedcirclefill" bg="#FFD9ECFF" />
		<color name="expanderpressedarrowstroke" bg="#FF000000" />
		<color name="expanderdisabledcirclestroke" bg="#FFBCBCBC" />
		<color name="expanderdisabledcirclefill" bg="#FFE6E6E6" />
		<color name="expanderdisabledarrowstroke" bg="#FF707070" />
		<color name="progressbarprogress" bg="#FFE5C365" />
		<color name="progressbarbackground" bg="#FFFEFEFE" />
		<color name="progressbarborder" bg="#FFE5C365" />
		<color name="resizegripperforeground" fg="#FFFFFF" bg="#BBC5D7" color3="#6D83A9" />
		<color name="environmentscrollbararrowbackground" bg="#FFE8E8EC" />
		<color name="environmentscrollbararrowdisabledbackground" bg="#FFE8E8EC" />
		<color name="environmentscrollbararrowglyph" bg="#FF868999" />
		<color name="environmentscrollbararrowglyphdisabled" bg="#FFCACBD3" />
		<color name="environmentscrollbararrowglyphmouseover" bg="#FF1C97EA" />
		<color name="environmentscrollbararrowglyphpressed" bg="#FF007ACC" />
		<color name="environmentscrollbararrowmouseoverbackground" bg="#FFE8E8EC" />
		<color name="environmentscrollbararrowpressedbackground" bg="#FFE8E8EC" />
		<color name="environmentscrollbarbackground" bg="#FFE8E8EC" />
		<color name="environmentscrollbarborder" bg="#FFFFFFFF" />
		<color name="environmentscrollbarthumbbackground" bg="#FFC2C3C9" />
		<color name="environmentscrollbarthumbdisabled" bg="#FFE8E8EC" />
		<color name="environmentscrollbarthumbmouseoverbackground" bg="#FF686868" />
		<color name="environmentscrollbarthumbpressedbackground" bg="#FF5B5B5B" />
		<color name="statusbardebugging" fg="White" bg="#CA5100" />
		<color name="tooltipbackground" fg="White" bg="White" />
		<color name="tooltipborderbrush" bg="#767676" />
		<color name="tooltipforeground" fg="Black" />
		<color name="screentip" fg="#FF000000" bg="#FFFFFFE1" />
		<color name="screentipborder" bg="#FF000000" />
		<color name="completiontooltip" fg="#FF1E1E1E" bg="#FFE7E8EC" />
		<color name="completiontooltipborder" bg="#FFCCCEDB" />
		<color name="quickinfo" fg="#FF1E1E1E" bg="#FFE7E8EC" />
		<color name="quickinfoborder" bg="#FFCCCEDB" />
		<color name="signaturehelp" fg="#FF1E1E1E" bg="#FFE7E8EC" />
		<color name="signaturehelpborder" bg="#FFCCCEDB" />
		<color name="cilbutton" fg="#FF1E1E1E" bg="Transparent" />
		<color name="cilbuttonborder" bg="Transparent" />
		<color name="cilbuttonborderfocused" bg="#FFE5C365" />
		<color name="cilbuttonborderhover" bg="#FFE5C365" />
		<color name="cilbuttonborderpressed" bg="#FFE5C365" />
		<color name="cilbuttonerror" bg="Pink" />
		<color name="cilbuttonerrorborder" bg="Red" />
		<color name="cilbuttonfocused" fg="#FF1E1E1E" bg="#FFE0E0E0" />
		<color name="cilbuttonhover" fg="#FF1E1E1E" bg="#FFE0E0E0" />
		<color name="cilbuttonpressed" fg="#FFFFFFFF" bg="#FFC0C0C0" />
		<color name="cilcheckboxbackground" bg="#FFFEFEFE" />
		<color name="cilcheckboxbackgrounddisabled" bg="#FFF6F6F6" />
		<color name="cilcheckboxbackgroundfocused" bg="#FFFDF4BF" />
		<color name="cilcheckboxbackgroundhover" bg="#FFFDF4BF" />
		<color name="cilcheckboxbackgroundpressed" bg="#FFE5C365" />
		<color name="cilcheckboxborder" bg="#FF717171" />
		<color name="cilcheckboxborderdisabled" bg="#FFC6C6C6" />
		<color name="cilcheckboxborderfocused" bg="#FFE5C365" />
		<color name="cilcheckboxborderhover" bg="#FFE5C365" />
		<color name="cilcheckboxborderpressed" bg="#FFE5C365" />
		<color name="cilcheckboxglyph" bg="#FF1E1E1E" />
		<color name="cilcheckboxglyphdisabled" bg="#FFA2A4A5" />
		<color name="cilcheckboxglyphfocused" bg="#FF424242" />
		<color name="cilcheckboxglyphhover" bg="#FF424242" />
		<color name="cilcheckboxglyphpressed" bg="#FF1E1E1E" />
		<color name="cilcheckboxtext" bg="#FF1E1E1E" />
		<color name="cilcheckboxtextdisabled" bg="#FFA2A4A5" />
		<color name="cilcheckboxtextfocused" bg="#FF424242" />
		<color name="cilcheckboxtexthover" bg="#FF424242" />
		<color name="cilcheckboxtextpressed" bg="#FF1E1E1E" />
		<color name="cilcomboboxborderfocused" bg="#FFE5C365" />
		<color name="cilcomboboxborderhover" bg="#FFE5C365" />
		<color name="cilcomboboxborderpressed" bg="#FFE5C364" />
		<color name="cilcomboboxerror" bg="Pink" />
		<color name="cilcomboboxerrorborder" bg="Red" />
		<color name="cilcomboboxlistbackground" bg="#FFFFFFFF" />
		<color name="cilcomboboxlistborder" bg="#FF9BA7B7" />
		<color name="cilcomboboxlistitembackgroundhover" bg="#FFF8F8F8" />
		<color name="cilcomboboxlistitemborderhover" bg="#FFF0F0F0" />
		<color name="cilcomboboxlistitemtexthover" bg="#FF000000" />
		<color name="cilgridviewborder" bg="Black" />
		<color name="cilgridviewitemcontainermouseoverhoverborder" bg="#FFF0F0F0" />
		<color name="cilgridviewitemcontainerselectedborder" bg="#FFF0F0F0" />
		<color name="cilgridviewitemcontainerselectedinactiveborder" bg="#FFF0F0F0" />
		<color name="cilgridviewitemcontainerselectedmouseoverborder" bg="#FFE0E0E0" />
		<color name="cilgridviewlistitemhoverfill" bg="#FFF8F8F8" />
		<color name="cilgridviewlistitemselectedfill" bg="#FFF8F8F8" />
		<color name="cilgridviewlistitemselectedhoverfill" bg="#FFE8E8E8" />
		<color name="cilgridviewlistitemselectedinactivefill" bg="#FFF8F8F8" />
		<color name="cilgridviewlistviewitemfocusvisualstroke" bg="#FFD0D0D0" />
		<color name="cillistboxborder" bg="Black" />
		<color name="cillistboxitemmouseoverbackground" bg="#FFF8F8F8" />
		<color name="cillistboxitemmouseoverborder" bg="#FFF0F0F0" />
		<color name="cillistboxitemselectedactivebackground" bg="#FFF8F8F8" />
		<color name="cillistboxitemselectedactiveborder" bg="#FFF0F0F0" />
		<color name="cillistboxitemselectedinactivebackground" bg="#FFF8F8F8" />
		<color name="cillistboxitemselectedinactiveborder" bg="#FFF0F0F0" />
		<color name="cillistviewitem0" bg="White" />
		<color name="cillistviewitem1" bg="White" />
		<color name="ciltextboxdisabled" fg="#FFA2A4A5" bg="#FFD5DCE8" />
		<color name="ciltextboxdisabledborder" bg="#FFA4ADBA" />
		<color name="ciltextboxerror" fg="#FF000000" bg="Pink" />
		<color name="ciltextboxerrorborder" bg="Red" />
		<color name="ciltextboxfocusedborder" bg="#FFE5C365" />
		<color name="ciltextboxmouseoverborder" bg="#FFE5C365" />
		<color name="ciltextboxselection" bg="#FF3399FF" />
		<color name="gridviewbackground" bg="White" />
		<color name="gridviewborder" bg="Black" />
		<color name="headerdefault" fg="#FF1E1E1E" bg="#FFF6F6F6" />
		<color name="headerglyph" bg="#FF717171" />
		<color name="headermousedown" fg="#FFFFFFFF" bg="#FF007ACC" />
		<color name="headermouseover" fg="#FF1E1E1E" bg="#FFFEFEFE" />
		<color name="headermouseoverglyph" bg="#FF1E1E1E" />
		<color name="headerseparatorline" bg="#FFCCCEDB" />
		<color name="gridviewlistviewforeground" bg="#1E1E1E" />
		<color name="gridviewitemcontainermouseoverhoverborder" bg="#FFE8E8E8" />
		<color name="gridviewitemcontainerselectedborder" bg="#FFE8E8E8" />
		<color name="gridviewitemcontainerselectedinactiveborder" bg="#FFE8E8E8" />
		<color name="gridviewitemcontainerselectedmouseoverborder" bg="#FFE8E8E8" />
		<color name="gridviewlistitemhoverfill" bg="#FFF0F0F0" />
		<color name="gridviewlistitemselectedfill" bg="#FFF0F0F0" />
		<color name="gridviewlistitemselectedhoverfill" bg="#FFF0F0F0" />
		<color name="gridviewlistitemselectedinactivefill" bg="#FFF0F0F0" />
		<color name="gridviewlistviewitemfocusvisualstroke" bg="#FFE8E8E8" />
		<color name="decompilertextviewwaitadorner" fg="Black" bg="#C0FFFFFF" />
		<color name="listarrowbackground" bg="Black" />
		<color name="treeviewitemmouseover" fg="Black" bg="#FFE8E8E8" />
		<color name="treeviewitemselected" fg="Black" bg="#FFD8D8D8" />
		<color name="treeview" fg="#FF000000" bg="#FFFFFFFF" />
		<color name="treeviewborder" bg="Black" />
		<color name="treeviewglyph" bg="#FF1E1E1E" />
		<color name="treeviewglyphmouseover" bg="#FF007ACC" />
		<color name="tvitemalternationbackground" bg="WhiteSmoke" />
		<color name="appsettingstreeview" fg="#FF000000" bg="#FFFFFFFF" />
		<color name="appsettingstreeviewborder" bg="Black" />
		<color name="environmentfiletabbackground" bg="#FF364E6F" />
		<color name="environmentfiletabborder" bg="#FF364E6F" />
		<color name="environmentfiletabbuttondowninactiveborder" bg="#FFE5C365" />
		<color name="environmentfiletabbuttondowninactive" bg="#FFFDF4BF" />
		<color name="environmentfiletabbuttondowninactiveglyph" bg="#FF000000" />
		<color name="environmentfiletabbuttondownselectedactiveborder" bg="#FFE5C365" />
		<color name="environmentfiletabbuttondownselectedactive" bg="#FFFDF4BF" />
		<color name="environmentfiletabbuttondownselectedactiveglyph" bg="#FF000000" />
		<color name="environmentfiletabbuttondownselectedinactiveborder" bg="#FFE5C365" />
		<color name="environmentfiletabbuttondownselectedinactive" bg="#FFFDF4BF" />
		<color name="environmentfiletabbuttondownselectedinactiveglyph" bg="#FF000000" />
		<color name="environmentfiletabbuttonhoverinactiveborder" bg="#FFE5C365" />
		<color name="environmentfiletabbuttonhoverinactive" bg="#FFFFFCF4" />
		<color name="environmentfiletabbuttonhoverinactiveglyph" bg="#FF000000" />
		<color name="environmentfiletabbuttonhoverselectedactiveborder" bg="#FFE5C365" />
		<color name="environmentfiletabbuttonhoverselectedactive" bg="#FFFFFCF4" />
		<color name="environmentfiletabbuttonhoverselectedactiveglyph" bg="#FF000000" />
		<color name="environmentfiletabbuttonhoverselectedinactiveborder" bg="#FFE5C365" />
		<color name="environmentfiletabbuttonhoverselectedinactive" bg="#FFFFFCF4" />
		<color name="environmentfiletabbuttonhoverselectedinactiveglyph" bg="#FF000000" />
		<color name="environmentfiletabbuttonselectedactiveglyph" bg="#FF75633D" />
		<color name="environmentfiletabbuttonselectedinactiveglyph" bg="#FFCED4DD" />
		<color name="environmentfiletabinactiveborder" bg="#FF4D6082" />
		<color name="environmentfiletabinactivegradient" fg="#FF4D6082" bg="#FF4D6082" />
		<color name="environmentfiletabinactivetext" bg="#FFFFFFFF" />
		<color name="environmentfiletabselectedborder" bg="#FFFFF29D" />
		<color name="environmentfiletabselectedgradient" fg="#FFFFF29D" bg="#FFFFF29D" color3="#FFFFF29D" color4="#FFFFF29D" />
		<color name="environmentfiletabselectedtext" bg="#FF000000" />
		<color name="environmentfiletabtext" bg="#FFFFFFFF" />
		<color name="environmentfiletabhotgradient" fg="#FF5B7199" bg="#FF5B7199" />
		<color name="environmentfiletabhotborder" bg="#FF5B7199" />
		<color name="environmentfiletabhottext" bg="#FFFFFFFF" />
		<color name="environmentfiletabhotglyph" bg="#FFCED4DD" />
		<color name="environmenttitlebaractive" fg="#FF000000" bg="#FFFFF29D" />
		<color name="environmenttitlebaractiveborder" bg="#FFFFF29D" />
		<color name="environmenttitlebaractivegradient" fg="#FFFFF29D" bg="#FFFFF29D" color3="#FFFFF29D" color4="#FFFFF29D" />
		<color name="environmenttitlebardraghandle" bg="#FF4D6082" />
		<color name="environmenttitlebardraghandleactive" bg="#FFFFF29D" />
		<color name="environmenttitlebarinactive" fg="#FFFFFFFF" bg="#FF4D6082" />
		<color name="environmenttitlebarinactiveborder" bg="#FF4D6082" />
		<color name="environmenttitlebarinactivegradient" fg="#FF4D6082" bg="#FF4D6082" />
		<color name="environmenttoolwindow" bg="#FFFFFFFF" />
		<color name="environmenttoolwindowborder" bg="#FF8E9BBC" />
		<color name="environmenttoolwindowbuttonactiveglyph" bg="#FF75633D" />
		<color name="environmenttoolwindowbuttondown" bg="#FFFFE8A6" />
		<color name="environmenttoolwindowbuttondownactiveglyph" bg="#FF000000" />
		<color name="environmenttoolwindowbuttondownborder" bg="#FFE5C365" />
		<color name="environmenttoolwindowbuttonhoveractive" bg="#FFFFFCF4" />
		<color name="environmenttoolwindowbuttonhoveractiveborder" bg="#FFE5C365" />
		<color name="environmenttoolwindowbuttonhoveractiveglyph" bg="#FF000000" />
		<color name="environmenttoolwindowbuttonhoverinactive" bg="#FFFFFCF4" />
		<color name="environmenttoolwindowbuttonhoverinactiveborder" bg="#FFE5C365" />
		<color name="environmenttoolwindowbuttonhoverinactiveglyph" bg="#FF000000" />
		<color name="environmenttoolwindowbuttoninactiveglyph" bg="#FFCED4DD" />
		<color name="environmenttoolwindowtabborder" bg="#FF4B5C74" />
		<color name="environmenttoolwindowtabgradient" fg="#FF4D6082" bg="#FF4D6082" />
		<color name="environmenttoolwindowtabmouseoverbackgroundgradient" fg="#FF5B7199" bg="#FF5B7199" />
		<color name="environmenttoolwindowtabmouseoverborder" bg="#FF5B7199" />
		<color name="environmenttoolwindowtabmouseovertext" fg="#FFFFFFFF" />
		<color name="environmenttoolwindowtabselectedactivetext" fg="#FF000000" />
		<color name="environmenttoolwindowtabselectedborder" bg="#FFFFFFFF" />
		<color name="environmenttoolwindowtabselectedtab" bg="#FFFFFFFF" />
		<color name="environmenttoolwindowtabselectedtext" fg="#FF000000" />
		<color name="environmenttoolwindowtabtext" fg="#FFFFFFFF" />
		<color name="searchboxwatermark" fg="#FF6D6D6D" />
		<color name="memorywindowdisabled" bg="#40000000" />
		<color name="treeviewnode" fg="#FF000000" />
		<color name="environmentdropdownglyph" bg="#FF1B293E" />
		<color name="environmentdropdownmouseoverglyph" bg="#FF000000" />
		<color name="environmentdropdownmousedownglyph" bg="#FF000000" />
		<color name="environmentcommandbarmouseoverbackground" fg="#FFFDF4BF" bg="#FFFDF4BF" color3="#FFFDF4BF" color4="#FFFDF4BF" />
		<color name="environmentcommandbarmousedownbackground" fg="#FFFFF29D" bg="#FFFFF29D" color3="#FFFFF29D" />
		<color name="environmentcomboboxdisabledbackground" bg="#FFD5DCE8" />
		<color name="environmenticongeneralstroke" bg="#00000000" />
		<color name="environmenticongeneralfill" bg="#FF424242" />
		<color name="environmenticonactionfill" bg="#FF00529B" />
		<color name="searchcontrolmouseoverdropdownbuttonglyph" bg="#FF1E1E1E" />
		<color name="hexsearchcontrolmouseoverdropdownbuttonglyph" bg="#FF1E1E1E" />
		<color name="hexsearchingtextbox" fg="#FF000000" bg="#FFB7B7B7" />
		<color name="hexsearchingtextboxborder" bg="#FF0048B5" />
		<color name="environmentcommandbartoolbarseparator" bg="#FF8591A2" />
		<color name="environmentcommandbartoolbarseparatorhighlight" bg="#FFD6DBE9" />
		<color name="debuggerbreakpointglyphmargincontrolborder" bg="#FFDCE0EC" />
		<color name="debuggerbreakpointglyphmargincontrolbackground" bg="#FFF0F0F0" />
		<color name="debuggerbreakpointglyphmargincontrolhoverbackground" bg="#FFFFFCF4" />
		<color name="hyperlinknormal" bg="#FF0066CC" />
		<color name="hyperlinkmouseover" bg="Red" />
		<color name="hyperlinkdisabled" bg="#FF6D6D6D" />
	</colors>
</theme>
