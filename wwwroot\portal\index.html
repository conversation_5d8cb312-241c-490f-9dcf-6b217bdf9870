<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppTech Portal - ظ†ط¸ط§ظ… ط¥ط¯ط§ط±ط© ط§ظ„ط£ط¹ظ…ط§ظ„</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            direction: rtl;
        }
        
        .navbar-custom {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
            color: white !important;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            color: white !important;
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }
        
        .main-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        
        .main-header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-weight: 700;
        }
        
        .dashboard-grid {
            margin-top: -30px;
            position: relative;
            z-index: 10;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            border: none;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .card-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .card-description {
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
            color: white;
            transform: translateY(-2px);
        }
        
        .stats-section {
            background: white;
            padding: 40px 0;
            margin: 40px 0;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #3498db;
            display: block;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 1.1rem;
            margin-top: 10px;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0;
            margin-top: 60px;
        }
        
        .alert-custom {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .breadcrumb-custom {
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="/portal">
                <i class="fas fa-rocket me-2"></i>
                AppTech Portal
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/portal">
                            <i class="fas fa-home me-1"></i>ط§ظ„ط±ط¦ظٹط³ظٹط©
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#dashboard">
                            <i class="fas fa-chart-line me-1"></i>ظ„ظˆط­ط© ط§ظ„طھط­ظƒظ…
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#agents">
                            <i class="fas fa-users me-1"></i>ط§ظ„ظˆظƒظ„ط§ط،
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#clients">
                            <i class="fas fa-building me-1"></i>ط§ظ„ط¹ظ…ظ„ط§ط،
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#merchants">
                            <i class="fas fa-store me-1"></i>ط§ظ„طھط¬ط§ط±
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/api">
                            <i class="fas fa-plug me-1"></i>API
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/collections_system">
                            <i class="fas fa-money-bill me-1"></i>ط§ظ„طھط­طµظٹظ„ط§طھ
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Header -->
    <div class="main-header">
        <div class="container">
            <h1><i class="fas fa-rocket me-3"></i>ظ…ط±ط­ط¨ط§ظ‹ ط¨ظƒ ظپظٹ AppTech Portal</h1>
            <p class="lead">ظ†ط¸ط§ظ… ط¥ط¯ط§ط±ط© ط§ظ„ط£ط¹ظ…ط§ظ„ ط§ظ„ظ…طھظƒط§ظ…ظ„ - ط§ظ„ظˆط§ط¬ظ‡ط© ط§ظ„ط£طµظ„ظٹط©</p>
            <div class="breadcrumb-custom d-inline-block">
                <i class="fas fa-info-circle me-2"></i>
                ط§ظ„ظ†ط¸ط§ظ… ظٹط¹ظ…ظ„ ظپظٹ ظˆط¶ط¹ ط§ظ„ط¹ط±ط¶ ط§ظ„طھظˆط¶ظٹط­ظٹ - ط¬ظ…ظٹط¹ ط§ظ„ظˆط¸ط§ط¦ظپ ظ…طھط§ط­ط© ظ„ظ„ط§ط³طھط¹ط±ط§ط¶
            </div>
        </div>
    </div>

    <!-- Dashboard Grid -->
    <div class="container dashboard-grid">
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card text-center">
                    <i class="fas fa-chart-line card-icon text-primary"></i>
                    <h3 class="card-title">ظ„ظˆط­ط© ط§ظ„طھط­ظƒظ…</h3>
                    <p class="card-description">ط¹ط±ط¶ ط´ط§ظ…ظ„ ظ„ظ„ظ†ط¸ط§ظ… ظˆط§ظ„ط¥ط­طµط§ط¦ظٹط§طھ ط§ظ„ط±ط¦ظٹط³ظٹط© ظˆط§ظ„ظ…ط¤ط´ط±ط§طھ ط§ظ„ظ…ظ‡ظ…ط©</p>
                    <a href="#dashboard" class="btn btn-custom">
                        <i class="fas fa-arrow-left me-2"></i>ط¯ط®ظˆظ„ ظ„ظˆط­ط© ط§ظ„طھط­ظƒظ…
                    </a>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card text-center">
                    <i class="fas fa-users card-icon text-success"></i>
                    <h3 class="card-title">ط¥ط¯ط§ط±ط© ط§ظ„ظˆظƒظ„ط§ط،</h3>
                    <p class="card-description">ط¥ط¶ط§ظپط© ظˆط¥ط¯ط§ط±ط© ط­ط³ط§ط¨ط§طھ ط§ظ„ظˆظƒظ„ط§ط، ظˆط§ظ„ظ…ظ†ط¯ظˆط¨ظٹظ† ظˆطھطھط¨ط¹ ط£ط¯ط§ط¦ظ‡ظ…</p>
                    <a href="#agents" class="btn btn-custom">
                        <i class="fas fa-arrow-left me-2"></i>ط¥ط¯ط§ط±ط© ط§ظ„ظˆظƒظ„ط§ط،
                    </a>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card text-center">
                    <i class="fas fa-building card-icon text-info"></i>
                    <h3 class="card-title">ط¥ط¯ط§ط±ط© ط§ظ„ط¹ظ…ظ„ط§ط،</h3>
                    <p class="card-description">ط¥ط¯ط§ط±ط© ط­ط³ط§ط¨ط§طھ ط§ظ„ط¹ظ…ظ„ط§ط، ظˆط§ظ„ط®ط¯ظ…ط§طھ ط§ظ„ظ…ظ‚ط¯ظ…ط© ظˆظ…طھط§ط¨ط¹ط© ط§ظ„ظ…ط¹ط§ظ…ظ„ط§طھ</p>
                    <a href="#clients" class="btn btn-custom">
                        <i class="fas fa-arrow-left me-2"></i>ط¥ط¯ط§ط±ط© ط§ظ„ط¹ظ…ظ„ط§ط،
                    </a>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card text-center">
                    <i class="fas fa-store card-icon text-warning"></i>
                    <h3 class="card-title">ط¥ط¯ط§ط±ط© ط§ظ„طھط¬ط§ط±</h3>
                    <p class="card-description">ط¥ط¯ط§ط±ط© ط­ط³ط§ط¨ط§طھ ط§ظ„طھط¬ط§ط± ظˆط§ظ„ظ…ط¹ط§ظ…ظ„ط§طھ ط§ظ„طھط¬ط§ط±ظٹط© ظˆط§ظ„ظ…ط¯ظپظˆط¹ط§طھ</p>
                    <a href="#merchants" class="btn btn-custom">
                        <i class="fas fa-arrow-left me-2"></i>ط¥ط¯ط§ط±ط© ط§ظ„طھط¬ط§ط±
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Section -->
    <div class="container">
        <div class="stats-section">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-item">
                        <span class="stat-number">1,234</span>
                        <div class="stat-label">ط¥ط¬ظ…ط§ظ„ظٹ ط§ظ„ظˆظƒظ„ط§ط،</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <span class="stat-number">5,678</span>
                        <div class="stat-label">ط§ظ„ط¹ظ…ظ„ط§ط، ط§ظ„ظ†ط´ط·ظٹظ†</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <span class="stat-number">890</span>
                        <div class="stat-label">ط§ظ„طھط¬ط§ط± ط§ظ„ظ…ط³ط¬ظ„ظٹظ†</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <span class="stat-number">12,345</span>
                        <div class="stat-label">ط§ظ„ظ…ط¹ط§ظ…ظ„ط§طھ ط§ظ„ظٹظˆظ…ظٹط©</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Features -->
    <div class="container">
        <div class="row">
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                        ط§ظ„طھظ‚ط§ط±ظٹط± ظˆط§ظ„ط¥ط­طµط§ط¦ظٹط§طھ
                    </h3>
                    <p class="card-description">طھظ‚ط§ط±ظٹط± ظ…ظپطµظ„ط© ظˆطھط­ظ„ظٹظ„ط§طھ ط´ط§ظ…ظ„ط© ظ„ط¬ظ…ظٹط¹ ط§ظ„ط¹ظ…ظ„ظٹط§طھ ظˆط§ظ„ط£ظ†ط´ط·ط©</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>طھظ‚ط§ط±ظٹط± ط§ظ„ظ…ط¨ظٹط¹ط§طھ ط§ظ„ظٹظˆظ…ظٹط© ظˆط§ظ„ط´ظ‡ط±ظٹط©</li>
                        <li><i class="fas fa-check text-success me-2"></i>ط¥ط­طµط§ط¦ظٹط§طھ ط£ط¯ط§ط، ط§ظ„ظˆظƒظ„ط§ط،</li>
                        <li><i class="fas fa-check text-success me-2"></i>طھط­ظ„ظٹظ„ ط§ظ„ط£ط±ط¨ط§ط­ ظˆط§ظ„ط®ط³ط§ط¦ط±</li>
                        <li><i class="fas fa-check text-success me-2"></i>طھظ‚ط§ط±ظٹط± ط±ط¶ط§ ط§ظ„ط¹ظ…ظ„ط§ط،</li>
                    </ul>
                    <a href="#reports" class="btn btn-custom">
                        <i class="fas fa-chart-line me-2"></i>ط¹ط±ط¶ ط§ظ„طھظ‚ط§ط±ظٹط±
                    </a>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <h3 class="card-title">
                        <i class="fas fa-cogs me-2 text-secondary"></i>
                        ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ظ†ط¸ط§ظ…
                    </h3>
                    <p class="card-description">طھط®طµظٹطµ ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ظ†ط¸ط§ظ… ظˆط§ظ„طھظپط¶ظٹظ„ط§طھ ط§ظ„ط¹ط§ظ…ط© ظˆط§ظ„ط£ظ…ط§ظ†</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ظ…ط³طھط®ط¯ظ…ظٹظ† ظˆط§ظ„طµظ„ط§ط­ظٹط§طھ</li>
                        <li><i class="fas fa-check text-success me-2"></i>ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ط£ظ…ط§ظ† ظˆط§ظ„ط­ظ…ط§ظٹط©</li>
                        <li><i class="fas fa-check text-success me-2"></i>ط§ظ„ظ†ط³ط® ط§ظ„ط§ط­طھظٹط§ط·ظٹط© ط§ظ„طھظ„ظ‚ط§ط¦ظٹط©</li>
                        <li><i class="fas fa-check text-success me-2"></i>ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ط¥ط´ط¹ط§ط±ط§طھ</li>
                    </ul>
                    <a href="#settings" class="btn btn-custom">
                        <i class="fas fa-cog me-2"></i>ط§ظ„ط¥ط¹ط¯ط§ط¯ط§طھ
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status Alert -->
    <div class="container">
        <div class="alert-custom">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5><i class="fas fa-info-circle me-2"></i>ط­ط§ظ„ط© ط§ظ„ظ†ط¸ط§ظ… ط§ظ„ط­ط§ظ„ظٹط©</h5>
                    <p class="mb-0">
                        <strong>ط§ظ„ط­ط§ظ„ط©:</strong> ط§ظ„ظ†ط¸ط§ظ… ظٹط¹ظ…ظ„ ظپظٹ ظˆط¶ط¹ ط§ظ„ط¹ط±ط¶ ط§ظ„طھظˆط¶ظٹط­ظٹ ط¨ط¯ظˆظ† ظ‚ط§ط¹ط¯ط© ط¨ظٹط§ظ†ط§طھ
                        <br>
                        <strong>ط§ظ„ظˆطµظˆظ„:</strong> ط¬ظ…ظٹط¹ ط§ظ„ظˆط¸ط§ط¦ظپ ظ…طھط§ط­ط© ظ„ظ„ط§ط³طھط¹ط±ط§ط¶ ظˆط§ظ„ط§ط®طھط¨ط§ط±
                        <br>
                        <strong>ط¢ط®ط± طھط­ط¯ظٹط«:</strong> <span id="currentTime"></span>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <i class="fas fa-server fa-3x text-success"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>AppTech Portal</h5>
                    <p>ظ†ط¸ط§ظ… ط¥ط¯ط§ط±ط© ط§ظ„ط£ط¹ظ…ط§ظ„ ط§ظ„ظ…طھظƒط§ظ…ظ„ - ط§ظ„ظˆط§ط¬ظ‡ط© ط§ظ„ط£طµظ„ظٹط©</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>آ© 2024 AppTech System - ط¬ظ…ظٹط¹ ط§ظ„ط­ظ‚ظˆظ‚ ظ…ط­ظپظˆط¸ط©</p>
                    <p>طھظ… ط§ظ„طھط·ظˆظٹط± ط¨ظˆط§ط³ط·ط© ظپط±ظٹظ‚ AppTech ط§ظ„ظ…طھط®طµطµ</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // طھط­ط¯ظٹط« ط§ظ„ظˆظ‚طھ
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        }
        updateTime();
        setInterval(updateTime, 1000);

        // طھط£ط«ظٹط±ط§طھ طھظپط§ط¹ظ„ظٹط©
        document.addEventListener('DOMContentLoaded', function() {
            // طھط£ط«ظٹط± ط¸ظ‡ظˆط± ط§ظ„ط¨ط·ط§ظ‚ط§طھ
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // طھط£ط«ظٹط± hover ط¹ظ„ظ‰ ط§ظ„ط¥ط­طµط§ط¦ظٹط§طھ
            const statItems = document.querySelectorAll('.stat-item');
            statItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>