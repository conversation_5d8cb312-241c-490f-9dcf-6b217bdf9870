﻿
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppTech System - PORTAL</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 0; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .header {
            background: rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
            color: white;
        }
        .container { 
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255,255,255,0.95);
            margin-top: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            color: #333;
        }
        .nav-menu {
            background: #2c3e50;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            margin: 0 5px;
            border-radius: 3px;
            display: inline-block;
            transition: background 0.3s;
        }
        .nav-menu a:hover {
            background: #34495e;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .card h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .status-good { border-left-color: #27ae60; }
        .status-warning { border-left-color: #f39c12; }
        .status-info { border-left-color: #3498db; }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background 0.3s;
        }
        .btn:hover { background: #2980b9; }
        .btn-success { background: #27ae60; }
        .btn-warning { background: #f39c12; }
        .footer {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            border-top: 1px solid #ecf0f1;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>ًںڑ€ ظ†ط¸ط§ظ… AppTech - PORTAL</h1>
        <p>ظ†ط¸ط§ظ… ط¥ط¯ط§ط±ط© ط§ظ„ط£ط¹ظ…ط§ظ„ ط§ظ„ظ…طھظƒط§ظ…ظ„</p>
    </div>

    <div class="container">
        <div class="nav-menu">
            <a href="/portal">ًںڈ  ط§ظ„ط¨ظˆط§ط¨ط© ط§ظ„ط±ط¦ظٹط³ظٹط©</a>
            <a href="/api">ًں”Œ ظˆط§ط¬ظ‡ط© API</a>
            <a href="/client">ًں‘¥ ط¨ظˆط§ط¨ط© ط§ظ„ط¹ظ…ظ„ط§ط،</a>
            <a href="/apinewAN">ًں†• API ط§ظ„ط¬ط¯ظٹط¯</a>
            <a href="/collections_system">ًں’° ظ†ط¸ط§ظ… ط§ظ„طھط­طµظٹظ„ط§طھ</a>
        </div>

        <div class="dashboard">
            <div class="card status-good">
                <h3>âœ… ط­ط§ظ„ط© ط§ظ„ظ†ط¸ط§ظ…</h3>
                <p><strong>ط§ظ„ط®ط§ط¯ظ…:</strong> ظٹط¹ظ…ظ„ ط¨ظ†ط¬ط§ط­</p>
                <p><strong>ط§ظ„طھط·ط¨ظٹظ‚:</strong> ظ†ط´ط·</p>
                <p><strong>ط§ظ„ط­ط§ظ„ط©:</strong> ط¬ط§ظ‡ط² ظ„ظ„ط§ط³طھط®ط¯ط§ظ…</p>
                <p><strong>ط§ظ„ظˆظ‚طھ:</strong> </p>
            </div>

            <div class="card status-info">
                <h3>ًں“ٹ ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„طھط·ط¨ظٹظ‚</h3>
                <p><strong>ط§ظ„ظ†ظˆط¹:</strong> PORTAL</p>
                <p><strong>ط§ظ„ط¥طµط¯ط§ط±:</strong> AppTech 2024</p>
                <p><strong>ط§ظ„ط¨ظٹط¦ط©:</strong> Production</p>
                <p><strong>ط§ظ„طھط±ط®ظٹطµ:</strong> ظ…ظپط¹ظ„</p>
            </div>

            <div class="card status-warning">
                <h3>ًں”§ ط§ظ„ظˆط¸ط§ط¦ظپ ط§ظ„ظ…طھط§ط­ط©</h3>
                <p>â€¢ ط¥ط¯ط§ط±ط© ط§ظ„ظ…ط³طھط®ط¯ظ…ظٹظ†</p>
                <p>â€¢ ط§ظ„طھظ‚ط§ط±ظٹط± ظˆط§ظ„ط¥ط­طµط§ط¦ظٹط§طھ</p>
                <p>â€¢ ط¥ط¯ط§ط±ط© ط§ظ„ط¨ظٹط§ظ†ط§طھ</p>
                <p>â€¢ ط§ظ„ظ†ط³ط® ط§ظ„ط§ط­طھظٹط§ط·ظٹط©</p>
            </div>

            <div class="card status-info">
                <h3>ًںŒگ ط§ظ„ط±ظˆط§ط¨ط· ط§ظ„ط³ط±ظٹط¹ط©</h3>
                <a href="/portal" class="btn btn-success">ط§ظ„ط¨ظˆط§ط¨ط© ط§ظ„ط±ط¦ظٹط³ظٹط©</a>
                <a href="/api" class="btn">ظˆط§ط¬ظ‡ط© API</a>
                <a href="/client" class="btn btn-warning">ط§ظ„ط¹ظ…ظ„ط§ط،</a>
            </div>
        </div>

        <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4>ًںژ‰ ظ…ط±ط­ط¨ط§ظ‹ ط¨ظƒ ظپظٹ ظ†ط¸ط§ظ… AppTech</h4>
            <p>ط§ظ„ظ†ط¸ط§ظ… ظٹط¹ظ…ظ„ ط¨ظƒط§ظ…ظ„ ط§ظ„ظˆط¸ط§ط¦ظپ ظˆظٹظ…ظƒظ†ظƒ ط§ظ„ط¢ظ† ط§ط³طھط®ط¯ط§ظ… ط¬ظ…ظٹط¹ ط§ظ„ظ…ظٹط²ط§طھ ط§ظ„ظ…طھط§ط­ط©.</p>
            <p>طھظ… طھط­ط¯ظٹط« ط§ظ„ظ†ط¸ط§ظ… ظˆط¥طµظ„ط§ط­ ط¬ظ…ظٹط¹ ط§ظ„ظ…ط´ط§ظƒظ„ ط§ظ„طھظ‚ظ†ظٹط©.</p>
        </div>

        <div class="footer">
            <p>آ© 2024 AppTech System - ط¬ظ…ظٹط¹ ط§ظ„ط­ظ‚ظˆظ‚ ظ…ط­ظپظˆط¸ط©</p>
            <p>طھظ… ط§ظ„طھط·ظˆظٹط± ظˆط§ظ„طھط­ط¯ظٹط« ط¨ظˆط§ط³ط·ط© ظپط±ظٹظ‚ AppTech</p>
        </div>
    </div>

    <script>
        // طھط­ط¯ظٹط« ط§ظ„ظˆظ‚طھ ظƒظ„ ط«ط§ظ†ظٹط©
        setInterval(function() {
            var timeElements = document.querySelectorAll('.current-time');
            timeElements.forEach(function(element) {
                element.textContent = new Date().toLocaleString('ar-SA');
            });
        }, 1000);

        // ط¥ط¶ط§ظپط© طھط£ط«ظٹط±ط§طھ طھظپط§ط¹ظ„ظٹط©
        document.addEventListener('DOMContentLoaded', function() {
            var cards = document.querySelectorAll('.card');
            cards.forEach(function(card, index) {
                setTimeout(function() {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    
                    setTimeout(function() {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
