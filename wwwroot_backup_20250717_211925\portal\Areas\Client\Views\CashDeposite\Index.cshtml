﻿@{
    Layout = "~/Views/Shared/_CrudLayout.cshtml";
}
@*<label for="pin">PIN: </label><input type="password" name="pin" id="pin" maxlength="4" size="5" value="" title="Correct Pin!" style="z-index: 99999;" />*@

<script>
    $(function() {
        $('#pin').keyup(function(e) {
            if ($(this).val() === "123") {
                $(this).val('');
                try {
                    $(this).balloon({
                        position: "top right",
                        showDuration: 125,
                        minLifetime: 2000,
                        tipSize: 4,
                        css: {
                            fontSize: ".7rem",
                            minWidth: ".7rem",
                            padding: ".2rem .5rem",
                            border: "1px solid rgba(212, 212, 212, .4)",
                            borderRadius: "3px",
                            boxShadow: "2px 2px 4px #555",
                            color: "#eee",
                            backgroundColor: "#111",
                            opacity: "0.85",
                            zIndex: "32767",
                            textAlign: "left"
                        }
                    });

                    $(this).showBalloon();
                } catch (e) {
                    ar(e);
                }
            }
        });
        //$('#test').on('click',
        //    function () {
        //        i('Amount key down');

        //        // var words = toWords($('#Amount').val());
        //        // $(this).attr('title', words);
        //        var words = "hoe thjere";
        //        try {
        //            $(this).balloon({
        //                position: "top right",
        //                showDuration: 125,
        //                minLifetime: 2000,
        //                tipSize: 4
        //            });

        //            $(this).showBalloon();
        //        } catch (e) {
        //            ar(e);
        //        }
        //        i('Amount to words ' + words);
        //    });
    })
</script>