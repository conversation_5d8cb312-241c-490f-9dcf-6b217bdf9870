﻿@using Microsoft.AspNet.Identity
@model AppTech.MSMS.Web.Models.AccountModel

<p>
    المستخدم <strong>@User.Identity.GetUserName()</strong>.
</p>

@using (Ajax.BeginForm("ChangePassword", new AjaxOptions
{
    Confirm = "هل انت متأكد تريد تغيير كلمة المرور",
    OnSuccess = "onSuccess",
    OnFailure = "onFailure",
    LoadingElementId = "loader"
}))
{
    @Html.AntiForgeryToken()
    <h4>تغيير كلمة المرور</h4>
    <hr/>
    @Html.ValidationSummary()
    <div class="form-group">
        @Html.LabelFor(m => m.OldPassword, new {@class = "col-md-2 control-label"})
        <div class="col-md-4">
            @Html.PasswordFor(m => m.OldPassword, new {@class = "form-control"})
            @Html.ValidationMessageFor(m => m.OldPassword, string.Empty, new {@class = "invalid"})
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(m => m.NewPassword, new {@class = "col-md-2 control-label"})
        <div class="col-md-4">
            @Html.PasswordFor(m => m.NewPassword, new {@class = "form-control"})
            @Html.ValidationMessageFor(m => m.NewPassword, string.Empty, new {@class = "invalid"})
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(m => m.ConfirmPassword, new {@class = "col-md-2 control-label"})
        <div class="col-md-4">
            @Html.PasswordFor(m => m.ConfirmPassword, new {@class = "form-control"})
            @Html.ValidationMessageFor(m => m.ConfirmPassword, string.Empty, new {@class = "invalid"})
        </div>
    </div>


    
    <div class="form-group">
        <div class="col-md-offset-2 col-md-10">
            <button class="btn btn-primary btn-info btn-bold btn-block"  type="submit" id="submit-button" value="save">
                <i class="ace-icon fa fa-save bigger-110"></i>
                تغيير كلمة المرور
            </button>
        </div>
    </div>
}

<script>

    $(function() {
        $("#submit-button").text('تغيير كلمة المرور');
    });

    function onSuccess(data) {
        ar(data.Message);
    }

    function onFailure() {
        ar('لم يتمكن من تغيير كلمة المرور');
    }
</script>