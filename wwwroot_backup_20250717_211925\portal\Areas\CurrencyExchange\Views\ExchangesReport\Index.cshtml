﻿@model AppTech.MSMS.Domain.Reports.Models.CurrencyExchangeModel
    @{
        ViewBag.Title = "تقرير البيع والشراء";
        Layout = "~/Views/Shared/_Report.cshtml";
    }

    @{
        Html.RenderPartial("_DateControl");
    }
    <span class="lbl"> النوع </span>
    @Html.EnumDropDownListFor(m => m.Type)

    <div class="hr hr-dotted hr-24"></div>
    <div class="space-6"></div>

    <span class="lbl"> العملة</span>
    @Html.DropDownListFor(m => m.CurrencyID, (SelectList)ViewBag.Currencies)
    <div class="space-6"></div>


    <span class="lbl">اسم الحساب </span>
    <select id="AccountID" name="AccountID" class="select2" placeholder="كافة الحسابات"></select>
    <div class="space-6"></div>


    <span class="lbl">الحساب المقابل </span>
    <select id="ExchangeAccountID" name="ExchangeAccountID" class="select2" placeholder="كافة الحسابات"></select>
    <div class="space-6"></div>


    <span class="lbl">اسم المستخدم </span>
    <select id="UserID" name="UserID" class="select2" placeholder="كافة المستخدمين"></select>
    <div class="space-6"></div>



    <script>
        $(function () {

            fillDataList('AccountID', '/Print/GetAccounts', false, 'كافة الحسابات');
            fillDataList('ExchangeAccountID', '/Print/GetAccounts', false, 'كافة الحسابات');
            fillDataList('UserID', '/Print/GetUsers', false, 'كافة المستخدمين');

            $("#Type")[0].SelectedIndex = 0;

            //select2
            $('.select2').css('width', '200px').select2({ allowClear: true });
            $('#select2-multiple-style .btn').on('click',
                function (e) {
                    var target = $(this).find('input[type=radio]');
                    var which = parseInt(target.val());
                    if (which == 2) $('.select2').addClass('tag-input-style');
                    else $('.select2').removeClass('tag-input-style');
                });
        });

    </script>