﻿@using AppTech.MSMS.Domain
<!DOCTYPE html>
<html lang="ar" dir="rtl" ng-app="AngularMVCApp">
<head>
    <link href="" rel="shortcut icon" type="image/x-icon"/>
    <title>@ClientLicense.Customer.CompanyInfo.Name</title>
    <meta charset="utf-8"/>
    <meta name="keywords" content=""/>
    <meta name="description" content="@AppTechInfo.Name"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")
    @RenderSection("Styles", false)
    
    <style>
        .se-pre-con {
            position: fixed;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            background: url(../../Content/images/loader-64x/Preloader_2.gif) center no-repeat #fff;
        }
    </style>

</head>
<body class="rtl">

    <div class="se-pre-con"></div>
    <audio id="audio" src="@Url.Content("~/Content/sounds/notify.mp3")"></audio>

    @{ Html.RenderPartial("_header"); }
    <div class="main-container" id="main-container">

        <div id="sidebar" class="sidebar responsive skin-1 ">
            <div class="navigation">
                <div class="partialMainContents" data-url="/home/<USER>">
                    @Html.Partial("_Indicator")
                </div>
            </div>
            <div class="sidebar-toggle sidebar-collapse" id="sidebar-collapse">
                <i id="sidebar-toggle-icon" class="ace-icon fa fa-angle-double-left" data-icon1="ace-icon fa fa-angle-double-left" data-icon2="ace-icon fa fa-angle-double-right"></i>
            </div>
        </div>


        <div class="main-content" style="padding: 0px; background: whitesmoke;">
            <div class="main-content-inner">

                <div class="breadcrumbs ace-save-state" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <h4 id="page-title">
                                لوحة المراقبة
                            </h4>
                        </li>

                        <li class="active"></li>
                    </ul><!-- /.breadcrumb -->
                    @*  <div class="nav-search" id="nav-search">
                                       <form class="form-search">
                                    <span class="input-icon">
                                        <input type="text" placeholder="Search ..." class="nav-search-input" id="nav-search-input" autocomplete="off" />
                                        <i class="ace-icon fa fa-search nav-search-icon"></i>
                                    </span>
                                </form>
                        </div><!-- /.nav-search -->*@
                </div><!-- /.breadcrumb -->

                <div class="page-content" style="padding-top: 10px; border-top: solid 1px darkblue">
                    @*<div class="page-header">
                            <h4 id="page-titlexx">
                            </h4>
                        </div>*@

                    <div class="row">
                        <div class="col-xs-12">
                            <section>
                                @RenderBody()
                            </section>
                        </div><!-- /.col -->
                    </div><!-- /.row -->
                </div> <!-- /page-content -->
            </div><!-- /.main-content inner -->
        </div><!-- /.main-content -->
        @Html.Partial("_Footer")

        <a href="" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
            <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
        </a>
        
        <div id="dialog-message" class="hide">
            <div class="modal-body">
                @Html.Partial("_Indicator")
            </div>
        </div>
    </div>

    @Scripts.Render("~/bundles/frenchi")
@*<script src="~/signalr/hubs"></script>*@
<script type="text/javascript">
    if('ontouchstart' in document.documentElement) document.write("<script src='~/Scripts/jquery.mobile.custom.min.js'>"+"<"+"/script>");
</script>
  
    <script>
        try {
            var clipboard = new ClipboardJS('.clipboard');
            clipboard.on('success', function (e) {
                $(e.trigger).text("تم النسخ!");
                e.clearSelection();
                setTimeout(function () {
                    $(e.trigger).text("نسخ");
                }, 2500);
            });

            clipboard.on('error', function (e) {
                $(e.trigger).text("فشل النسخ");
                setTimeout(function () {
                    $(e.trigger).text("نسخ");
                }, 2500);
            });

        }
        catch (e) { }
    </script>


    <script>
        $(window).on('load',
            function () {
                $(".se-pre-con").fadeOut("slow");
            });

        var site = site || {};
        site.baseUrl = site.baseUrl || "";
        $(document).ready(function () {
            
            $("#page-title").val('لوحة المراقبة');
            $(".partialMainContents").each(function (index, item) {
                var url = site.baseUrl + $(item).data("url");
                if (url && url.length > 0) {
                    $(item).load(url);
                }
            });
            initButtonLoading();
            initSetting();
            initNotificationPermission();
        });

        function initSetting() {
            try {
                i('GetSettings');
                AjaxCall('/Home/GetSettings').done(function (response) {
                    i('GetSettings>> res ' + response);

                    if (response === undefined)
                        return;
                    if (response.notify) {
                      //  var notify = response.notify;
                        var notifyInterval = response.notifyInterval;
                        var interval = 60000 * Number(notifyInterval);
                        setInterval(checkOrders, interval);

                        //     initChatHub();
                    }
                }).fail(function (error) {
                    i(error);
                });

            } catch (e) {
                i(e);
            }
        }



        function checkOrders() {
            AjaxCall('/Home/CheckOrders').done(function (response) {
                if (Number(response) > 0) {
                    playSound();
                    $(".pending-orders").text('+' + response);
                    Notify("", " يوجد " + response + " طلب قيد التنفيذ ");
                }
            }).fail(function (error) {
                i(error);
            });

        }

        function Notify(name, message) {
            try {
                i('notify');
                Push.create('هناك طلبات جديده',
                    {
                        body: message,
                        //icon: '~/favicon.ico',
                        tag: 'orders_noti',
                        link: '#!/route/Admin/Order',
                        requireInteraction: true,
                        timeout: 8000, // Timeout before notification closes automatically.
                        vibrate: [100, 100, 100], // An array of vibration pulses for mobile devices.
                        onClick: function () {
                            window.focus();
                            this.close();
                        }
                    }
                );
            } catch (e) {
                i(e);
            }
        }

        function playSound() {
            try {
                var sound = document.getElementById("audio");
                sound.play();

            } catch (e) {
                ar(e);
            }
        }

        function chatHub() {
            var chat = $.connection.chatHub;
            chat.client.NewMessage = function (name, message) {
                i(message);
                playSound();
                var currentCount = $(".pending-orders").text();
                i('count ' + currentCount);
                var count = Number(currentCount) + 1;
                $(".pending-orders").text('+' + count);
                Notify(name, message);
            };
            $.connection.hub.start().done(function () {
                i('load signal hub started');
                //$('#BtnSend').click(function () {
                //    chat.server.notify($('#UserName').val(), $('#TxtMessage').val());
                //    $('#TxtMessage').val('').focus();
                //});
            });
        }
        function htmlEncode(value) {
            var encodedValue = $('<div />').text(value).html();
            return encodedValue;
        }

        function initButtonLoading() {
            $('.loading').on('click',
                function () {
                    var $this = $(this);
                    $this.button('loading');
                });
        }
        function initNotificationPermission() {
            try {
                Push.Permission.request();
            } catch (e) {
                i('initNotificationPermission err: '+e);
            }
        }
    </script>
    @RenderSection("Scripts", false)

</body>
</html>