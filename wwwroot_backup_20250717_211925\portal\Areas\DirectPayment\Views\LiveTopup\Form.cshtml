﻿@model AppTech.MSMS.Domain.Models.LiveTopup
@using Obout.Mvc.ComboBox

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<div class="form-group">
    @Html.LabelFor(model => model.ProviderID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ProviderID, (SelectList)ViewBag.Providers)
        @Html.ValidationMessageFor(model => model.ProviderID)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ServiceID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ServiceID, (SelectList)ViewBag.Services)
        @Html.ValidationMessageFor(model => model.ServiceID)
    </div>
</div>

<div class="form-group" id="saba-options">
    @Html.Label("النوع", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.Type, new[]
        {
            new SelectListItem {Text = "الكل", Value = "0"},
            new SelectListItem {Text = "دفع مسبق-فئات", Value = "1"},
            new SelectListItem {Text = "فوترة", Value = "2"},
            new SelectListItem {Text = "دفع مسبق- مفتوح", Value = "3"}
        })
        @Html.ValidationMessageFor(model => model.Type, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Active, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="checkbox">
            @Html.EditorFor(model => model.Active)
            @Html.ValidationMessageFor(model => model.Active, "", new { @class = "text-danger" })
        </div>
    </div>
</div>


@if (AppTech.MSMS.Domain.DomainManager.TopupRoaming)
{
    <div id="Account-State-View">
        <div class="form-group">
            @Html.Label("نوع الحساب", new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownListFor(model => model.AccountState, new[]
                {
                    new SelectListItem {Text = "كافة الحسابات", Value = "1"},
                    new SelectListItem {Text = "مجموعة", Value = "2"},
                    new SelectListItem {Text = "حساب محدد", Value = "3"}
                })
                @Html.ValidationMessageFor(model => model.AccountState, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group" id="specifc">
            <div class="col-md-12">
                @Html.Label("اسم الحساب", new { @class = "control-label col-md-2" })
                @Html.Obout(new ComboBox("AccountID")
                {
                    Width = 300,
                    SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
                    FilterType = ComboBoxFilterType.Contains,
                    LoadingText = "Loading"
                })

                @Html.ValidationMessageFor(model => model.AccountID)
            </div>
        </div>

        <div class="form-group" id="group">
            @Html.Label("المجموعة", new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownListFor(model => model.AccountGroupID, (SelectList)ViewBag.Groups, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.AccountGroupID, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>
}


<script>

    function setAccountState() {

        var num = Number($("#AccountState").children("option:selected").val());
        i('selected state: ' + num);
        if (num === 1) {
            $('#specifc').hide();
            $('#group').hide();

        } else if (num === 3) {
            $('#specifc').show();
            $('#group').hide();

        } else if (num === 2) {
            $('#specifc').hide();
            $('#group').show();
        }
    }

    function setSabaOptions() {
        if (Number($('#ServiceID').val())==3) {
            $('#saba-options').show();
        }
        else
            $('#saba-options').hide();
    }
    $(function () {
        setSabaOptions();
        setAccountState();
        $('#AccountState').on('change',
            function () {
                setAccountState();
            });

        $('#ServiceID').on('change',
            function () {
                i('serv on change');
                setSabaOptions();
            });
    })
</script>