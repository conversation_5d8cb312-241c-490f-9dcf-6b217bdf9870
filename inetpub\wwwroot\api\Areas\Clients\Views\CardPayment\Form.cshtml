﻿@model AppTech.MSMS.Domain.Models.CardPayment

@{
    Layout = "~/Views/Shared/_FormLayout.cshtml";
}
@*<form id="form">*@
<div class="form-group">
    <label class="col-sm-2 control-label">النوع </label>
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.CardTypeID, (SelectList)ViewBag.Types, new { })
        @Html.ValidationMessageFor(model => model.CardTypeID)
    </div>
</div>
<div class="form-group">
    <label class="col-sm-2 control-label">الفئة </label>
    <div class="col-md-10">
        <select id="CardFactionID" name="CardFactionID" class="select2" style="width: 110px;" placeholder="اختر فئه" required></select>
        @Html.ValidationMessageFor(model => model.CardFactionID)
    </div>
</div>
<div class="form-group">
    <label class="col-sm-2 control-label">رقم الهاتف </label>
    <div class="col-md-10">
        @Html.EditorFor(model => model.Phone)
        @Html.ValidationMessageFor(model => model.Phone)
    </div>
</div>
<input type="number" id="Cost" hidden />
@*<button class="btn btn-primary btn-info btn-bold btn-block loading" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار" type="submit" id="submit" value="submit">
        <i class="ace-icon fa fa-save bigger-110"></i>
        حفظ
    </button>*@
@*</form>*@

<script>
    $(function () {
        var CardType = $("#CardTypeID").val();
        loadFactionList(CardType);
    });
    $("#CardTypeID").on("change", function () {
        var id = $("#CardTypeID").val();
        loadFactionList(id);
    });
    function loadFactionList(id) {
        i('loadDataList');
        fillDataList('CardFactionID', '/Clients/CardPayment/GetCardFactions?id=' + id, false, "اختر فئــه");
        i('CardFactionID ');

    };

    $("#CardFactionID").on("change", function () {
        var CardFaction = $("#CardFactionID").val();
        $.post("Clients/CardPayment/GetCost", { id: CardFaction }, function (data) {
            $("#Cost").val(data);
        });
    });

    $(function () {
        $("#cancel-button").hide();
        $("#submit-button").text('طلب الكرت');

        $("#submit-button").on('click', function () {
            var phone = '';

            if ($("#Phone").val() > 700000000)
                phone += ' لرقم ' + $("#Phone").val();

            var msg = "سوف يتم شراء كرت " +
                $("#CardTypeID option:selected").text()
                + ' فئة : ' + $("#CardFactionID option:selected").text()
                + phone
                + ' بمبلغ ' + $("#Cost").val() + ' ريال يمني  '
                + ' هل انت متأكد؟';

            if (!confirm(msg)) {
                i('not confirmed');
                history.go(0);
                return false;
            } else {
                i('confirmed');
                return true;
            }
        })
    });

</script>
