﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.Gsm

@{
    Layout = "~/Views/Shared/_MultiForm.cshtml";
}


<div class="form-group">
    @Html.LabelFor(model => model.GroupName, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.GroupName, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.GroupName, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.Label("تصدير من ملف اكسل", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.ViaExcel, new { htmlAttributes = new { onClick = "toggle(this)" } })
    </div>
</div>
<div class="form-group excel-browser">
    <label class="col-sm-2 control-label">أختر ملف الأكسل</label>
    <div style="position: relative;">
        <input type="file" name="excelfile" size="40">
    </div>
</div>
<div class="form-group sim-row">
    @Html.EditorFor(model => model.ID, new { htmlAttributes = new { @style = "display: none" } })
    <div class="form-group">
        @Html.LabelFor(model => model.Number, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Number, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Number, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Status, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Status, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Status)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Status_chng_date, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Status_chng_date, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Status_chng_date)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Account_code, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Account_code, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Account_code)
        </div>
    </div><div class="form-group">
        @Html.LabelFor(model => model.Bills, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Bills, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Bills)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Limit, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Limit, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Limit)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Deposit, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Deposit, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Deposit)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.BN, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.BN, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.BN)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.On, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.On, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.On)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Invoice_date, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Invoice_date, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Invoice_date)
        </div>
    </div>
</div>
<script>
    $(".excel-browser").hide();
    function toggle(source) {
        if (source.checked) {

            $(".excel-browser").show();
            $(".sim-row").hide();
        }
        else {

            $(".excel-browser").hide();
            $(".sim-row").show();
        }
    }
</script>