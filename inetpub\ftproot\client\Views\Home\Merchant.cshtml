﻿@using AppTech.MSMS.Web.Security
<div class="alert alert-block alert-success">
    <button type="button" class="close" data-dismiss="alert">
        <i class="ace-icon fa fa-times"></i>
    </button>


    <h4>مرحباً @User.FirstName</h4>

    <strong class="green">
        الرصيد الحالي هو
    </strong>,
    @ViewBag.Balance


</div>

<div class="row">
    <div class="space-6"></div>
    <div class="profile-user-info profile-user-info-striped">

        <div class="profile-info-row">
            <div class="profile-info-name"> رقم الجهة </div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @CurrentUser.Merchant.Number </span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> اسم الجهة </div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @CurrentUser.Merchant.Name </span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name">اسم المالك </div>

            <div class="profile-info-value">
                <span class="editable"> @CurrentUser.Merchant.OwnerName</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> الهاتف </div>

            <div class="profile-info-value">
                <span class="editable"> @CurrentUser.Merchant.PhoneNumber</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> العنوان </div>

            <div class="profile-info-value">
                <span class="editable"> @CurrentUser.Merchant.Address</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> عدد المدفوعات </div>

            <div class="profile-info-value">
                <span class="editable"> @ViewBag.PaymentCounts</span>
            </div>
        </div>


    </div>

</div>

<div class="hr hr32 hr-dotted"></div>