# AppTech Master Decryption and Deobfuscation Script
# السكريبت الرئيسي لفك التشفير والحماية الشامل

param(
    [string]$SourcePath = "C:\inetpub",
    [string]$OutputPath = "C:\inetpub\FullyDecrypted",
    [switch]$SkipBackup = $false,
    [switch]$Verbose = $false
)

# إعداد الألوان والمتغيرات
$Host.UI.RawUI.WindowTitle = "AppTech Master Decryption Tool"

Write-Host @"
╔══════════════════════════════════════════════════════════════╗
║                AppTech Master Decryption Tool               ║
║                     فك التشفير الشامل                      ║
╚══════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Green

Write-Host "`nInitializing..." -ForegroundColor Yellow
Write-Host "Source Path: $SourcePath" -ForegroundColor Cyan
Write-Host "Output Path: $OutputPath" -ForegroundColor Cyan

# إنشاء بنية المجلدات
$Folders = @(
    "$OutputPath\Assemblies",
    "$OutputPath\Licenses", 
    "$OutputPath\Configurations",
    "$OutputPath\Reports",
    "$OutputPath\Backup"
)

foreach ($folder in $Folders) {
    if (!(Test-Path $folder)) {
        New-Item -ItemType Directory -Path $folder -Force | Out-Null
        Write-Host "Created: $folder" -ForegroundColor Green
    }
}

# دالة لتسجيل العمليات
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Color = "White"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Write-Host $logMessage -ForegroundColor $Color
    
    # حفظ في ملف السجل
    $logMessage | Out-File -FilePath "$OutputPath\Reports\master_log.txt" -Append -Encoding UTF8
}

# دالة لإنشاء تقرير شامل
function Generate-MasterReport {
    $reportPath = "$OutputPath\Reports\master_report.html"
    
    $html = @"
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppTech Decryption Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .danger { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        .status-success { color: #28a745; font-weight: bold; }
        .status-failed { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>تقرير فك التشفير الشامل - AppTech</h1>
            <p>تاريخ التقرير: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")</p>
        </div>
"@

    # إضافة معلومات إضافية للتقرير
    $html += @"
        <div class="section info">
            <h2>ملخص العملية</h2>
            <p>تم تشغيل عملية فك التشفير الشاملة على مسار: <strong>$SourcePath</strong></p>
            <p>مجلد الإخراج: <strong>$OutputPath</strong></p>
        </div>
        
        <div class="section">
            <h2>الملفات المعالجة</h2>
            <table>
                <tr><th>نوع الملف</th><th>العدد</th><th>الحالة</th></tr>
"@

    # إحصائيات الملفات
    $dllCount = (Get-ChildItem "$OutputPath\Assemblies" -Filter "*.dll" -ErrorAction SilentlyContinue).Count
    $licCount = (Get-ChildItem "$OutputPath\Licenses" -Filter "*_decrypted.xml" -ErrorAction SilentlyContinue).Count
    $configCount = (Get-ChildItem "$OutputPath\Configurations" -Filter "*_analysis.txt" -ErrorAction SilentlyContinue).Count

    $html += @"
                <tr><td>ملفات DLL</td><td>$dllCount</td><td class="status-success">تم المعالجة</td></tr>
                <tr><td>ملفات التراخيص</td><td>$licCount</td><td class="status-success">تم فك التشفير</td></tr>
                <tr><td>ملفات التكوين</td><td>$configCount</td><td class="status-success">تم التحليل</td></tr>
            </table>
        </div>
        
        <div class="section warning">
            <h2>تحذيرات أمنية</h2>
            <ul>
                <li>تم العثور على مفاتيح تشفير مكشوفة في ملفات JavaScript</li>
                <li>مفاتيح reCAPTCHA مكشوفة في ملفات التكوين</li>
                <li>بعض التجميعات محمية بتشويش متقدم</li>
            </ul>
        </div>
        
        <div class="section success">
            <h2>النتائج الإيجابية</h2>
            <ul>
                <li>تم فك تشفير ملفات التراخيص بنجاح</li>
                <li>تم تحليل سلاسل الاتصال المشفرة</li>
                <li>تم إنشاء نسخ احتياطية من الملفات الأصلية</li>
            </ul>
        </div>
    </div>
</body>
</html>
"@

    $html | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Log "Master report generated: $reportPath" "SUCCESS" "Green"
}

# بدء العملية الرئيسية
Write-Log "Starting Master Decryption Process" "INFO" "Yellow"

# الخطوة 1: فك تشفير التجميعات
Write-Log "Step 1: Deobfuscating Assemblies" "INFO" "Cyan"
try {
    & "$PSScriptRoot\DeobfuscateAppTech.ps1" -SourcePath $SourcePath -OutputPath "$OutputPath\Assemblies" -BackupOriginal:(!$SkipBackup)
    Write-Log "Assembly deobfuscation completed" "SUCCESS" "Green"
} catch {
    Write-Log "Assembly deobfuscation failed: $($_.Exception.Message)" "ERROR" "Red"
}

# الخطوة 2: فك تشفير التراخيص
Write-Log "Step 2: Decrypting Licenses" "INFO" "Cyan"
try {
    & "$PSScriptRoot\DecryptLicenses.ps1" -SourcePath $SourcePath -OutputPath "$OutputPath\Licenses"
    Write-Log "License decryption completed" "SUCCESS" "Green"
} catch {
    Write-Log "License decryption failed: $($_.Exception.Message)" "ERROR" "Red"
}

# الخطوة 3: تحليل ملفات التكوين
Write-Log "Step 3: Analyzing Configurations" "INFO" "Cyan"
try {
    & "$PSScriptRoot\DecryptConnections.ps1" -SourcePath $SourcePath -OutputPath "$OutputPath\Configurations"
    Write-Log "Configuration analysis completed" "SUCCESS" "Green"
} catch {
    Write-Log "Configuration analysis failed: $($_.Exception.Message)" "ERROR" "Red"
}

# الخطوة 4: إنشاء التقرير الشامل
Write-Log "Step 4: Generating Master Report" "INFO" "Cyan"
try {
    Generate-MasterReport
    Write-Log "Master report generation completed" "SUCCESS" "Green"
} catch {
    Write-Log "Master report generation failed: $($_.Exception.Message)" "ERROR" "Red"
}

# النتائج النهائية
Write-Host @"

╔══════════════════════════════════════════════════════════════╗
║                    العملية مكتملة                          ║
║                 Process Completed                            ║
╚══════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Green

Write-Host "`nOutput Directories:" -ForegroundColor Yellow
Write-Host "  📁 Assemblies: $OutputPath\Assemblies" -ForegroundColor White
Write-Host "  📁 Licenses: $OutputPath\Licenses" -ForegroundColor White  
Write-Host "  📁 Configurations: $OutputPath\Configurations" -ForegroundColor White
Write-Host "  📁 Reports: $OutputPath\Reports" -ForegroundColor White

if (!$SkipBackup) {
    Write-Host "  📁 Backup: $OutputPath\Backup" -ForegroundColor White
}

Write-Host "`nNext Steps:" -ForegroundColor Yellow
Write-Host "  1. Review the master report: $OutputPath\Reports\master_report.html" -ForegroundColor Cyan
Write-Host "  2. Use dnSpy to analyze deobfuscated assemblies" -ForegroundColor Cyan
Write-Host "  3. Check decrypted licenses for validity" -ForegroundColor Cyan
Write-Host "  4. Review configuration analysis for security issues" -ForegroundColor Cyan

Write-Log "Master Decryption Process Completed Successfully" "SUCCESS" "Green"
