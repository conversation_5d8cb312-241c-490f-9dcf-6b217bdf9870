﻿//$(function () {
//    $('#example1').DataTable();
//    $('#example2').DataTable({
//        "paging": true,
//        "lengthChange": false,
//        "searching": false,
//        "ordering": true,
//        "info": true,
//        "autoWidth": false
//    });
//});

jQuery(document).ready(function ($) {

    try {
     
        //$('#simple-table tfoot th').each(function () {
        //    var title = $(this).text();
        //    $(this).html('<input type="text" placeholder=" ' + title + '" />');
        //});
        //$('#example1').DataTable();
        var table = $("#simple-table").DataTable({
            //"createdRow": function ( row, data, index ) {
            //   // if ( data[5].replace(/[\$,]/g, '') * 1 > 150000 ) {
            //     //   $('td', row).eq(5).addClass('highlight');
            //    //}
            //},
            //stateSave: true,
            "paging": false,
            "order": [[0, "desc"]],
            "lengthChange": false,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            dom: "Bfrtip",
            buttons: [
                "copy", "csv", "excel", "pdf", "print"
            ],
            "language": {
                "decimal": ",",
                "thousands": ".",
                "sProcessing": "جارٍ التحميل...",
                "sLengthMenu": "أظهر _MENU_ مدخلات",
                "sZeroRecords": "لم يعثر على أية سجلات",
                "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
                "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
                "sInfoPostFix": "",
                "sSearch": "البحث:",
                "sUrl": "",
                "oPaginate": {
                    "sFirst": "الأول",
                    "sPrevious": "السابق",
                    "sNext": "التالي",
                    "sLast": "الأخير"
                }
            }
        });

        //$('a.toggle-vis').on('click', function (e) {
        //    e.preventDefault();

        //    // Get the column API object
        //    var column = table.column($(this).attr('data-column'));

        //    // Toggle the visibility
        //    column.visible(!column.visible());
        //});

        //hover col and rows
        //$('#simple-table tbody')
        //    .on( 'mouseenter', 'td', function () {
        //        var colIdx = table.cell(this).index().column;

        //        $( table.cells().nodes() ).removeClass( 'highlight' );
        //        $( table.column( colIdx ).nodes() ).addClass( 'highlight' );
        //    } );

        //$('#simple-table tbody').on('click', 'tr', function () {
        //    var data = table.row( this ).data();
        //    alert( 'You clicked on '+data[0]+'\'s row' );
        //});

        // Apply the search
        //table.columns().every(function () {
        //    var that = this;

        //    $('input', this.footer()).on('keyup change', function () {
        //        if (that.search() !== this.value) {
        //            i('onsearch: val: ' + this.value);
        //            that.search(this.value).draw();
        //        }
        //    });
        //});
      
    } catch (e) {
        i("data error: " + e);
    }
});