﻿@model AppTech.MSMS.Domain.Models.ProductCategory
@{
    Layout = "~/Views/Shared/_MultiForm.cshtml"; 
}
    <div>
        <div class="form-group">
            @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Name)
                @Html.ValidationMessageFor(model => model.Name)
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Note)
                @Html.ValidationMessageFor(model => model.Note)
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.ImageName, new { @class = "control-label col-md-2" })
            <div style="position: relative;">
                <input type="file" name="ImageData" size="40" onchange="showImg(this)">
            </div>
            <img class="img-thumbnail" width="150" height="150" id="preview"
                 src="@Url.Action("GetImage", "ProductCategory",new {Model.ID})" />
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Description, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Description)
            </div>
        </div>


    </div>