﻿@model AppTech.MSMS.Web.Models.LoginModel

@{
    ViewBag.Title = "تسجيل دخول  العملاء";
    var returnUrl = ViewBag.ReturnUrl != null ? "?ReturnUrl=" + ViewBag.ReturnUrl : "";
    Layout = null;
}



<!DOCTYPE html>
<html lang="ar">
<head>
    <title>تسجيل الدخول</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" type="text/css" href="~/Content/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="~/Content/login.css">
    <!--===============================================================================================-->

</head>
<body>

<div class="limiter">
    <div class="container">
        <div class="wrap">
            <div class="login100-pic js-tilt" data-tilt>
                <img src="~/Photos/img-01.png" alt="IMG">
            </div>

            @*<form method="post" action="Clients/AccountService/Login@(returnUrl)" class="login100-form validate-form">*@
            @using (Html.BeginForm(new {returnUrl}))
            {
                @Html.AntiForgeryToken()
                @Html.ValidationSummary(true)
                <span class="login100-form-title">
                    XX تسجيل الدخول العملاء
                </span>

                <div class="wrap-input100 validate-input" data-validate="Valid email is required: <EMAIL>">

                    @Html.TextBoxFor(m => m.Email, new {placeholder = "اسم المستخدم", @class = "input100"})
                    @Html.ValidationMessageFor(m => m.Email, string.Empty, new {@class = "invalid"})
                    <span class="focus-input100"></span>
                    <span class="symbol-input100">
                        <i class="fa fa-envelope" aria-hidden="true"></i>
                    </span>
                </div>

                <div class="wrap-input100 validate-input" data-validate="Password is required">
                    @Html.PasswordFor(m => m.Password, new {placeholder = "كلمة المرور", @class = "input100"})
                    @Html.ValidationMessageFor(m => m.Password, string.Empty, new {@class = "invalid"})
                    <span class="focus-input100"></span>
                    <span class="symbol-input100">
                        <i class="fa fa-lock" aria-hidden="true"></i>
                    </span>
                </div>

                <div class="container-login100-form-btn">
                    <button class="login100-form-btn" type="submit">
                        الدخول
                    </button>
                </div>

                @*<div class="text-center p-t-12">
                            <span class="txt1">
                                Forgot
                            </span>
                            <a class="txt2" href="#">
                                Username / Password?
                            </a>
                        </div>

                        <div class="text-center p-t-136">
                            <a class="txt2" href="#">
                                Create your AccountService
                                <i class="fa fa-long-arrow-right m-l-5" aria-hidden="true"></i>
                            </a>
                        </div>*@
            @*</form>*@
            }
        </div>
    </div>
</div>

</body>
</html>