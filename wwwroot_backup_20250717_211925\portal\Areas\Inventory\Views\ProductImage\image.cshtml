﻿@model IEnumerable<AppTech.MSMS.Domain.Models.ProductImage>
@{
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}
@*@Url.Action("OnFormLoad", "ProductImage")*@

<p>
    <a class="blue" href="" onclick="openModal('@ViewBag.ProductID')">
        <i class="ace-icon fa fa-plus bigger-150"></i>
        <span>أضافة صورة جديدة</span>
    </a>
</p>
<div id="list">
    @Html.Partial("_images")
</div>
    @Html.Partial("_Modal")

    <script>
        function openModal(id) {
            i('open modal id' + id);
            openViewAsModal('Inventory/ProductImage/AddOrEditImage?productID=' + id, "صورة جديدة");
        }
    </script>
