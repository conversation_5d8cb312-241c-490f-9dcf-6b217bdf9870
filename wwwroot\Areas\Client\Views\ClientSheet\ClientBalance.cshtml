﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Web.Models.ClientsBalancseModel

@{
    Layout = "~/Views/Shared/_Report.cshtml";
}

<div class="form-horizontal">


    <div class="form-group">
        @Html.Label("كافة العملاء", new {@class = "checkbox col-md-6"})
        <div class="col-md-6">
            @Html.CheckBoxFor(x => x.AllClients, new {})
        </div>
    </div>


    <div id="client">
        <span class="lbl">اسم العميل </span>
        <div class="form-group">
            <div class="col-md-10">
                @Html.Obout(new ComboBox("AccountID")
                {
                    Width = 230,
                    FilterType = ComboBoxFilterType.Contains
                })

            </div>
        </div>

    </div>


    <div class="form-group">
        @Html.Label("الحالة", new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EnumDropDownListFor(x => x.Status)
        </div>
    </div>


  
    <span class="lbl"> العملة</span>
    <select name="CurrencyID">
        <option value="0">كافة العملات</option>
        <option value="1">يمني</option>
        <option value="2">دولار</option>
        <option value="3">سعودي</option>
    </select>
    
    <div class="hr hr-dotted hr-24"></div>
    <div class="space-6"></div>
</div>

<script>
    $("#export_to_excel").hide();

    //$("#export-pdf").on('click',
    //    function () {

    //        ar('overrode export');
    //        var form = $(this).closest("form");
    //        var url = "?";
    //        url += form.serialize();;
    //        url = "/" + $("#Controller").val() + "/ClientBalancePdf" + url;
    //        i('url ' + url);
    //        window.location.href = url;
    //    });


    $('#AllClients').on('click',
        function() {
            $("#client").toggle(!this.checked);
        });
</script>