﻿r,@model AppTech.MSMS.Domain.Models.RSS

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    @Html.LabelFor(model => model.Feed, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Feed, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Feed, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.Label("نشط", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.Active, new[]
        {
            new SelectListItem {Text = "نعم", Value = bool.TrueString},
            new SelectListItem {Text = "لا", Value = bool.FalseString}
        })
    </div>
</div>