﻿@model AppTech.MSMS.Domain.Models.RemittanceCommission
@using Obout.Mvc.ComboBox

@{
    Layout = "/Views/Shared/_Form.cshtml";
}
<input type="hidden" name="VoucherType" value="11" />

@*<div class="form-group">
        @Html.Label("نوع الحوالة", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.VoucherType, new[]
            {
                new SelectListItem {Text = "قبض حوالة", Value = "1"},
                new SelectListItem {Text = "صرف حوالة", Value = "2"},
                new SelectListItem {Text = "توريد حوالة", Value = "3"},
                new SelectListItem {Text = "تصدير حوالة", Value = "4"}
            })
        </div>
    </div>*@

<div class="form-group">
    @Html.LabelFor(model => model.CurrencyID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CurrencyID, (SelectList)ViewBag.Currencies, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.CurrencyID, "", new { @class = "text-danger" })
    </div>
</div>





<div class="form-group">
    @Html.LabelFor(model => model.AccountState, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.AccountState, new[]
        {
            new SelectListItem {Text = "كافة الحسابات", Value = "1"},
            new SelectListItem {Text = "مجموعة", Value = "2"},
            new SelectListItem {Text = "حساب محدد", Value = "3"}
        })
        @Html.ValidationMessageFor(model => model.AccountState, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group" id="specifc2">
    <div class="col-md-12">
        @Html.Label("اسم الحساب", new { @class = "control-label col-md-2" })
        @Html.Obout(new ComboBox("AccountID")
        {
            Width = 300,
            SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains,
            LoadingText = "Loading"
        })

        @Html.ValidationMessageFor(model => model.AccountID)
    </div>
</div>


<div class="form-group" id="group2">
    @Html.LabelFor(model => model.AccountGroupID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.AccountGroupID, (SelectList)ViewBag.AccountGroups, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.AccountGroupID, "", new { @class = "text-danger" })
    </div>
</div>



<div class="form-group">
    @Html.LabelFor(model => model.TargetState, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.TargetState, new[]
        {
            new SelectListItem {Text = "كافة الحسابات", Value = "1"},
            new SelectListItem {Text = "مجموعة", Value = "2"},
            new SelectListItem {Text = "حساب محدد", Value = "3"}
        })
        @Html.ValidationMessageFor(model => model.TargetState, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group" id="specifc">

    <div class="form-group" id="specifc2">
        <div class="col-md-12">
            @Html.Label("اسم الحساب", new { @class = "control-label col-md-2" })
            @Html.Obout(new ComboBox("AccountID")
            {
                Width = 300,
                SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
                FilterType = ComboBoxFilterType.Contains,
                LoadingText = "Loading"
            })

            @Html.ValidationMessageFor(model => model.AccountID)
        </div>
    </div>
</div>


<div class="form-group" id="group">
    @Html.LabelFor(model => model.TargetGroupID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.TargetGroupID, (SelectList)ViewBag.AccountGroups, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.TargetGroupID, "", new { @class = "text-danger" })
    </div>
</div>










<div class="form-group form-inline">
    @Html.LabelFor(model => model.StartAmount, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.StartAmount, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.StartAmount)
    </div>
</div>


<div class="form-group form-inline">
    @Html.LabelFor(model => model.EndAmount, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.EndAmount, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.EndAmount)
    </div>
</div>

<div class="form-group">
    @Html.Label("نوع العمولة", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CommmissionType, new[]
        {
            new SelectListItem {Text = "بالمبلغ", Value = "0"},
            new SelectListItem {Text = "بالنسبة", Value = "1"}
        })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.CommissionCurrencyID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CommissionCurrencyID, (SelectList)ViewBag.CommissionCurrencies, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.CommissionCurrencyID, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group form-inline">
    @Html.Label("العموله التجارية", new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.CenterCommission, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.CenterCommission)
    </div>
</div>

<div class="form-group form-inline">
    @Html.Label("العمولة الشخصية", new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.PointCommission, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.PointCommission)
    </div>
</div>


<div class="form-group form-inline">
    @Html.LabelFor(model => model.IsAgainst, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.IsAgainst, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.IsAgainst)
    </div>
</div>

<script>

    function setTargetState() {
        var num = Number($("#TargetState").children("option:selected").val());
        i('selected state: ' + num);
        if (num === 1) {
            $('#specifc').hide();
            $('#group').hide();

        } else if (num === 3) {
            $('#specifc').show();
            $('#group').hide();

        } else if (num === 2) {
            $('#specifc').hide();
            $('#group').show();
        }
    }
    function setState() {

        var num = Number($("#AccountState").children("option:selected").val());
        i('selected state: ' + num);
        if (num === 1) {
            $('#specifc2').hide();
            $('#group2').hide();

        } else if (num === 3) {
            $('#specifc2').show();
            $('#group2').hide();

        } else if (num === 2) {
            $('#specifc2').hide();
            $('#group2').show();
        }
    }

    $(function () {
        $('#specifc').hide();
        $('#group').hide();

        setState();
        setTargetState();
        $('#AccountState').on('change',
            function () {
                setState();
            });

        $('#TargetState').on('change',
            function () {

                setTargetState();

            });


        $('#StartAmount').on('input',
            function () {
                i('Amount key down');
                var words = tafqeet($('#StartAmount').val());
                i('Amount to words ' + words);
                $('#words').text(words);
            });


        $('#EndAmount').on('input',
            function () {
                i('Amount key down');
                var words = tafqeet($('#EndAmount').val());
                i('Amount to words ' + words);
                $('#words').text(words);
            });


        $('#CenterCommission').on('input',
            function () {
                i('Amount key down');
                var words = tafqeet($('#CenterCommission').val());
                i('Amount to words ' + words);
                $('#words').text(words);
            });


        $('#PointCommission').on('input',
            function () {
                i('Amount key down');
                var words = tafqeet($('#PointCommission').val());
                i('Amount to words ' + words);
                $('#words').text(words);
            });
    })
</script>
