﻿@model AppTech.MSMS.Web.Models.BalanceSheetModel
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}
@{
    Html.RenderPartial("_DateControl");
}


<span class="lbl"> العملة</span>
@Html.DropDownListFor(m => m.CurrencyID, (SelectList) ViewBag.Currencies)
<div class="hr hr-dotted hr-24"></div>
<div class="space-6"></div>
<span class="lbl"> نوع التقرير</span>
@Html.DropDownListFor(m => m.Type, (SelectList) ViewBag.ReportType)