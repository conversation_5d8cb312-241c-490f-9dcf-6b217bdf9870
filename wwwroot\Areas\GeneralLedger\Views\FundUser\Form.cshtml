﻿@model AppTech.MSMS.Domain.Models.FundUser

@{
    ViewBag.Title = "مستخدمين الصناديق";
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    @Html.LabelFor(model => model.FundID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.FundID, (SelectList) ViewBag.Funds, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.FundID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.UserID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.UserID, (SelectList) ViewBag.Users, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.UserID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
    </div>
</div>