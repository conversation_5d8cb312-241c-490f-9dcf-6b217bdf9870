﻿<div id="main-form">
    <input type="hidden" id="FormTitle" value="@ViewBag.FormTitle">
    @using (Ajax.BeginForm(new AjaxOptions
    {
        OnBegin = "return OnFormBegin()",
        OnSuccess = "onCrudSuccess",
        OnFailure = "onCrudFailure",
    }))
    {
        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new {@class = "text-danger"})
        <section>
            <div class="form-horizontal">
                @RenderBody()
            </div>
        </section>
        <div class="hr hr32 hr-dotted"></div>
         @Html.Partial("_FormAction")
    }
</div>

<script>
    
    var formHelper = {
        onSuccess: function(data) {
            log('onCrudSuccess');
            hideFormLoading();
            $("#modal").modal('hide');
            showSuccess("تم حفط بنجاح");
            //try {
            //    var pager = Patterns.Art.Pager;
            //    pager.activateList();

            //} catch (e) {
            //    alert(e);
            //}
        },
        onBegin: function(context) {
            return true;
        }
    }

    function OnFormBegin(context) {
      return  formHelper.onBegin(context);
    }

    function onCrudSuccess(data) {
        formHelper.onSuccess(data);
    }

    function onCrudFailure(xhr, textStatus, errorThrown) {
        hideFormLoading();
        parseAndShowError(xhr, textStatus, errorThrown)
    }


</script>