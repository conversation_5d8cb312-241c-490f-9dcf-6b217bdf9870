﻿@model AppTech.MSMS.Domain.Models.UserInfo

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<input type="hidden" name="ParentID" value="@ViewBag.ParentID"/>
<input type="hidden" name="ParentType" value="@ViewBag.ParentType"/>

@if (Model.ID == 0)
{
    <div class="form-group">
        @Html.Label("اسم المستخدم", new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EditorFor(model => model.UserName)
            @Html.ValidationMessageFor(model => model.UserName)
        </div>
    </div>

    <div class="form-group">
        @Html.Label("كلمة المرور", new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.EditorFor(model => model.Password)
            @Html.ValidationMessageFor(model => model.Password)
        </div>
    </div>
}
else
{
    <div class="form-group">
        @Html.Label("الحالة", new {@class = "control-label col-md-2"})
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.Status, new[]
            {
                new SelectListItem {Text = "نشط", Value = "1"},
                new SelectListItem {Text = "موقف", Value = "0"}
            })
        </div>
    </div>
}



<script>
    hideLoading();
</script>
<script>

    $(function() {
        try {
            formHelper.onSuccess = function(data) {
                log('user party form');
                i('data> ' + data);
                $("#modal").modal('hide');
                $("#list").html(data);
            }
            formHelper.onBegin = function(context) {
                return true;
            };
        } catch (e) {
        }
    });


</script>