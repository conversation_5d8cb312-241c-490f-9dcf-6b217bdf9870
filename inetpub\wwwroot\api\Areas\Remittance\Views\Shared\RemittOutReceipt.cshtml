﻿@model AppTech.MSMS.Web.Areas.Remittance.Models.RemittanceReceiptModel
@Styles.Render("~/Content/print")

<style>
    table {
        border: 1px solid #CCC;
        border-collapse: collapse;
    }

    td { border: none; }


  


   
    /*.middlepane {
        width: 50%;
        height: 100%;
        float: left;
        background-color: royalblue;
        border-collapse: collapse;
    }*/

    .rightpane {
        width: 70%;
        height: 50%;
        position: relative;
        float: right;
        border-collapse: collapse;
        border: 1px solid black;
    }

    .leftpane {
        width: 30%;
        height: 50%;
        float: left;
        border-collapse: collapse;
        border: 1px solid black;
    }

    .toppane {
        width: 100%;
        height: 100px;
        border-collapse: collapse;
        background-color: #4da6ff;
    }
    
</style>
<body class="no-skin rtl ">

<div class="container">


    <div class=" col-sm-12" style="border: 1px solid black;">

        <div class="col-sm-6 pull-right" style="margin-top: 10px">
            التاريخ : @DateTime.Now.ToString("yyyy/MM/dd")
        </div>


        @*<div class="col-sm-6 align-center" style="margin-top: 10px">
                @Html.DisplayFor(model => model.Type)
            </div>*@

        <div class="col-sm-6 align-left" style="margin-top: 10px">
            رقم الحوالة : @Html.DisplayFor(model => model.RemittanceNumber)
        </div>

        <div class="col-sm-12 align-center" style="border-bottom: 1px solid black; margin-top: 1px; margin-bottom: 20px; width: 100%; padding: 8px 0px; height: 30px; font: bold 18px Helvetica, Sans-Serif;">
            @Html.DisplayFor(model => model.Title)
        </div>

    </div>

    <div class="rightpane">

        <div class="table table-responsive">
            <table id="simple-table" class="table" border="0">
                <tr>
                    <td>
                        انا الموقع أدناه:
                        @Html.DisplayFor(model => model.BenficiaryName)
                    </td>

                </tr>

            </table>

            <table id="" class="table">
                <tr>
                    <td class=" col-sm-12">
                        استلمت مبلغا وقدره: @Html.DisplayFor(model => model.AmountInText)

                    </td>

                </tr>

                <tr>
                    <td class=" col-sm-12">
                        المحولة لي من المرسل
                        @Html.DisplayFor(model => model.SenderName)
                    </td>
                </tr>

                <tr>
                    <td class=" col-sm-6">
                        عن طريق:
                        @Html.DisplayFor(model => model.TargetName)
                    </td>
                    <td class=" col-sm-6">
                        تاريخ الحوالة
                        @Html.DisplayFor(model => model.Date)
                    </td>

                </tr>


                <tr>
                    <td>
                        ملاحظات
                        @Html.DisplayFor(model => model.Note)
                    </td>

                </tr>

            </table>
            <table class="table">


                <tr>
                    <td>
                        اسم المستلم
                    </td>
                    <td>
                        .....................................................
                    </td>
                </tr>
                <tr>
                    <td>
                        هاتف المستلم
                    </td>
                    <td>
                        .....................................................
                    </td>
                </tr>

                <tr>
                    <td>
                        التوقيع
                    </td>
                    <td></td>
                </tr>

                <tr>
                    <td>
                        استلمت المبلغ المذكور اعلاه كاملا وفي حالة جيدة
                    </td>

                </tr>

            </table>
        </div>
    </div>
    <div class="leftpane">
        <img class="img-thumbnail" width="150" height="150" id="preview"
             src="~/Photos/profile-pic.jpg"/>
    </div>

</div>
</body>