# AppTech Deobfuscation and Decryption Tools
## أدوات فك التشفير والحماية لنظام AppTech

### نظرة عامة
هذه مجموعة شاملة من الأدوات لفك تشفير وحماية ملفات نظام AppTech، بما في ذلك:
- فك تشويش .NET assemblies
- فك تشفير ملفات التراخيص
- تحليل ملفات التكوين
- استخراج سلاسل الاتصال المشفرة

### الأدوات المتوفرة

#### 1. MasterDecryption.ps1 (السكريبت الرئيسي)
السكريبت الشامل الذي يقوم بتشغيل جميع عمليات فك التشفير.

**الاستخدام:**
```powershell
.\MasterDecryption.ps1 -SourcePath "C:\inetpub" -OutputPath "C:\inetpub\FullyDecrypted"
```

**المعاملات:**
- `SourcePath`: مسار المجلد المصدر (افتراضي: C:\inetpub)
- `OutputPath`: مسار مجلد الإخراج (افتراضي: C:\inetpub\FullyDecrypted)
- `SkipBackup`: تخطي إنشاء النسخ الاحتياطية
- `Verbose`: عرض تفاصيل إضافية

#### 2. DeobfuscateAppTech.ps1
فك تشويش ملفات .NET DLL باستخدام de4dot.

**الاستخدام:**
```powershell
.\DeobfuscateAppTech.ps1 -SourcePath "C:\inetpub" -OutputPath "C:\inetpub\Deobfuscated"
```

#### 3. DecryptLicenses.ps1
فك تشفير ملفات التراخيص (.lic) المشفرة بـ Base64.

**الاستخدام:**
```powershell
.\DecryptLicenses.ps1 -SourcePath "C:\inetpub" -OutputPath "C:\inetpub\DecryptedLicenses"
```

#### 4. DecryptConnections.ps1
تحليل وفك تشفير ملفات التكوين وسلاسل الاتصال.

**الاستخدام:**
```powershell
.\DecryptConnections.ps1 -SourcePath "C:\inetpub" -OutputPath "C:\inetpub\DecryptedConnections"
```

### المتطلبات

#### الأدوات المطلوبة:
1. **de4dot** - موجود في `C:\inetpub\de4dot\`
2. **dnSpy** - موجود في `C:\inetpub\DeobfuscationTools\dnSpy\`
3. **PowerShell 5.0+** - مثبت مع Windows
4. **.NET Framework 4.5+** - مطلوب لتشغيل الأدوات

#### صلاحيات النظام:
- صلاحيات المدير (Administrator) مطلوبة
- صلاحيات القراءة والكتابة على مجلدات المصدر والهدف

### خطوات التشغيل

#### الطريقة السريعة (موصى بها):
```powershell
# فتح PowerShell كمدير
cd "C:\inetpub\DeobfuscationTools"

# تشغيل السكريبت الرئيسي
.\MasterDecryption.ps1
```

#### الطريقة المتقدمة:
```powershell
# 1. فك تشويش التجميعات فقط
.\DeobfuscateAppTech.ps1

# 2. فك تشفير التراخيص فقط
.\DecryptLicenses.ps1

# 3. تحليل التكوينات فقط
.\DecryptConnections.ps1
```

### بنية الإخراج

```
FullyDecrypted/
├── Assemblies/          # ملفات DLL مفكوكة التشويش
├── Licenses/            # ملفات التراخيص مفكوكة التشفير
├── Configurations/      # تحليل ملفات التكوين
├── Reports/             # التقارير والسجلات
│   ├── master_report.html
│   └── master_log.txt
└── Backup/              # النسخ الاحتياطية
```

### أنواع الحماية المكتشفة

#### 1. تشويش .NET Assemblies
- **الأداة المستخدمة**: de4dot
- **الملفات المتأثرة**: جميع ملفات AppTech*.dll
- **نوع الحماية**: Name obfuscation, Control flow obfuscation

#### 2. تشفير ملفات التراخيص
- **التشفير**: Base64 encoding
- **التوقيع**: XML Digital Signature (RSA-SHA1)
- **المحتوى**: معلومات الترخيص والصلاحيات

#### 3. تشفير سلاسل الاتصال
- **التشفير**: AES-128-CBC
- **المفتاح**: 8080808080808080 (مكشوف في الكود)
- **الموقع**: ملفات منفصلة مشار إليها في web.config

#### 4. تشفير JavaScript
- **التشفير**: AES-128-CBC
- **الاستخدام**: تشفير كلمات المرور في المتصفح
- **المشكلة**: المفتاح مكشوف في الكود المصدري

### نصائح الأمان

#### ⚠️ تحذيرات:
1. **مفاتيح التشفير مكشوفة** في ملفات JavaScript
2. **مفاتيح reCAPTCHA مكشوفة** في ملفات web.config
3. **بعض التجميعات** قد تحتوي على حماية إضافية

#### ✅ توصيات:
1. تغيير مفاتيح التشفير المكشوفة
2. نقل الإعدادات الحساسة إلى متغيرات البيئة
3. استخدام Azure Key Vault أو حلول مماثلة
4. تطبيق تشفير أقوى للبيانات الحساسة

### استكشاف الأخطاء

#### مشاكل شائعة:

**1. خطأ في صلاحيات الوصول:**
```powershell
# تشغيل PowerShell كمدير
Start-Process PowerShell -Verb RunAs
```

**2. de4dot لا يعمل:**
```powershell
# التحقق من وجود الملف
Test-Path "C:\inetpub\de4dot\de4dot.exe"
```

**3. فشل في فك التشفير:**
- تحقق من سجل الأخطاء في `Reports\master_log.txt`
- تأكد من صحة مسارات الملفات

### الدعم والمساعدة

للحصول على مساعدة إضافية:
1. راجع ملف السجل: `Reports\master_log.txt`
2. افحص التقرير الشامل: `Reports\master_report.html`
3. استخدم dnSpy لتحليل الملفات يدوياً

### إخلاء المسؤولية

هذه الأدوات مخصصة لأغراض التحليل والأمان فقط. يجب استخدامها بمسؤولية وفقاً للقوانين المحلية والدولية.
