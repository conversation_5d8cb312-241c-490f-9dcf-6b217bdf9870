﻿@model AppTech.MSMS.Domain.Models.Instruction


@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.LabelFor(model => model.Number, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Number, new {htmlAttributes = new {}})
        @Html.ValidationMessageFor(model => model.Number, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Text, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Text, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Text, "", new {@class = "text-danger"})
    </div>
</div>