﻿@model AppTech.MSMS.Domain.Models.CurrencyRate
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<div class="form-group">
    @Html.Label("العملة", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.SourceCurrencyID, (SelectList)ViewBag.Currencies)
        @Html.ValidationMessageFor(model => model.SourceCurrencyID)
    </div>
</div>


<div class="form-group">
    @Html.Label("سعر التحويل", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.ExchangeRate)
        @Html.ValidationMessageFor(model => model.ExchangeRate)
    </div>
</div>

<div class="form-group">
    @Html.Label("سعر البيع", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.SellPrice)
        @Html.ValidationMessageFor(model => model.SellPrice)
    </div>
</div>


<div class="form-group">
    @Html.Label("سعر الشراء", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.BuyPrice)
        @Html.ValidationMessageFor(model => model.BuyPrice)
    </div>
</div>


<div class="form-group">
    @Html.Label("الحالة", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.RadioButtonFor(x => x.UpDown, true) Down
        @Html.RadioButtonFor(x => x.UpDown, false) Up
        @Html.ValidationMessageFor(model => model.UpDown)
    </div>
</div>


<div class="form-group">
    @Html.Label("ملاحظات", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note)
        @Html.ValidationMessageFor(model => model.Note)
    </div>
</div>

