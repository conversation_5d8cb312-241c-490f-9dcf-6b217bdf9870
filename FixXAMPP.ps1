# XAMPP Fix Script for Collections System
# سكريبت إصلاح XAMPP لنظام التحصيلات

Write-Host @"
╔══════════════════════════════════════════════════════════════╗
║                    XAMPP Fix Script                         ║
║                 سكريبت إصلاح XAMPP                         ║
╚══════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Green

Write-Host "`nDiagnosing XAMPP issues..." -ForegroundColor Yellow

# التحقق من حالة Apache
Write-Host "`n1. Checking Apache status..." -ForegroundColor Cyan
$apacheProcess = Get-Process -Name "*httpd*" -ErrorAction SilentlyContinue
if ($apacheProcess) {
    Write-Host "✅ Apache is running (PID: $($apacheProcess.Id -join ', '))" -ForegroundColor Green
} else {
    Write-Host "❌ Apache is not running" -ForegroundColor Red
    Write-Host "Starting Apache..." -ForegroundColor Yellow
    Start-Process -FilePath "C:\xampp\apache_start.bat" -WindowStyle Hidden
}

# التحقق من حالة MySQL
Write-Host "`n2. Checking MySQL status..." -ForegroundColor Cyan
$mysqlProcess = Get-Process -Name "*mysql*" -ErrorAction SilentlyContinue
if ($mysqlProcess) {
    Write-Host "✅ MySQL is running (PID: $($mysqlProcess.Id -join ', '))" -ForegroundColor Green
} else {
    Write-Host "❌ MySQL is not running" -ForegroundColor Red
    Write-Host "Attempting to fix MySQL..." -ForegroundColor Yellow
    
    # إصلاح مشكلة MySQL
    Write-Host "  - Stopping any existing MySQL processes..." -ForegroundColor Gray
    Get-Process -Name "*mysql*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
    
    Write-Host "  - Removing MySQL service if exists..." -ForegroundColor Gray
    & "C:\xampp\mysql\bin\mysqld.exe" --remove mysql 2>$null
    
    Write-Host "  - Fixing data directory permissions..." -ForegroundColor Gray
    icacls "C:\xampp\mysql\data" /grant "Everyone:(OI)(CI)F" /T >$null 2>&1
    
    Write-Host "  - Starting MySQL in standalone mode..." -ForegroundColor Gray
    Start-Process -FilePath "C:\xampp\mysql\bin\mysqld.exe" -ArgumentList "--defaults-file=C:\xampp\mysql\bin\my.ini", "--standalone" -WindowStyle Hidden
    
    Start-Sleep -Seconds 5
    
    # التحقق من نجاح البدء
    $mysqlCheck = Get-Process -Name "*mysql*" -ErrorAction SilentlyContinue
    if ($mysqlCheck) {
        Write-Host "✅ MySQL started successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ MySQL failed to start" -ForegroundColor Red
    }
}

# التحقق من المنافذ
Write-Host "`n3. Checking ports..." -ForegroundColor Cyan
$port80 = netstat -an | findstr ":80.*LISTENING"
$port3306 = netstat -an | findstr ":3306.*LISTENING"

if ($port80) {
    Write-Host "✅ Port 80 (Apache): Active" -ForegroundColor Green
} else {
    Write-Host "❌ Port 80 (Apache): Not active" -ForegroundColor Red
}

if ($port3306) {
    Write-Host "✅ Port 3306 (MySQL): Active" -ForegroundColor Green
} else {
    Write-Host "❌ Port 3306 (MySQL): Not active" -ForegroundColor Red
}

Write-Host "`n✨ XAMPP Fix Script Completed! ✨" -ForegroundColor Green
Write-Host "`nTo access Collections System:" -ForegroundColor Yellow
Write-Host "Open browser: http://localhost/collections_system" -ForegroundColor Cyan
