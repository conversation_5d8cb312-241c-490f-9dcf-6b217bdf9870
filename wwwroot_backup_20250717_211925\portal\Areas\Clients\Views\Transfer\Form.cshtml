﻿@model AppTech.MSMS.Domain.Models.CashTransfer

@{
    Layout = "~/Views/Shared/_FormLayout.cshtml";
}


<input type="hidden" name="Device" value="Web"/>

<div class="form-group form-inline">
    @Html.Label("رقم العميل", new {@class = "control-label col-md-2"})
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.RefNumber)
        @Ajax.ActionLink(
            "بحث",
            "GetClientName",
            null,
            //new {number= " getNumber()" },

            new AjaxOptions
            {
                // UpdateTargetId="CustomerList", // <-- DOM element ID to update
                //InsertionMode = InsertionMode.Replace, // <-- Replace the content of DOM element
                LoadingElementId = "loader",
                OnSuccess = "onSuccess",
                HttpMethod = "GET" // <-- HTTP method
            },
            new {@class = "btn btn-primary btn-round", onclick = "this.href = '/clients/Transfer/GetClientName?number=' + document.getElementById('RefNumber').value;"}
            )
        @Html.ValidationMessageFor(model => model.RefNumber)
    </div>

</div>

<div class="form-group" id="client-name-row">
    @Html.Label("اسم العميل", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        <input type="text" id="client-name" value="" readonly="readonly"/>
    </div>
</div>


<div class="form-group">
    <label class="control-label  col-sm-2">العملة</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.CurrencyID, new[]
        {
            new SelectListItem {Text = "يمني", Value = "1"},
            new SelectListItem {Text = "دولار", Value = "2"},
            new SelectListItem {Text = "سعودي", Value = "3"}
        })

    </div>
    @Html.ValidationMessageFor(model => model.CurrencyID)
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Amount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Amount)
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note)
        @Html.ValidationMessageFor(model => model.Note)
    </div>
</div>


<script>

    $("#client-name-row").hide();

    function onSuccess(data) {

        if (data.Success) {
            $("#client-name-row").show();
            $("#client-name").val(data.Message);
        } else {

            alert('لم يتمكن العثور  على العميل بهذا الرقم');
        }

    }

    function getNumber() {
        return document.getElementById('Number').value;
    }
</script>