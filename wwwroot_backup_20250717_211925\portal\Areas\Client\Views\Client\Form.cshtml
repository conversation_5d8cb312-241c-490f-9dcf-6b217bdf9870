﻿@model AppTech.MSMS.Domain.Models.Client
@{
                /**/

                Layout = "~/Views/Shared/_Form.cshtml";
}

<h4 class="header blue bolder smaller">بيانات عامة</h4>
@Html.HiddenFor(model => model.Channel, new { value = "web" })

<div class="form-group">
    @Html.Label("رقم الهاتف", new { @class = "control-label col-md-2" })

    <div class="col-sm-10">
        <span class="input-icon input-icon-right">
            @Html.EditorFor(model => model.PhoneNumber, new { @class = "input-medium input-mask-phone", id = "form-field-phone", placeholder = "رقم الحساب" })
            @Html.ValidationMessageFor(model => model.PhoneNumber)
            <i class="ace-icon fa fa-phone fa-flip-horizontal"></i>
        </span>
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label no-padding-right" for="Name">اسم العميل</label>
    <div class="col-sm-10">
        @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Name)
    </div>
</div>

@if (!AppTech.MSMS.Domain.DomainManager.None_Clients_Types)
{
<div class="form-group">
    <label class="col-sm-2 control-label">نوع العميل</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.Type, new[]
        {
            new SelectListItem {Text = "تجاري", Value = "0", Selected = true},
            new SelectListItem {Text = "شخصي", Value = "1"}
        })
    </div>
    @Html.ValidationMessageFor(model => model.Type)
</div>



}

<div class="form-group">
    @Html.Label("مجموعة الصلاحيات", new { @class = "control-label col-md-2" })

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.ActivateBy, (SelectList)ViewBag.Groups, new { @class = "" })
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label no-padding-right" for="form-field-first">نوع النشاط</label>
    <div class="col-sm-10">
        @Html.EditorFor(model => model.ShopName)
        @Html.ValidationMessageFor(model => model.ShopName)
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label no-padding-right" for="form-field-note">ملاحظات</label>

    <div class="col-sm-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })

    </div>
</div>




<div class="space-4"></div>

<h4 class="header blue bolder smaller">بيانات التواصل</h4>
<div class="form-group">
    <label class="col-sm-2 control-label no-padding-right" for="form-field-website">العنوان</label>

    <div class="col-sm-10">
        @Html.EditorFor(model => model.Address, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
        @Html.ValidationMessageFor(model => model.Address)
    </div>
</div>


<div class="form-group">
    <label class="col-sm-2 control-label no-padding-right" for="ContactNumber">ارقام التواصل</label>

    <div class="col-sm-10">
        @Html.EditorFor(model => model.ContactNumber, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
        @Html.ValidationMessageFor(model => model.ContactNumber)
    </div>
</div>


<div class="form-group">
    <label class="col-sm-2 control-label no-padding-right" for="email">الأيميل</label>

    <div class="col-sm-10">
        <span class="input-icon input-icon-right">
            @Html.EditorFor(model => model.Email, new { @class = "form-control", id = "form-field-email" })
            @Html.ValidationMessageFor(model => model.Email)
            <i class="ace-icon fa fa-mail-reply"></i>
        </span>
    </div>
</div>

<div class="space"></div>
@if (Model.ID == 0)
{
    <h4 class="header blue bolder smaller">ييانات تسجيل الدخول</h4>
    <div class="form-group">
        <label class="col-sm-2 control-label no-padding-right">اسم المستخدم</label>
        <div class="col-sm-10">
            <input type="text" name="Username" id="Username" class="" />
            @Html.ValidationMessageFor(model => model.Username)
        </div>
    </div>

    <div class="space-4"></div>

    <div class="form-group">
        <label class="col-sm-2 control-label no-padding-right">كلمة المرور</label>
        <div class="col-sm-10">
            <input type="text" name="Password" class="" />
            @Html.ValidationMessageFor(model => model.Password)
        </div>
    </div>
}

<h4 class="header blue bolder smaller">بيانات البطاقة</h4>
<div class="space-4"></div>
<div class="form-group">
    <label class="col-sm-2 control-label">نوع البطاقة</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.CardType, new[]
        {
            new SelectListItem {Text = "شخصية", Value = "شخصية"},
            new SelectListItem {Text = "جواز سفر", Value = "جواز سفر"},
            new SelectListItem {Text = "عائلية", Value = "عائلية"},
            new SelectListItem {Text = "عسكرية", Value = "عسكرية"},
            new SelectListItem {Text = "أخرى", Value = "أخرى"}
        })


    </div>
    @Html.ValidationMessageFor(model => model.CardType)
</div>

<div class="space-4"></div>

<div class="form-group">
    <label class="col-sm-2 control-label">رقم البطاقة</label>
    <div class="col-sm-10">
        @Html.EditorFor(model => model.CardNumber, new { @class = "col-xs-12 col-sm-10" })
        @Html.ValidationMessageFor(model => model.CardType)
    </div>
</div>

<div class="space-4"></div>

<div class="form-group">
    <label class="col-sm-2 control-label">مكان الأصدار </label>
    <div class="col-sm-10">
        @Html.EditorFor(model => model.CardIssuePlace, new { @class = "col-xs-12 col-sm-10" })
        @Html.ValidationMessageFor(model => model.CardIssuePlace)
    </div>
</div>
<div class="space-4"></div>

<div class="form-group">
    <label class="col-sm-2 control-label date-picker">تاريخ الأصدار</label>
    <div class="col-sm-10">
        @Html.EditorFor(model => model.CardIssueDate, new { @class = "col-xs-12 col-sm-10 date-picker" })
        @Html.ValidationMessageFor(model => model.CardIssueDate)
    </div>
</div>


<div class="space-4"></div>

<script>

    $(function () {
        try {
            $("#PhoneNumber").on('change paste input', function () {
                $("#Username").val($("#PhoneNumber").val());
            });
            $('.date-picker').datepicker({
                dateFormat: "dd/MM/yy",
                changeMonth: true,
                changeYear: true,
                yearRange: "-60:+0",
                autoclose: true,
                todayHighlight: true
            }).next().on('click',
                function () {
                    $(this).prev().focus();
                });
        } catch (e) {
            alert("Couldnt set date-picker: " + e);
        }
    });

</script>