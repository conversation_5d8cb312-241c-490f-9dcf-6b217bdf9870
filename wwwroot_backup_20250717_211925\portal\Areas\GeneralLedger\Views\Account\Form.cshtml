﻿@model AppTech.MSMS.Domain.Models.Account
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    @Html.LabelFor(model => model.ParentNumber, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.ParentNumber, (SelectList) ViewBag.Accounts, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.ParentNumber, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Name, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Name, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Type, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        <select name="Type">
            <option value="رئيسي">رئيسي</option>
            <option value="فرعي">فرعي</option>
        </select>
        @Html.ValidationMessageFor(model => model.Type, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Description, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Description, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Description, "", new {@class = "text-danger"})
    </div>
</div>