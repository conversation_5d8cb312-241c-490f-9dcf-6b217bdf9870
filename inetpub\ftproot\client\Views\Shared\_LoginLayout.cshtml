﻿@using AppTech.MSMS.Domain
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>@ClientLicense.Customer.CompanyInfo.Name</title>
    <meta name="description" content="@AppTechInfo.Name" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    @Styles.Render("~/Content/login")

    <script src="https://www.google.com/recaptcha/api.js" async defer></script>

</head>

<body class="login-layout">

<section>
    @RenderBody()
</section>
    

@Html.Partial("_Footer")
@Scripts.Render("~/bundles/login")
<script>
    try {
        var clipboard = new ClipboardJS('.clipboard');
        clipboard.on('success', function (e) {
            $(e.trigger).text("تم النسخ!");
            e.clearSelection();
            setTimeout(function () {
                $(e.trigger).text("نسخ");
            }, 2500);
        });
        console.log('copy pass ');
    }
    catch (e) {console.log('copy error '+e); }
</script>
</body>
</html>