﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.RemittanceOut
@{
    Layout = "~/Views/Shared/_MultiLayout.cshtml";
}

@Html.HiddenFor(x => x.RemittanceID)
@Html.HiddenFor(x => x.RemittanceNumber)
@Html.HiddenFor(x => x.CurrencyID)
@Html.HiddenFor(x => x.Amount)
<div class="form-group">
    <div class="col-md-12">
        @Html.Label("حساب الصرف", new {@class = "control-label col-md-2"})
        @Html.Obout(new ComboBox("BenficiaryID")
        {
            Width = 300,
            FilterType = ComboBoxFilterType.Contains
        })

        @Html.ValidationMessageFor(model => model.BenficiaryID)
    </div>
</div>
@*<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>*@

<script>

    $(function() {

        try {
            formHelper.onSuccess = function(data) {
                log('remittanceout.onSuccess');
                //        hideFormLoading();
                i('data> ' + data);
                //  $('#crudform')[0].reset();

                ar('تم صرف الحوالة بنجاح');
                //if (confirm("هل تريد طباعة سند صرف حوالة")) {
                //PrintReceipt('صرف حوالة', data);
                //}
                window.location.href = '/#!/route/Admin/RemittanceOutSync';
            }
            formHelper.onBegin = function(context) {
                var msg = "سوف يتم صرف هذه الحوالة هل انت متأكد";
                if (!confirm(msg)) {
                    i('dont withdraw');
                    return false;
                }
                return true;
                //      showFormLoading();
            };
        } catch (e) {
        }
    });


</script>