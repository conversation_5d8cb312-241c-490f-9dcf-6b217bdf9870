﻿@model AppTech.MSMS.Domain.Models.ExchangerTarget
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

    <div class="form-group">
        @Html.LabelFor(model => model.RemittancePointID, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(m => m.RemittancePointID, (SelectList)ViewBag.Targets)
            @Html.ValidationMessageFor(model => model.RemittancePointID)
        </div>
    </div>


    <div class="form-group">
        @Html.LabelFor(model => model.ExchangerID, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(m => m.ExchangerID, (SelectList)ViewBag.Exchangers)
            @Html.ValidationMessageFor(model => model.ExchangerID)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.ProviderID, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(m => m.ProviderID, (SelectList)ViewBag.Providers)
            @Html.ValidationMessageFor(model => model.ProviderID)
        </div>
    </div>


    <div class="form-group">
        @Html.LabelFor(model => model.SyncID, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            <div class="checkbox">
                @Html.EditorFor(model => model.SyncID)
                @Html.ValidationMessageFor(model => model.SyncID, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>


    <div class="form-group">
        @Html.LabelFor(model => model.Active, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            <div class="checkbox">
                @Html.EditorFor(model => model.Active)
                @Html.ValidationMessageFor(model => model.Active, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>

