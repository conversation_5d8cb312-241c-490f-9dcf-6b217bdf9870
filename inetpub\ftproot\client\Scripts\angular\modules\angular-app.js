﻿var app = angular.module("AngularMVCApp", ["ngRoute"]);//, 'ui.bootstrap'

/*CONFIG*/
app.run(function ($rootScope, $location, $route, $timeout) {

    $rootScope.config = {};
    $rootScope.config.app_url = $location.url();
    $rootScope.config.app_path = $location.path();
    $rootScope.layout = {};
    $rootScope.layout.loading = false;

    $rootScope.$on("$routeChangeStart", function () {
   //     console.log('$routeChangeStart');
        //show loading gif
        $timeout(function () {
            $rootScope.layout.loading = true;
        });
    });
    $rootScope.$on("$routeChangeSuccess", function () {
      //  console.log('$routeChangeSuccess');
        //hide loading gif
        $timeout(function () {
            $rootScope.layout.loading = false;
        }, 200);
    });
    $rootScope.$on("$routeChangeError", function (event, toState, toParams, fromState, fromParams, error) {
        console.log("$routeChangeError :" + event+ toState+ toParams+ fromState+ fromParams+ error);
        //hide loading gif
 //       if (error && !error.authenticated) {
   //         ar('ليس لديك صلاحية');
     //   }
     ar('انتهت الجلسة قم بأعادة تسجيل الدخول');
        // parseAndShowError(xhr, textStatus, errorThrown);
        $rootScope.layout.loading = false;

    });
});

//app.controller("LandingPageController", LandingPageController);
app.controller("Profile", function ($scope, $http, $window) {

    $scope.person = {};
    $scope.changePassword = function () {
     
        $http({
            method: "POST",
            url: "/Account/ChangePassword",
            data: $scope.person,
            headers: {
                'RequestVerificationToken': $scope.antiForgeryToken
            }
        }).success(function (data, status, headers, config) {
            $scope.message = "";
resetButton();
            if (data.Success === false) {
                var str = data.Message;
                $window.alert(str);
                $scope.message = str;
            }
            else {
                $window.alert("تم تعديل كلمة المرور بنجاح");
                $scope.success = "تم تعديل كلمة المرور بنجاح";
                $scope.person = {};
            }
        }).error(function (data, status, headers, config) {
            $scope.message = "حدث خطء غير معروف , الرجاء المحاولة لاحقاَ";
        });
    };    
});
app.controller("CrudController", function($scope ,$http,$window) {


    $scope.AddRecord=function() {

      
//        $window.alert("Add Click");
        //$http.get("/RSS/AddOrEdit").success(function(result) {

        //    var modal = document.getElementById('modal');
        //   modal.find("div.modal-body").html(result);
        //    modal.modal("show");
        //});

    }
});

var configFunction = function ($routeProvider, $locationProvider, $urlRouterProvider) {

 //   $locationProvider.html5Mode(true);
    $routeProvider
        .when("/dashboard", { templateUrl: "home/dashboard" })
        .when("/error", { templateUrl: "error/index" })
        .when("/account/:action",
            {templateUrl: function (params) { return "/Account/" + params.action; }
        })
        .when("/:controller/:action",
            {
                templateUrl: function (params) { return params.controller+"/" + params.action; }
            })
        .when("/route/:area/:controller", {
            templateUrl: function (params) { return "/" + params.area + "/" + params.controller; }
           // , controller: "ctrl"
        })
        .when("/route/:area/:controller/:action", {
            templateUrl: function (params) { return "/" + params.area + "/" + params.controller + "/" + params.action; }
            //, controller: "ctrl"
        })
        .when("/route/:area/:controller/:action/:id", {
            templateUrl: function (params) { return "/" + params.area + "/" + params.controller + "/" + params.action + "/" + params.id; },
            cache: false
        })
        .when("/UsersDetails/:roleId/:id",
            {
                templateUrl: function (params) { return "/Security/User/UsersDetail?parentType=" + params.roleId + "&id=" + params.id; },
                cache: false
            })
        
        .otherwise({ redirectTo: "/dashboard" });

}
configFunction.$inject = ["$routeProvider"];

app.config(configFunction);

