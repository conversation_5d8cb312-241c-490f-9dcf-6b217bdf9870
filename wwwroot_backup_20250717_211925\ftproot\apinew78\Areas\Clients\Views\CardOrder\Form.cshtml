﻿@model AppTech.MSMS.Domain.Models.CardOrder

@{
    Layout = "~/Views/Shared/_FormLayout.cshtml";
}

<div class="form-group">
    <label class="col-sm-2 control-label">النوع </label>
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.CardTypeID, (SelectList)ViewBag.Types, new { })
        @Html.ValidationMessageFor(model => model.CardTypeID)
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">الفئة </label>
    <div class="col-md-10">
        <select id="CardFactionID" name="CardFactionID" class="select2" style="width: 110px;" placeholder="اختر فئه" required></select>
        @Html.ValidationMessageFor(model => model.CardFactionID)
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">ID</label>
    <div class="col-md-10">
        @Html.EditorFor(model => model.UserID)
        @Html.ValidationMessageFor(model => model.UserID)
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label"> اسم المستخدم</label>
    <div class="col-md-10">
        @Html.EditorFor(model => model.Username)
        @Html.ValidationMessageFor(model => model.Username)
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">كلمة المرور </label>
    <div class="col-md-10">
        @Html.EditorFor(model => model.Password)
        @Html.ValidationMessageFor(model => model.Password)
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">الإيميل </label>
    <div class="col-md-10">
        @Html.EditorFor(model => model.Email)
        @Html.ValidationMessageFor(model => model.Email)
    </div>
</div>
<div class="form-group">
    <label class="col-sm-2 control-label">رقم الهاتف </label>
    <div class="col-md-10">
        @Html.EditorFor(model => model.Phone)
        @Html.ValidationMessageFor(model => model.Phone)
    </div>
</div>
<input type="number" id="Cost" hidden />

<script>
    $(function () {
        var CardType = $("#CardTypeID").val();
        loadFactionList(CardType);
    });
    //$(function () {
    //    $("#cancel-button").hide();
    //    $("#submit-button").text('طلب الشحن');

    //    $("#submit-button").on('click', function () {
    //        var msg = "سوف يتم شحن " +
    //            $("#CardTypeID option:selected").text()
    //            + ' فئة : ' + $("#CardFactionID option:selected").text()
    //            + ' لرقم ' + $("#Phone").val() +
    //            ' هل انت متأكد';
    //        if (!confirm(msg)) {
    //            i('not confirmed');
    //            history.go(0);
    //            return false;
    //        } else {
    //            i('confirmed');
    //            return true;
    //        }
    //    })
    //});
    $("#CardTypeID").on("change", function () {
        var id = $("#CardTypeID").val();
        loadFactionList(id);
    });
    function loadFactionList(id) {
        i('loadDataList');
        fillDataList('CardFactionID', '/Clients/CardOrder/GetCardFactions?id=' + id, false, "اختر فئــه");
    };

    $("#CardFactionID").on("change", function () {
        var CardFaction = $("#CardFactionID").val();
        $.post("Clients/CardOrder/GetCost", { id: CardFaction }, function (data) {
            $("#Cost").val(data);
        });
    });

    $(function () {
        $("#cancel-button").hide();
        $("#submit-button").text('طلب الكرت');

        $("#submit-button").on('click', function () {
            var phone = '';

            if ($("#Phone").val() > 700000000)
                phone += ' لرقم ' + $("#Phone").val();

            var msg = "سوف يتم شحن  " +
                $("#CardTypeID option:selected").text()
                + ' فئة : ' + $("#CardFactionID option:selected").text()
                + phone
                + ' بمبلغ ' + $("#Cost").val() + ' ريال يمني  '
                + ' هل انت متأكد؟';

            if (!confirm(msg)) {
                i('not confirmed');
                history.go(0);
                return false;
            } else {
                i('confirmed');
                return true;
            }
        })
    });

</script>
