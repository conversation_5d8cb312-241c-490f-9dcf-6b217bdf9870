<<type>>
<Module>
<Module>
<Module>
<Module>
<<type>>
m1YIJgJljBnbLqmbTc.coYnSfbCmV1X9VtrH2
UHrxHNFwiAlW4aQNKl
coYnSfbCmV1X9VtrH2
UHrxHNFwiAlW4aQNKl
<<type>>
AppTech.Security.Licensing.AppLicenseProvider
AppTech.Security.Licensing.AppLicenseProvider
AppLicenseProvider
AppLicenseProvider
dsFwPLy2f
VerifyFromLicFile
Et6bYyaYM
customerLicense
YJoJYnSfC
_currentInstance
DV1AX9Vtr
_type
h2pR1YIJg
_licensed
<<type>>
AppTech.Security.Licensing.CertificateHelper
AppTech.Security.Licensing.CertificateHelper
CertificateHelper
CertificateHelper
<<type>>
AppTech.Security.Licensing.CustomerFiles
AppTech.Security.Licensing.CustomerFiles
CustomerFiles
CustomerFiles
<<type>>
AppTech.Security.Licensing.CustomerInfo
AppTech.Security.Licensing.CustomerInfo
CustomerInfo
CustomerInfo
KjB7nbLqm
<ID>k__BackingField
bTcsc16mc
<Name>k__BackingField
kpnH1aMKD
<Number>k__BackingField
FYAP4UTVC
<EnglishName>k__BackingField
iJmar2eEB
<CompanyName>k__BackingField
Q2IFaTWeo
<Contacts>k__BackingField
rKMDJvB1Y
<Address>k__BackingField
EWJyDiTLu
<Description>k__BackingField
<<type>>
AppTech.Security.Licensing.CustormTypeTypeConverter
AppTech.Security.Licensing.CustormTypeTypeConverter
CustormTypeTypeConverter
CustormTypeTypeConverter
<<type>>
AppTech.Security.Licensing.CustomerLicense
AppTech.Security.Licensing.CustomerLicense
CustomerLicense
CustomerLicense
sOl2yrkKk
set_State
W5LfHNHh6
set_LicenseSetting
h5hpavP0p
SetLicenseSetting
rWXgBlypc
<State>k__BackingField
LrMVV2ABv
<LicenseSetting>k__BackingField
SpljctTSY
type
<<type>>
AppTech.Security.Licensing.LicenseState
AppTech.Security.Licensing.LicenseState
LicenseState
LicenseState
<<type>>
KTVCPJRmr2eEBN2IaT.I16mcyApn1aMKDvYA4
AppTech.Security.Licensing.DesigntimeLicense
I16mcyApn1aMKDvYA4
DesigntimeLicense
<<type>>
AppTech.Security.Licensing.LicenseTypes
AppTech.Security.Licensing.LicenseTypes
LicenseTypes
LicenseTypes
<<type>>
AppTech.Security.Licensing.LicenseStatus
AppTech.Security.Licensing.LicenseStatus
LicenseStatus
LicenseStatus
<<type>>
AppTech.Security.Licensing.VersionType
AppTech.Security.Licensing.VersionType
VersionType
VersionType
<<type>>
AppTech.Security.Licensing.SystemLevel
AppTech.Security.Licensing.SystemLevel
SystemLevel
SystemLevel
<<type>>
AppTech.Security.Licensing.HardwareInfo
AppTech.Security.Licensing.HardwareInfo
HardwareInfo
HardwareInfo
eouLswH22
GetDiskVolumeSerialNumber
kt78Sfb8e
GetProcessorId
dp4k1KCoG
GetMotherboardID
OOFMSB8yE
SplitInParts
<<type>>
AppTech.Security.Licensing.IdentifierManager
AppTech.Security.Licensing.IdentifierManager
IdentifierManager
IdentifierManager
A3f6vmX4A
GetHash
J3o0bKAFO
GetHexString
csa5GO1sA
identifier
GJn9JP8MB
identifier
ATftDuS0k
cpuId
Ac2rMqyoX
biosId
rCZKPpcUR
diskId
T1HoTpFAX
baseId
UBVeYceEO
videoId
j1C4oQDff
macId
GXOXpNXs6
fingerPrint
<<type>>
AppTech.Security.Licensing.ShowInLicenseInfoAttribute
AppTech.Security.Licensing.ShowInLicenseInfoAttribute
ShowInLicenseInfoAttribute
ShowInLicenseInfoAttribute
<<type>>
AppTech.Security.Licensing.LicenseEntity
AppTech.Security.Licensing.LicenseEntity
LicenseEntity
LicenseEntity
oV9mWovjv
<AppName>k__BackingField
zSXxHHJNp
<UID>k__BackingField
dXVv1e9fO
<Type>k__BackingField
pRtTG81eD
<VersionType>k__BackingField
eQ8dGRcBA
<ExpiryDate>k__BackingField
KZJcY41CN
<CreateDateTime>k__BackingField
<<type>>
AppTech.Security.Licensing.LicenseHelper
AppTech.Security.Licensing.LicenseHelper
LicenseHelper
LicenseHelper
<<type>>
AppTech.Security.Licensing.LicenseInfo
AppTech.Security.Licensing.LicenseInfo
LicenseInfo
LicenseInfo
wCYEMI0VO
<SystemLevel>k__BackingField
V0GIUy8Qn
<MaxUsers>k__BackingField
EKwU0tVjv
<MaxBranches>k__BackingField
BgQl5EQcT
<Modules>k__BackingField
qaO11vgr6
<CustomerNumber>k__BackingField
HUGiQW8Xq
<CustomerName>k__BackingField
<<type>>
AppTech.Security.Licensing.LicenseSetting
AppTech.Security.Licensing.LicenseSetting
LicenseSetting
LicenseSetting
DcJYSgg3s
<CustomerInfo>k__BackingField
CJDBWhIP6
<SystemLevel>k__BackingField
hsDhcmsIe
<VersionType>k__BackingField
y4XNMsWs2
<MaxUsers>k__BackingField
r5GOwDZT7
<MaxBranches>k__BackingField
pFYC7vusT
<Modules>k__BackingField
q7XW9omQs
<Properties>k__BackingField
<<type>>
AppTech.Security.Licensing.LicenseVerifier
AppTech.Security.Licensing.LicenseVerifier
LicenseVerifier
LicenseVerifier
DonSahLxZ
VerifyXml
<<type>>
AppTech.Security.Licensing.Controls.LicenseActivateControl
AppTech.Security.Licensing.Controls.LicenseActivateControl
LicenseActivateControl
LicenseActivateControl
b4AGLODhk
get_CertificatePublicKeyData
QKN3qXCVO
lnkCopy_LinkClicked
ApGZJKH7k
InitializeComponent
xE6Qo5tZ3
<AppName>k__BackingField
qEWqvdBmb
<CertificatePublicKeyData>k__BackingField
oVqnFZeMB
<ShowMessageAfterValidation>k__BackingField
PPAzG4Ov0
<LicenseObjectType>k__BackingField
RbTwuoV3YQ
components
g1xwwgw7Kf
grpbxLicense
DrAwbkLVLR
txtLicense
zsFwJfRgZ9
grpbxUID
gVlwAEIAw7
lblUIDTip
lUJwRQ5CJS
lnkCopy
SmAw7VBjFy
txtUID
<<type>>
AppTech.Common.AppFiles
AppTech.Common.AppFiles
AppFiles
AppFiles
<<type>>
AppTech.Common.AppSetting
AppTech.Common.AppSetting
AppSetting
AppSetting
pFLwsLO544
<ConnectionStringFile>k__BackingField
UkswHg6n6f
<AppLicenseName>k__BackingField
BL2wPyaX1P
appType
<<type>>
AppTech.Common.DbProvider
AppTech.Common.DbProvider
DbProvider
DbProvider
<<type>>
AppTech.Common.ConnnectionStringSource
AppTech.Common.ConnnectionStringSource
ConnnectionStringSource
ConnnectionStringSource
<<type>>
AppTech.Common.SessionStatus
AppTech.Common.SessionStatus
SessionStatus
SessionStatus
<<type>>
AppTech.Common.Channel
AppTech.Common.Channel
Channel
Channel
<<type>>
AppTech.Common.FileLogger
AppTech.Common.FileLogger
FileLogger
FileLogger
<<type>>
AppTech.Common.RandomGenerator
AppTech.Common.RandomGenerator
RandomGenerator
RandomGenerator
<<type>>
AppTech.Common.Utils.Reflector
AppTech.Common.Utils.Reflector
Reflector
Reflector
<<type>>
AppTech.Common.Utils.DataFormat
AppTech.Common.Utils.DataFormat
DataFormat
DataFormat
<<type>>
ruH5hasvP0pkOlyrkK.Seo8KM7JvB1YxWJDiT
AppTech.Common.Utils.Excel
Seo8KM7JvB1YxWJDiT
Excel
QUwwaiOVlM
ReadExcel
<<type>>
AppTech.Common.Utils.ImageConverter
AppTech.Common.Utils.ImageConverter
ImageConverter
ImageConverter
<<type>>
AppTech.Common.Utils.RegistryManager
AppTech.Common.Utils.RegistryManager
RegistryManager
RegistryManager
<<type>>
AppTech.Common.Utils.Validator
AppTech.Common.Utils.Validator
Validator
Validator
<<type>>
AppTech.Common.Helpers.Money
AppTech.Common.Helpers.Money
Money
Money
LtawF92uEk
Ahad
kYYwD8fRIr
Degree_One
H5lwyRipXh
Degree_Two
jZawpZaMTd
Hundredth
q9Kw2FL9OT
ToFractions
y67wfkQesG
ReadThreeDigits
DV1wgL9h9B
Tenth
t26wVPL2iE
Tenth_Ahad
<<type>>
AppTech.Common.Helpers.TypeHelper
AppTech.Common.Helpers.TypeHelper
TypeHelper
TypeHelper
<<type>>
LrMV2APBvWplctTSYR.sQ5LHNHHh6LWXBlypc
AppTech.Common.Helpers.SettingHelper
sQ5LHNHHh6LWXBlypc
SettingHelper
Gojwjyh9xw
GetCurrentComputerName
l1VwLwkYBp
GetDbNameFromConnectionString
zZpw85Y0Qs
GetAppSetting
XFVwkeZgMU
SetAppSetting
<<type>>
AppTech.Common.Helpers.XMLHelper
AppTech.Common.Helpers.XMLHelper
XMLHelper
XMLHelper
XvowMphjRq
get_FileName
H4jw0HZjvd
xmlDocument
kYVw6wyg6x
FileName
<<type>>
AppTech.Common.Extensions.DatatableExtension
AppTech.Common.Extensions.DatatableExtension
DatatableExtension
DatatableExtension
<<type>>
AppTech.Common.Extensions.DateExtension
AppTech.Common.Extensions.DateExtension
DateExtension
DateExtension
<<type>>
AppTech.Common.Extensions.EnumExtensions
AppTech.Common.Extensions.EnumExtensions
EnumExtensions
EnumExtensions
<<type>>
AppTech.Common.Extensions.Extensions
AppTech.Common.Extensions.Extensions
Extensions
Extensions
<<type>>
AppTech.Common.Extensions.BrowserCapabilitiesExtension
AppTech.Common.Extensions.BrowserCapabilitiesExtension
BrowserCapabilitiesExtension
BrowserCapabilitiesExtension
<<type>>
AppTech.Common.Extensions.CollectionExtension
AppTech.Common.Extensions.CollectionExtension
CollectionExtension
CollectionExtension
sBnw535FR6
GetProperty
<<type>>
AppTech.Common.Extensions.EnumExtension
AppTech.Common.Extensions.EnumExtension
EnumExtension
EnumExtension
<<type>>
AppTech.Common.Extensions.QueryStringExtension
AppTech.Common.Extensions.QueryStringExtension
QueryStringExtension
QueryStringExtension
<<type>>
AppTech.Common.Extensions.SerializationExtension
AppTech.Common.Extensions.SerializationExtension
SerializationExtension
SerializationExtension
<<type>>
AppTech.Common.Extensions.StringExtension
AppTech.Common.Extensions.StringExtension
StringExtension
StringExtension
<<type>>
AppTech.Common.Extensions.NullExtension
AppTech.Common.Extensions.NullExtension
NullExtension
NullExtension
<<type>>
AppTech.Common.Extensions.StreamExtensions
AppTech.Common.Extensions.StreamExtensions
StreamExtensions
StreamExtensions
<<type>>
AppTech.Common.Extensions.XmlDocumentExtensions
AppTech.Common.Extensions.XmlDocumentExtensions
XmlDocumentExtensions
XmlDocumentExtensions
<<type>>
AppTech.Common.Events.EventsHelper
AppTech.Common.Events.EventsHelper
EventsHelper
EventsHelper
JbTw9hBHbH
InvokeDelegate
<<type>>
AppTech.Common.Events.GenericEventHandler
AppTech.Common.Events.GenericEventHandler
GenericEventHandler
GenericEventHandler
<<type>>
AppTech.Common.Events.GenericEventHandler`1
AppTech.Common.Events.GenericEventHandler`1
GenericEventHandler`1
GenericEventHandler`1
<<type>>
AppTech.Common.Events.GenericEventHandler`2
AppTech.Common.Events.GenericEventHandler`2
GenericEventHandler`2
GenericEventHandler`2
<<type>>
AppTech.Common.Events.GenericEventHandler`3
AppTech.Common.Events.GenericEventHandler`3
GenericEventHandler`3
GenericEventHandler`3
<<type>>
AppTech.Common.Events.GenericEventHandler`4
AppTech.Common.Events.GenericEventHandler`4
GenericEventHandler`4
GenericEventHandler`4
<<type>>
AppTech.Common.Events.GenericEventHandler`5
AppTech.Common.Events.GenericEventHandler`5
GenericEventHandler`5
GenericEventHandler`5
<<type>>
AppTech.Common.Events.GenericEventHandler`6
AppTech.Common.Events.GenericEventHandler`6
GenericEventHandler`6
GenericEventHandler`6
<<type>>
AppTech.Common.Events.GenericEventHandler`7
AppTech.Common.Events.GenericEventHandler`7
GenericEventHandler`7
GenericEventHandler`7
<<type>>
AppTech.Common.DTO.ResponseInfo
AppTech.Common.DTO.ResponseInfo
ResponseInfo
ResponseInfo
QZcwtPKkKM
<ReqType>k__BackingField
eIUwrEnDwV
<Success>k__BackingField
K5wwKcifA3
<Result>k__BackingField
GJewoJb9Vl
<Error>k__BackingField
<<type>>
AppTech.Common.DTO.MasterRecord
AppTech.Common.DTO.MasterRecord
MasterRecord
MasterRecord
hltweGwUv8
<ID>k__BackingField
gZDw4LT2NY
<DetailRecords>k__BackingField
<<type>>
AppTech.Common.DTO.RequestInfo
AppTech.Common.DTO.RequestInfo
RequestInfo
RequestInfo
LofwXWXjaI
<Domain>k__BackingField
OvSwmh9uVT
<Target>k__BackingField
k9VwxbvPvD
<Channel>k__BackingField
pqJwvM49dr
<ReqType>k__BackingField
qLrwT6uWmR
<AsJson>k__BackingField
bQtwdrXpSD
<SID>k__BackingField
d5xwcc4CrV
<Entity>k__BackingField
Y28wE7vHNb
<Query>k__BackingField
nLNwITOk9n
<Method>k__BackingField
gL3wUdv307
<Extra>k__BackingField
<<type>>
AppTech.Common.DTO.ExtraInfo
AppTech.Common.DTO.ExtraInfo
ExtraInfo
ExtraInfo
nqlwlkVRP9
<Extras>k__BackingField
<<type>>
AppTech.Common.DTO.Extra
AppTech.Common.DTO.Extra
Extra
Extra
bIFw1V7sb1
<Key>k__BackingField
oKpwir4h5e
<Value>k__BackingField
<<type>>
AppTech.Common.DTO.QueryInfo
AppTech.Common.DTO.QueryInfo
QueryInfo
QueryInfo
KCSwYRO8C2
<Name>k__BackingField
PBUwBktRHS
<Page>k__BackingField
kPVwhy66Ei
<PageSize>k__BackingField
M9pwNEMOwl
<Where>k__BackingField
C30wOjS09Z
<OrderBy>k__BackingField
YiAwCQKuG1
<All>k__BackingField
<<type>>
AppTech.Common.DTO.MethodInfo
AppTech.Common.DTO.MethodInfo
MethodInfo
MethodInfo
B4KwWomDgV
<Args>k__BackingField
zF3wSyeIEU
<Name>k__BackingField
<<type>>
AppTech.Common.DTO.ReqType
AppTech.Common.DTO.ReqType
ReqType
ReqType
<<type>>
AppTech.Common.Data.Data
AppTech.Common.Data.Data
Data
Data
niQw3CPknQ
<Id>k__BackingField
D1mwZk4FJU
<Name>k__BackingField
cJLwGXaxKq
<ExtraId>k__BackingField
<<type>>
AppTech.Common.Data.DataResponse
AppTech.Common.Data.DataResponse
DataResponse
DataResponse
FccwQf0Wae
<Ref>k__BackingField
WhZwqswXcT
<DataList>k__BackingField
<<type>>
AppTech.Common.Data.DataTableHelper
AppTech.Common.Data.DataTableHelper
DataTableHelper
DataTableHelper
ARLwnw84Bq
GetValue
TKdwzxKlLk
IsDataContract
FAGbuCcSZN
MatchingTableRow
<<type>>
AppTech.Common.Data.DataContractSerializer`1
AppTech.Common.Data.DataContractSerializer`1
DataContractSerializer`1
DataContractSerializer`1
gkLbwWbRKk
m_DataContractSerializer
<<type>>
R1sATJynJP8MBeTfDu.FmX4AID3obKAFOJsaG
AppTech.Common.Security.EncrytionHelper
FmX4AID3obKAFOJsaG
EncrytionHelper
NMAbxZMHY4
get_Content
E2ebvhMWOY
set_Content
E6BbdtUsig
get_CryptoException
cvybcTIots
set_CryptoException
uqbbIEfbtr
get_Encoding
YSxbU9Q0Ab
set_Encoding
tkCb1mgFOJ
get_EncryptionAlgorithm
PsAbi1jKLJ
set_EncryptionAlgorithm
g83bBdOXC0
get_IsHashAlgorithm
rZKbNgNRXD
get_Key
ie5bOcJTuU
set_Key
KGQbbiQeHd
_Decrypt
KBfbJenHnp
_Decrypt
Y4ubATiFbj
_Encrypt
oMAbRtLfiM
_Encrypt
xWrb7QRpa0
BytesToHex
OIqbslSLcZ
Clear
hfZbHBm7w1
ClearBuffer
bfrbP0oJCt
ComputeHash
cLybaguyHP
ComputeHash
CmybFM17R1
ComputeHash
J68bDEs0hk
Decrypt
ir9by0XwHe
Decrypt
ySkbpmGYHn
DecryptFile
bdnb2LiVke
DecryptFile
wiwbfFvc1t
DecryptFile
ltObgo4VZI
DecryptString
gl0bVv3Eyh
DerivePassword
JjDbj7coMJ
Encrypt
b0abLjlhZS
Encrypt
AnOb8t7XGY
EncryptFile
gh8bkhRGBO
EncryptFile
iv1bM5wiWj
EncryptFile
GDSb6BpbFC
EncryptString
QQ5b0PsHHa
GenerateHash
KQUb5QXYH4
GenerateSalt
AFnb9ct6uU
GetTextFromFile
OAibtn46bs
HexToBytes
XbibrnRCwq
Init
XMhbKvGjll
Init
Tltbo9YFDt
smethod_0
D9ebesbab7
smethod_1
Iygb442nQt
SymmetricDecrypt
xGjbXv6unI
SymmetricEncrypt
z98bmUo9wN
ValidateRSAKeys
BWCbCXEbS6
IV_8
R9qbWw7o1A
IV_16
gyObS8HVvM
IV_24
GMVb3asQBc
IV_32
dWmbZ3q9lC
SALT_BYTES
ihabGc7fAU
<Content>k__BackingField
e4bbQ27LDJ
<CryptoException>k__BackingField
AmxbqAy6gN
<Encoding>k__BackingField
gq7bn6UIL9
<EncryptionAlgorithm>k__BackingField
nQjbzTN5hR
<Key>k__BackingField
AhNbTClLVG
Content
LnvbEoVhYK
CryptoException
y4WbldUnoO
Encoding
kprbYlJem8
EncryptionAlgorithm
EmnbhaiBcw
IsHashAlgorithm
<<type>>
AppTech.Common.Security.BASE36
AppTech.Common.Security.BASE36
BASE36
BASE36
V8IJuFofy1
Reverse
GWZJwtuouu
_charArray
<<type>>
AppTech.Common.Security.KeyManager
AppTech.Common.Security.KeyManager
KeyManager
KeyManager
IRVJbZkyiO
_androidKey
<<type>>
AppTech.Common.Security.DataSecurity
AppTech.Common.Security.DataSecurity
DataSecurity
DataSecurity
ME3JJWb4j7
Encrypt
kE5JALulBR
Decrypt
V3qJRF6XxK
GetRijndaelManaged
KrZJ7YgqYl
bytes
<<type>>
AppTech.Common.Properties.Resources
AppTech.Common.Properties.Resources
Resources
Resources
af0Js0yhg5
get_ResourceManager
uFfJPMtPUb
get_Culture
qimJaeGUMG
set_Culture
PBDJD7gTgo
get_AK
WVkJyxaE5I
resourceMan
MPIJpR9LtZ
resourceCulture
OZFJHGAiOc
ResourceManager
GIcJFfVWPZ
Culture
<<type>>
AppTech.Common.Exceptions.AuthenticationException
AppTech.Common.Exceptions.AuthenticationException
AuthenticationException
AuthenticationException
<<type>>
AppTech.Common.Exceptions.AppTechException
AppTech.Common.Exceptions.AppTechException
AppTechException
AppTechException
<<type>>
AppTech.Common.Exceptions.ExceptionManager
AppTech.Common.Exceptions.ExceptionManager
ExceptionManager
ExceptionManager
<<type>>
AppTech.Common.Exceptions.ReportException
AppTech.Common.Exceptions.ReportException
ReportException
ReportException
<<type>>
AppTech.Common.Exceptions.SessionException
AppTech.Common.Exceptions.SessionException
SessionException
SessionException
<<type>>
AppTech.Common.Entities.IBranchable
AppTech.Common.Entities.IBranchable
IBranchable
IBranchable
<<type>>
AppTech.Common.Entities.Result
AppTech.Common.Entities.Result
Result
Result
FmEJ2BQV7E
<Success>k__BackingField
xySJfbOwm1
<Message>k__BackingField
<<type>>
AppTech.Common.Entities.IAuditableEntity
AppTech.Common.Entities.IAuditableEntity
IAuditableEntity
IAuditableEntity
<<type>>
AppTech.Common.Entities.IEntity
AppTech.Common.Entities.IEntity
IEntity
IEntity
<<type>>
AppTech.Common.Entities.Error
AppTech.Common.Entities.Error
Error
Error
KrGJgUncNB
<Code>k__BackingField
FVdJVyZ0ML
<Message>k__BackingField
gvUJjCn7KA
<Detail>k__BackingField
x2RJLM8sma
<Type>k__BackingField
<<type>>
AppTech.Common.Entities.ErrorType
AppTech.Common.Entities.ErrorType
ErrorType
ErrorType
<<type>>
AppTech.Common.Db.DbConnectionHelper
AppTech.Common.Db.DbConnectionHelper
DbConnectionHelper
DbConnectionHelper
tTMJ8l0c7F
SaveToEncryptedFile
<<type>>
AppTech.Common.Db.DBConnection
AppTech.Common.Db.DBConnection
DBConnection
DBConnection
RVlJkDaF6A
set_ConnectionString
xIbJMEosmU
set_DbName
jmaJ6B2eER
<ConnectionString>k__BackingField
i6aJ0pvmOK
<DbName>k__BackingField
<<type>>
AppTech.Common.Db.XmlDbConnection
AppTech.Common.Db.XmlDbConnection
XmlDbConnection
XmlDbConnection
<<type>>
AppTech.Common.Db.EncryptedDbConnection
AppTech.Common.Db.EncryptedDbConnection
EncryptedDbConnection
EncryptedDbConnection
zPMJ5PnACc
ExtractConnectionString
S7aJ9LAmBB
_password
kVUJt3rdAh
<UserName>k__BackingField
<<type>>
AppTech.Common.Abstracts.IError
AppTech.Common.Abstracts.IError
IError
IError
<<type>>
AppTech.Common.Abstracts.ILogger
AppTech.Common.Abstracts.ILogger
ILogger
ILogger
<<type>>
<PrivateImplementationDetails>
<PrivateImplementationDetails>
<PrivateImplementationDetails>
<PrivateImplementationDetails>
<<type>>
AppTech.Security.Licensing.HardwareInfo/<SplitInParts>d__3
AppTech.Security.Licensing.HardwareInfo/<SplitInParts>d__3
<SplitInParts>d__3
<SplitInParts>d__3
<<type>>
AppTech.Security.Licensing.ShowInLicenseInfoAttribute/FormatType
AppTech.Security.Licensing.ShowInLicenseInfoAttribute/FormatType
FormatType
FormatType
<<type>>
LrMV2APBvWplctTSYR.sQ5LHNHHh6LWXBlypc/<>c__DisplayClass2_0
AppTech.Common.Helpers.SettingHelper/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
LrMV2APBvWplctTSYR.sQ5LHNHHh6LWXBlypc/<>c__DisplayClass3_0
AppTech.Common.Helpers.SettingHelper/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.Common.Extensions.CollectionExtension/<>c__DisplayClass5_0
AppTech.Common.Extensions.CollectionExtension/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.Common.Extensions.EnumExtension/<ToEnumerable>d__0
AppTech.Common.Extensions.EnumExtension/<ToEnumerable>d__0
<ToEnumerable>d__0
<ToEnumerable>d__0
<<type>>
AppTech.Common.Events.EventsHelper/T0kec2pMqyoX8CZPpc
AppTech.Common.Events.EventsHelper/AsyncFire
T0kec2pMqyoX8CZPpc
AsyncFire
<<type>>
AppTech.Common.Events.EventsHelper/<>c
AppTech.Common.Events.EventsHelper/<>c
<>c
<>c
<<type>>
AppTech.Common.Data.DataTableHelper/<>c
AppTech.Common.Data.DataTableHelper/<>c
<>c
<>c
<<type>>
R1sATJynJP8MBeTfDu.FmX4AID3obKAFOJsaG/KRS1HT2pFAX3BVYceE
AppTech.Common.Security.EncrytionHelper/Algorithm
KRS1HT2pFAX3BVYceE
Algorithm
<<type>>
R1sATJynJP8MBeTfDu.FmX4AID3obKAFOJsaG/RF1CoQfDffTXOpNXs6
AppTech.Common.Security.EncrytionHelper/EncodingType
RF1CoQfDffTXOpNXs6
EncodingType
<<type>>
R1sATJynJP8MBeTfDu.FmX4AID3obKAFOJsaG/oV9WovgjvKSXHHJNpA
AppTech.Common.Security.EncrytionHelper/EncryptAlgorithmEnum
oV9WovgjvKSXHHJNpA
EncryptAlgorithmEnum
<<type>>
R1sATJynJP8MBeTfDu.FmX4AID3obKAFOJsaG/CV1e9fVOVRtG81eDRQ
AppTech.Common.Security.EncrytionHelper/HashAlgorithmEnum
CV1e9fVOVRtG81eDRQ
HashAlgorithmEnum
<<type>>
R1sATJynJP8MBeTfDu.FmX4AID3obKAFOJsaG/sGRcBAjUZJY41CN9CY
AppTech.Common.Security.EncrytionHelper/KeySize
sGRcBAjUZJY41CN9CY
KeySize
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=6
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=6
__StaticArrayInitTypeSize=6
__StaticArrayInitTypeSize=6
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=11
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=11
__StaticArrayInitTypeSize=11
__StaticArrayInitTypeSize=11
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=16
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=24
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=24
__StaticArrayInitTypeSize=24
__StaticArrayInitTypeSize=24
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=32
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
<<type>>
<Module>{683EDBD7-BE67-42DF-9F69-957D6005E46B}
<Module>{683EDBD7-BE67-42DF-9F69-957D6005E46B}
<Module>{683EDBD7-BE67-42DF-9F69-957D6005E46B}
<Module>{683EDBD7-BE67-42DF-9F69-957D6005E46B}
<<type>>
xVjveg8Q5EQcTjaO1v.tI0VOZL0GUy8QnxKw0
SOj3wtG2Ob7xEudvw7.CDCWSn7SaPjUwoq2Cc
tI0VOZL0GUy8QnxKw0
CDCWSn7SaPjUwoq2Cc
cGLJrDIe5W
TWp4PNnQc
<<type>>
xVjveg8Q5EQcTjaO1v.tI0VOZL0GUy8QnxKw0/SFU4mbT3GMret7THonf
SOj3wtG2Ob7xEudvw7.CDCWSn7SaPjUwoq2Cc/SFU4mbT3GMret7THonf
SFU4mbT3GMret7THonf
SFU4mbT3GMret7THonf
<<type>>
Us4JDWMhIP6PsDcmsI.or6cUGkQW8XqXcJSgg
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il
or6cUGkQW8XqXcJSgg
DyyVDbaRvM1YfIq9il
aTQJK5kwHk
creoiNvd7
DIFJorgqA2
jZiU8kt7k
nVBJeG212u
yIEeUuogE
dlcJ4a0LHI
HNMMnrD0K
n9VJXxHJGS
U6ZIpjiMV
HtnJm1uTUi
TYIaeXNeW
kiyJx7POQQ
rI3lmZ9FL
vJMJvxeRJA
SuhhReBcy
CVGJTTcUOK
QWOOk18h0
e93JdE5BGK
BjkXsyRir
kTiJcv4DMJ
mCC9ZT9yx
tqeJEr41D4
b82VQ34LR
rOmJIUuAMa
P4kZBQ8Uk
sXCJUGLT0d
KX0HrYNeb
oqUJljy8vL
pvQ2Nvbv9
YS4J1cloCq
KqVWF2r0M
OixJiS0tLp
SR2f8Si0X
FAFJYwHpek
LXFsnj021
RjgJBVUA1B
jMyYFyWuy
jNeJhWadts
NvQ34uZt895nxEhi2FIr
znLJNvmSrU
gVU0QeojF
xX4JOuFm8c
HK2JaffxR
txHJCW8T3I
ubITRqgdO
qQ5JWP0BIi
vEB6drODu
R3vJS3KxhQ
vZF7RiFiF
oUcJ3hGLCX
puGi6bKKk
moAJZOO58G
ROhFJh1RB
J2jJGx9fb6
T7LBbJ4ta
q2OJQVTOSD
fMdPu7i25
Ke8JqgrjDh
yMayDYsjD
KIiJn0Auvs
Kxm8CyXvJ
Do7JztGORK
JkHjxJCFT
ywFAuFmt86
eM2t2dfoT
VPVAwDWKTK
vDfq2bW1V
J4qAbZSAfb
B3XRfqih9
YTiAJyKEX5
sVk5WFvVV
WiDAABWjKV
E3GryunuI
vZrARe2TY1
yxOcIGI9u
pmFA7kYpnt
Oihu8LNHm
siDAssipjc
ifqQyNVWS
AQnAHXuPrM
hcDmskCdX
SF7APH9gpZ
mKgSOTjDj
fV3AaMYQCn
aYTwtN0c5
T7pAFh3LYF
udfDaXdkp
UdeADjCupZ
NrL10qsNW
nvRAy5c1AA
j8hgmZJ7n
Tv8ApSW18J
M6EKmwjSJ
gZhA22Pplq
PVVpfAGtG
loaAfpWkmA
cQCd71PIW
Q50Ag79yry
lodECQQVs
a2iAVByh3O
VvPxdPh3O
pIfAjSB0Tu
hIsn23p8h
kDOAL4qO7B
dKMLoMpMs
q0uA8u5QiD
ghLACNa05
SVxAkdnyjT
c9FNce5cf
CkXAMQ748W
diL3t0peo
z1SA6GEoFE
sMgC0o5PW
bsCA09DaTn
S0FvrGWpN
xOEA51Hk1k
hSjGubHK9
njCA96MfwV
d1uknJpcW
jw5Ath1a2l
uS9zmJ6WC
kPvAr8xgW4
i244bikuos
VfOAKicY6h
bFB44BUGlg
qkyAoUrEol
x3c4o2PyTx
wmvAeMZq95
phV4Uu6SUx
vjRA43Z1PO
Qwp4ejR7FG
bmWAX6XsKA
TWn4MujlZv
C6DAmfmGB3
NFL4IGyoc7
dl9AxI8l4c
WS94a0Vnlv
kfSAvtVJBn
XtL4lyIIgx
MLTATUskXy
firstrundone
i1wAdcHU4e
IBe4hEip2A
<<type>>
Us4JDWMhIP6PsDcmsI.or6cUGkQW8XqXcJSgg/Br4XMs6Ws2L5GwDZT7
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/AXBrnIFfMAfABnJrF9
Br4XMs6Ws2L5GwDZT7
AXBrnIFfMAfABnJrF9
<<type>>
Us4JDWMhIP6PsDcmsI.or6cUGkQW8XqXcJSgg/Br4XMs6Ws2L5GwDZT7/pFY7vu0sTj7X9omQsm`1
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/AXBrnIFfMAfABnJrF9/z0oyxsqySXMDuI4ZyY`1
pFY7vu0sTj7X9omQsm`1
z0oyxsqySXMDuI4ZyY`1
<<type>>
Us4JDWMhIP6PsDcmsI.or6cUGkQW8XqXcJSgg/VJKH7k9b4ALODhk9E6
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/ay67rn8SHAWRagidNL
VJKH7k9b4ALODhk9E6
ay67rn8SHAWRagidNL
qZgAc6ghGO
D4r4O0AxSI
<<type>>
Us4JDWMhIP6PsDcmsI.or6cUGkQW8XqXcJSgg/z5tZ3jtEWvdBmbgVqF
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/rL2N9N6wh7IWY3IC3G
z5tZ3jtEWvdBmbgVqF
rL2N9N6wh7IWY3IC3G
<<type>>
Us4JDWMhIP6PsDcmsI.or6cUGkQW8XqXcJSgg/VeMBhPrAG4Ov0ObToV
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/LhmiV9AUoOr1v5yhIs
VeMBhPrAG4Ov0ObToV
LhmiV9AUoOr1v5yhIs
<<type>>
Us4JDWMhIP6PsDcmsI.or6cUGkQW8XqXcJSgg/UYQs1xKgw7KfXrAkLV
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/Lk7BwHKFmNJY32ZC3n
UYQs1xKgw7KfXrAkLV
Lk7BwHKFmNJY32ZC3n
PKxAEnd5Ap
bV44XU8KQo
TDTAIaOSqw
Uu349Vtr47
<<type>>
Us4JDWMhIP6PsDcmsI.or6cUGkQW8XqXcJSgg/rRKsFfoRgZ9HVlEIAw
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/WDRJe2H6E4HVV6PGZs
rRKsFfoRgZ9HVlEIAw
WDRJe2H6E4HVV6PGZs
<<type>>
pFLLO54443ksg6n6f4.fyUJQ5eCJSCmAVBjFy
cH8IXcwQY4Peh2qpAn.xrUtBVoaXtCT6B0w6a
fyUJQ5eCJSCmAVBjFy
xrUtBVoaXtCT6B0w6a
pvBAUh3Z3m
ywq4VEynyU
<<type>>
i92uEkmDYY8fRIrc5l.r2yaX1XPNUwiOVlMlt
c5uHW3cSW8ou55rAF3.KKr6hZkjvwWjdm9A4Z
r2yaX1XPNUwiOVlMlt
KKr6hZkjvwWjdm9A4Z
A3UAlXqpxp
Uur4ZuAaiM
<<type>>
q9OTr6v7kQesGmV1L9.eipXhFxZaZaMTdj9KF
CvXDWVQi7y2eO3gBAC.OsyMlHJSvCHNZySQs6
eipXhFxZaZaMTdj9KF
OsyMlHJSvCHNZySQs6
<<type>>
Ewp1VwdkYBprZp5Y0Q.P9BM26TPL2iETojyh9
zsd5DaYg9lOJGmlIO4.R2mIapWar4cwoqqx6Q
P9BM26TPL2iETojyh9
R2mIapWar4cwoqqx6Q
gn3A14avW1
HNM4YkXJs5
tE6AiJYXZI
pfJ40gjxwv
B2lAY7TMi2
eBxqprrF8
alqABwOeXP
Ypf4J7ba8u
IFKAhlZFkj
CCw4Tb9h3V
Iy5ANjkLj0
n3x46T2MQ2
dfWAOPkI4p
WP947UZNwy
yRjACmVZmQ
Fko4i7KTuh
<<type>>
Ewp1VwdkYBprZp5Y0Q.P9BM26TPL2iETojyh9/gqFVeZcgMUQvophjRq
zsd5DaYg9lOJGmlIO4.R2mIapWar4cwoqqx6Q/dde9wksVEKdElHkEKH
gqFVeZcgMUQvophjRq
dde9wksVEKdElHkEKH
<<type>>
Ewp1VwdkYBprZp5Y0Q.P9BM26TPL2iETojyh9/kYVwygE6xc4jHZjvd8
zsd5DaYg9lOJGmlIO4.R2mIapWar4cwoqqx6Q/T9eZG8XLTT9vNo3j18
kYVwygE6xc4jHZjvd8
T9eZG8XLTT9vNo3j18
KDfAWdoaXL
IWZ4FNxMCV
i33AS0Wttp
X4o4BaXNNW
RtAA3n2ExM
ReR4PkWY9i
tGAAZwnwHg
XZO4yOqtpA
kbVAGWu4Ux
pcT48wm9UY
LAoAQTbhws
Y9l4jroko9
rMXAqZN1SZ
OY84tBcMwd
oxAAncyUXR
JrQ4qkE5mX
wfaAznxSZy
iRM4R10ean
MWsRuB1xfu
AGe45CEX5X
UfDRwqPVb4
Goe4rkO7Su
rGURbM7d9k
Tt04cJf5Ud
RTZRJSLFiZ
wDU4ucXGpO
mnBRAxQg6k
HGp4Q5R9ww
PhVRRMsBqC
FvC4mE2qIR
YYER7dX96b
iv04SsOrFF
TvARsfFBhM
zBi4wdjAN2
a5LRHUDtA0
PN14D93Kyx
WucRPoxu5C
ulr41vALu8
XCgRaADNfB
lQp4gbkEqU
CvmRFkIZOs
IRA4KTlYfd
<<type>>
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}
<<type>>
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=256
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=256
__StaticArrayInitTypeSize=256
__StaticArrayInitTypeSize=256
<<type>>
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=40
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=40
__StaticArrayInitTypeSize=40
__StaticArrayInitTypeSize=40
<<type>>
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=30
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=30
__StaticArrayInitTypeSize=30
__StaticArrayInitTypeSize=30
<<type>>
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=32
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
<<type>>
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=16
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
<<type>>
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=64
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=64
__StaticArrayInitTypeSize=64
__StaticArrayInitTypeSize=64
<<type>>
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=18
<PrivateImplementationDetails>{8EA978DC-266B-4191-B16C-56C8E0C340C1}/__StaticArrayInitTypeSize=18
__StaticArrayInitTypeSize=18
__StaticArrayInitTypeSize=18
