<?xml version="1.0"?>
<doc>
    <assembly>
        <name>dnSpy.Roslyn</name>
    </assembly>
    <members>
        <member name="T:dnSpy.Roslyn.Compiler.CSharp.CSharpCompilerSettings">
            <summary>
            C# compiler settings
            </summary>
        </member>
        <member name="E:dnSpy.Roslyn.Compiler.CSharp.CSharpCompilerSettings.PropertyChanged">
            <summary>
            Raised when a property is changed
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Compiler.CSharp.CSharpCompilerSettings.OnPropertyChanged(System.String)">
            <summary>
            Raises <see cref="E:dnSpy.Roslyn.Compiler.CSharp.CSharpCompilerSettings.PropertyChanged"/>
            </summary>
            <param name="propName">Name of property that got changed</param>
        </member>
        <member name="P:dnSpy.Roslyn.Compiler.CSharp.CSharpCompilerSettings.PreprocessorSymbols">
            <summary>
            Conditional compilation symbols, separated by ';' or ','
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Compiler.CSharp.CSharpCompilerSettings.Optimize">
            <summary>
            Optimize the code (release builds)
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Compiler.CSharp.CSharpCompilerSettings.CheckOverflow">
            <summary>
            Add overflow checks
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Compiler.CSharp.CSharpCompilerSettings.AllowUnsafe">
            <summary>
            Allow unsafe code
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Compiler.CSharp.CSharpCompilerSettingsBase.Clone">
            <summary>
            Clones this instance
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Compiler.CSharp.CSharpCompilerSettingsBase.CopyTo(dnSpy.Roslyn.Compiler.CSharp.CSharpCompilerSettingsBase)">
            <summary>
            Copies this to <paramref name="other"/> and returns <paramref name="other"/>
            </summary>
            <param name="other">Other instance</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettings">
            <summary>
            Visual Basic compiler settings
            </summary>
        </member>
        <member name="E:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettings.PropertyChanged">
            <summary>
            Raised when a property is changed
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettings.OnPropertyChanged(System.String)">
            <summary>
            Raises <see cref="E:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettings.PropertyChanged"/>
            </summary>
            <param name="propName">Name of property that got changed</param>
        </member>
        <member name="P:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettings.PreprocessorSymbols">
            <summary>
            Conditional compilation symbols, separated by ';' or ','. Key=Value pairs are allowed, but only a limited set of values
            are supported (<see cref="T:System.Boolean"/>, <see cref="T:System.Int32"/>, <see cref="T:System.Double"/>, <see cref="T:System.String"/>). String values can have double quotes.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettings.Optimize">
            <summary>
            Optimize the code (release builds)
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettings.OptionExplicit">
            <summary>
            Require explicit declaration of variables
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettings.OptionInfer">
            <summary>
            Allow type inference of variables
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettings.OptionStrict">
            <summary>
            Enforce strict language semantics
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettings.OptionCompareBinary">
            <summary>
            true to use binary-style string comparisons, false to use text-style string comparisons
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettings.EmbedVBRuntime">
            <summary>
            true to always embed the VB runtime, false to use the default behavior (either use the runtime in the GAC or
            embed it depending on the target framework)
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettingsBase.Clone">
            <summary>
            Clones this instance
            </summary>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettingsBase.CopyTo(dnSpy.Roslyn.Compiler.VisualBasic.VisualBasicCompilerSettingsBase)">
            <summary>
            Copies this to <paramref name="other"/> and returns <paramref name="other"/>
            </summary>
            <param name="other">Other instance</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Debugger.ExpressionCompiler.VisualBasic.GeneratedNames.TryParseStateMachineHoistedUserVariableName(System.String,System.String@,System.Int32@)">
            <summary>
            Try to parse the local name and return <paramref name="variableName"/> and <paramref name="index"/> if successful.
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Debugger.ValueNodes.DbgDotNetValueNodeProviderFactory.HasNoChildren(dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Returns true if <paramref name="type"/> is a primitive type that doesn't show any members,
            eg. integers, booleans, floating point numbers, strings
            </summary>
            <param name="type">Type to check</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Debugger.ValueNodes.DynamicMetaObjectProviderDebugViewHelper.GetDynamicMetaObjectProviderDebugViewConstructor(dnSpy.Debugger.DotNet.Metadata.DmdAppDomain)">
            <summary>
            Returns the debug view constructor for objects implementing <see cref="T:System.Dynamic.IDynamicMetaObjectProvider"/>
            or COM objects.
            
            The debug view is in the <c>Microsoft.CSharp</c> assembly. If it's not been loaded, this method
            returns null.
            </summary>
            <param name="appDomain">AppDomain</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Debugger.ValueNodes.EnumerableDebugViewHelper.GetEnumerableDebugViewConstructor(dnSpy.Debugger.DotNet.Metadata.DmdType)">
            <summary>
            Returns an enumerable debug view. These types are located in System.Core / System.Linq. If <paramref name="enumerableType"/>
            is <see cref="T:System.Collections.IEnumerable"/>, then <c>System.Linq.SystemCore_EnumerableDebugView</c>'s constructor
            is returned, else if <paramref name="enumerableType"/> is <see cref="T:System.Collections.Generic.IEnumerable`1"/>, then
            <c>System.Linq.SystemCore_EnumerableDebugView`1</c>'s constructor is returned.
            </summary>
            <param name="enumerableType">Enumerable type, must be one of <see cref="T:System.Collections.IEnumerable"/>, <see cref="T:System.Collections.Generic.IEnumerable`1"/></param>
            <returns></returns>
        </member>
        <member name="F:dnSpy.Roslyn.Debugger.ValueNodes.MemberValueNodeInfo.InheritanceLevel">
            <summary>
            Most derived class is level 0, and least derived class (<see cref="T:System.Object"/>) has the highest level
            </summary>
        </member>
        <member name="F:dnSpy.Roslyn.Debugger.ValueNodes.TupleField.DefaultName">
            <summary>
            Item1, Item2, etc
            </summary>
        </member>
        <member name="F:dnSpy.Roslyn.Debugger.ValueNodes.TupleField.Fields">
            <summary>
            All fields that must be accessed in order to get the value shown in the UI, eg. Rest.Rest.Item3
            </summary>
        </member>
        <member name="T:dnSpy.Roslyn.Utilities.UnicodeCharacterUtilities">
            <summary>
            Defines a set of helper methods to classify Unicode characters.
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Utilities.UnicodeCharacterUtilities.IsIdentifierPartCharacter(System.Char)">
            <summary>
            Returns true if the Unicode character can be a part of an identifier.
            </summary>
            <param name="ch">The Unicode character.</param>
        </member>
        <member name="M:dnSpy.Roslyn.Utilities.UnicodeCharacterUtilities.IsValidIdentifier(System.String)">
            <summary>
            Check that the name is a valid Unicode identifier.
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Utilities.UnicodeCharacterUtilities.IsFormattingChar(System.Char)">
            <summary>
            Returns true if the Unicode character is a formatting character (Unicode class Cf).
            </summary>
            <param name="ch">The Unicode character.</param>
        </member>
        <member name="M:dnSpy.Roslyn.Utilities.UnicodeCharacterUtilities.IsFormattingChar(System.Globalization.UnicodeCategory)">
            <summary>
            Returns true if the Unicode character is a formatting character (Unicode class Cf).
            </summary>
            <param name="cat">The Unicode character.</param>
        </member>
        <member name="T:dnSpy.Roslyn.Documentation.IRoslynDocumentationProviderFactory">
            <summary>
            Creates <see cref="T:Microsoft.CodeAnalysis.DocumentationProvider"/>s
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Documentation.IRoslynDocumentationProviderFactory.TryCreate(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.CodeAnalysis.DocumentationProvider"/> or returns null if it wasn't possible to create one
            </summary>
            <param name="filename">Filename</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Intellisense.Completions.RoslynCompletionSet.GetDescriptionAsync(dnSpy.Roslyn.Intellisense.Completions.RoslynCompletion,System.Threading.CancellationToken)">
            <summary>
            Gets the description or null if none
            </summary>
            <param name="completion">Completion</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Intellisense.QuickInfo.InformationQuickInfoContentControl.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Intellisense.SignatureHelp.SignatureHelpSession.TryGetSession(Microsoft.VisualStudio.Language.Intellisense.ISignatureHelpSession)">
            <summary>
            Gets the Roslyn sig help session stored in a <see cref="T:Microsoft.VisualStudio.Language.Intellisense.ISignatureHelpSession"/> or null if none
            </summary>
            <param name="session">Intellisense sig help session</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.ClassesToolTip">
            <summary>
              Looks up a localized string similar to Classes.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.CompilerSettings_AllowUnsafe">
            <summary>
              Looks up a localized string similar to Allow unsafe code.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.CompilerSettings_CheckOverflow">
            <summary>
              Looks up a localized string similar to Check for arithmetic overflow/underflow.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.CompilerSettings_EmbedVBRuntime">
            <summary>
              Looks up a localized string similar to Embed Visual Basic runtime.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.CompilerSettings_Optimize">
            <summary>
              Looks up a localized string similar to Optimize code.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.CompilerSettings_OptionCompareBinary">
            <summary>
              Looks up a localized string similar to Option compare: binary.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.CompilerSettings_OptionExplicit">
            <summary>
              Looks up a localized string similar to Option explicit.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.CompilerSettings_OptionInfer">
            <summary>
              Looks up a localized string similar to Option infer.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.CompilerSettings_OptionStrict">
            <summary>
              Looks up a localized string similar to Option strict.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.CompilerSettings_PreprocessorSymbols">
            <summary>
              Looks up a localized string similar to Conditional compilation symbols.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.CompletionSet_All">
            <summary>
              Looks up a localized string similar to All.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.ConstantsToolTip">
            <summary>
              Looks up a localized string similar to Constants.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DebuggerExpressionHasNoValue">
            <summary>
              Looks up a localized string similar to Expression has been evaluated and has no value.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DebuggerVarsWindow_CSharp_StaticMembers">
            <summary>
              Looks up a localized string similar to Static members.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DebuggerVarsWindow_DynamicView">
            <summary>
              Looks up a localized string similar to Dynamic View.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DebuggerVarsWindow_Error_PropertyName">
            <summary>
              Looks up a localized string similar to Error.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DebuggerVarsWindow_ExpandDynamicViewMessage">
            <summary>
              Looks up a localized string similar to Expanding the Dynamic View will get the dynamic members for the object.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DebuggerVarsWindow_ExpandResultsViewMessage">
            <summary>
              Looks up a localized string similar to Expanding the Results View will enumerate the IEnumerable.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DebuggerVarsWindow_InstanceMembers">
            <summary>
              Looks up a localized string similar to Instance members.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DebuggerVarsWindow_RawView">
            <summary>
              Looks up a localized string similar to Raw View.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DebuggerVarsWindow_ResultsView">
            <summary>
              Looks up a localized string similar to Results View.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DebuggerVarsWindow_VisualBasic_SharedMembers">
            <summary>
              Looks up a localized string similar to Shared members.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DelegatesToolTip">
            <summary>
              Looks up a localized string similar to Delegates.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DynamicView_MustBeDynamicOrComType">
            <summary>
              Looks up a localized string similar to Only COM or Dynamic objects can have Dynamic View.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.DynamicViewAssemblyNotLoaded">
            <summary>
              Looks up a localized string similar to Dynamic View requires {0} to be referenced.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.EnumsToolTip">
            <summary>
              Looks up a localized string similar to Enums.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.EventsToolTip">
            <summary>
              Looks up a localized string similar to Events.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.ExtensionMethodsToolTip">
            <summary>
              Looks up a localized string similar to Extension methods.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.FieldsToolTip">
            <summary>
              Looks up a localized string similar to Fields.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.FilterExpressionEvaluator_CompiledExpressionThrewAnException">
            <summary>
              Looks up a localized string similar to Compiled expression threw an exception: {0}.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.FilterExpressionEvaluator_InvalidExpression">
            <summary>
              Looks up a localized string similar to Expression is invalid.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.InterfacesToolTip">
            <summary>
              Looks up a localized string similar to Interfaces.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.KeywordsToolTip">
            <summary>
              Looks up a localized string similar to Keywords.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.LocalsAndParametersToolTip">
            <summary>
              Looks up a localized string similar to Locals and parameters.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.LocalsWindow_MethodOrProperty_Returned">
            <summary>
              Looks up a localized string similar to {0} returned.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.LocalsWindow_TypeVariables">
            <summary>
              Looks up a localized string similar to Type variables.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.MethodsToolTip">
            <summary>
              Looks up a localized string similar to Methods.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.ModulesToolTip">
            <summary>
              Looks up a localized string similar to Modules.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.NamespacesToolTip">
            <summary>
              Looks up a localized string similar to Namespaces.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.PropertiesToolTip">
            <summary>
              Looks up a localized string similar to Properties.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.ResultsView_MustBeEnumerableType">
            <summary>
              Looks up a localized string similar to Only Enumerable types can have Results View.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.SnippetsToolTip">
            <summary>
              Looks up a localized string similar to Snippets.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.StructuresToolTip">
            <summary>
              Looks up a localized string similar to Structures.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.SystemCoreDllNotLoaded">
            <summary>
              Looks up a localized string similar to Results View requires {0} to be referenced.
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Properties.dnSpy_Roslyn_Resources.TypeDoesNotExistInAssembly">
            <summary>
              Looks up a localized string similar to Type &apos;{0}&apos; doesn&apos;t exist in assembly &apos;{1}&apos;.
            </summary>
        </member>
        <member name="T:dnSpy.Roslyn.Text.Classification.ITaggedTextElementProvider">
            <summary>
            Creates a <see cref="T:System.Windows.Controls.TextBlock"/>. Call its <see cref="M:System.IDisposable.Dispose"/> method
            to clean up its resources.
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Classification.ITaggedTextElementProvider.Create(System.String,System.Collections.Immutable.ImmutableArray{Microsoft.CodeAnalysis.TaggedText},System.Boolean)">
            <summary>
            Creates a <see cref="T:System.Windows.Controls.TextBlock"/>
            </summary>
            <param name="tag">Tag, can be null</param>
            <param name="taggedParts">Tagged parts to classify</param>
            <param name="colorize">true if it should be colorized</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Classification.ITaggedTextElementProviderService.Create(Microsoft.VisualStudio.Utilities.IContentType,System.String)">
            <summary>
            Creates a <see cref="T:dnSpy.Roslyn.Text.Classification.ITaggedTextElementProvider"/>
            </summary>
            <param name="contentType">Content type</param>
            <param name="category">Category, eg. <see cref="F:dnSpy.Contracts.Settings.AppearanceCategory.AppearanceCategoryConstants.TextEditor"/></param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Roslyn.Text.Classification.RoslynClassificationTypes">
            <summary>
            Classification types used by <see cref="T:dnSpy.Roslyn.Text.Classification.RoslynClassifier"/>
            </summary>
        </member>
        <member name="F:dnSpy.Roslyn.Text.Classification.RoslynClassificationTypes.Default">
            <summary>
            Gets the default instance
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Classification.RoslynClassificationTypes.GetClassificationTypeInstance(dnSpy.Contracts.Text.Classification.IThemeClassificationTypeService)">
            <summary>
            Gets the cached instance that contains <see cref="T:Microsoft.VisualStudio.Text.Classification.IClassificationType"/> values
            </summary>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Roslyn.Text.Classification.ClassifierResult">
            <summary>
            Classifier result
            </summary>
        </member>
        <member name="F:dnSpy.Roslyn.Text.Classification.ClassifierResult.Span">
            <summary>
            Span
            </summary>
        </member>
        <member name="F:dnSpy.Roslyn.Text.Classification.ClassifierResult.Color">
            <summary>
            Color
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Classification.ClassifierResult.#ctor(Microsoft.VisualStudio.Text.Span,System.Object)">
            <summary>
            Constructor
            </summary>
            <param name="span">Span</param>
            <param name="color">Color</param>
        </member>
        <member name="T:dnSpy.Roslyn.Text.Classification.RoslynClassifier">
            <summary>
            Roslyn classifier
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Classification.RoslynClassifier.#ctor(Microsoft.CodeAnalysis.SyntaxNode,Microsoft.CodeAnalysis.SemanticModel,Microsoft.CodeAnalysis.Workspace,dnSpy.Roslyn.Text.Classification.RoslynClassificationTypes,System.Object,System.Threading.CancellationToken)">
            <summary>
            Constructor
            </summary>
            <param name="syntaxRoot">Syntax root</param>
            <param name="semanticModel">Semantic model</param>
            <param name="workspace">Workspace</param>
            <param name="roslynClassificationTypes">Colors</param>
            <param name="defaultColor">Default color if a token can't be classified or null to not use anything</param>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Classification.RoslynClassifier.GetColors(Microsoft.CodeAnalysis.Text.TextSpan)">
            <summary>
            Returns all colors
            </summary>
            <param name="textSpan">Span to classify</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Roslyn.Text.Classification.TaggedTextClassifierContext">
            <summary>
            Context passed to the <see cref="T:Microsoft.CodeAnalysis.TaggedText"/> classifiers
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Text.Classification.TaggedTextClassifierContext.TaggedParts">
            <summary>
            Gets all tagged parts
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Classification.TaggedTextClassifierContext.Create(System.String,System.Collections.Immutable.ImmutableArray{Microsoft.CodeAnalysis.TaggedText},System.Boolean)">
            <summary>
            Creates an instance
            </summary>
            <param name="tag">Tag, can be null</param>
            <param name="taggedParts">Tagged parts</param>
            <param name="colorize">true if it should be colorized</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Roslyn.Text.Classification.TextTagsHelper">
            <summary>
            Converts <see cref="T:Microsoft.CodeAnalysis.TextTags"/> tags to <see cref="T:dnSpy.Contracts.Text.TextColor"/> values
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Classification.TextTagsHelper.ToTextColor(System.String)">
            <summary>
            Converts <paramref name="textTag"/> to a <see cref="T:dnSpy.Contracts.Text.TextColor"/> value
            </summary>
            <param name="textTag">One of the text tags found in <see cref="T:Microsoft.CodeAnalysis.TextTags"/></param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Roslyn.Text.Extensions">
            <summary>
            Extension methods
            </summary>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Extensions.AsText(Microsoft.VisualStudio.Text.ITextSnapshot)">
            <summary>
            Converts <paramref name="textSnapshot"/> to a <see cref="T:Microsoft.CodeAnalysis.Text.SourceText"/>
            </summary>
            <param name="textSnapshot">Snapshot</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Extensions.AsTextContainer(Microsoft.VisualStudio.Text.ITextBuffer)">
            <summary>
            Converts <paramref name="textBuffer"/> to a <see cref="T:Microsoft.CodeAnalysis.Text.SourceTextContainer"/>
            </summary>
            <param name="textBuffer">Text buffer</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Extensions.TryFindEditorTextBuffer(Microsoft.CodeAnalysis.Text.SourceTextContainer)">
            <summary>
            Returns a <see cref="T:Microsoft.VisualStudio.Text.ITextBuffer"/> or null
            </summary>
            <param name="textContainer">Text container</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Extensions.TryGetWorkspace(Microsoft.VisualStudio.Text.ITextBuffer)">
            <summary>
            Returns the workspace or null
            </summary>
            <param name="buffer">Text buffer</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Extensions.TryFindEditorSnapshot(Microsoft.CodeAnalysis.Text.SourceText)">
            <summary>
            Gets the snapshot or null
            </summary>
            <param name="sourceText">Source text</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Extensions.TryFindEditorTextImage(Microsoft.CodeAnalysis.Text.SourceText)">
            <summary>
            Gets the text image or null
            </summary>
            <param name="sourceText">Source text</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Extensions.ToTextSpan(Microsoft.VisualStudio.Text.Span)">
            <summary>
            Converts <paramref name="span"/> to a <see cref="T:Microsoft.CodeAnalysis.Text.TextSpan"/>
            </summary>
            <param name="span">Span</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Extensions.ToSpan(Microsoft.CodeAnalysis.Text.TextSpan)">
            <summary>
            Converts <paramref name="textSpan"/> to a <see cref="T:Microsoft.VisualStudio.Text.Span"/>
            </summary>
            <param name="textSpan"></param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Extensions.GetOpenDocumentInCurrentContextWithChanges(Microsoft.VisualStudio.Text.ITextSnapshot)">
            <summary>
            Gets the document or null
            </summary>
            <param name="snapshot">Snapshot</param>
            <returns></returns>
        </member>
        <member name="M:dnSpy.Roslyn.Text.Extensions.GetOpenDocumentInCurrentContextWithChanges(Microsoft.CodeAnalysis.Text.SourceText)">
            <summary>
            Gets the document or null
            </summary>
            <param name="text">Source text</param>
            <returns></returns>
        </member>
        <member name="T:dnSpy.Roslyn.Text.RoslynMefHostServices">
            <summary>
            Shared <see cref="T:Microsoft.CodeAnalysis.Host.Mef.MefHostServices"/> instance
            </summary>
        </member>
        <member name="P:dnSpy.Roslyn.Text.RoslynMefHostServices.DefaultServices">
            <summary>
            Gets the shared <see cref="T:Microsoft.CodeAnalysis.Host.Mef.MefHostServices"/> instance
            </summary>
        </member>
        <member name="T:dnSpy.Roslyn.Text.Tagging.AsyncTagger`2">
             <summary>
             Async tagger base class. Multiple <see cref="M:dnSpy.Roslyn.Text.Tagging.AsyncTagger`2.GetTags(Microsoft.VisualStudio.Text.NormalizedSnapshotSpanCollection)"/>
             calls are handled by one task to prevent too many created tasks.
             </summary>
             <typeparam name="TTagType">Type of tag, eg. <see cref="T:Microsoft.VisualStudio.Text.Tagging.IClassificationTag"/></typeparam>
             <typeparam name="TUserAsyncState">User async state type</typeparam>
             <remarks>
             All tags are cached. The cache is invalidated whenever <see cref="M:dnSpy.Roslyn.Text.Tagging.AsyncTagger`2.GetTags(Microsoft.VisualStudio.Text.NormalizedSnapshotSpanCollection)"/>
             is called with a new snapshot.
            
             It currently doesn't try to re-use the old calculated tags. It could return those (after
             TranslateTo()'ing them to the new snapshot) while it executes the async code in the background
             that calculates the new tags.
             </remarks>
        </member>
    </members>
</doc>
